"use strict";exports.id=982,exports.ids=[982],exports.modules={2982:(e,t,n)=>{n.d(t,{TU:()=>c});var r=n(7554),a=n(917),o=n(8280),i=n(6517);class s{constructor(){this.models={"llama3-8b-8192":{name:"Llama 3 8B",maxTokens:8192,bestFor:["general","fast"],costPer1kTokens:.05},"llama3-70b-8192":{name:"Llama 3 70B",maxTokens:8192,bestFor:["complex","detailed"],costPer1kTokens:.59},"mixtral-8x7b-32768":{name:"Mixtral 8x7B",maxTokens:32768,bestFor:["long-form","technical"],costPer1kTokens:.24},"gemma-7b-it":{name:"Gemma 7B",maxTokens:8192,bestFor:["efficient","instruction"],costPer1kTokens:.07}},this.client=new r.ZP({apiKey:a.vc.apis.groq.apiKey}),this.defaultConfig={model:"llama3-8b-8192",temperature:.7,maxTokens:4e3,topP:.9,frequencyPenalty:0,presencePenalty:0}}async generateContent(e,t){return(0,o.bi)("groq",async()=>{let n=Date.now(),r=this.selectOptimalModel(e),a=this.buildSystemPrompt(e),o=this.buildUserPrompt(e);console.log("Generating content with Groq...",{model:r.model,wordCount:e.targetWordCount,contentType:e.contentType});let s=await this.client.chat.completions.create({model:r.model,messages:[{role:"system",content:a},{role:"user",content:o}],temperature:r.temperature,max_tokens:r.maxTokens,top_p:r.topP,frequency_penalty:r.frequencyPenalty,presence_penalty:r.presencePenalty,stream:!1}),c=Date.now()-n,l=s.usage?.total_tokens||0,u=this.calculateCost(r.model,l);t&&await (0,i.xc)({user_id:t,api_name:"groq",endpoint:"chat/completions",method:"POST",tokens_used:l,cost:u,response_time_ms:c,status_code:200});let d=s.choices[0]?.message?.content||"",h=await this.parseAndStructureContent(d,e);return console.log("Content generation completed",{responseTime:c,tokensUsed:l,cost:u,wordCount:h.seoMetrics.wordCount}),h},{method:"POST",endpoint:"chat/completions",model:this.selectOptimalModel(e).model,...e},t)}selectOptimalModel(e){let t="llama3-8b-8192";e.targetWordCount>2e3?t="mixtral-8x7b-32768":"technical"===e.tone||"blog"===e.contentType?t="llama3-70b-8192":e.targetWordCount<500&&(t="gemma-7b-it");let n=this.models[t];return{model:t,temperature:this.getOptimalTemperature(e),maxTokens:Math.min(Math.floor(1.5*e.targetWordCount),n.maxTokens-1e3),topP:.9,frequencyPenalty:"blog"===e.contentType?.1:0,presencePenalty:0}}getOptimalTemperature(e){switch(e.contentType){case"service":case"product":return .3;case"blog":default:return .7;case"landing":return .5;case"faq":return .2}}buildSystemPrompt(e){return`You are an expert SEO content writer specializing in ${e.industry} industry content. Your task is to create ${e.contentType} content that ranks highly in search engines.

CRITICAL REQUIREMENTS:
1. Write in ${e.tone} tone for ${e.intent} intent
2. Target exactly ${e.targetWordCount} words
3. Use clear subject-verb-object sentence structure
4. NEVER use these forbidden words: meticulous, navigating, complexities, realm, bespoke, tailored, towards, underpins, ever-changing, ever-evolving, the world of, not only, seeking more than just, designed to enhance, it's not merely, our suite, it is advisable, daunting, in the heart of, when it comes to, in the realm of, amongst, unlock the secrets, unveil the secrets, robust
5. Focus on E-E-A-T: Expertise, Experience, Authoritativeness, Trustworthiness
6. Include natural keyword variations and LSI keywords
7. Write for humans first, search engines second

CONTENT STRUCTURE:
- Strong, compelling title with primary keyword
- Meta description (150-160 characters)
- Clear heading hierarchy (H1, H2, H3, H4)
- Natural keyword distribution throughout content
- Actionable insights and practical information
- Strong conclusion with clear next steps

SEO OPTIMIZATION:
- Primary keyword: "${e.keyword}"
- Target location: ${e.location}
- Industry context: ${e.industry}
- Content intent: ${e.intent}
${e.brandVoice?`- Brand voice: ${e.brandVoice}`:""}
${e.targetAudience?`- Target audience: ${e.targetAudience}`:""}

QUALITY STANDARDS:
- Write engaging, valuable content that users want to read
- Use data, statistics, and examples when relevant
- Include practical tips and actionable advice
- Maintain readability with short paragraphs and bullet points
- Ensure content flows naturally and logically

Return the content in this exact JSON format:
{
  "title": "SEO-optimized title with primary keyword",
  "metaDescription": "Compelling 150-160 character meta description",
  "content": "Full article content with proper HTML formatting",
  "outline": {
    "headings": [
      {"level": 1, "text": "H1 heading", "keywords": ["keyword1", "keyword2"]},
      {"level": 2, "text": "H2 heading", "keywords": ["keyword1"]}
    ]
  }
}`}buildUserPrompt(e){let t=`Create ${e.contentType} content about "${e.keyword}" targeting ${e.location}.

CONTENT REQUIREMENTS:
- Industry: ${e.industry}
- Tone: ${e.tone}
- Intent: ${e.intent}
- Word count: ${e.targetWordCount} words
- Include practical examples and actionable advice
- Write for ${e.targetAudience||"general audience"}`;return e.competitorData&&(t+=`

COMPETITOR INSIGHTS:
Based on competitor analysis, include these elements:
- Average competitor word count: ${e.competitorData.averageWordCount||"Not specified"}
- Common topics covered: ${e.competitorData.commonTopics?.join(", ")||"Not specified"}
- Content gaps to address: ${e.competitorData.contentGaps?.join(", ")||"Not specified"}`),e.templateStructure&&(t+=`

CONTENT STRUCTURE:
Follow this template structure:
${JSON.stringify(e.templateStructure,null,2)}`),e.includeSchema&&(t+=`

SCHEMA MARKUP:
Include appropriate schema markup suggestions for this content type.`),e.includeFaq&&(t+=`

FAQ SECTION:
Include a comprehensive FAQ section with 5-8 relevant questions and detailed answers.`),e.includeImages&&(t+=`

IMAGE SUGGESTIONS:
Provide specific image prompts and alt text suggestions for optimal visual content.`),t+=`

Ensure the content is original, engaging, and provides genuine value to readers searching for "${e.keyword}" in ${e.location}.`}async parseAndStructureContent(e,t){try{let n=JSON.parse(e),r=await this.analyzeSeoMetrics(n.content,t.keyword),a={title:n.title,metaDescription:n.metaDescription,content:n.content,outline:n.outline||await this.generateOutline(n.content),seoMetrics:r,suggestions:await this.generateSuggestions(n.content,t)};return t.includeSchema&&(a.schemaMarkup=await this.generateSchemaMarkup(t)),t.includeImages&&(a.imagePrompts=await this.generateImagePrompts(n.content,t)),t.includeFaq&&(a.faqSection=await this.extractFaqSection(n.content)),a.internalLinks=await this.generateInternalLinks(n.content,t),a.externalLinks=await this.generateExternalLinks(n.content,t),a}catch(n){return console.error("Failed to parse structured content, falling back to text parsing:",n),this.parseUnstructuredContent(e,t)}}async parseUnstructuredContent(e,t){let n=e.split("\n").filter(e=>e.trim()),r=n[0]?.replace(/^#\s*/,"")||`${t.keyword} - ${t.contentType}`,a=n.find(e=>e.length>50&&!e.startsWith("#")),o=a?a.substring(0,160).trim()+"...":`Learn about ${t.keyword} in ${t.location}. Expert insights and practical advice.`,i=await this.analyzeSeoMetrics(e,t.keyword);return{title:r,metaDescription:o,content:e,outline:await this.generateOutline(e),seoMetrics:i,suggestions:await this.generateSuggestions(e,t)}}async analyzeSeoMetrics(e,t){let n=e.toLowerCase().split(/\s+/).filter(e=>e.length>0).length;t.toLowerCase().split(/\s+/);let r=this.countKeywordOccurrences(e,t),a=r/n*100,o=this.countHeadings(e),i=this.extractLsiKeywords(e,t),s=this.extractEntities(e),c=this.calculateReadabilityScore(e),l=this.calculateSeoScore({keywordDensity:a,headingCount:o,wordCount:n,readabilityScore:c,keywordCount:r});return{keywordDensity:Math.round(100*a)/100,headingCount:o,wordCount:n,readabilityScore:c,seoScore:l,keywordVariations:this.extractKeywordVariations(e,t),lsiKeywords:i,entities:s}}countKeywordOccurrences(e,t){let n=RegExp(t.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),"gi");return(e.match(n)||[]).length}countHeadings(e){let t={h1:0,h2:0,h3:0,h4:0,h5:0,h6:0};for(let n=1;n<=6;n++){let r=RegExp(`<h${n}[^>]*>.*?</h${n}>`,"gi");t[`h${n}`]=(e.match(r)||[]).length}return t}extractLsiKeywords(e,t){let n=e.toLowerCase().split(/\s+/),r=new Set(["the","and","or","but","in","on","at","to","for","of","with","by"]),a=new Map;return n.forEach(e=>{(e=e.replace(/[^\w]/g,"")).length>3&&!r.has(e)&&a.set(e,(a.get(e)||0)+1)}),Array.from(a.entries()).filter(([e,n])=>n>2&&!t.toLowerCase().includes(e)).sort((e,t)=>t[1]-e[1]).slice(0,10).map(([e])=>e)}extractEntities(e){let t=e.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*\b/g)||[];return Array.from(new Set(t)).filter(e=>e.length>3).slice(0,15)}extractKeywordVariations(e,t){let n=new Set;return t.toLowerCase().split(/\s+/).forEach(t=>{let r=RegExp(`\\b${t}\\w*\\b`,"gi");(e.match(r)||[]).forEach(e=>n.add(e.toLowerCase()))}),Array.from(n).slice(0,10)}calculateReadabilityScore(e){let t=e.split(/[.!?]+/).length,n=e.split(/\s+/).length;return Math.max(0,Math.min(100,Math.round(206.835-n/t*1.015-this.countSyllables(e)/n*84.6)))}countSyllables(e){return(e.toLowerCase().match(/\b\w+\b/g)||[]).reduce((e,t)=>e+(t.match(/[aeiouy]+/g)?.length||1),0)}calculateSeoScore(e){let t=0;return e.keywordDensity>=1&&e.keywordDensity<=3?t+=25:e.keywordDensity>.5&&e.keywordDensity<5&&(t+=15),1===e.headingCount.h1&&(t+=10),e.headingCount.h2>=2&&(t+=15),e.headingCount.h3>=1&&(t+=10),e.wordCount>=500&&(t+=15),e.wordCount>=1e3&&(t+=10),e.readabilityScore>=60&&(t+=15),Math.min(100,t)}async generateOutline(e){let t;let n=/<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi,r=[];for(;null!==(t=n.exec(e));)r.push({level:parseInt(t[1]),text:t[2].replace(/<[^>]*>/g,""),keywords:[],wordCount:0});return{title:r.find(e=>1===e.level)?.text||"Untitled",metaDescription:"",headings:r,keywordDistribution:[],estimatedReadingTime:Math.ceil(e.split(/\s+/).length/200)}}async generateSuggestions(e,t){let n=[],r=e.split(/\s+/).length;r<.9*t.targetWordCount&&n.push(`Content is ${t.targetWordCount-r} words short of target`);let a=this.countKeywordOccurrences(e,t.keyword);0===a?n.push("Primary keyword not found in content"):1===a&&n.push("Consider adding the primary keyword 1-2 more times");let o=this.countHeadings(e);return 0===o.h1&&n.push("Add an H1 heading with the primary keyword"),o.h2<2&&n.push("Add more H2 headings to improve content structure"),n}async generateSchemaMarkup(e){return JSON.stringify({"@context":"https://schema.org","@type":this.getSchemaType(e.contentType),name:e.keyword,description:`${e.keyword} in ${e.location}`},null,2)}getSchemaType(e){switch(e){case"service":return"Service";case"product":return"Product";case"blog":return"Article";case"faq":return"FAQPage";default:return"WebPage"}}async generateImagePrompts(e,t){return[{alt:`${t.keyword} in ${t.location}`,caption:`Professional ${t.keyword} services`,placement:"header",description:`High-quality image showing ${t.keyword} related to ${t.industry}`}]}async extractFaqSection(e){let t;let n=/(?:Q:|Question:|FAQ:)\s*(.*?)\n(?:A:|Answer:)\s*(.*?)(?=\n|$)/gi,r=[];for(;null!==(t=n.exec(e))&&r.length<8;)r.push({question:t[1].trim(),answer:t[2].trim(),keywords:[]});return r}async generateInternalLinks(e,t){return[{anchor:`${t.keyword} services`,url:`/services/${t.keyword.toLowerCase().replace(/\s+/g,"-")}`,type:"internal",relevanceScore:.9}]}async generateExternalLinks(e,t){return[{anchor:"Industry Statistics",url:"https://example.com/industry-stats",type:"external",relevanceScore:.8}]}calculateCost(e,t){let n=this.models[e];return n?t/1e3*n.costPer1kTokens:0}getAvailableModels(){return this.models}async healthCheck(){let e=Date.now();try{let t=await this.client.chat.completions.create({model:"llama3-8b-8192",messages:[{role:"user",content:"Hi"}],max_tokens:10}),n=Date.now()-e;return{status:t?"healthy":"unhealthy",model:"llama3-8b-8192",latency:n}}catch(t){return{status:"unhealthy",model:"llama3-8b-8192",latency:Date.now()-e}}}}let c=new s}};
// Professional SEO Analysis Page
// Enterprise-grade SEO analysis with comprehensive reporting

'use client'

import * as React from "react"
import { AnalysisResults } from "@/components/seo/analysis-results"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, type SelectOption } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { StepProgress } from "@/components/ui/progress"
import { cn } from "@/lib/utils"
import {
  MagnifyingGlassIcon,
  DocumentTextIcon,
  ClipboardDocumentIcon,
  SparklesIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline"

interface AnalysisRequest {
  url?: string
  content?: string
  keyword: string
  location: string
  competitorUrls: string[]
}

interface AnalysisData {
  overallScore: number
  keywordAnalysis: {
    score: number
    primaryKeywordDensity: number
    secondaryKeywordDensity: number
    recommendations: Array<{
      type: string
      priority: 'high' | 'medium' | 'low'
      description: string
      impact: number
    }>
  }
  headingAnalysis: {
    score: number
    totalHeadings: number
    optimizedHeadings: number
    hierarchy: {
      isValid: boolean
      issues: string[]
    }
    recommendations: Array<{
      type: string
      priority: 'high' | 'medium' | 'low'
      description: string
      impact: number
    }>
  }
  contentQuality: {
    score: number
    wordCount: number
    readabilityScore: number
    uniqueWords: number
    recommendations: Array<{
      type: string
      priority: 'high' | 'medium' | 'low'
      description: string
      impact: number
    }>
  }
  competitorAnalysis?: {
    averageWordCount: number
    averageScore: number
    topPerformerScore: number
    gaps: string[]
    opportunities: Array<{
      type: string
      description: string
      impact: 'high' | 'medium' | 'low'
    }>
  }
  lsiKeywords: Array<{
    keyword: string
    relevanceScore: number
    frequency: number
    category: string
  }>
  recommendations: Array<{
    type: string
    priority: 'high' | 'medium' | 'low'
    title: string
    description: string
    expectedImpact: number
  }>
}

const countries: SelectOption[] = [
  { value: 'US', label: 'United States' },
  { value: 'UK', label: 'United Kingdom' },
  { value: 'CA', label: 'Canada' },
  { value: 'AU', label: 'Australia' },
  { value: 'DE', label: 'Germany' },
  { value: 'FR', label: 'France' },
]

export default function AnalysisPage() {
  const [currentStep, setCurrentStep] = React.useState(0)
  const [analysisType, setAnalysisType] = React.useState<'url' | 'content'>('url')
  const [loading, setLoading] = React.useState(false)
  const [analysisResults, setAnalysisResults] = React.useState<AnalysisData | null>(null)
  const [error, setError] = React.useState<string | null>(null)
  
  const [formData, setFormData] = React.useState<AnalysisRequest>({
    url: '',
    content: '',
    keyword: '',
    location: 'US',
    competitorUrls: []
  })
  
  const [competitorUrl, setCompetitorUrl] = React.useState('')
  const [errors, setErrors] = React.useState<Record<string, string>>({})

  const steps = [
    'Configure',
    'Analyze',
    'Results'
  ]

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    if (!formData.keyword.trim()) {
      newErrors.keyword = 'Target keyword is required'
    }

    if (analysisType === 'url') {
      if (!formData.url?.trim()) {
        newErrors.url = 'URL is required'
      } else {
        try {
          new URL(formData.url)
        } catch {
          newErrors.url = 'Please enter a valid URL'
        }
      }
    } else {
      if (!formData.content?.trim()) {
        newErrors.content = 'Content is required'
      } else if (formData.content.length < 100) {
        newErrors.content = 'Content must be at least 100 characters'
      }
    }

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) return

    setLoading(true)
    setError(null)
    setCurrentStep(1)

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 3000))
      
      // Mock analysis results
      const mockResults: AnalysisData = {
        overallScore: 73,
        keywordAnalysis: {
          score: 68,
          primaryKeywordDensity: 1.8,
          secondaryKeywordDensity: 0.9,
          recommendations: [
            {
              type: 'keyword',
              priority: 'high',
              description: 'Increase primary keyword density to 2-3% for better optimization',
              impact: 8
            },
            {
              type: 'keyword',
              priority: 'medium',
              description: 'Add more LSI keywords to improve semantic relevance',
              impact: 6
            }
          ]
        },
        headingAnalysis: {
          score: 82,
          totalHeadings: 8,
          optimizedHeadings: 6,
          hierarchy: {
            isValid: true,
            issues: []
          },
          recommendations: [
            {
              type: 'structure',
              priority: 'medium',
              description: 'Add target keyword to H2 headings for better optimization',
              impact: 5
            }
          ]
        },
        contentQuality: {
          score: 69,
          wordCount: 847,
          readabilityScore: 74,
          uniqueWords: 312,
          recommendations: [
            {
              type: 'content',
              priority: 'high',
              description: 'Expand content to 1200+ words to match top competitors',
              impact: 9
            },
            {
              type: 'readability',
              priority: 'low',
              description: 'Content readability is good, maintain current style',
              impact: 3
            }
          ]
        },
        competitorAnalysis: {
          averageWordCount: 1250,
          averageScore: 78,
          topPerformerScore: 89,
          gaps: [
            'Missing FAQ section that competitors have',
            'Lack of internal linking structure',
            'No schema markup implementation'
          ],
          opportunities: [
            {
              type: 'content',
              description: 'Add comprehensive FAQ section to match competitor content depth',
              impact: 'high'
            },
            {
              type: 'technical',
              description: 'Implement schema markup for better SERP features',
              impact: 'medium'
            }
          ]
        },
        lsiKeywords: [
          { keyword: 'search engine optimization', relevanceScore: 0.92, frequency: 3, category: 'primary' },
          { keyword: 'digital marketing', relevanceScore: 0.85, frequency: 2, category: 'secondary' },
          { keyword: 'content strategy', relevanceScore: 0.78, frequency: 1, category: 'supporting' },
          { keyword: 'keyword research', relevanceScore: 0.88, frequency: 2, category: 'secondary' },
          { keyword: 'SERP ranking', relevanceScore: 0.82, frequency: 1, category: 'supporting' }
        ],
        recommendations: [
          {
            type: 'content',
            priority: 'high',
            title: 'Expand Content Length',
            description: 'Increase content to 1200+ words to match competitor standards and improve ranking potential',
            expectedImpact: 9
          },
          {
            type: 'keyword',
            priority: 'high',
            title: 'Optimize Keyword Density',
            description: 'Increase primary keyword density to 2-3% while maintaining natural flow',
            expectedImpact: 8
          },
          {
            type: 'structure',
            priority: 'medium',
            title: 'Add FAQ Section',
            description: 'Include comprehensive FAQ section to match competitor content depth',
            expectedImpact: 7
          },
          {
            type: 'technical',
            priority: 'medium',
            title: 'Implement Schema Markup',
            description: 'Add structured data markup for better SERP features and visibility',
            expectedImpact: 6
          }
        ]
      }

      setAnalysisResults(mockResults)
      setCurrentStep(2)

    } catch (err) {
      setError('Failed to analyze content. Please try again.')
      console.error('Analysis error:', err)
    } finally {
      setLoading(false)
    }
  }

  const addCompetitorUrl = () => {
    if (competitorUrl.trim() && formData.competitorUrls.length < 5) {
      try {
        new URL(competitorUrl)
        setFormData(prev => ({
          ...prev,
          competitorUrls: [...prev.competitorUrls, competitorUrl.trim()]
        }))
        setCompetitorUrl('')
      } catch {
        setErrors(prev => ({ ...prev, competitorUrl: 'Please enter a valid URL' }))
      }
    }
  }

  const removeCompetitorUrl = (index: number) => {
    setFormData(prev => ({
      ...prev,
      competitorUrls: prev.competitorUrls.filter((_, i) => i !== index)
    }))
  }

  const resetAnalysis = () => {
    setCurrentStep(0)
    setAnalysisResults(null)
    setError(null)
    setFormData({
      url: '',
      content: '',
      keyword: '',
      location: 'US',
      competitorUrls: []
    })
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-gray-900">SEO Analysis</h1>
        <p className="text-gray-600 mt-2">
          Analyze your content's SEO performance and get actionable recommendations
        </p>
      </div>

      {/* Progress Steps */}
      <StepProgress steps={steps} currentStep={currentStep} />

      {/* Error Display */}
      {error && (
        <Card className="border-red-200 bg-red-50">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <div className="text-red-600">⚠️</div>
              <p className="text-red-800">{error}</p>
              <Button variant="outline" size="sm" onClick={resetAnalysis}>
                Try Again
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analysis Configuration Form */}
      {currentStep === 0 && (
        <Card className="w-full max-w-4xl mx-auto">
          <CardHeader>
            <CardTitle>Configure SEO Analysis</CardTitle>
            <CardDescription>
              Enter your content details and target keywords for comprehensive analysis
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              {/* Analysis Type Selection */}
              <div className="space-y-4">
                <label className="text-sm font-medium">Analysis Type</label>
                <div className="grid grid-cols-2 gap-4">
                  <Button
                    type="button"
                    variant={analysisType === 'url' ? 'default' : 'outline'}
                    onClick={() => setAnalysisType('url')}
                    className="h-auto p-4 justify-start"
                  >
                    <div className="flex items-start space-x-3">
                      <MagnifyingGlassIcon className="h-5 w-5 mt-1" />
                      <div className="text-left">
                        <div className="font-medium">Analyze URL</div>
                        <div className="text-sm opacity-70">
                          Analyze an existing webpage
                        </div>
                      </div>
                    </div>
                  </Button>
                  <Button
                    type="button"
                    variant={analysisType === 'content' ? 'default' : 'outline'}
                    onClick={() => setAnalysisType('content')}
                    className="h-auto p-4 justify-start"
                  >
                    <div className="flex items-start space-x-3">
                      <DocumentTextIcon className="h-5 w-5 mt-1" />
                      <div className="text-left">
                        <div className="font-medium">Analyze Content</div>
                        <div className="text-sm opacity-70">
                          Paste content directly
                        </div>
                      </div>
                    </div>
                  </Button>
                </div>
              </div>

              {/* URL or Content Input */}
              {analysisType === 'url' ? (
                <Input
                  label="Website URL"
                  placeholder="https://example.com/page"
                  value={formData.url || ''}
                  onChange={(e) => setFormData(prev => ({ ...prev, url: e.target.value }))}
                  error={errors.url}
                  required
                  helperText="Enter the URL you want to analyze"
                />
              ) : (
                <div className="space-y-2">
                  <label className="text-sm font-medium">Content to Analyze</label>
                  <textarea
                    placeholder="Paste your content here..."
                    value={formData.content || ''}
                    onChange={(e) => setFormData(prev => ({ ...prev, content: e.target.value }))}
                    className="w-full h-40 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    required
                  />
                  {errors.content && (
                    <p className="text-sm text-red-600">{errors.content}</p>
                  )}
                  <p className="text-sm text-gray-500">Minimum 100 characters required</p>
                </div>
              )}

              {/* Target Keyword and Location */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <Input
                  label="Target Keyword"
                  placeholder="e.g., SEO services"
                  value={formData.keyword}
                  onChange={(e) => setFormData(prev => ({ ...prev, keyword: e.target.value }))}
                  error={errors.keyword}
                  required
                  helperText="The main keyword you want to rank for"
                />

                <Select
                  label="Target Location"
                  options={countries}
                  value={formData.location}
                  onChange={(value) => setFormData(prev => ({ ...prev, location: value }))}
                  required
                />
              </div>

              {/* Competitor URLs */}
              <div className="space-y-4">
                <label className="text-sm font-medium">
                  Competitor URLs (Optional)
                  <span className="text-gray-500 ml-1">- Up to 5 URLs for comparison</span>
                </label>
                
                <div className="flex space-x-2">
                  <Input
                    placeholder="https://competitor.com"
                    value={competitorUrl}
                    onChange={(e) => setCompetitorUrl(e.target.value)}
                    error={errors.competitorUrl}
                    className="flex-1"
                  />
                  <Button
                    type="button"
                    variant="outline"
                    onClick={addCompetitorUrl}
                    disabled={!competitorUrl.trim() || formData.competitorUrls.length >= 5}
                  >
                    Add
                  </Button>
                </div>

                {formData.competitorUrls.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm text-gray-600">Competitor URLs to analyze:</p>
                    <div className="space-y-2">
                      {formData.competitorUrls.map((url, index) => (
                        <div key={index} className="flex items-center justify-between bg-gray-50 p-2 rounded">
                          <span className="text-sm truncate flex-1">{url}</span>
                          <Button
                            type="button"
                            variant="ghost"
                            size="sm"
                            onClick={() => removeCompetitorUrl(index)}
                            className="ml-2"
                          >
                            Remove
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Submit Button */}
              <div className="flex justify-end">
                <Button
                  type="submit"
                  loading={loading}
                  disabled={loading}
                  className="min-w-[120px]"
                >
                  {loading ? 'Analyzing...' : 'Start Analysis'}
                </Button>
              </div>
            </form>
          </CardContent>
        </Card>
      )}

      {/* Loading State */}
      {loading && (
        <Card>
          <CardContent className="p-8">
            <div className="flex flex-col items-center space-y-4">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary"></div>
              <div className="text-center">
                <h3 className="text-lg font-medium">Analyzing Your Content...</h3>
                <p className="text-gray-600">
                  Running comprehensive SEO analysis and competitor comparison
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Analysis Results */}
      {analysisResults && currentStep >= 2 && (
        <>
          <AnalysisResults results={analysisResults} />
          
          {/* Action Buttons */}
          <div className="flex justify-center space-x-4">
            <Button variant="outline" onClick={resetAnalysis}>
              Analyze New Content
            </Button>
            <Button>
              <SparklesIcon className="h-4 w-4 mr-2" />
              Generate Optimized Content
            </Button>
          </div>
        </>
      )}
    </div>
  )
}

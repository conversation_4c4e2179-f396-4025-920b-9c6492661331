// API Service Types

export interface GroqConfig {
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
}

export interface SerperConfig {
  apiKey: string;
  gl: string; // Country code
  hl: string; // Language code
  num: number; // Number of results
}

export interface SupabaseConfig {
  url: string;
  anonKey: string;
  serviceRoleKey: string;
}

export interface ApiError {
  message: string;
  code: number;
  details?: any;
}

export interface RateLimitInfo {
  limit: number;
  remaining: number;
  reset: number;
}

export interface ApiMetrics {
  requests_made: number;
  requests_remaining: number;
  cost_incurred: number;
  last_request: string;
}

export interface ContentGenerationResponse {
  content: string;
  metadata: {
    word_count: number;
    keywords_used: string[];
    headings_generated: number;
    seo_score: number;
  };
  suggestions: {
    improvements: string[];
    additional_keywords: string[];
    content_gaps: string[];
  };
}

export interface CompetitorAnalysisResponse {
  competitors: CompetitorData[];
  averages: {
    word_count: number;
    keyword_density: number;
    heading_count: number;
    seo_score: number;
  };
  recommendations: {
    optimal_word_count: number;
    recommended_keywords: string[];
    content_structure: string[];
  };
}

export interface CompetitorData {
  url: string;
  title: string;
  meta_description: string;
  word_count: number;
  headings: {
    h1: string[];
    h2: string[];
    h3: string[];
    h4: string[];
    h5: string[];
    h6: string[];
  };
  keyword_analysis: {
    primary_keywords: KeywordAnalysis[];
    secondary_keywords: KeywordAnalysis[];
    lsi_keywords: string[];
  };
  technical_seo: {
    title_length: number;
    meta_description_length: number;
    has_schema: boolean;
    images_with_alt: number;
    internal_links: number;
    external_links: number;
  };
}

export interface KeywordAnalysis {
  keyword: string;
  count: number;
  density: number;
  positions: number[];
}

export interface SeoAnalysisRequest {
  keyword: string;
  country: string;
  language: string;
  num_competitors: number;
  content_type: string;
  industry: string;
}

export interface SeoAnalysisResponse {
  keyword_data: {
    primary_keyword: string;
    related_keywords: string[];
    search_volume: number;
    competition: 'low' | 'medium' | 'high';
    cpc: number;
  };
  competitor_analysis: CompetitorAnalysisResponse;
  content_recommendations: {
    optimal_length: number;
    recommended_structure: string[];
    must_include_topics: string[];
    content_gaps: string[];
  };
  technical_recommendations: {
    title_suggestions: string[];
    meta_description_suggestions: string[];
    heading_structure: string[];
    internal_link_opportunities: string[];
  };
}
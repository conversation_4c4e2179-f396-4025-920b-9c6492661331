<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Pro - System Test</title>
    <link href="css/main.css" rel="stylesheet">
    <style>
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
        }
        .test-section {
            background: white;
            border-radius: 8px;
            padding: 2rem;
            margin-bottom: 2rem;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-result {
            padding: 1rem;
            border-radius: 4px;
            margin: 0.5rem 0;
        }
        .test-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .test-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .test-warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.5rem 1rem;
            border-radius: 4px;
            cursor: pointer;
            margin: 0.25rem;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .test-log {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 4px;
            padding: 1rem;
            font-family: monospace;
            font-size: 0.875rem;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>SEO Pro - System Test Dashboard</h1>
        <p>This page tests all core functionality of the SEO Pro application.</p>

        <!-- Configuration Test -->
        <div class="test-section">
            <h2>Configuration Test</h2>
            <button class="test-button" onclick="testConfiguration()">Test Configuration</button>
            <div id="config-results"></div>
        </div>

        <!-- Supabase Connection Test -->
        <div class="test-section">
            <h2>Supabase Connection Test</h2>
            <button class="test-button" onclick="testSupabase()">Test Supabase</button>
            <div id="supabase-results"></div>
        </div>

        <!-- API Integration Test -->
        <div class="test-section">
            <h2>API Integration Test</h2>
            <button class="test-button" onclick="testGroqAPI()">Test Groq API</button>
            <button class="test-button" onclick="testSerperAPI()">Test Serper API</button>
            <div id="api-results"></div>
        </div>

        <!-- Content Generation Test -->
        <div class="test-section">
            <h2>Content Generation Test</h2>
            <button class="test-button" onclick="testContentGeneration()">Test Content Generation</button>
            <div id="content-results"></div>
        </div>

        <!-- SEO Analysis Test -->
        <div class="test-section">
            <h2>SEO Analysis Test</h2>
            <button class="test-button" onclick="testSEOAnalysis()">Test SEO Analysis</button>
            <div id="seo-results"></div>
        </div>

        <!-- System Log -->
        <div class="test-section">
            <h2>System Log</h2>
            <button class="test-button" onclick="clearLog()">Clear Log</button>
            <div id="system-log" class="test-log"></div>
        </div>
    </div>

    <!-- Core JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/supabase.js"></script>
    <script src="js/api.js"></script>
    <script src="js/seo-analyzer.js"></script>
    <script src="js/content-generator.js"></script>
    <script src="js/app-init.js"></script>

    <script>
        let testLog = [];

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${type.toUpperCase()}: ${message}`;
            testLog.push(logEntry);
            updateLogDisplay();
            console.log(logEntry);
        }

        function updateLogDisplay() {
            const logElement = document.getElementById('system-log');
            logElement.textContent = testLog.join('\n');
            logElement.scrollTop = logElement.scrollHeight;
        }

        function clearLog() {
            testLog = [];
            updateLogDisplay();
        }

        function showResult(containerId, message, type) {
            const container = document.getElementById(containerId);
            const resultDiv = document.createElement('div');
            resultDiv.className = `test-result test-${type}`;
            resultDiv.textContent = message;
            container.appendChild(resultDiv);
        }

        function clearResults(containerId) {
            const container = document.getElementById(containerId);
            container.innerHTML = '';
        }

        // Configuration Test
        function testConfiguration() {
            clearResults('config-results');
            log('Testing configuration...');

            try {
                if (typeof CONFIG === 'undefined') {
                    throw new Error('CONFIG object not found');
                }

                // Test Supabase config
                if (!CONFIG.SUPABASE.URL || !CONFIG.SUPABASE.ANON_KEY) {
                    throw new Error('Supabase configuration missing');
                }
                showResult('config-results', '✓ Supabase configuration loaded', 'success');

                // Test API config
                if (!CONFIG.APIS.GROQ.API_KEY || CONFIG.APIS.GROQ.API_KEY.includes('your_')) {
                    showResult('config-results', '⚠ Groq API key not configured', 'warning');
                } else {
                    showResult('config-results', '✓ Groq API key configured', 'success');
                }

                if (!CONFIG.APIS.SERPER.API_KEY || CONFIG.APIS.SERPER.API_KEY.includes('your_')) {
                    showResult('config-results', '⚠ Serper API key not configured', 'warning');
                } else {
                    showResult('config-results', '✓ Serper API key configured', 'success');
                }

                log('Configuration test completed');
            } catch (error) {
                showResult('config-results', `✗ Configuration error: ${error.message}`, 'error');
                log(`Configuration test failed: ${error.message}`, 'error');
            }
        }

        // Supabase Test
        async function testSupabase() {
            clearResults('supabase-results');
            log('Testing Supabase connection...');

            try {
                // Wait for app initialization
                if (window.appInitializer) {
                    await window.appInitializer.waitForInitialization();
                }

                if (!window.supabaseClient) {
                    throw new Error('Supabase client not initialized');
                }

                showResult('supabase-results', '✓ Supabase client initialized', 'success');

                // Test connection
                const testResult = await window.supabaseClient.testConnection();
                if (testResult.success) {
                    showResult('supabase-results', '✓ Supabase connection successful', 'success');
                } else {
                    throw new Error(testResult.error);
                }

                log('Supabase test completed successfully');
            } catch (error) {
                showResult('supabase-results', `✗ Supabase error: ${error.message}`, 'error');
                log(`Supabase test failed: ${error.message}`, 'error');
            }
        }

        // Groq API Test
        async function testGroqAPI() {
            clearResults('api-results');
            log('Testing Groq API...');

            try {
                if (!window.apiClient) {
                    throw new Error('API client not initialized');
                }

                const testPrompt = "Write a short test sentence about SEO.";
                const result = await window.apiClient.generateContent({
                    prompt: testPrompt,
                    maxTokens: 50
                });

                if (result.success) {
                    showResult('api-results', '✓ Groq API connection successful', 'success');
                    showResult('api-results', `Response: ${result.content.substring(0, 100)}...`, 'success');
                } else {
                    throw new Error(result.error);
                }

                log('Groq API test completed successfully');
            } catch (error) {
                showResult('api-results', `✗ Groq API error: ${error.message}`, 'error');
                log(`Groq API test failed: ${error.message}`, 'error');
            }
        }

        // Serper API Test
        async function testSerperAPI() {
            log('Testing Serper API...');

            try {
                if (!window.apiClient) {
                    throw new Error('API client not initialized');
                }

                const result = await window.apiClient.searchGoogle('SEO tools', { num: 3 });

                if (result.success) {
                    showResult('api-results', '✓ Serper API connection successful', 'success');
                    showResult('api-results', `Found ${result.data.organic?.length || 0} search results`, 'success');
                } else {
                    throw new Error(result.error);
                }

                log('Serper API test completed successfully');
            } catch (error) {
                showResult('api-results', `✗ Serper API error: ${error.message}`, 'error');
                log(`Serper API test failed: ${error.message}`, 'error');
            }
        }

        // Content Generation Test
        async function testContentGeneration() {
            clearResults('content-results');
            log('Testing content generation...');

            try {
                if (!window.apiClient) {
                    throw new Error('API client not initialized');
                }

                const params = {
                    keywords: ['SEO', 'content marketing'],
                    industry: 'technology',
                    contentType: 'blog-post',
                    tone: 'professional',
                    wordCount: 200,
                    targetAudience: 'marketers'
                };

                const result = await window.apiClient.generateSEOContent(params);

                if (result.success) {
                    showResult('content-results', '✓ Content generation successful', 'success');
                    showResult('content-results', `Generated ${result.content.split(' ').length} words`, 'success');
                } else {
                    throw new Error(result.error);
                }

                log('Content generation test completed successfully');
            } catch (error) {
                showResult('content-results', `✗ Content generation error: ${error.message}`, 'error');
                log(`Content generation test failed: ${error.message}`, 'error');
            }
        }

        // SEO Analysis Test
        async function testSEOAnalysis() {
            clearResults('seo-results');
            log('Testing SEO analysis...');

            try {
                if (!window.SEOAnalyzer) {
                    throw new Error('SEO Analyzer not loaded');
                }

                const analyzer = new SEOAnalyzer();
                const testContent = `
                    <h1>SEO Content Marketing Guide</h1>
                    <p>This is a comprehensive guide about SEO content marketing strategies.</p>
                    <h2>What is SEO Content Marketing?</h2>
                    <p>SEO content marketing combines search engine optimization with content creation to drive organic traffic.</p>
                    <h2>Best Practices</h2>
                    <p>Here are the best practices for SEO content marketing success.</p>
                `;

                const result = await analyzer.analyzeContent(testContent, ['SEO', 'content marketing'], {
                    url: 'https://example.com/test',
                    industry: 'marketing'
                });

                if (result.success) {
                    showResult('seo-results', '✓ SEO analysis completed', 'success');
                    showResult('seo-results', `Overall score: ${result.results.overallScore}/100`, 'success');
                    showResult('seo-results', `Found ${result.results.recommendations.length} recommendations`, 'success');
                } else {
                    throw new Error(result.error);
                }

                log('SEO analysis test completed successfully');
            } catch (error) {
                showResult('seo-results', `✗ SEO analysis error: ${error.message}`, 'error');
                log(`SEO analysis test failed: ${error.message}`, 'error');
            }
        }

        // Auto-run basic tests on page load
        document.addEventListener('DOMContentLoaded', function() {
            log('SEO Pro System Test Dashboard loaded');
            
            // Wait a bit for modules to load, then run basic tests
            setTimeout(() => {
                testConfiguration();
            }, 1000);
        });
    </script>
</body>
</html>

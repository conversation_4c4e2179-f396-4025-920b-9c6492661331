const cheerio = require('cheerio');
const natural = require('natural');
const stopword = require('stopword');
const axios = require('axios');

class ExternalLinksRecommender {
  constructor(content, keyword, options = {}) {
    this.content = content;
    this.keyword = keyword;
    this.options = {
      maxSuggestions: 15,
      minAuthorityScore: 0.4,
      maxLinksPerDomain: 2,
      includeNoFollow: true,
      contextWindow: 30,
      ...options
    };
    
    // Parse content
    this.$ = cheerio.load(`<div>${content}</div>`);
    this.plainText = this.$.text();
    this.words = this.getWords();
    this.sentences = this.getSentences();
    this.existingExternalLinks = this.extractExistingExternalLinks();
    
    // High-authority domains database (in production, this would be from an API or database)
    this.authorityDomains = this.getAuthorityDomains();
  }

  // Main method to generate external linking recommendations
  async generateRecommendations() {
    try {
      // Extract topics and entities that could benefit from external sources
      const linkOpportunities = this.identifyLinkOpportunities();
      
      // Find relevant high-authority sources
      const authorityRecommendations = await this.findAuthorityRecommendations(linkOpportunities);
      
      // Analyze existing external links
      const existingLinkAnalysis = this.analyzeExistingExternalLinks();
      
      // Generate optimization recommendations
      const optimizationTips = this.generateOptimizationRecommendations(existingLinkAnalysis);
      
      // Filter and rank recommendations
      const finalRecommendations = this.optimizeRecommendations(authorityRecommendations);

      return {
        success: true,
        recommendations: finalRecommendations,
        linkOpportunities,
        existingLinkAnalysis,
        optimizationTips,
        metadata: {
          opportunitiesFound: linkOpportunities.length,
          recommendationsGenerated: finalRecommendations.length,
          existingExternalLinks: this.existingExternalLinks.length
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        recommendations: [],
        linkOpportunities: [],
        existingLinkAnalysis: null,
        optimizationTips: []
      };
    }
  }

  // Identify opportunities for external linking in content
  identifyLinkOpportunities() {
    const opportunities = [];
    
    // Extract topics that need validation/sources
    const validationNeeds = this.findValidationNeeds();
    
    // Find statistics and claims that need sources
    const unsourcedClaims = this.findUnsourcedClaims();
    
    // Identify technical concepts that could link to documentation
    const technicalConcepts = this.findTechnicalConcepts();
    
    // Find industry terms and tools that could link to official sources
    const industryTerms = this.findIndustryTerms();
    
    // Combine all opportunities
    opportunities.push(...validationNeeds);
    opportunities.push(...unsourcedClaims);
    opportunities.push(...technicalConcepts);
    opportunities.push(...industryTerms);
    
    // Score and rank opportunities
    return this.scoreOpportunities(opportunities);
  }

  // Find statements that need validation/citation
  findValidationNeeds() {
    const validationPhrases = [
      'studies show', 'research indicates', 'experts say', 'according to',
      'data reveals', 'statistics show', 'proven to', 'evidence suggests',
      'research proves', 'study found', 'analysis shows', 'survey revealed'
    ];
    
    const opportunities = [];
    
    this.sentences.forEach(sentence => {
      validationPhrases.forEach(phrase => {
        if (sentence.toLowerCase().includes(phrase)) {
          const context = this.getContextAroundPhrase(sentence, phrase);
          opportunities.push({
            type: 'validation',
            phrase,
            sentence,
            context,
            priority: 'high',
            linkType: 'research',
            suggestedDomains: ['ncbi.nlm.nih.gov', 'scholar.google.com', 'research.google.com'],
            reasoning: 'Claims require authoritative sources for credibility'
          });
        }
      });
    });
    
    return opportunities;
  }

  // Find unsourced statistical claims
  findUnsourcedClaims() {
    const opportunities = [];
    
    // Look for percentage and numerical claims
    const statPattern = /\b\d+(\.\d+)?%|\b\d{1,3}(,\d{3})*(\.\d+)?\s*(million|billion|thousand|users|people|companies)\b/gi;
    
    this.sentences.forEach(sentence => {
      const stats = sentence.match(statPattern);
      if (stats && !this.hasNearbyLink(sentence)) {
        stats.forEach(stat => {
          opportunities.push({
            type: 'statistic',
            phrase: stat,
            sentence,
            context: this.getContextAroundPhrase(sentence, stat),
            priority: 'high',
            linkType: 'data_source',
            suggestedDomains: ['statista.com', 'census.gov', 'bls.gov', 'pew.org'],
            reasoning: 'Statistical claims need credible data sources'
          });
        });
      }
    });
    
    return opportunities;
  }

  // Find technical concepts that could link to documentation
  findTechnicalConcepts() {
    const opportunities = [];
    
    // Industry-specific technical terms
    const technicalTerms = {
      'seo': ['Google Search Console', 'PageSpeed Insights', 'Google Analytics', 'Schema.org'],
      'web_development': ['MDN Web Docs', 'W3C', 'React', 'Vue.js', 'Angular'],
      'marketing': ['HubSpot', 'Mailchimp', 'Google Ads', 'Facebook Ads'],
      'analytics': ['Google Analytics', 'Adobe Analytics', 'Mixpanel', 'Segment'],
      'security': ['OWASP', 'NIST', 'CVE', 'CERT']
    };
    
    const allTerms = Object.values(technicalTerms).flat();
    
    this.sentences.forEach(sentence => {
      allTerms.forEach(term => {
        if (sentence.toLowerCase().includes(term.toLowerCase()) && !this.hasNearbyLink(sentence)) {
          opportunities.push({
            type: 'technical',
            phrase: term,
            sentence,
            context: this.getContextAroundPhrase(sentence, term),
            priority: 'medium',
            linkType: 'documentation',
            suggestedDomains: this.getTechnicalDomains(term),
            reasoning: 'Technical terms benefit from official documentation links'
          });
        }
      });
    });
    
    return opportunities;
  }

  // Find industry terms and brand mentions
  findIndustryTerms() {
    const opportunities = [];
    
    // Common industry tools and platforms
    const industryTools = [
      'WordPress', 'Shopify', 'Wix', 'Squarespace', 'Salesforce',
      'Slack', 'Zoom', 'Microsoft Teams', 'Asana', 'Trello',
      'GitHub', 'GitLab', 'Bitbucket', 'AWS', 'Azure', 'Google Cloud'
    ];
    
    this.sentences.forEach(sentence => {
      industryTools.forEach(tool => {
        if (sentence.toLowerCase().includes(tool.toLowerCase()) && !this.hasNearbyLink(sentence)) {
          opportunities.push({
            type: 'industry_tool',
            phrase: tool,
            sentence,
            context: this.getContextAroundPhrase(sentence, tool),
            priority: 'medium',
            linkType: 'official_site',
            suggestedDomains: [this.getOfficialDomain(tool)],
            reasoning: 'Tool mentions should link to official sources'
          });
        }
      });
    });
    
    return opportunities;
  }

  // Find relevant authority recommendations using search simulation
  async findAuthorityRecommendations(opportunities) {
    const recommendations = [];
    
    for (const opportunity of opportunities.slice(0, 10)) { // Limit to prevent too many API calls
      try {
        // Simulate finding authority sources for this opportunity
        const authoritySources = await this.findAuthoritySources(opportunity);
        
        authoritySources.forEach(source => {
          recommendations.push({
            anchorText: opportunity.phrase,
            targetUrl: source.url,
            targetDomain: source.domain,
            authorityScore: source.authorityScore,
            relevanceScore: source.relevanceScore,
            overallScore: (source.authorityScore + source.relevanceScore) / 2,
            context: opportunity.context,
            linkType: opportunity.linkType,
            reasoning: opportunity.reasoning,
            noFollow: this.shouldUseNoFollow(source, opportunity),
            placement: this.suggestPlacement(opportunity)
          });
        });
      } catch (error) {
        console.error(`Error finding sources for ${opportunity.phrase}:`, error);
      }
    }
    
    return recommendations;
  }

  // Simulate finding authority sources (in production, this would use real APIs)
  async findAuthoritySources(opportunity) {
    const sources = [];
    
    // Based on opportunity type, suggest relevant authority domains
    const domainSuggestions = this.getDomainSuggestionsForOpportunity(opportunity);
    
    domainSuggestions.forEach(domain => {
      const authorityScore = this.calculateDomainAuthority(domain);
      const relevanceScore = this.calculateRelevanceToOpportunity(domain, opportunity);
      
      if (authorityScore > this.options.minAuthorityScore) {
        sources.push({
          url: this.constructUrl(domain, opportunity),
          domain,
          authorityScore,
          relevanceScore,
          title: this.generateLinkTitle(domain, opportunity)
        });
      }
    });
    
    return sources.sort((a, b) => b.authorityScore - a.authorityScore).slice(0, 3);
  }

  // Get domain suggestions based on opportunity type
  getDomainSuggestionsForOpportunity(opportunity) {
    const { type, linkType } = opportunity;
    
    if (type === 'validation' || linkType === 'research') {
      return ['ncbi.nlm.nih.gov', 'scholar.google.com', 'researchgate.net', 'nature.com', 'science.org'];
    }
    
    if (type === 'statistic' || linkType === 'data_source') {
      return ['statista.com', 'census.gov', 'bls.gov', 'pewresearch.org', 'gallup.com'];
    }
    
    if (type === 'technical' || linkType === 'documentation') {
      return ['developer.mozilla.org', 'w3.org', 'github.com', 'stackoverflow.com'];
    }
    
    if (type === 'industry_tool') {
      return opportunity.suggestedDomains || [];
    }
    
    // Default high-authority domains
    return ['wikipedia.org', 'harvard.edu', 'stanford.edu', 'mit.edu'];
  }

  // Calculate domain authority (simplified scoring)
  calculateDomainAuthority(domain) {
    const authorityScores = {
      // Research and academic
      'ncbi.nlm.nih.gov': 0.95,
      'scholar.google.com': 0.90,
      'nature.com': 0.92,
      'science.org': 0.90,
      'researchgate.net': 0.75,
      
      // Government and statistics
      'census.gov': 0.95,
      'bls.gov': 0.90,
      'nih.gov': 0.95,
      'cdc.gov': 0.93,
      'who.int': 0.90,
      
      // Educational institutions
      'harvard.edu': 0.88,
      'stanford.edu': 0.87,
      'mit.edu': 0.86,
      'berkeley.edu': 0.85,
      
      // Tech and documentation
      'developer.mozilla.org': 0.85,
      'w3.org': 0.83,
      'github.com': 0.80,
      'stackoverflow.com': 0.78,
      
      // Industry sources
      'statista.com': 0.70,
      'pewresearch.org': 0.85,
      'gallup.com': 0.80,
      'reuters.com': 0.82,
      'bbc.com': 0.80,
      
      // General reference
      'wikipedia.org': 0.75
    };
    
    return authorityScores[domain] || 0.4; // Default medium authority
  }

  // Calculate relevance to opportunity
  calculateRelevanceToOpportunity(domain, opportunity) {
    const { type, phrase, keyword } = opportunity;
    
    // Domain specialization relevance
    const specializations = {
      'ncbi.nlm.nih.gov': ['health', 'medical', 'research', 'study'],
      'statista.com': ['statistics', 'data', 'market', 'survey'],
      'developer.mozilla.org': ['web', 'javascript', 'html', 'css', 'api'],
      'w3.org': ['web', 'standards', 'html', 'css', 'accessibility'],
      'github.com': ['code', 'software', 'development', 'programming']
    };
    
    const domainKeywords = specializations[domain] || [];
    const searchTerms = [phrase, this.keyword, type].join(' ').toLowerCase();
    
    let relevanceScore = 0.5; // Base relevance
    
    domainKeywords.forEach(keyword => {
      if (searchTerms.includes(keyword)) {
        relevanceScore += 0.1;
      }
    });
    
    return Math.min(relevanceScore, 1.0);
  }

  // Analyze existing external links
  analyzeExistingExternalLinks() {
    const analysis = {
      totalExternalLinks: this.existingExternalLinks.length,
      domains: this.getUniqueDomains(),
      authorityAnalysis: this.analyzeAuthorityDistribution(),
      nofollowAnalysis: this.analyzeNofollowUsage(),
      anchorTextAnalysis: this.analyzeExternalAnchorTexts(),
      issues: []
    };
    
    // Identify issues
    if (analysis.totalExternalLinks === 0) {
      analysis.issues.push('No external links found. Add authoritative sources to support claims.');
    }
    
    if (analysis.authorityAnalysis.lowAuthorityRatio > 0.6) {
      analysis.issues.push('Too many low-authority external links. Focus on high-quality sources.');
    }
    
    if (analysis.domains.length < 3 && analysis.totalExternalLinks > 5) {
      analysis.issues.push('Limited domain diversity. Link to varied authoritative sources.');
    }
    
    return analysis;
  }

  // Generate optimization recommendations
  generateOptimizationRecommendations(analysis) {
    const recommendations = [];
    
    if (analysis.totalExternalLinks < 3) {
      recommendations.push({
        type: 'quantity',
        priority: 'medium',
        message: 'Add more external links to authoritative sources to support your content',
        action: 'Identify 2-3 claims that could benefit from external validation'
      });
    }
    
    if (analysis.authorityAnalysis.lowAuthorityRatio > 0.5) {
      recommendations.push({
        type: 'quality',
        priority: 'high',
        message: 'Replace low-authority links with high-quality sources',
        action: 'Review existing external links and upgrade to more authoritative sources'
      });
    }
    
    if (analysis.nofollowAnalysis.nofollowRatio < 0.3) {
      recommendations.push({
        type: 'nofollow',
        priority: 'low',
        message: 'Consider adding nofollow attributes to some external links',
        action: 'Add nofollow to promotional or less-essential external links'
      });
    }
    
    return recommendations;
  }

  // Optimize and filter recommendations
  optimizeRecommendations(recommendations) {
    // Remove duplicates
    const deduped = this.removeDuplicateRecommendations(recommendations);
    
    // Limit links per domain
    const domainLimited = this.limitLinksPerDomain(deduped);
    
    // Sort by overall score
    const sorted = domainLimited.sort((a, b) => b.overallScore - a.overallScore);
    
    // Limit total recommendations
    return sorted.slice(0, this.options.maxSuggestions);
  }

  // Helper methods
  getWords() {
    return this.plainText
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  getSentences() {
    return this.plainText
      .split(/[.!?]+/)
      .filter(sentence => sentence.trim().length > 0)
      .map(sentence => sentence.trim());
  }

  extractExistingExternalLinks() {
    const links = [];
    this.$('a').each((i, elem) => {
      const $link = this.$(elem);
      const href = $link.attr('href') || '';
      
      if (this.isExternalLink(href)) {
        links.push({
          href,
          text: $link.text().trim(),
          title: $link.attr('title') || '',
          nofollow: $link.attr('rel')?.includes('nofollow') || false,
          domain: this.extractDomain(href)
        });
      }
    });
    return links;
  }

  isExternalLink(href) {
    if (!href) return false;
    return href.startsWith('http') && !href.includes(window?.location?.hostname);
  }

  extractDomain(url) {
    try {
      return new URL(url).hostname;
    } catch {
      return url;
    }
  }

  hasNearbyLink(sentence) {
    // Check if sentence already contains a link
    const linkPattern = /<a\s+[^>]*href/i;
    return linkPattern.test(sentence);
  }

  getContextAroundPhrase(sentence, phrase) {
    const words = sentence.split(/\s+/);
    const phraseIndex = sentence.toLowerCase().indexOf(phrase.toLowerCase());
    const wordIndex = sentence.substring(0, phraseIndex).split(/\s+/).length;
    
    const start = Math.max(0, wordIndex - this.options.contextWindow / 2);
    const end = Math.min(words.length, wordIndex + this.options.contextWindow / 2);
    
    return words.slice(start, end).join(' ');
  }

  scoreOpportunities(opportunities) {
    return opportunities
      .map(opp => ({
        ...opp,
        score: this.calculateOpportunityScore(opp)
      }))
      .sort((a, b) => b.score - a.score);
  }

  calculateOpportunityScore(opportunity) {
    let score = 0.5; // Base score
    
    // Priority weight
    if (opportunity.priority === 'high') score += 0.3;
    else if (opportunity.priority === 'medium') score += 0.2;
    
    // Type weight
    if (opportunity.type === 'validation') score += 0.2;
    else if (opportunity.type === 'statistic') score += 0.2;
    else if (opportunity.type === 'technical') score += 0.1;
    
    return Math.min(score, 1.0);
  }

  getTechnicalDomains(term) {
    const domainMap = {
      'Google Search Console': ['search.google.com'],
      'Google Analytics': ['analytics.google.com'],
      'Schema.org': ['schema.org'],
      'MDN Web Docs': ['developer.mozilla.org'],
      'React': ['reactjs.org'],
      'Vue.js': ['vuejs.org'],
      'Angular': ['angular.io']
    };
    
    return domainMap[term] || ['developer.mozilla.org'];
  }

  getOfficialDomain(tool) {
    const officialDomains = {
      'WordPress': 'wordpress.org',
      'Shopify': 'shopify.com',
      'Salesforce': 'salesforce.com',
      'GitHub': 'github.com',
      'AWS': 'aws.amazon.com',
      'Azure': 'azure.microsoft.com'
    };
    
    return officialDomains[tool] || `${tool.toLowerCase().replace(/\s+/g, '')}.com`;
  }

  constructUrl(domain, opportunity) {
    // Simplified URL construction - in production, this would use search APIs
    return `https://${domain}`;
  }

  generateLinkTitle(domain, opportunity) {
    return `${opportunity.phrase} - ${domain}`;
  }

  shouldUseNoFollow(source, opportunity) {
    // Use nofollow for commercial domains or when linking to competitors
    const commercialDomains = ['.com', '.net', '.biz'];
    const isCommercial = commercialDomains.some(tld => source.domain.includes(tld));
    
    return isCommercial && opportunity.linkType !== 'documentation';
  }

  suggestPlacement(opportunity) {
    return {
      type: 'inline',
      suggestion: `Link "${opportunity.phrase}" to provide additional context and validation`,
      bestPractices: [
        'Place link naturally within the sentence',
        'Ensure link adds value for readers',
        'Use descriptive anchor text'
      ]
    };
  }

  getUniqueDomains() {
    const domains = this.existingExternalLinks.map(link => link.domain);
    return [...new Set(domains)];
  }

  analyzeAuthorityDistribution() {
    const totalLinks = this.existingExternalLinks.length;
    if (totalLinks === 0) return { lowAuthorityRatio: 0, avgAuthority: 0 };
    
    const authorityScores = this.existingExternalLinks.map(link => 
      this.calculateDomainAuthority(link.domain)
    );
    
    const lowAuthorityCount = authorityScores.filter(score => score < 0.6).length;
    const avgAuthority = authorityScores.reduce((sum, score) => sum + score, 0) / authorityScores.length;
    
    return {
      lowAuthorityRatio: lowAuthorityCount / totalLinks,
      avgAuthority
    };
  }

  analyzeNofollowUsage() {
    const totalLinks = this.existingExternalLinks.length;
    if (totalLinks === 0) return { nofollowRatio: 0, nofollowCount: 0 };
    
    const nofollowCount = this.existingExternalLinks.filter(link => link.nofollow).length;
    
    return {
      nofollowRatio: nofollowCount / totalLinks,
      nofollowCount
    };
  }

  analyzeExternalAnchorTexts() {
    const anchorTexts = this.existingExternalLinks.map(link => link.text);
    const avgLength = anchorTexts.reduce((sum, text) => sum + text.length, 0) / Math.max(anchorTexts.length, 1);
    
    return {
      avgLength,
      totalCount: anchorTexts.length,
      uniqueCount: new Set(anchorTexts).size
    };
  }

  removeDuplicateRecommendations(recommendations) {
    const seen = new Set();
    return recommendations.filter(rec => {
      const key = `${rec.targetDomain}-${rec.anchorText}`;
      if (seen.has(key)) return false;
      seen.add(key);
      return true;
    });
  }

  limitLinksPerDomain(recommendations) {
    const domainCounts = new Map();
    
    return recommendations.filter(rec => {
      const count = domainCounts.get(rec.targetDomain) || 0;
      if (count < this.options.maxLinksPerDomain) {
        domainCounts.set(rec.targetDomain, count + 1);
        return true;
      }
      return false;
    });
  }

  getAuthorityDomains() {
    // This would be expanded with more comprehensive authority domain data
    return [
      'ncbi.nlm.nih.gov', 'nature.com', 'science.org', 'scholar.google.com',
      'census.gov', 'bls.gov', 'who.int', 'cdc.gov', 'nih.gov',
      'harvard.edu', 'stanford.edu', 'mit.edu', 'berkeley.edu',
      'developer.mozilla.org', 'w3.org', 'github.com', 'stackoverflow.com',
      'statista.com', 'pewresearch.org', 'gallup.com', 'reuters.com'
    ];
  }
}

module.exports = ExternalLinksRecommender;
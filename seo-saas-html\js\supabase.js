// Supabase client and database utilities for SEO Pro
// This file handles all database operations and authentication

// Import Supabase from CDN (add this to HTML head)
// <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>

class SupabaseClient {
    constructor() {
        this.client = null;
        this.user = null;
        this.session = null;
        this.initialized = false;
        this.init();
    }

    // Initialize Supabase client
    async init() {
        try {
            if (typeof supabase === 'undefined') {
                throw new Error('Supabase library not loaded. Please include the Supabase CDN script.');
            }

            this.client = supabase.createClient(
                CONFIG.SUPABASE.URL,
                CONFIG.SUPABASE.ANON_KEY,
                {
                    auth: {
                        autoRefreshToken: true,
                        persistSession: true,
                        detectSessionInUrl: true
                    }
                }
            );

            // Get initial session
            const { data: { session }, error } = await this.client.auth.getSession();
            if (error) {
                console.error('Error getting session:', error);
            } else {
                this.session = session;
                this.user = session?.user || null;
            }

            // Listen for auth changes
            this.client.auth.onAuthStateChange((event, session) => {
                this.session = session;
                this.user = session?.user || null;
                this.handleAuthStateChange(event, session);
            });

            this.initialized = true;
            console.log('Supabase client initialized successfully');
        } catch (error) {
            console.error('Failed to initialize Supabase client:', error);
            throw error;
        }
    }

    // Handle authentication state changes
    handleAuthStateChange(event, session) {
        switch (event) {
            case 'SIGNED_IN':
                console.log('User signed in:', session.user.email);
                this.redirectToDashboard();
                break;
            case 'SIGNED_OUT':
                console.log('User signed out');
                this.redirectToLogin();
                break;
            case 'TOKEN_REFRESHED':
                console.log('Token refreshed');
                break;
            case 'USER_UPDATED':
                console.log('User updated');
                break;
        }
    }

    // Redirect to dashboard after login
    redirectToDashboard() {
        if (window.location.pathname.includes('login') || window.location.pathname.includes('register')) {
            window.location.href = '/dashboard.html';
        }
    }

    // Redirect to login when logged out
    redirectToLogin() {
        if (!window.location.pathname.includes('login') && !window.location.pathname.includes('register') && window.location.pathname !== '/') {
            window.location.href = '/login.html';
        }
    }

    // Authentication methods
    async signUp(email, password, userData = {}) {
        try {
            const { data, error } = await this.client.auth.signUp({
                email,
                password,
                options: {
                    data: userData
                }
            });

            if (error) throw error;
            return { success: true, data };
        } catch (error) {
            console.error('Sign up error:', error);
            return { success: false, error: error.message };
        }
    }

    async signIn(email, password) {
        try {
            const { data, error } = await this.client.auth.signInWithPassword({
                email,
                password
            });

            if (error) throw error;
            return { success: true, data };
        } catch (error) {
            console.error('Sign in error:', error);
            return { success: false, error: error.message };
        }
    }

    async signOut() {
        try {
            const { error } = await this.client.auth.signOut();
            if (error) throw error;
            return { success: true };
        } catch (error) {
            console.error('Sign out error:', error);
            return { success: false, error: error.message };
        }
    }

    async resetPassword(email) {
        try {
            const { error } = await this.client.auth.resetPasswordForEmail(email, {
                redirectTo: `${window.location.origin}/reset-password.html`
            });

            if (error) throw error;
            return { success: true };
        } catch (error) {
            console.error('Reset password error:', error);
            return { success: false, error: error.message };
        }
    }

    // User profile methods
    async getUserProfile() {
        if (!this.user) return null;

        try {
            const { data, error } = await this.client
                .from('profiles')
                .select('*')
                .eq('id', this.user.id)
                .single();

            if (error) throw error;
            return data;
        } catch (error) {
            console.error('Error fetching user profile:', error);
            return null;
        }
    }

    async updateUserProfile(updates) {
        if (!this.user) throw new Error('User not authenticated');

        try {
            const { data, error } = await this.client
                .from('profiles')
                .update(updates)
                .eq('id', this.user.id)
                .select()
                .single();

            if (error) throw error;
            return { success: true, data };
        } catch (error) {
            console.error('Error updating user profile:', error);
            return { success: false, error: error.message };
        }
    }

    // Content generation methods
    async saveContentGeneration(contentData) {
        if (!this.user) throw new Error('User not authenticated');

        try {
            const { data, error } = await this.client
                .from('content_generations')
                .insert({
                    user_id: this.user.id,
                    ...contentData
                })
                .select()
                .single();

            if (error) throw error;
            return { success: true, data };
        } catch (error) {
            console.error('Error saving content generation:', error);
            return { success: false, error: error.message };
        }
    }

    async getContentGenerations(limit = 10, offset = 0) {
        if (!this.user) return [];

        try {
            const { data, error } = await this.client
                .from('content_generations')
                .select('*')
                .eq('user_id', this.user.id)
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching content generations:', error);
            return [];
        }
    }

    // SEO analysis methods
    async saveSeoAnalysis(analysisData) {
        if (!this.user) throw new Error('User not authenticated');

        try {
            const { data, error } = await this.client
                .from('seo_analyses')
                .insert({
                    user_id: this.user.id,
                    ...analysisData
                })
                .select()
                .single();

            if (error) throw error;
            return { success: true, data };
        } catch (error) {
            console.error('Error saving SEO analysis:', error);
            return { success: false, error: error.message };
        }
    }

    async getSeoAnalyses(limit = 10, offset = 0) {
        if (!this.user) return [];

        try {
            const { data, error } = await this.client
                .from('seo_analyses')
                .select('*')
                .eq('user_id', this.user.id)
                .order('created_at', { ascending: false })
                .range(offset, offset + limit - 1);

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching SEO analyses:', error);
            return [];
        }
    }

    // Project methods
    async createProject(projectData) {
        if (!this.user) throw new Error('User not authenticated');

        try {
            const { data, error } = await this.client
                .from('projects')
                .insert({
                    user_id: this.user.id,
                    ...projectData
                })
                .select()
                .single();

            if (error) throw error;
            return { success: true, data };
        } catch (error) {
            console.error('Error creating project:', error);
            return { success: false, error: error.message };
        }
    }

    async getProjects() {
        if (!this.user) return [];

        try {
            const { data, error } = await this.client
                .from('projects')
                .select('*')
                .eq('user_id', this.user.id)
                .order('updated_at', { ascending: false });

            if (error) throw error;
            return data || [];
        } catch (error) {
            console.error('Error fetching projects:', error);
            return [];
        }
    }

    async updateProject(projectId, updates) {
        if (!this.user) throw new Error('User not authenticated');

        try {
            const { data, error } = await this.client
                .from('projects')
                .update(updates)
                .eq('id', projectId)
                .eq('user_id', this.user.id)
                .select()
                .single();

            if (error) throw error;
            return { success: true, data };
        } catch (error) {
            console.error('Error updating project:', error);
            return { success: false, error: error.message };
        }
    }

    // Usage tracking methods
    async trackUsage(resource, amount = 1) {
        if (!this.user) return;

        try {
            const { error } = await this.client
                .from('usage_tracking')
                .insert({
                    user_id: this.user.id,
                    resource,
                    amount,
                    date: new Date().toISOString().split('T')[0]
                });

            if (error) throw error;
        } catch (error) {
            console.error('Error tracking usage:', error);
        }
    }

    async getUsageStats(period = 'month') {
        if (!this.user) return {};

        try {
            const startDate = new Date();
            if (period === 'month') {
                startDate.setMonth(startDate.getMonth() - 1);
            } else if (period === 'week') {
                startDate.setDate(startDate.getDate() - 7);
            }

            const { data, error } = await this.client
                .from('usage_tracking')
                .select('resource, amount')
                .eq('user_id', this.user.id)
                .gte('date', startDate.toISOString().split('T')[0]);

            if (error) throw error;

            // Aggregate usage by resource
            const usage = {};
            data.forEach(record => {
                usage[record.resource] = (usage[record.resource] || 0) + record.amount;
            });

            return usage;
        } catch (error) {
            console.error('Error fetching usage stats:', error);
            return {};
        }
    }

    // Utility methods
    isAuthenticated() {
        return !!this.user;
    }

    getCurrentUser() {
        return this.user;
    }

    getSession() {
        return this.session;
    }

    // Check if user has required permissions
    async checkPermission(action, resource = null) {
        if (!this.user) return false;

        const profile = await this.getUserProfile();
        if (!profile) return false;

        // Check subscription limits
        const usage = await this.getUsageStats();
        const planFeatures = ConfigUtils.getPlanFeatures(profile.subscription_tier || 'free');

        switch (action) {
            case 'generate_content':
                return !ConfigUtils.isUsageExceeded(profile.subscription_tier || 'free', 'content_generations', usage.content_generations || 0);
            case 'analyze_seo':
                return !ConfigUtils.isUsageExceeded(profile.subscription_tier || 'free', 'seo_analyses', usage.seo_analyses || 0);
            case 'create_project':
                const projectCount = (await this.getProjects()).length;
                return !ConfigUtils.isUsageExceeded(profile.subscription_tier || 'free', 'projects', projectCount);
            default:
                return true;
        }
    }
}

// Create global Supabase client instance
let supabaseClient;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', async function() {
    try {
        supabaseClient = new SupabaseClient();
        window.supabaseClient = supabaseClient;
        console.log('Supabase client ready');
    } catch (error) {
        console.error('Failed to initialize Supabase client:', error);
    }
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SupabaseClient;
}

{"version": 3, "pages404": true, "caseSensitive": false, "basePath": "", "redirects": [{"source": "/:path+/", "destination": "/:path+", "internal": true, "statusCode": 308, "regex": "^(?:/((?:[^/]+?)(?:/(?:[^/]+?))*))/$"}], "headers": [], "dynamicRoutes": [], "staticRoutes": [{"page": "/", "regex": "^/(?:/)?$", "routeKeys": {}, "namedRegex": "^/(?:/)?$"}, {"page": "/_not-found", "regex": "^/_not\\-found(?:/)?$", "routeKeys": {}, "namedRegex": "^/_not\\-found(?:/)?$"}, {"page": "/auth/callback", "regex": "^/auth/callback(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/callback(?:/)?$"}, {"page": "/auth/signin", "regex": "^/auth/signin(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signin(?:/)?$"}, {"page": "/auth/signup", "regex": "^/auth/signup(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/signup(?:/)?$"}, {"page": "/auth/verify-email", "regex": "^/auth/verify\\-email(?:/)?$", "routeKeys": {}, "namedRegex": "^/auth/verify\\-email(?:/)?$"}, {"page": "/dashboard", "regex": "^/dashboard(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard(?:/)?$"}, {"page": "/dashboard/analysis", "regex": "^/dashboard/analysis(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/analysis(?:/)?$"}, {"page": "/dashboard/content", "regex": "^/dashboard/content(?:/)?$", "routeKeys": {}, "namedRegex": "^/dashboard/content(?:/)?$"}, {"page": "/favicon.ico", "regex": "^/favicon\\.ico(?:/)?$", "routeKeys": {}, "namedRegex": "^/favicon\\.ico(?:/)?$"}, {"page": "/minimal", "regex": "^/minimal(?:/)?$", "routeKeys": {}, "namedRegex": "^/minimal(?:/)?$"}, {"page": "/test-env", "regex": "^/test\\-env(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-env(?:/)?$"}, {"page": "/test-simple", "regex": "^/test\\-simple(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-simple(?:/)?$"}, {"page": "/test-supabase", "regex": "^/test\\-supabase(?:/)?$", "routeKeys": {}, "namedRegex": "^/test\\-supabase(?:/)?$"}], "dataRoutes": [], "rsc": {"header": "RSC", "varyHeader": "RSC, Next-Router-State-Tree, Next-Router-Prefetch, Next-Url", "prefetchHeader": "Next-Router-Prefetch", "didPostponeHeader": "x-nextjs-postponed", "contentTypeHeader": "text/x-component", "suffix": ".rsc", "prefetchSuffix": ".prefetch.rsc"}, "rewrites": []}
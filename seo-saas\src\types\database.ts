// Database Types for Supabase

export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          name: string;
          subscription_tier: 'free' | 'pro' | 'enterprise';
          api_usage: {
            groq: number;
            serper: number;
          };
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          email: string;
          name: string;
          subscription_tier?: 'free' | 'pro' | 'enterprise';
          api_usage?: {
            groq: number;
            serper: number;
          };
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          email?: string;
          name?: string;
          subscription_tier?: 'free' | 'pro' | 'enterprise';
          api_usage?: {
            groq: number;
            serper: number;
          };
          updated_at?: string;
        };
      };
      projects: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          description: string | null;
          target_keywords: string[];
          industry: string;
          country: string;
          language: string;
          settings: {
            content_type: string;
            tone: string;
            target_audience: string;
          };
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          description?: string | null;
          target_keywords: string[];
          industry: string;
          country: string;
          language: string;
          settings?: {
            content_type: string;
            tone: string;
            target_audience: string;
          };
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          name?: string;
          description?: string | null;
          target_keywords?: string[];
          industry?: string;
          country?: string;
          language?: string;
          settings?: {
            content_type: string;
            tone: string;
            target_audience: string;
          };
          updated_at?: string;
        };
      };
      content_generations: {
        Row: {
          id: string;
          project_id: string;
          title: string;
          content: string;
          keywords: string[];
          word_count: number;
          seo_score: number;
          competitor_analysis: any;
          optimization_data: {
            keyword_density: number;
            heading_count: number;
            readability_score: number;
            meta_tags: {
              title: string;
              description: string;
            };
          };
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          project_id: string;
          title: string;
          content: string;
          keywords: string[];
          word_count: number;
          seo_score: number;
          competitor_analysis?: any;
          optimization_data: {
            keyword_density: number;
            heading_count: number;
            readability_score: number;
            meta_tags: {
              title: string;
              description: string;
            };
          };
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          id?: string;
          project_id?: string;
          title?: string;
          content?: string;
          keywords?: string[];
          word_count?: number;
          seo_score?: number;
          competitor_analysis?: any;
          optimization_data?: {
            keyword_density: number;
            heading_count: number;
            readability_score: number;
            meta_tags: {
              title: string;
              description: string;
            };
          };
          updated_at?: string;
        };
      };
      competitor_analyses: {
        Row: {
          id: string;
          keyword: string;
          country: string;
          language: string;
          serp_data: any;
          analysis_data: any;
          created_at: string;
          expires_at: string;
        };
        Insert: {
          id?: string;
          keyword: string;
          country: string;
          language: string;
          serp_data: any;
          analysis_data: any;
          created_at?: string;
          expires_at: string;
        };
        Update: {
          id?: string;
          keyword?: string;
          country?: string;
          language?: string;
          serp_data?: any;
          analysis_data?: any;
          expires_at?: string;
        };
      };
      api_usage_logs: {
        Row: {
          id: string;
          user_id: string;
          service: 'groq' | 'serper';
          endpoint: string;
          tokens_used: number;
          cost: number;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          service: 'groq' | 'serper';
          endpoint: string;
          tokens_used: number;
          cost: number;
          created_at?: string;
        };
        Update: {
          id?: string;
          user_id?: string;
          service?: 'groq' | 'serper';
          endpoint?: string;
          tokens_used?: number;
          cost?: number;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      subscription_tier: 'free' | 'pro' | 'enterprise';
      content_type: 'article' | 'product' | 'service' | 'landing_page';
      tone: 'professional' | 'casual' | 'friendly' | 'authoritative';
      api_service: 'groq' | 'serper';
    };
  };
}

export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T];
// Enhanced Database Types for SEO SAAS Platform

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          subscription_tier: 'free' | 'pro' | 'enterprise';
          subscription_status: 'active' | 'cancelled' | 'past_due' | 'trialing';
          api_usage_limit: number;
          total_content_generated: number;
          total_api_calls: number;
          onboarding_completed: boolean;
          preferences: Json;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          subscription_tier?: 'free' | 'pro' | 'enterprise';
          subscription_status?: 'active' | 'cancelled' | 'past_due' | 'trialing';
          api_usage_limit?: number;
          total_content_generated?: number;
          total_api_calls?: number;
          onboarding_completed?: boolean;
          preferences?: Json;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          subscription_tier?: 'free' | 'pro' | 'enterprise';
          subscription_status?: 'active' | 'cancelled' | 'past_due' | 'trialing';
          api_usage_limit?: number;
          total_content_generated?: number;
          total_api_calls?: number;
          onboarding_completed?: boolean;
          preferences?: Json;
          updated_at?: string;
        };
      };
      projects: {
        Row: {
          id: string;
          user_id: string;
          name: string;
          description: string | null;
          industry: string;
          target_country: string;
          target_language: string;
          website_url: string | null;
          competitor_urls: string[] | null;
          brand_voice: string | null;
          target_audience: string | null;
          settings: Json;
          is_archived: boolean;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          name: string;
          description?: string | null;
          industry: string;
          target_country?: string;
          target_language?: string;
          website_url?: string | null;
          competitor_urls?: string[] | null;
          brand_voice?: string | null;
          target_audience?: string | null;
          settings?: Json;
          is_archived?: boolean;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          name?: string;
          description?: string | null;
          industry?: string;
          target_country?: string;
          target_language?: string;
          website_url?: string | null;
          competitor_urls?: string[] | null;
          brand_voice?: string | null;
          target_audience?: string | null;
          settings?: Json;
          is_archived?: boolean;
          updated_at?: string;
        };
      };
      content_generations: {
        Row: {
          id: string;
          user_id: string;
          project_id: string | null;
          keyword: string;
          location: string;
          industry: string;
          content_type: 'service' | 'blog' | 'product' | 'landing' | 'category' | 'faq';
          tone: 'professional' | 'conversational' | 'authoritative' | 'friendly' | 'technical' | 'casual';
          intent: 'informational' | 'commercial' | 'transactional' | 'navigational';
          target_word_count: number;
          include_images: boolean;
          include_schema: boolean;
          include_faq: boolean;
          generated_title: string | null;
          generated_meta_description: string | null;
          generated_content: string | null;
          content_outline: Json | null;
          seo_analysis: Json | null;
          competitor_data: Json | null;
          quality_score: number | null;
          seo_score: number | null;
          readability_score: number | null;
          word_count: number | null;
          heading_count: Json | null;
          keyword_density: number | null;
          lsi_keywords: string[] | null;
          entities: string[] | null;
          internal_links_suggested: number;
          external_links_suggested: number;
          generation_time_ms: number | null;
          tokens_used: number | null;
          api_cost: number | null;
          status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
          error_message: string | null;
          is_exported: boolean;
          export_formats: string[] | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          project_id?: string | null;
          keyword: string;
          location: string;
          industry: string;
          content_type: 'service' | 'blog' | 'product' | 'landing' | 'category' | 'faq';
          tone: 'professional' | 'conversational' | 'authoritative' | 'friendly' | 'technical' | 'casual';
          intent: 'informational' | 'commercial' | 'transactional' | 'navigational';
          target_word_count?: number;
          include_images?: boolean;
          include_schema?: boolean;
          include_faq?: boolean;
          generated_title?: string | null;
          generated_meta_description?: string | null;
          generated_content?: string | null;
          content_outline?: Json | null;
          seo_analysis?: Json | null;
          competitor_data?: Json | null;
          quality_score?: number | null;
          seo_score?: number | null;
          readability_score?: number | null;
          word_count?: number | null;
          heading_count?: Json | null;
          keyword_density?: number | null;
          lsi_keywords?: string[] | null;
          entities?: string[] | null;
          internal_links_suggested?: number;
          external_links_suggested?: number;
          generation_time_ms?: number | null;
          tokens_used?: number | null;
          api_cost?: number | null;
          status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
          error_message?: string | null;
          is_exported?: boolean;
          export_formats?: string[] | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          project_id?: string | null;
          keyword?: string;
          location?: string;
          industry?: string;
          content_type?: 'service' | 'blog' | 'product' | 'landing' | 'category' | 'faq';
          tone?: 'professional' | 'conversational' | 'authoritative' | 'friendly' | 'technical' | 'casual';
          intent?: 'informational' | 'commercial' | 'transactional' | 'navigational';
          target_word_count?: number;
          include_images?: boolean;
          include_schema?: boolean;
          include_faq?: boolean;
          generated_title?: string | null;
          generated_meta_description?: string | null;
          generated_content?: string | null;
          content_outline?: Json | null;
          seo_analysis?: Json | null;
          competitor_data?: Json | null;
          quality_score?: number | null;
          seo_score?: number | null;
          readability_score?: number | null;
          word_count?: number | null;
          heading_count?: Json | null;
          keyword_density?: number | null;
          lsi_keywords?: string[] | null;
          entities?: string[] | null;
          internal_links_suggested?: number;
          external_links_suggested?: number;
          generation_time_ms?: number | null;
          tokens_used?: number | null;
          api_cost?: number | null;
          status?: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
          error_message?: string | null;
          is_exported?: boolean;
          export_formats?: string[] | null;
          updated_at?: string;
        };
      };
      api_usage_logs: {
        Row: {
          id: string;
          user_id: string | null;
          content_generation_id: string | null;
          api_name: 'groq' | 'serper' | 'supabase' | 'openai';
          endpoint: string | null;
          method: string | null;
          request_data: Json | null;
          response_data: Json | null;
          tokens_used: number;
          cost: number;
          response_time_ms: number | null;
          status_code: number | null;
          error_message: string | null;
          ip_address: string | null;
          user_agent: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          content_generation_id?: string | null;
          api_name: 'groq' | 'serper' | 'supabase' | 'openai';
          endpoint?: string | null;
          method?: string | null;
          request_data?: Json | null;
          response_data?: Json | null;
          tokens_used?: number;
          cost?: number;
          response_time_ms?: number | null;
          status_code?: number | null;
          error_message?: string | null;
          ip_address?: string | null;
          user_agent?: string | null;
          created_at?: string;
        };
        Update: {
          user_id?: string | null;
          content_generation_id?: string | null;
          api_name?: 'groq' | 'serper' | 'supabase' | 'openai';
          endpoint?: string | null;
          method?: string | null;
          request_data?: Json | null;
          response_data?: Json | null;
          tokens_used?: number;
          cost?: number;
          response_time_ms?: number | null;
          status_code?: number | null;
          error_message?: string | null;
          ip_address?: string | null;
          user_agent?: string | null;
        };
      };
      user_subscriptions: {
        Row: {
          id: string;
          user_id: string;
          plan_type: 'free' | 'pro' | 'enterprise';
          status: 'active' | 'cancelled' | 'past_due' | 'trialing' | 'incomplete';
          current_period_start: string | null;
          current_period_end: string | null;
          trial_end: string | null;
          stripe_customer_id: string | null;
          stripe_subscription_id: string | null;
          stripe_price_id: string | null;
          cancel_at_period_end: boolean;
          cancelled_at: string | null;
          billing_cycle_anchor: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          plan_type: 'free' | 'pro' | 'enterprise';
          status: 'active' | 'cancelled' | 'past_due' | 'trialing' | 'incomplete';
          current_period_start?: string | null;
          current_period_end?: string | null;
          trial_end?: string | null;
          stripe_customer_id?: string | null;
          stripe_subscription_id?: string | null;
          stripe_price_id?: string | null;
          cancel_at_period_end?: boolean;
          cancelled_at?: string | null;
          billing_cycle_anchor?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          plan_type?: 'free' | 'pro' | 'enterprise';
          status?: 'active' | 'cancelled' | 'past_due' | 'trialing' | 'incomplete';
          current_period_start?: string | null;
          current_period_end?: string | null;
          trial_end?: string | null;
          stripe_customer_id?: string | null;
          stripe_subscription_id?: string | null;
          stripe_price_id?: string | null;
          cancel_at_period_end?: boolean;
          cancelled_at?: string | null;
          billing_cycle_anchor?: string | null;
          updated_at?: string;
        };
      };
      content_templates: {
        Row: {
          id: string;
          name: string;
          description: string | null;
          industry: string;
          content_type: string;
          template_structure: Json;
          prompt_template: string;
          example_output: string | null;
          tags: string[] | null;
          is_active: boolean;
          is_premium: boolean;
          usage_count: number;
          created_by: string | null;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id?: string;
          name: string;
          description?: string | null;
          industry: string;
          content_type: string;
          template_structure: Json;
          prompt_template: string;
          example_output?: string | null;
          tags?: string[] | null;
          is_active?: boolean;
          is_premium?: boolean;
          usage_count?: number;
          created_by?: string | null;
          created_at?: string;
          updated_at?: string;
        };
        Update: {
          name?: string;
          description?: string | null;
          industry?: string;
          content_type?: string;
          template_structure?: Json;
          prompt_template?: string;
          example_output?: string | null;
          tags?: string[] | null;
          is_active?: boolean;
          is_premium?: boolean;
          usage_count?: number;
          updated_at?: string;
        };
      };
      seo_analyses: {
        Row: {
          id: string;
          content_generation_id: string | null;
          keyword: string;
          location: string;
          search_volume: number | null;
          keyword_difficulty: number | null;
          competition_level: 'low' | 'medium' | 'high' | null;
          competitors: Json;
          averages: Json;
          recommendations: Json;
          technical_seo: Json | null;
          content_gaps: Json | null;
          topic_clusters: Json | null;
          user_intent_analysis: Json | null;
          optimization_score: number | null;
          opportunity_score: number | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          content_generation_id?: string | null;
          keyword: string;
          location: string;
          search_volume?: number | null;
          keyword_difficulty?: number | null;
          competition_level?: 'low' | 'medium' | 'high' | null;
          competitors: Json;
          averages: Json;
          recommendations: Json;
          technical_seo?: Json | null;
          content_gaps?: Json | null;
          topic_clusters?: Json | null;
          user_intent_analysis?: Json | null;
          optimization_score?: number | null;
          opportunity_score?: number | null;
          created_at?: string;
        };
        Update: {
          content_generation_id?: string | null;
          keyword?: string;
          location?: string;
          search_volume?: number | null;
          keyword_difficulty?: number | null;
          competition_level?: 'low' | 'medium' | 'high' | null;
          competitors?: Json;
          averages?: Json;
          recommendations?: Json;
          technical_seo?: Json | null;
          content_gaps?: Json | null;
          topic_clusters?: Json | null;
          user_intent_analysis?: Json | null;
          optimization_score?: number | null;
          opportunity_score?: number | null;
        };
      };
      competitor_cache: {
        Row: {
          id: string;
          cache_key: string;
          keyword: string;
          location: string;
          industry: string | null;
          competitor_data: Json;
          serp_data: Json;
          analysis_metadata: Json | null;
          expires_at: string;
          created_at: string;
        };
        Insert: {
          id?: string;
          cache_key: string;
          keyword: string;
          location: string;
          industry?: string | null;
          competitor_data: Json;
          serp_data: Json;
          analysis_metadata?: Json | null;
          expires_at: string;
          created_at?: string;
        };
        Update: {
          cache_key?: string;
          keyword?: string;
          location?: string;
          industry?: string | null;
          competitor_data?: Json;
          serp_data?: Json;
          analysis_metadata?: Json | null;
          expires_at?: string;
        };
      };
      user_activity_logs: {
        Row: {
          id: string;
          user_id: string | null;
          action_type: string;
          action_details: Json | null;
          ip_address: string | null;
          user_agent: string | null;
          session_id: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id?: string | null;
          action_type: string;
          action_details?: Json | null;
          ip_address?: string | null;
          user_agent?: string | null;
          session_id?: string | null;
          created_at?: string;
        };
        Update: {
          user_id?: string | null;
          action_type?: string;
          action_details?: Json | null;
          ip_address?: string | null;
          user_agent?: string | null;
          session_id?: string | null;
        };
      };
      content_exports: {
        Row: {
          id: string;
          user_id: string;
          content_generation_id: string;
          export_format: 'html' | 'markdown' | 'docx' | 'pdf' | 'json';
          export_settings: Json | null;
          file_url: string | null;
          file_size_bytes: number | null;
          download_count: number;
          expires_at: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          content_generation_id: string;
          export_format: 'html' | 'markdown' | 'docx' | 'pdf' | 'json';
          export_settings?: Json | null;
          file_url?: string | null;
          file_size_bytes?: number | null;
          download_count?: number;
          expires_at?: string | null;
          created_at?: string;
        };
        Update: {
          export_format?: 'html' | 'markdown' | 'docx' | 'pdf' | 'json';
          export_settings?: Json | null;
          file_url?: string | null;
          file_size_bytes?: number | null;
          download_count?: number;
          expires_at?: string | null;
        };
      };
      content_feedback: {
        Row: {
          id: string;
          user_id: string;
          content_generation_id: string;
          rating: number | null;
          feedback_text: string | null;
          feedback_categories: string[] | null;
          is_helpful: boolean | null;
          improvement_suggestions: string | null;
          created_at: string;
        };
        Insert: {
          id?: string;
          user_id: string;
          content_generation_id: string;
          rating?: number | null;
          feedback_text?: string | null;
          feedback_categories?: string[] | null;
          is_helpful?: boolean | null;
          improvement_suggestions?: string | null;
          created_at?: string;
        };
        Update: {
          rating?: number | null;
          feedback_text?: string | null;
          feedback_categories?: string[] | null;
          is_helpful?: boolean | null;
          improvement_suggestions?: string | null;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      handle_new_user: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      increment_content_generated: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      increment_api_calls: {
        Args: Record<PropertyKey, never>;
        Returns: undefined;
      };
      clean_expired_cache: {
        Args: Record<PropertyKey, never>;
        Returns: void;
      };
    };
    Enums: {
      subscription_tier: 'free' | 'pro' | 'enterprise';
      subscription_status: 'active' | 'cancelled' | 'past_due' | 'trialing' | 'incomplete';
      content_type: 'service' | 'blog' | 'product' | 'landing' | 'category' | 'faq';
      tone: 'professional' | 'conversational' | 'authoritative' | 'friendly' | 'technical' | 'casual';
      intent: 'informational' | 'commercial' | 'transactional' | 'navigational';
      api_service: 'groq' | 'serper' | 'supabase' | 'openai';
      competition_level: 'low' | 'medium' | 'high';
      export_format: 'html' | 'markdown' | 'docx' | 'pdf' | 'json';
      status: 'pending' | 'processing' | 'completed' | 'failed' | 'cancelled';
    };
  };
}

// JSON type for JSONB columns
export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[];

// Helper types for easier access
export type Tables<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Row'];
export type Inserts<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Insert'];
export type Updates<T extends keyof Database['public']['Tables']> = Database['public']['Tables'][T]['Update'];
export type Enums<T extends keyof Database['public']['Enums']> = Database['public']['Enums'][T];

// Specific table types for convenience
export type Profile = Tables<'profiles'>;
export type Project = Tables<'projects'>;
export type ContentGeneration = Tables<'content_generations'>;
export type ApiUsageLog = Tables<'api_usage_logs'>;
export type UserSubscription = Tables<'user_subscriptions'>;
export type ContentTemplate = Tables<'content_templates'>;
export type SeoAnalysis = Tables<'seo_analyses'>;
export type CompetitorCache = Tables<'competitor_cache'>;
export type UserActivityLog = Tables<'user_activity_logs'>;
export type ContentExport = Tables<'content_exports'>;
export type ContentFeedback = Tables<'content_feedback'>;

// Insert types for convenience
export type ProfileInsert = Inserts<'profiles'>;
export type ProjectInsert = Inserts<'projects'>;
export type ContentGenerationInsert = Inserts<'content_generations'>;
export type ApiUsageLogInsert = Inserts<'api_usage_logs'>;

// Update types for convenience
export type ProfileUpdate = Updates<'profiles'>;
export type ProjectUpdate = Updates<'projects'>;
export type ContentGenerationUpdate = Updates<'content_generations'>;
// Professional Skeleton Loading Components
// Enterprise-grade loading states for better UX

import * as React from "react"
import { cn } from "@/lib/utils"

function Skeleton({
  className,
  ...props
}: React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "animate-pulse rounded-md bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%]",
        className
      )}
      style={{
        animation: "shimmer 2s infinite",
      }}
      {...props}
    />
  )
}

// Card Skeleton
function CardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("p-6 border rounded-lg bg-card", className)}>
      <div className="flex items-start space-x-4">
        <Skeleton className="h-12 w-12 rounded-lg" />
        <div className="flex-1 space-y-3">
          <Skeleton className="h-5 w-3/4" />
          <Skeleton className="h-4 w-full" />
          <Skeleton className="h-4 w-2/3" />
        </div>
      </div>
    </div>
  )
}

// Feature Skeleton
function FeatureSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("p-6 border rounded-xl bg-card shadow-lg", className)}>
      <div className="space-y-4">
        <div className="flex items-center space-x-4">
          <Skeleton className="h-16 w-16 rounded-2xl" />
          <div className="flex-1">
            <Skeleton className="h-6 w-3/4 mb-2" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-5/6" />
          </div>
        </div>
      </div>
    </div>
  )
}

// Testimonial Skeleton
function TestimonialSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("p-8 bg-gradient-to-br from-blue-50 to-purple-50 rounded-3xl shadow-xl", className)}>
      <div className="text-center space-y-4">
        <div className="flex justify-center space-x-1">
          {[...Array(5)].map((_, i) => (
            <Skeleton key={i} className="h-6 w-6 rounded-full" />
          ))}
        </div>
        <div className="space-y-3">
          <Skeleton className="h-6 w-full mx-auto" />
          <Skeleton className="h-6 w-5/6 mx-auto" />
          <Skeleton className="h-6 w-4/5 mx-auto" />
        </div>
        <div className="flex items-center justify-center space-x-4 pt-4">
          <Skeleton className="h-12 w-12 rounded-full" />
          <div>
            <Skeleton className="h-5 w-32 mb-1" />
            <Skeleton className="h-4 w-24" />
          </div>
        </div>
      </div>
    </div>
  )
}

// Pricing Card Skeleton
function PricingCardSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("p-6 border rounded-lg bg-card shadow-lg", className)}>
      <div className="text-center pb-4">
        <Skeleton className="h-8 w-24 mx-auto mb-4" />
        <div className="mb-4">
          <Skeleton className="h-12 w-20 mx-auto mb-1" />
          <Skeleton className="h-4 w-16 mx-auto" />
        </div>
        <Skeleton className="h-5 w-full" />
      </div>
      <div className="space-y-6">
        <div className="space-y-3">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="flex items-center space-x-3">
              <Skeleton className="h-5 w-5 rounded-full" />
              <Skeleton className="h-4 flex-1" />
            </div>
          ))}
        </div>
        <Skeleton className="h-11 w-full rounded-md" />
      </div>
    </div>
  )
}

// Stats Skeleton
function StatsSkeleton({ className }: { className?: string }) {
  return (
    <div className={cn("grid grid-cols-2 lg:grid-cols-4 gap-8", className)}>
      {[...Array(4)].map((_, i) => (
        <div key={i} className="glass rounded-2xl p-6 text-center">
          <Skeleton className="h-12 w-12 rounded-xl mx-auto mb-4" />
          <Skeleton className="h-8 w-16 mx-auto mb-2" />
          <Skeleton className="h-4 w-20 mx-auto" />
        </div>
      ))}
    </div>
  )
}

// Navigation Skeleton
function NavigationSkeleton({ className }: { className?: string }) {
  return (
    <nav className={cn("fixed top-0 w-full z-50 glass backdrop-blur-md border-b border-white/20", className)}>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          {/* Logo */}
          <div className="flex items-center space-x-3">
            <Skeleton className="w-10 h-10 rounded-xl" />
            <div>
              <Skeleton className="h-5 w-32 mb-1" />
              <Skeleton className="h-3 w-12" />
            </div>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center space-x-8">
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-4 w-16" />
            <Skeleton className="h-10 w-32 rounded-md" />
          </div>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <Skeleton className="h-8 w-8 rounded" />
          </div>
        </div>
      </div>
    </nav>
  )
}

// Hero Section Skeleton
function HeroSkeleton({ className }: { className?: string }) {
  return (
    <section className={cn("relative pt-24 pb-20 lg:pt-32 lg:pb-32 overflow-hidden", className)}>
      <div className="relative max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center">
          {/* Announcement Badge */}
          <div className="flex justify-center mb-8">
            <Skeleton className="h-10 w-64 rounded-full" />
          </div>

          {/* Main Headline */}
          <div className="space-y-4 mb-8">
            <Skeleton className="h-16 w-full max-w-4xl mx-auto" />
            <Skeleton className="h-16 w-5/6 max-w-3xl mx-auto" />
          </div>

          {/* Subtitle */}
          <div className="space-y-3 mb-12">
            <Skeleton className="h-6 w-full max-w-4xl mx-auto" />
            <Skeleton className="h-6 w-4/5 max-w-3xl mx-auto" />
          </div>

          {/* CTA Buttons */}
          <div className="flex flex-col sm:flex-row gap-4 justify-center mb-16">
            <Skeleton className="h-14 w-48 rounded-lg" />
            <Skeleton className="h-14 w-40 rounded-lg" />
          </div>

          {/* Stats */}
          <StatsSkeleton />
        </div>
      </div>
    </section>
  )
}

// Complete Page Skeleton
function PageSkeleton() {
  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100">
      <NavigationSkeleton />
      <HeroSkeleton />
      
      {/* Features Section */}
      <section className="py-24 bg-gradient-to-b from-white to-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-20">
            <Skeleton className="h-6 w-24 mx-auto mb-6 rounded-full" />
            <Skeleton className="h-12 w-3/4 max-w-2xl mx-auto mb-6" />
            <Skeleton className="h-6 w-full max-w-3xl mx-auto" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[...Array(6)].map((_, i) => (
              <FeatureSkeleton key={i} />
            ))}
          </div>
        </div>
      </section>

      {/* Testimonials Section */}
      <section className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Skeleton className="h-6 w-32 mx-auto mb-6 rounded-full" />
            <Skeleton className="h-12 w-3/4 mx-auto mb-6" />
            <Skeleton className="h-6 w-full max-w-2xl mx-auto mb-16" />
            
            <TestimonialSkeleton />
          </div>
        </div>
      </section>

      {/* Pricing Section */}
      <section className="py-20 bg-gradient-to-br from-gray-50 to-blue-50">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <Skeleton className="h-6 w-28 mx-auto mb-6 rounded-full" />
            <Skeleton className="h-12 w-3/4 mx-auto mb-6" />
            <Skeleton className="h-6 w-full" />
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-6xl mx-auto">
            {[...Array(3)].map((_, i) => (
              <PricingCardSkeleton key={i} />
            ))}
          </div>
        </div>
      </section>
    </div>
  )
}

export { 
  Skeleton, 
  CardSkeleton, 
  FeatureSkeleton, 
  TestimonialSkeleton, 
  PricingCardSkeleton, 
  StatsSkeleton,
  NavigationSkeleton,
  HeroSkeleton,
  PageSkeleton 
}
# SEO SAAS Backend API

## Setup Instructions

1. **Install Dependencies**
   ```bash
   cd backend
   npm install
   ```

2. **Environment Configuration**
   - Copy `.env.example` to `.env`
   - Add your API keys:
     - `GROQ_API_KEY`: Get from https://console.groq.com
     - `SERPER_API_KEY`: Get from https://serper.dev
     - `SUPABASE_URL`: Your Supabase project URL
     - `SUPABASE_ANON_KEY`: Your Supabase anon key
     - `SUPABASE_SERVICE_KEY`: Your Supabase service key
   - Generate a secure `JWT_SECRET` (use: `node -e "console.log(require('crypto').randomBytes(64).toString('hex'))"`)

3. **Run the Server**
   ```bash
   # Development mode
   npm run dev

   # Production mode
   npm start
   ```

## API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/verify` - Verify JWT token

### Content Generation
- `POST /api/content/generate` - Generate SEO content (requires auth)
- `GET /api/content/list` - List user's generated content
- `GET /api/content/:id` - Get specific content

### SEO Analysis
- `POST /api/analysis/competitors` - Analyze competitor pages
- `POST /api/analysis/content` - Analyze content SEO score

### Export
- `POST /api/export/html` - Export content as HTML
- `POST /api/export/markdown` - Export content as Markdown
- `POST /api/export/json` - Export multiple contents as JSON

## Security Features
- JWT authentication
- Rate limiting
- CORS protection
- Helmet security headers
- Input validation
- SQL injection protection via Supabase

## Rate Limits
- Default: 100 requests per 15 minutes
- Configurable via environment variables

## Usage Limits by Tier
- **Free**: 5 content generations, 10 analyses per day
- **Professional**: 50 content generations, 100 analyses per day
- **Business**: 200 content generations, 500 analyses per day
- **Enterprise**: Unlimited
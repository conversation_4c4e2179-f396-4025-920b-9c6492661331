<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Projects - SEO Pro</title>
    <meta name="description" content="Manage your SEO projects, content library, and team collaboration. Organize all your content generation and analysis work in one place.">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style">
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" href="images/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <a href="index.html" class="logo">
                    <span class="logo-text">SEO Pro</span>
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <ul class="nav-menu">
                <li><a href="dashboard.html" class="nav-link">Dashboard</a></li>
                <li><a href="content-generator.html" class="nav-link">Generate</a></li>
                <li><a href="seo-analysis.html" class="nav-link">Analyze</a></li>
                <li><a href="projects.html" class="nav-link active">Projects</a></li>
            </ul>
            
            <!-- User Menu -->
            <div class="nav-user">
                <div class="user-avatar">
                    <img src="images/avatars/user.jpg" alt="User Avatar" class="avatar">
                </div>
                <div class="user-menu">
                    <a href="profile.html" class="user-menu-item">Profile</a>
                    <a href="settings.html" class="user-menu-item">Settings</a>
                    <a href="billing.html" class="user-menu-item">Billing</a>
                    <a href="login.html" class="user-menu-item">Logout</a>
                </div>
            </div>
            
            <!-- Mobile Menu Button -->
            <button class="mobile-menu-btn" aria-label="Toggle menu">
                <span class="hamburger"></span>
            </button>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <div class="page-header-content">
                    <h1 class="page-title">Projects</h1>
                    <p class="page-description">
                        Organize your content generation and SEO analysis work into projects for better collaboration and tracking.
                    </p>
                </div>
                <div class="page-actions">
                    <button class="btn btn-primary" onclick="openCreateProjectModal()">
                        <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd"/>
                        </svg>
                        New Project
                    </button>
                </div>
            </div>

            <!-- Project Stats -->
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">12</h3>
                        <p class="stat-label">Active Projects</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">247</h3>
                        <p class="stat-label">Content Pieces</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">8</h3>
                        <p class="stat-label">Team Members</p>
                    </div>
                </div>
                <div class="stat-card">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">87</h3>
                        <p class="stat-label">Avg SEO Score</p>
                    </div>
                </div>
            </div>

            <!-- Filters and Search -->
            <div class="filters-section">
                <div class="filters-left">
                    <div class="search-box">
                        <svg class="search-icon" viewBox="0 0 20 20" fill="currentColor">
                            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"/>
                        </svg>
                        <input type="text" placeholder="Search projects..." class="search-input">
                    </div>
                </div>
                <div class="filters-right">
                    <select class="filter-select">
                        <option value="all">All Projects</option>
                        <option value="active">Active</option>
                        <option value="completed">Completed</option>
                        <option value="archived">Archived</option>
                    </select>
                    <select class="filter-select">
                        <option value="recent">Recently Updated</option>
                        <option value="name">Name A-Z</option>
                        <option value="created">Date Created</option>
                    </select>
                </div>
            </div>

            <!-- Projects Grid -->
            <div class="projects-grid">
                <div class="project-card">
                    <div class="project-header">
                        <div class="project-info">
                            <h3 class="project-name">E-commerce SEO Campaign</h3>
                            <p class="project-description">Complete SEO strategy for online retail store</p>
                        </div>
                        <div class="project-status">
                            <span class="status-badge active">Active</span>
                        </div>
                    </div>
                    <div class="project-stats">
                        <div class="project-stat">
                            <span class="stat-label">Content</span>
                            <span class="stat-value">24 pieces</span>
                        </div>
                        <div class="project-stat">
                            <span class="stat-label">SEO Score</span>
                            <span class="stat-value">92/100</span>
                        </div>
                        <div class="project-stat">
                            <span class="stat-label">Team</span>
                            <span class="stat-value">5 members</span>
                        </div>
                    </div>
                    <div class="project-progress">
                        <div class="progress-info">
                            <span class="progress-label">Progress</span>
                            <span class="progress-percentage">75%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 75%"></div>
                        </div>
                    </div>
                    <div class="project-footer">
                        <div class="project-team">
                            <div class="team-avatars">
                                <img src="images/avatars/user1.jpg" alt="Team member" class="team-avatar">
                                <img src="images/avatars/user2.jpg" alt="Team member" class="team-avatar">
                                <img src="images/avatars/user3.jpg" alt="Team member" class="team-avatar">
                                <span class="team-count">+2</span>
                            </div>
                        </div>
                        <div class="project-actions">
                            <button class="btn btn-ghost btn-sm">View</button>
                            <button class="btn btn-primary btn-sm">Edit</button>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-header">
                        <div class="project-info">
                            <h3 class="project-name">SaaS Content Strategy</h3>
                            <p class="project-description">Blog content and landing pages for B2B SaaS</p>
                        </div>
                        <div class="project-status">
                            <span class="status-badge active">Active</span>
                        </div>
                    </div>
                    <div class="project-stats">
                        <div class="project-stat">
                            <span class="stat-label">Content</span>
                            <span class="stat-value">18 pieces</span>
                        </div>
                        <div class="project-stat">
                            <span class="stat-label">SEO Score</span>
                            <span class="stat-value">88/100</span>
                        </div>
                        <div class="project-stat">
                            <span class="stat-label">Team</span>
                            <span class="stat-value">3 members</span>
                        </div>
                    </div>
                    <div class="project-progress">
                        <div class="progress-info">
                            <span class="progress-label">Progress</span>
                            <span class="progress-percentage">60%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 60%"></div>
                        </div>
                    </div>
                    <div class="project-footer">
                        <div class="project-team">
                            <div class="team-avatars">
                                <img src="images/avatars/user4.jpg" alt="Team member" class="team-avatar">
                                <img src="images/avatars/user5.jpg" alt="Team member" class="team-avatar">
                                <img src="images/avatars/user6.jpg" alt="Team member" class="team-avatar">
                            </div>
                        </div>
                        <div class="project-actions">
                            <button class="btn btn-ghost btn-sm">View</button>
                            <button class="btn btn-primary btn-sm">Edit</button>
                        </div>
                    </div>
                </div>

                <div class="project-card">
                    <div class="project-header">
                        <div class="project-info">
                            <h3 class="project-name">Healthcare Blog Series</h3>
                            <p class="project-description">Medical content with compliance requirements</p>
                        </div>
                        <div class="project-status">
                            <span class="status-badge completed">Completed</span>
                        </div>
                    </div>
                    <div class="project-stats">
                        <div class="project-stat">
                            <span class="stat-label">Content</span>
                            <span class="stat-value">12 pieces</span>
                        </div>
                        <div class="project-stat">
                            <span class="stat-label">SEO Score</span>
                            <span class="stat-value">95/100</span>
                        </div>
                        <div class="project-stat">
                            <span class="stat-label">Team</span>
                            <span class="stat-value">2 members</span>
                        </div>
                    </div>
                    <div class="project-progress">
                        <div class="progress-info">
                            <span class="progress-label">Progress</span>
                            <span class="progress-percentage">100%</span>
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: 100%"></div>
                        </div>
                    </div>
                    <div class="project-footer">
                        <div class="project-team">
                            <div class="team-avatars">
                                <img src="images/avatars/user7.jpg" alt="Team member" class="team-avatar">
                                <img src="images/avatars/user8.jpg" alt="Team member" class="team-avatar">
                            </div>
                        </div>
                        <div class="project-actions">
                            <button class="btn btn-ghost btn-sm">View</button>
                            <button class="btn btn-outline btn-sm">Archive</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Create Project Modal -->
    <div class="modal" id="create-project-modal">
        <div class="modal-backdrop" onclick="closeCreateProjectModal()"></div>
        <div class="modal-content">
            <div class="modal-header">
                <h2 class="modal-title">Create New Project</h2>
                <button class="modal-close" onclick="closeCreateProjectModal()">
                    <svg viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"/>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <form class="project-form">
                    <div class="form-group">
                        <label for="project-name" class="form-label">Project Name</label>
                        <input type="text" id="project-name" class="form-input" placeholder="Enter project name" required>
                    </div>
                    <div class="form-group">
                        <label for="project-description" class="form-label">Description</label>
                        <textarea id="project-description" class="form-textarea" placeholder="Describe your project goals and objectives" rows="3"></textarea>
                    </div>
                    <div class="form-row">
                        <div class="form-group">
                            <label for="project-industry" class="form-label">Industry</label>
                            <select id="project-industry" class="form-select">
                                <option value="">Select Industry</option>
                                <option value="technology">Technology</option>
                                <option value="healthcare">Healthcare</option>
                                <option value="finance">Finance</option>
                                <option value="ecommerce">E-commerce</option>
                                <option value="realestate">Real Estate</option>
                                <option value="education">Education</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="project-type" class="form-label">Project Type</label>
                            <select id="project-type" class="form-select">
                                <option value="content">Content Generation</option>
                                <option value="seo">SEO Analysis</option>
                                <option value="competitor">Competitor Research</option>
                                <option value="comprehensive">Comprehensive Campaign</option>
                            </select>
                        </div>
                    </div>
                    <div class="form-group">
                        <label for="team-members" class="form-label">Team Members</label>
                        <input type="text" id="team-members" class="form-input" placeholder="Enter email addresses separated by commas">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-ghost" onclick="closeCreateProjectModal()">Cancel</button>
                <button type="submit" class="btn btn-primary">Create Project</button>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3 class="footer-title">SEO Pro</h3>
                    <p class="footer-description">AI-powered SEO content generation platform</p>
                </div>
                <div class="footer-section">
                    <h4 class="footer-heading">Product</h4>
                    <ul class="footer-links">
                        <li><a href="features.html">Features</a></li>
                        <li><a href="pricing.html">Pricing</a></li>
                        <li><a href="dashboard.html">Dashboard</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SEO Pro. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        function openCreateProjectModal() {
            document.getElementById('create-project-modal').classList.add('active');
        }
        
        function closeCreateProjectModal() {
            document.getElementById('create-project-modal').classList.remove('active');
        }
    </script>
</body>
</html>

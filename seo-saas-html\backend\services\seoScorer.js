const cheerio = require('cheerio');
const natural = require('natural');
const stopword = require('stopword');

class SEOScorer {
  constructor(content, targetKeyword, options = {}) {
    this.content = content;
    this.targetKeyword = targetKeyword.toLowerCase();
    this.options = {
      minWordCount: 300,
      maxWordCount: 3000,
      optimalKeywordDensity: { min: 1, max: 3 },
      ...options
    };
    
    // Parse content with cheerio if it's HTML
    this.$ = cheerio.load(`<div>${content}</div>`);
    this.plainText = this.$.text();
    this.words = this.getWords();
    this.sentences = this.getSentences();
  }

  // Main scoring function
  calculateSEOScore() {
    const scores = {
      keyword: this.scoreKeywordOptimization(),
      content: this.scoreContentQuality(),
      structure: this.scoreContentStructure(),
      readability: this.scoreReadability(),
      technical: this.scoreTechnicalSEO()
    };

    // Weight the scores
    const weights = {
      keyword: 0.25,    // 25%
      content: 0.25,    // 25%
      structure: 0.20,  // 20%
      readability: 0.15, // 15%
      technical: 0.15   // 15%
    };

    const totalScore = Object.keys(scores).reduce((total, category) => {
      return total + (scores[category].score * weights[category]);
    }, 0);

    return {
      overallScore: Math.round(totalScore),
      categoryScores: scores,
      recommendations: this.generateRecommendations(scores),
      metrics: this.getContentMetrics()
    };
  }

  // Keyword optimization scoring
  scoreKeywordOptimization() {
    const score = { score: 0, details: {}, issues: [] };
    
    // Keyword density
    const keywordCount = this.countKeywordOccurrences();
    const density = (keywordCount / this.words.length) * 100;
    score.details.keywordDensity = density;
    
    if (density >= this.options.optimalKeywordDensity.min && 
        density <= this.options.optimalKeywordDensity.max) {
      score.score += 30;
    } else if (density < this.options.optimalKeywordDensity.min) {
      score.score += Math.max(0, 15 - (this.options.optimalKeywordDensity.min - density) * 5);
      score.issues.push(`Keyword density too low (${density.toFixed(2)}%). Aim for ${this.options.optimalKeywordDensity.min}-${this.options.optimalKeywordDensity.max}%.`);
    } else {
      score.score += Math.max(0, 30 - (density - this.options.optimalKeywordDensity.max) * 3);
      score.issues.push(`Keyword density too high (${density.toFixed(2)}%). Risk of keyword stuffing.`);
    }

    // H1 keyword presence
    const h1Tags = this.$('h1');
    const h1HasKeyword = h1Tags.text().toLowerCase().includes(this.targetKeyword);
    if (h1HasKeyword) {
      score.score += 20;
    } else {
      score.issues.push('Primary keyword not found in H1 tag.');
    }

    // First paragraph keyword presence
    const firstParagraph = this.$('p').first().text().toLowerCase();
    if (firstParagraph.includes(this.targetKeyword)) {
      score.score += 15;
    } else {
      score.issues.push('Primary keyword not found in first paragraph.');
    }

    // Title keyword presence (if title tag exists)
    const title = this.$('title').text().toLowerCase();
    if (title && title.includes(this.targetKeyword)) {
      score.score += 15;
    }

    // LSI keywords and variations
    const lsiScore = this.scoreLSIKeywords();
    score.score += lsiScore;
    score.details.lsiKeywords = this.findLSIKeywords();

    return {
      score: Math.min(100, score.score),
      details: score.details,
      issues: score.issues
    };
  }

  // Content quality scoring
  scoreContentQuality() {
    const score = { score: 0, details: {}, issues: [] };
    
    // Word count
    const wordCount = this.words.length;
    score.details.wordCount = wordCount;
    
    if (wordCount >= this.options.minWordCount && wordCount <= this.options.maxWordCount) {
      score.score += 25;
    } else if (wordCount < this.options.minWordCount) {
      score.score += Math.max(0, (wordCount / this.options.minWordCount) * 25);
      score.issues.push(`Content too short (${wordCount} words). Aim for at least ${this.options.minWordCount} words.`);
    } else {
      score.score += 20; // Slightly lower for very long content
      score.issues.push(`Content very long (${wordCount} words). Consider breaking into smaller sections.`);
    }

    // Unique words ratio
    const uniqueWords = [...new Set(this.words)];
    const uniqueRatio = uniqueWords.length / this.words.length;
    score.details.uniqueWordsRatio = uniqueRatio;
    
    if (uniqueRatio > 0.5) {
      score.score += 20;
    } else {
      score.score += uniqueRatio * 40;
      score.issues.push('Low vocabulary diversity. Consider using more varied language.');
    }

    // Sentence structure variety
    const avgSentenceLength = this.words.length / this.sentences.length;
    score.details.avgSentenceLength = avgSentenceLength;
    
    if (avgSentenceLength >= 10 && avgSentenceLength <= 20) {
      score.score += 15;
    } else {
      score.score += Math.max(0, 15 - Math.abs(avgSentenceLength - 15));
      if (avgSentenceLength > 25) {
        score.issues.push('Sentences too long. Break complex sentences for better readability.');
      } else if (avgSentenceLength < 8) {
        score.issues.push('Sentences too short. Combine related ideas for better flow.');
      }
    }

    // Content depth indicators
    const lists = this.$('ul, ol').length;
    const subheadings = this.$('h2, h3, h4, h5, h6').length;
    
    if (lists > 0 && subheadings > 0) {
      score.score += 15;
    } else {
      score.score += Math.max(0, (lists + subheadings) * 3);
      score.issues.push('Add more lists and subheadings to improve content structure.');
    }

    return {
      score: Math.min(100, score.score),
      details: score.details,
      issues: score.issues
    };
  }

  // Content structure scoring
  scoreContentStructure() {
    const score = { score: 0, details: {}, issues: [] };
    
    // Heading hierarchy
    const headings = {
      h1: this.$('h1').length,
      h2: this.$('h2').length,
      h3: this.$('h3').length,
      h4: this.$('h4').length,
      h5: this.$('h5').length,
      h6: this.$('h6').length
    };
    
    score.details.headings = headings;
    
    // Check for single H1
    if (headings.h1 === 1) {
      score.score += 25;
    } else if (headings.h1 === 0) {
      score.issues.push('Missing H1 tag. Add a primary heading.');
    } else {
      score.issues.push('Multiple H1 tags found. Use only one H1 per page.');
    }

    // Check for subheadings
    const totalSubheadings = headings.h2 + headings.h3 + headings.h4;
    if (totalSubheadings >= 2) {
      score.score += 20;
    } else {
      score.score += totalSubheadings * 10;
      score.issues.push('Add more subheadings (H2, H3) to improve content structure.');
    }

    // Paragraph structure
    const paragraphs = this.$('p');
    let longParagraphs = 0;
    
    paragraphs.each((i, elem) => {
      const pText = this.$(elem).text();
      const pWords = pText.split(/\s+/).filter(word => word.length > 0);
      if (pWords.length > 100) {
        longParagraphs++;
      }
    });

    if (longParagraphs === 0) {
      score.score += 20;
    } else {
      score.score += Math.max(0, 20 - longParagraphs * 5);
      score.issues.push(`${longParagraphs} paragraph(s) are too long. Break them into smaller chunks.`);
    }

    // Lists and formatting
    const lists = this.$('ul, ol').length;
    const emphasis = this.$('strong, b, em, i').length;
    
    if (lists > 0) score.score += 15;
    if (emphasis > 0) score.score += 10;
    
    if (lists === 0) {
      score.issues.push('Add bullet points or numbered lists for better scannability.');
    }

    return {
      score: Math.min(100, score.score),
      details: score.details,
      issues: score.issues
    };
  }

  // Readability scoring
  scoreReadability() {
    const score = { score: 0, details: {}, issues: [] };
    
    // Flesch Reading Ease
    const fleschScore = this.calculateFleschScore();
    score.details.fleschScore = fleschScore;
    
    if (fleschScore >= 60) {
      score.score += 30;
    } else if (fleschScore >= 30) {
      score.score += 20;
      score.issues.push('Content readability could be improved. Simplify complex sentences.');
    } else {
      score.score += 10;
      score.issues.push('Content is difficult to read. Use simpler language and shorter sentences.');
    }

    // Average words per sentence
    const avgWordsPerSentence = this.words.length / this.sentences.length;
    if (avgWordsPerSentence <= 20) {
      score.score += 25;
    } else {
      score.score += Math.max(0, 25 - (avgWordsPerSentence - 20));
      score.issues.push('Sentences are too long. Aim for under 20 words per sentence.');
    }

    // Passive voice detection (simplified)
    const passiveCount = this.countPassiveVoice();
    const passiveRatio = passiveCount / this.sentences.length;
    
    if (passiveRatio < 0.1) {
      score.score += 20;
    } else {
      score.score += Math.max(0, 20 - passiveRatio * 100);
      score.issues.push('Reduce passive voice usage. Use active voice for clearer communication.');
    }

    // Transition words
    const transitionWords = this.countTransitionWords();
    if (transitionWords > this.sentences.length * 0.1) {
      score.score += 15;
    } else {
      score.issues.push('Add transition words to improve content flow.');
    }

    return {
      score: Math.min(100, score.score),
      details: score.details,
      issues: score.issues
    };
  }

  // Technical SEO scoring
  scoreTechnicalSEO() {
    const score = { score: 0, details: {}, issues: [] };
    
    // Meta description check
    const metaDescription = this.$('meta[name="description"]').attr('content');
    if (metaDescription) {
      const metaLength = metaDescription.length;
      if (metaLength >= 120 && metaLength <= 160) {
        score.score += 20;
      } else {
        score.score += 10;
        score.issues.push(`Meta description length (${metaLength}) should be 120-160 characters.`);
      }
    } else {
      score.issues.push('Missing meta description.');
    }

    // Title tag check
    const title = this.$('title').text();
    if (title) {
      const titleLength = title.length;
      if (titleLength >= 30 && titleLength <= 60) {
        score.score += 20;
      } else {
        score.score += 10;
        score.issues.push(`Title length (${titleLength}) should be 30-60 characters.`);
      }
    } else {
      score.issues.push('Missing title tag.');
    }

    // Image alt text
    const images = this.$('img');
    let imagesWithAlt = 0;
    
    images.each((i, elem) => {
      if (this.$(elem).attr('alt')) {
        imagesWithAlt++;
      }
    });

    if (images.length === 0) {
      score.score += 15; // No images is okay
    } else if (imagesWithAlt === images.length) {
      score.score += 15;
    } else {
      score.score += (imagesWithAlt / images.length) * 15;
      score.issues.push(`${images.length - imagesWithAlt} image(s) missing alt text.`);
    }

    // Internal and external links
    const internalLinks = this.$('a[href^="/"], a[href^="#"]').length;
    const externalLinks = this.$('a[href^="http"]').length;
    
    if (internalLinks > 0) score.score += 10;
    if (externalLinks > 0) score.score += 10;
    
    if (internalLinks === 0) {
      score.issues.push('Add internal links to related content.');
    }

    // Schema markup check (simplified)
    const schemaScript = this.$('script[type="application/ld+json"]');
    if (schemaScript.length > 0) {
      score.score += 15;
    } else {
      score.issues.push('Consider adding schema markup for better search visibility.');
    }

    return {
      score: Math.min(100, score.score),
      details: score.details,
      issues: score.issues
    };
  }

  // Helper methods
  getWords() {
    return this.plainText
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  getSentences() {
    return this.plainText
      .split(/[.!?]+/)
      .filter(sentence => sentence.trim().length > 0);
  }

  countKeywordOccurrences() {
    const regex = new RegExp(this.targetKeyword.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'gi');
    return (this.plainText.match(regex) || []).length;
  }

  scoreLSIKeywords() {
    const lsiKeywords = this.findLSIKeywords();
    return Math.min(20, lsiKeywords.length * 3);
  }

  findLSIKeywords() {
    // Simplified LSI keyword detection
    const keywordParts = this.targetKeyword.split(' ');
    const relatedTerms = [];
    
    // Find terms that appear frequently with keyword parts
    keywordParts.forEach(part => {
      const regex = new RegExp(`\\b\\w*${part}\\w*\\b`, 'gi');
      const matches = this.plainText.match(regex) || [];
      relatedTerms.push(...matches.filter(match => 
        match.toLowerCase() !== part && match.length > 3
      ));
    });

    return [...new Set(relatedTerms)].slice(0, 10);
  }

  calculateFleschScore() {
    const avgSentenceLength = this.words.length / this.sentences.length;
    const avgSyllables = this.calculateAverageSyllables();
    
    return 206.835 - (1.015 * avgSentenceLength) - (84.6 * avgSyllables);
  }

  calculateAverageSyllables() {
    const syllableCount = this.words.reduce((total, word) => {
      return total + this.countSyllables(word);
    }, 0);
    
    return syllableCount / this.words.length;
  }

  countSyllables(word) {
    // Simplified syllable counting
    const vowels = 'aeiouy';
    let count = 0;
    let previousWasVowel = false;
    
    for (let i = 0; i < word.length; i++) {
      const isVowel = vowels.includes(word[i].toLowerCase());
      if (isVowel && !previousWasVowel) {
        count++;
      }
      previousWasVowel = isVowel;
    }
    
    // Handle silent e
    if (word.endsWith('e') && count > 1) {
      count--;
    }
    
    return Math.max(1, count);
  }

  countPassiveVoice() {
    // Simplified passive voice detection
    const passiveIndicators = ['was', 'were', 'been', 'being', 'be'];
    let count = 0;
    
    this.sentences.forEach(sentence => {
      const words = sentence.toLowerCase().split(/\s+/);
      const hasPassiveIndicator = passiveIndicators.some(indicator => 
        words.includes(indicator)
      );
      
      if (hasPassiveIndicator) {
        count++;
      }
    });
    
    return count;
  }

  countTransitionWords() {
    const transitionWords = [
      'however', 'moreover', 'furthermore', 'additionally', 'consequently',
      'therefore', 'meanwhile', 'nevertheless', 'nonetheless', 'similarly',
      'likewise', 'conversely', 'on the other hand', 'in contrast',
      'for example', 'for instance', 'specifically', 'in particular'
    ];
    
    let count = 0;
    const text = this.plainText.toLowerCase();
    
    transitionWords.forEach(word => {
      const regex = new RegExp(`\\b${word}\\b`, 'g');
      const matches = text.match(regex);
      if (matches) count += matches.length;
    });
    
    return count;
  }

  getContentMetrics() {
    return {
      wordCount: this.words.length,
      sentenceCount: this.sentences.length,
      paragraphCount: this.$('p').length,
      headingCount: this.$('h1, h2, h3, h4, h5, h6').length,
      imageCount: this.$('img').length,
      linkCount: this.$('a').length,
      keywordDensity: (this.countKeywordOccurrences() / this.words.length) * 100,
      avgWordsPerSentence: this.words.length / this.sentences.length,
      fleschScore: this.calculateFleschScore()
    };
  }

  generateRecommendations(scores) {
    const recommendations = [];
    
    // Collect all issues
    Object.values(scores).forEach(category => {
      if (category.issues) {
        recommendations.push(...category.issues);
      }
    });

    // Add priority recommendations based on scores
    if (scores.keyword.score < 60) {
      recommendations.unshift('Priority: Improve keyword optimization');
    }
    
    if (scores.structure.score < 60) {
      recommendations.unshift('Priority: Improve content structure with better headings');
    }
    
    if (scores.content.score < 60) {
      recommendations.unshift('Priority: Increase content quality and depth');
    }

    return recommendations.slice(0, 10); // Limit to top 10 recommendations
  }
}

module.exports = SEOScorer;
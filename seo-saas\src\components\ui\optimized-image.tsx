// Professional Optimized Image Component
// Enterprise-grade image loading with progressive enhancement

'use client'

import Image from 'next/image'
import { useState } from 'react'
import { cn } from '@/lib/utils'

interface OptimizedImageProps {
  src: string
  alt: string
  width?: number
  height?: number
  fill?: boolean
  className?: string
  priority?: boolean
  quality?: number
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  sizes?: string
  onLoadingComplete?: () => void
}

export function OptimizedImage({
  src,
  alt,
  width,
  height,
  fill = false,
  className,
  priority = false,
  quality = 85,
  placeholder = 'empty',
  blurDataURL,
  sizes,
  onLoadingComplete,
}: OptimizedImageProps) {
  const [isLoading, setIsLoading] = useState(true)
  const [hasError, setHasError] = useState(false)

  const handleLoad = () => {
    setIsLoading(false)
    onLoadingComplete?.()
  }

  const handleError = () => {
    setIsLoading(false)
    setHasError(true)
  }

  if (hasError) {
    return (
      <div 
        className={cn(
          "flex items-center justify-center bg-gray-100 text-gray-400",
          className
        )}
        style={{ width, height }}
      >
        <svg
          className="w-8 h-8"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth={2}
            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
          />
        </svg>
      </div>
    )
  }

  return (
    <div className={cn("relative overflow-hidden", className)}>
      {isLoading && (
        <div className="absolute inset-0 bg-gradient-to-r from-gray-200 via-gray-300 to-gray-200 bg-[length:200%_100%] animate-pulse" />
      )}
      
      <Image
        src={src}
        alt={alt}
        width={width}
        height={height}
        fill={fill}
        priority={priority}
        quality={quality}
        placeholder={placeholder}
        blurDataURL={blurDataURL}
        sizes={sizes}
        className={cn(
          "transition-opacity duration-300",
          isLoading ? "opacity-0" : "opacity-100",
          className
        )}
        onLoad={handleLoad}
        onError={handleError}
        style={{
          objectFit: 'cover',
        }}
      />
    </div>
  )
}

// Hero Image Component
export function HeroImage({ className, ...props }: OptimizedImageProps) {
  return (
    <OptimizedImage
      {...props}
      className={cn("w-full h-auto", className)}
      priority={true}
      quality={90}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
    />
  )
}

// Avatar Image Component
export function AvatarImage({ className, ...props }: OptimizedImageProps) {
  return (
    <OptimizedImage
      {...props}
      className={cn("rounded-full", className)}
      quality={75}
      sizes="(max-width: 768px) 64px, 96px"
    />
  )
}

// Logo Image Component
export function LogoImage({ className, ...props }: OptimizedImageProps) {
  return (
    <OptimizedImage
      {...props}
      className={cn("w-auto h-auto", className)}
      priority={true}
      quality={95}
      sizes="(max-width: 768px) 120px, 160px"
    />
  )
}

// Card Image Component
export function CardImage({ className, ...props }: OptimizedImageProps) {
  return (
    <OptimizedImage
      {...props}
      className={cn("w-full h-48 object-cover", className)}
      quality={80}
      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
    />
  )
}

// Background Image Component with overlay support
interface BackgroundImageProps extends OptimizedImageProps {
  overlay?: boolean
  overlayColor?: string
  overlayOpacity?: number
  children?: React.ReactNode
}

export function BackgroundImage({
  children,
  overlay = false,
  overlayColor = 'black',
  overlayOpacity = 0.5,
  className,
  ...props
}: BackgroundImageProps) {
  return (
    <div className={cn("relative", className)}>
      <OptimizedImage
        {...props}
        fill
        className="absolute inset-0 z-0 object-cover"
      />
      
      {overlay && (
        <div
          className="absolute inset-0 z-10"
          style={{
            backgroundColor: overlayColor,
            opacity: overlayOpacity,
          }}
        />
      )}
      
      {children && (
        <div className="relative z-20">
          {children}
        </div>
      )}
    </div>
  )
}
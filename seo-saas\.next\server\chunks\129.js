exports.id=129,exports.ids=[129],exports.modules={8359:()=>{},3739:()=>{},8775:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},4212:(e,t,r)=>{Promise.resolve().then(r.bind(r,6954)),Promise.resolve().then(r.bind(r,1596)),Promise.resolve().then(r.bind(r,3750))},6954:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ClientOnly:()=>i,useClientOnly:()=>a});var n=r(2295),s=r(3729);function i({children:e,fallback:t=null}){let[r,i]=(0,s.useState)(!1);return((0,s.useEffect)(()=>{i(!0)},[]),r)?n.jsx(n.Fragment,{children:e}):n.jsx(n.Fragment,{children:t})}function a(){let[e,t]=(0,s.useState)(!1);return(0,s.useEffect)(()=>{t(!0)},[]),e}},1596:(e,t,r)=>{"use strict";r.r(t),r.d(t,{ErrorBoundary:()=>a});var n=r(2295),s=r(3729),i=r.n(s);class a extends i().Component{constructor(e){super(e),this.state={hasError:!1}}static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error Boundary caught an error:",e,t)}render(){if(this.state.hasError){let e=this.props.fallback||o;return n.jsx(e,{error:this.state.error,reset:()=>this.setState({hasError:!1,error:void 0})})}return this.props.children}}function o({error:e,reset:t}){return n.jsx("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100",children:(0,n.jsxs)("div",{className:"max-w-md w-full glass rounded-2xl shadow-xl p-8",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[n.jsx("div",{className:"w-16 h-16 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:n.jsx("svg",{className:"h-8 w-8 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:n.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"})})}),n.jsx("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Something went wrong"}),n.jsx("p",{className:"text-gray-600",children:"We're sorry for the inconvenience. Please try again."})]}),!1,(0,n.jsxs)("div",{className:"space-y-3",children:[n.jsx("button",{onClick:t,className:"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-all shadow-lg hover:shadow-xl",children:"Try Again"}),n.jsx("button",{onClick:()=>window.location.reload(),className:"w-full bg-white border border-gray-300 hover:border-blue-500 text-gray-700 hover:text-blue-600 px-6 py-3 rounded-lg font-medium transition-all",children:"Reload Page"})]}),n.jsx("div",{className:"mt-6 text-center",children:n.jsx("a",{href:"/",className:"inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors",children:"← Return to Home"})})]})})}},3750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AuthProvider:()=>o,useAuth:()=>l,useAuthGuard:()=>u,useSubscription:()=>c});var n=r(2295),s=r(3729),i=r(9299);let a=(0,s.createContext)(void 0);function o({children:e}){let[t,r]=(0,s.useState)(null),[o,l]=(0,s.useState)(null),[c,u]=(0,s.useState)(null),[d,h]=(0,s.useState)(null),[p,m]=(0,s.useState)(!0),[f,g]=(0,s.useState)(null),[S,b]=(0,s.useState)(!1),[x,w]=(0,s.useState)(null);(0,s.useEffect)(()=>{try{let e=(0,i.createSupabaseComponentClient)();w(e),b(!0)}catch(e){console.error("Failed to initialize Supabase client:",e),g("Failed to initialize authentication. Please check your configuration."),m(!1),b(!0)}},[]);let y=async e=>{if(!x)return console.error("Supabase client not initialized"),null;try{let{data:t,error:r}=await x.from("profiles").select("*").eq("id",e).single();if(r)return console.error("Error fetching profile:",r),null;return t}catch(e){return console.error("Error fetching profile:",e),null}},A=async e=>{if(!x)return console.error("Supabase client not initialized"),null;try{let{data:t,error:r}=await x.from("user_subscriptions").select("*").eq("user_id",e).single();if(r&&"PGRST116"!==r.code)return console.error("Error fetching subscription:",r),null;return t}catch(e){return console.error("Error fetching subscription:",e),null}};(0,s.useEffect)(()=>{if(!x)return;(async()=>{try{let{data:{session:e},error:t}=await x.auth.getSession();if(t){console.error("Error getting session:",t),m(!1);return}if(e?.user){l(e),r(e.user);let[t,n]=await Promise.all([y(e.user.id),A(e.user.id)]);u(t),h(n)}}catch(e){console.error("Error initializing auth:",e)}finally{m(!1)}})();let{data:{subscription:e}}=x.auth.onAuthStateChange(async(e,t)=>{if(console.log("Auth state changed:",e,t?.user?.id),l(t),r(t?.user??null),t?.user){let[e,r]=await Promise.all([y(t.user.id),A(t.user.id)]);u(e),h(r)}else u(null),h(null);m(!1)});return()=>{e.unsubscribe()}},[x]);let E=async e=>{if(!x)throw Error("Authentication not initialized");if(!t)throw Error("No user logged in");let{data:r,error:n}=await x.from("profiles").update({...e,updated_at:new Date().toISOString()}).eq("id",t.id).select().single();if(n)throw Error(n.message);u(r)},P=async()=>{t&&u(await y(t.id))},v=async()=>{t&&h(await A(t.id))},C={user:t,session:o,profile:c,subscription:d,loading:p,signIn:async(e,t)=>{if(!x)throw Error("Authentication not initialized");let{error:r}=await x.auth.signInWithPassword({email:e,password:t});if(r)throw Error(r.message)},signUp:async(e,t,r)=>{if(!x)throw Error("Authentication not initialized");let{error:n}=await x.auth.signUp({email:e,password:t,options:{data:{full_name:r}}});if(n)throw Error(n.message)},signInWithGoogle:async()=>{if(!x)throw Error("Authentication not initialized");let{error:e}=await x.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`,queryParams:{access_type:"offline",prompt:"consent"}}});if(e)throw Error(e.message)},signInWithGitHub:async()=>{if(!x)throw Error("Authentication not initialized");let{error:e}=await x.auth.signInWithOAuth({provider:"github",options:{redirectTo:`${window.location.origin}/auth/callback`}});if(e)throw Error(e.message)},signOut:async()=>{if(!x)throw Error("Authentication not initialized");let{error:e}=await x.auth.signOut();if(e)throw Error(e.message)},resetPassword:async e=>{if(!x)throw Error("Authentication not initialized");let{error:t}=await x.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/auth/reset-password`});if(t)throw Error(t.message)},updateProfile:E,refreshProfile:P,refreshSubscription:v};return S?n.jsx(a.Provider,{value:C,children:e}):n.jsx(a.Provider,{value:C,children:n.jsx("div",{className:"min-h-screen flex items-center justify-center",children:n.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})})}function l(){let e=(0,s.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(){let{subscription:e}=l(),t=e?.status==="active",r=e?.status==="trialing",n=e?.status==="past_due",s=e?.status==="cancelled";return{subscription:e,hasActiveSubscription:t,isOnTrial:r,isPastDue:n,isCancelled:s,canAccessFeature:r=>!!e&&("pro"===r?["pro","enterprise"].includes(e.plan_type)&&t:"enterprise"===r&&"enterprise"===e.plan_type&&t)}}function u(){let{user:e,loading:t}=l();return{isAuthenticated:!!e,isLoading:t,user:e}}},9299:(e,t,r)=>{"use strict";r.d(t,{createSupabaseComponentClient:()=>u,signIn:()=>d,signInWithGitHub:()=>m,signInWithGoogle:()=>p,signUp:()=>h,OQ:()=>c});var n=r(6300),s=r(7435);let i={supabaseUrl:"https://xpcbyzcaidfukddqniny.supabase.co",supabaseKey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A",supabaseServiceKey:process.env.SUPABASE_SERVICE_ROLE_KEY||"",appUrl:"http://localhost:3000",isServer:!0},a=function(){let e={NEXT_PUBLIC_SUPABASE_URL:"https://xpcbyzcaidfukddqniny.supabase.co",NEXT_PUBLIC_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A"},t=[],r=[];if(Object.entries(e).forEach(([e,r])=>{r&&""!==r.trim()||t.push(e)}),e.NEXT_PUBLIC_SUPABASE_URL)try{new URL(e.NEXT_PUBLIC_SUPABASE_URL)}catch{r.push("NEXT_PUBLIC_SUPABASE_URL is not a valid URL")}if(e.NEXT_PUBLIC_SUPABASE_ANON_KEY){let t=e.NEXT_PUBLIC_SUPABASE_ANON_KEY;t.startsWith("eyJ")&&3===t.split(".").length||r.push("NEXT_PUBLIC_SUPABASE_ANON_KEY appears to be invalid (not a JWT token)")}return{isValid:0===t.length&&0===r.length,missingVars:t,errors:r,vars:e}}(),o=(e,t)=>{};a.isValid||(["Supabase configuration error:",...a.missingVars.map(e=>`- Missing: ${e}`),...a.errors.map(e=>`- Error: ${e}`)].join("\n"),o("Environment validation failed",{validation:a}));let l=null;try{i.supabaseUrl&&i.supabaseKey?l=(0,n.eI)(i.supabaseUrl,i.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):o("Cannot create Supabase client: missing URL or key")}catch(e){o("Failed to create Supabase client",e)}try{i.supabaseUrl&&i.supabaseServiceKey&&(0,n.eI)(i.supabaseUrl,i.supabaseServiceKey,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}})}catch(e){o("Failed to create Supabase admin client",e)}let c=l,u=()=>{try{return(0,s.createClientComponentClient)()}catch(e){throw o("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function d(e,t){let r=u(),{data:n,error:s}=await r.auth.signInWithPassword({email:e,password:t});if(s)throw Error(s.message);return n}async function h(e,t,r){let n=u(),{data:s,error:i}=await n.auth.signUp({email:e,password:t,options:{data:{full_name:r},emailRedirectTo:"http://localhost:3000/auth/callback"}});if(i)throw Error(i.message);return s}async function p(){let e=u(),{data:t,error:r}=await e.auth.signInWithOAuth({provider:"google",options:{redirectTo:"http://localhost:3000/auth/callback",queryParams:{access_type:"offline",prompt:"consent"}}});if(r)throw Error(r.message);return t}async function m(){let e=u(),{data:t,error:r}=await e.auth.signInWithOAuth({provider:"github",options:{redirectTo:"http://localhost:3000/auth/callback"}});if(r)throw Error(r.message);return t}},6343:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>w,metadata:()=>x});var n=r(5036),s=r(9529),i=r.n(s);r(5023);var a=r(6843);let o=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx`),{__esModule:l,$$typeof:c}=o;o.default;let u=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#AuthProvider`);(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useAuth`),(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useSubscription`),(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useAuthGuard`);let d=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/components/error-boundary.tsx`),{__esModule:h,$$typeof:p}=d;d.default;let m=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/components/error-boundary.tsx#ErrorBoundary`),f=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/components/client-only.tsx`),{__esModule:g,$$typeof:S}=f;f.default;let b=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/components/client-only.tsx#ClientOnly`);(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/components/client-only.tsx#useClientOnly`);let x={title:"RankBoost AI - SEO Content Generation Platform",description:"Create high-ranking, SEO-optimized content with advanced AI technology",viewport:"width=device-width, initial-scale=1",icons:{icon:"/favicon.ico"}};function w({children:e}){return(0,n.jsxs)("html",{lang:"en",className:i().variable,children:[(0,n.jsxs)("head",{children:[n.jsx("meta",{charSet:"utf-8"}),n.jsx("meta",{name:"viewport",content:"width=device-width, initial-scale=1"}),n.jsx("meta",{name:"theme-color",content:"#3b82f6"}),n.jsx("link",{rel:"preconnect",href:"https://fonts.googleapis.com"}),n.jsx("link",{rel:"preconnect",href:"https://fonts.gstatic.com",crossOrigin:"anonymous"})]}),n.jsx("body",{className:`${i().className} antialiased`,children:n.jsx(m,{children:n.jsx(b,{children:n.jsx(u,{children:e})})})})]})}},5023:()=>{}};
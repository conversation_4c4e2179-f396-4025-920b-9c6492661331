// API Status and Health Check Route
// Comprehensive monitoring endpoint for all services

import { NextRequest, NextResponse } from 'next/server';
import { groqService } from '@/lib/services/groq';
import { serperService } from '@/lib/services/serper';
import { api<PERSON>rror<PERSON><PERSON><PERSON> } from '@/lib/error-handler';
import { createSupabaseServer } from '@/lib/supabase';
import { config } from '@/lib/config';

interface ServiceStatus {
  name: string;
  status: 'healthy' | 'unhealthy' | 'degraded';
  latency: number;
  details: any;
  lastCheck: string;
}

interface SystemHealth {
  overall: 'healthy' | 'unhealthy' | 'degraded';
  services: ServiceStatus[];
  timestamp: string;
  version: string;
  uptime: number;
  environment: string;
}

const startTime = Date.now();

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url);
  const detailed = searchParams.get('detailed') === 'true';
  const service = searchParams.get('service');

  try {
    // If specific service requested
    if (service) {
      return await getServiceStatus(service);
    }

    // Get comprehensive system health
    const systemHealth = await getSystemHealth(detailed);

    return NextResponse.json({
      success: true,
      data: systemHealth,
    }, {
      status: systemHealth.overall === 'healthy' ? 200 : 503,
      headers: {
        'Cache-Control': 'no-cache, no-store, must-revalidate',
        'X-Health-Check': systemHealth.overall,
        'X-Timestamp': systemHealth.timestamp,
      },
    });

  } catch (error) {
    console.error('Health check error:', error);
    
    return NextResponse.json({
      success: false,
      error: 'Health check failed',
      timestamp: new Date().toISOString(),
    }, { status: 500 });
  }
}

async function getSystemHealth(detailed: boolean = false): Promise<SystemHealth> {
  const services: ServiceStatus[] = [];
  const timestamp = new Date().toISOString();
  const uptime = Date.now() - startTime;

  // Check Groq service
  try {
    const groqStart = Date.now();
    const groqHealth = await groqService.healthCheck();
    const groqLatency = Date.now() - groqStart;

    services.push({
      name: 'groq',
      status: groqHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
      latency: groqLatency,
      details: detailed ? {
        model: groqHealth.model,
        apiLatency: groqHealth.latency,
        availableModels: Object.keys(groqService.getAvailableModels()).length,
      } : {},
      lastCheck: timestamp,
    });
  } catch (error) {
    services.push({
      name: 'groq',
      status: 'unhealthy',
      latency: -1,
      details: detailed ? { error: error instanceof Error ? error.message : 'Unknown error' } : {},
      lastCheck: timestamp,
    });
  }

  // Check Serper service
  try {
    const serperStart = Date.now();
    const serperHealth = await serperService.healthCheck();
    const serperLatency = Date.now() - serperStart;

    services.push({
      name: 'serper',
      status: serperHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
      latency: serperLatency,
      details: detailed ? {
        apiLatency: serperHealth.latency,
      } : {},
      lastCheck: timestamp,
    });
  } catch (error) {
    services.push({
      name: 'serper',
      status: 'unhealthy',
      latency: -1,
      details: detailed ? { error: error instanceof Error ? error.message : 'Unknown error' } : {},
      lastCheck: timestamp,
    });
  }

  // Check Supabase service
  try {
    const supabaseStart = Date.now();
    const supabase = createSupabaseServer();
    
    // Simple query to test connection
    const { data, error } = await supabase
      .from('profiles')
      .select('count')
      .limit(1);
    
    const supabaseLatency = Date.now() - supabaseStart;

    services.push({
      name: 'supabase',
      status: error ? 'unhealthy' : 'healthy',
      latency: supabaseLatency,
      details: detailed ? {
        url: config.supabase.url,
        connected: !error,
        error: error?.message,
      } : {},
      lastCheck: timestamp,
    });
  } catch (error) {
    services.push({
      name: 'supabase',
      status: 'unhealthy',
      latency: -1,
      details: detailed ? { error: error instanceof Error ? error.message : 'Unknown error' } : {},
      lastCheck: timestamp,
    });
  }

  // Check Error Handler and Circuit Breakers
  if (detailed) {
    const errorHandlerHealth = apiErrorHandler.getServiceHealth();
    
    services.push({
      name: 'error-handler',
      status: 'healthy', // Error handler is always available
      latency: 0,
      details: {
        circuitBreakers: errorHandlerHealth,
        cacheSize: Object.keys(errorHandlerHealth).length,
      },
      lastCheck: timestamp,
    });
  }

  // Determine overall health
  const healthyServices = services.filter(s => s.status === 'healthy').length;
  const totalServices = services.length;
  
  let overall: 'healthy' | 'unhealthy' | 'degraded';
  if (healthyServices === totalServices) {
    overall = 'healthy';
  } else if (healthyServices >= totalServices * 0.5) {
    overall = 'degraded';
  } else {
    overall = 'unhealthy';
  }

  return {
    overall,
    services,
    timestamp,
    version: '1.0.0',
    uptime,
    environment: config.app.environment,
  };
}

async function getServiceStatus(serviceName: string): Promise<NextResponse> {
  const timestamp = new Date().toISOString();

  try {
    let serviceStatus: ServiceStatus;

    switch (serviceName.toLowerCase()) {
      case 'groq':
        const groqStart = Date.now();
        const groqHealth = await groqService.healthCheck();
        const groqLatency = Date.now() - groqStart;

        serviceStatus = {
          name: 'groq',
          status: groqHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
          latency: groqLatency,
          details: {
            model: groqHealth.model,
            apiLatency: groqHealth.latency,
            availableModels: groqService.getAvailableModels(),
            config: {
              baseUrl: config.apis.groq.baseUrl,
              model: config.apis.groq.model,
              rateLimitPerMinute: config.apis.groq.rateLimitPerMinute,
            },
          },
          lastCheck: timestamp,
        };
        break;

      case 'serper':
        const serperStart = Date.now();
        const serperHealth = await serperService.healthCheck();
        const serperLatency = Date.now() - serperStart;

        serviceStatus = {
          name: 'serper',
          status: serperHealth.status === 'healthy' ? 'healthy' : 'unhealthy',
          latency: serperLatency,
          details: {
            apiLatency: serperHealth.latency,
            config: {
              baseUrl: config.apis.serper.baseUrl,
              rateLimitPerMinute: config.apis.serper.rateLimitPerMinute,
            },
          },
          lastCheck: timestamp,
        };
        break;

      case 'supabase':
        const supabaseStart = Date.now();
        const supabase = createSupabaseServer();
        
        const { data, error } = await supabase
          .from('profiles')
          .select('count')
          .limit(1);
        
        const supabaseLatency = Date.now() - supabaseStart;

        serviceStatus = {
          name: 'supabase',
          status: error ? 'unhealthy' : 'healthy',
          latency: supabaseLatency,
          details: {
            url: config.supabase.url,
            connected: !error,
            error: error?.message,
            version: 'PostgreSQL via Supabase',
          },
          lastCheck: timestamp,
        };
        break;

      case 'error-handler':
        const errorHandlerHealth = apiErrorHandler.getServiceHealth();
        
        serviceStatus = {
          name: 'error-handler',
          status: 'healthy',
          latency: 0,
          details: {
            circuitBreakers: errorHandlerHealth,
            cacheSize: Object.keys(errorHandlerHealth).length,
            config: {
              circuitBreakerThreshold: 5,
              circuitBreakerTimeout: 60000,
              resetTimeout: 300000,
            },
          },
          lastCheck: timestamp,
        };
        break;

      default:
        return NextResponse.json({
          success: false,
          error: `Unknown service: ${serviceName}`,
          availableServices: ['groq', 'serper', 'supabase', 'error-handler'],
        }, { status: 404 });
    }

    return NextResponse.json({
      success: true,
      data: serviceStatus,
    }, {
      status: serviceStatus.status === 'healthy' ? 200 : 503,
      headers: {
        'X-Service-Status': serviceStatus.status,
        'X-Service-Latency': serviceStatus.latency.toString(),
      },
    });

  } catch (error) {
    console.error(`Service status check failed for ${serviceName}:`, error);
    
    return NextResponse.json({
      success: false,
      error: `Failed to check ${serviceName} status`,
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp,
    }, { status: 500 });
  }
}

// POST endpoint for manual health checks or service restarts
export async function POST(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');
    const service = searchParams.get('service');

    // Simple admin check (in production, this would be more sophisticated)
    const adminKey = request.headers.get('x-admin-key');
    if (adminKey !== process.env.ADMIN_KEY) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    switch (action) {
      case 'reset-circuit-breakers':
        if (service) {
          // Reset specific service circuit breaker
          const errorHandler = apiErrorHandler;
          // In a real implementation, we'd have a method to reset specific circuit breakers
          return NextResponse.json({
            success: true,
            message: `Circuit breaker reset requested for ${service}`,
            timestamp: new Date().toISOString(),
          });
        } else {
          // Reset all circuit breakers
          return NextResponse.json({
            success: true,
            message: 'All circuit breakers reset requested',
            timestamp: new Date().toISOString(),
          });
        }

      case 'clear-cache':
        // Clear error handler cache
        apiErrorHandler.cleanupCache();
        return NextResponse.json({
          success: true,
          message: 'Cache cleared successfully',
          timestamp: new Date().toISOString(),
        });

      case 'force-health-check':
        const healthCheck = await getSystemHealth(true);
        return NextResponse.json({
          success: true,
          message: 'Forced health check completed',
          data: healthCheck,
        });

      default:
        return NextResponse.json({
          success: false,
          error: 'Unknown action',
          availableActions: ['reset-circuit-breakers', 'clear-cache', 'force-health-check'],
        }, { status: 400 });
    }

  } catch (error) {
    console.error('POST /api/status error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
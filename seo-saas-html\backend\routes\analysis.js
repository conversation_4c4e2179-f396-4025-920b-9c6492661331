const express = require('express');
const router = express.Router();
const axios = require('axios');
const cheerio = require('cheerio');
const natural = require('natural');
const stopword = require('stopword');
const { verifyToken, checkUsageLimit } = require('../middleware/auth');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Competitor analysis endpoint
router.post('/competitors', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const { keyword, location = 'United States', searchEngine = 'google' } = req.body;

    if (!keyword) {
      return res.status(400).json({ error: 'Keyword is required' });
    }

    // Call Serper API to get top 5 results
    const serperResponse = await axios.post(
      'https://google.serper.dev/search',
      {
        q: keyword,
        location,
        gl: location === 'United States' ? 'us' : location.toLowerCase().substring(0, 2),
        num: 5
      },
      {
        headers: {
          'X-API-KEY': process.env.SERPER_API_KEY,
          'Content-Type': 'application/json'
        }
      }
    );

    const searchResults = serperResponse.data.organic || [];
    
    // Analyze each competitor
    const competitorAnalysis = await Promise.all(
      searchResults.map(async (result) => {
        try {
          const analysis = await analyzeCompetitorPage(result.link);
          return {
            url: result.link,
            title: result.title,
            snippet: result.snippet,
            position: result.position,
            analysis
          };
        } catch (error) {
          console.error(`Error analyzing ${result.link}:`, error);
          return {
            url: result.link,
            title: result.title,
            snippet: result.snippet,
            position: result.position,
            analysis: { error: 'Failed to analyze page' }
          };
        }
      })
    );

    // Calculate averages
    const averages = calculateCompetitorAverages(competitorAnalysis);

    // Save analysis
    const { data: savedAnalysis } = await supabase
      .from('seo_analyses')
      .insert({
        user_id: req.user.id,
        keyword,
        analysis_type: 'competitor',
        results: {
          competitors: competitorAnalysis,
          averages,
          searchEngine,
          location
        },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    // Update usage tracking
    await updateUsageTracking(req.user.id, 'analyses');

    res.json({
      keyword,
      location,
      searchEngine,
      competitors: competitorAnalysis,
      averages,
      analysisId: savedAnalysis?.id
    });
  } catch (error) {
    console.error('Competitor analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze competitors' });
  }
});

// Content analysis endpoint
router.post('/content', verifyToken, async (req, res) => {
  try {
    const { content, url, targetKeyword } = req.body;

    if (!content && !url) {
      return res.status(400).json({ error: 'Content or URL is required' });
    }

    let textContent = content;
    
    // If URL provided, fetch content
    if (url && !content) {
      try {
        const response = await axios.get(url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
          }
        });
        const $ = cheerio.load(response.data);
        textContent = $('body').text();
      } catch (error) {
        return res.status(400).json({ error: 'Failed to fetch URL content' });
      }
    }

    // Analyze content
    const analysis = analyzeContent(textContent, targetKeyword);

    // Calculate SEO score
    const seoScore = calculateSEOScore(analysis);

    res.json({
      analysis,
      seoScore,
      recommendations: generateRecommendations(analysis, seoScore)
    });
  } catch (error) {
    console.error('Content analysis error:', error);
    res.status(500).json({ error: 'Failed to analyze content' });
  }
});

// Helper function to analyze competitor page
async function analyzeCompetitorPage(url) {
  try {
    const response = await axios.get(url, {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      },
      timeout: 10000
    });

    const $ = cheerio.load(response.data);
    
    // Remove script and style content
    $('script').remove();
    $('style').remove();
    
    // Extract text content
    const textContent = $('body').text();
    const words = textContent.split(/\s+/).filter(word => word.length > 0);
    
    // Extract headings
    const headings = {
      h1: $('h1').map((i, el) => $(el).text().trim()).get(),
      h2: $('h2').map((i, el) => $(el).text().trim()).get(),
      h3: $('h3').map((i, el) => $(el).text().trim()).get(),
      h4: $('h4').map((i, el) => $(el).text().trim()).get()
    };

    // Extract meta data
    const metaTitle = $('title').text() || '';
    const metaDescription = $('meta[name="description"]').attr('content') || '';

    // Extract images
    const images = $('img').map((i, el) => ({
      src: $(el).attr('src'),
      alt: $(el).attr('alt') || ''
    })).get();

    // Extract keywords and entities
    const { keywords, entities } = extractKeywordsAndEntities(textContent);

    return {
      wordCount: words.length,
      headings,
      headingCount: Object.values(headings).flat().length,
      metaTitle,
      metaDescription,
      images: images.length,
      imagesWithAlt: images.filter(img => img.alt).length,
      keywords,
      entities
    };
  } catch (error) {
    throw new Error(`Failed to analyze page: ${error.message}`);
  }
}

// Helper function to extract keywords and entities
function extractKeywordsAndEntities(text) {
  const tokenizer = new natural.WordTokenizer();
  const tokens = tokenizer.tokenize(text.toLowerCase());
  
  // Remove stopwords
  const filteredTokens = stopword.removeStopwords(tokens);
  
  // Calculate word frequency
  const frequency = {};
  filteredTokens.forEach(token => {
    if (token.length > 3) {
      frequency[token] = (frequency[token] || 0) + 1;
    }
  });
  
  // Sort by frequency
  const keywords = Object.entries(frequency)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 20)
    .map(([word, count]) => ({ word, count }));

  // Basic entity extraction (simplified)
  const entities = [];
  const capitalizedWords = text.match(/\b[A-Z][a-z]+(?:\s+[A-Z][a-z]+)*/g) || [];
  capitalizedWords.forEach(entity => {
    if (entity.length > 3 && !entities.includes(entity)) {
      entities.push(entity);
    }
  });

  return { keywords, entities: entities.slice(0, 10) };
}

// Helper function to calculate averages
function calculateCompetitorAverages(competitors) {
  const validCompetitors = competitors.filter(c => !c.analysis.error);
  
  if (validCompetitors.length === 0) {
    return null;
  }

  const total = validCompetitors.reduce((acc, comp) => {
    const analysis = comp.analysis;
    return {
      wordCount: acc.wordCount + analysis.wordCount,
      headingCount: acc.headingCount + analysis.headingCount,
      h1Count: acc.h1Count + analysis.headings.h1.length,
      h2Count: acc.h2Count + analysis.headings.h2.length,
      h3Count: acc.h3Count + analysis.headings.h3.length,
      images: acc.images + analysis.images
    };
  }, {
    wordCount: 0,
    headingCount: 0,
    h1Count: 0,
    h2Count: 0,
    h3Count: 0,
    images: 0
  });

  const count = validCompetitors.length;
  
  return {
    avgWordCount: Math.round(total.wordCount / count),
    avgHeadingCount: Math.round(total.headingCount / count),
    avgH1Count: Math.round(total.h1Count / count * 10) / 10,
    avgH2Count: Math.round(total.h2Count / count * 10) / 10,
    avgH3Count: Math.round(total.h3Count / count * 10) / 10,
    avgImages: Math.round(total.images / count * 10) / 10,
    topKeywords: extractTopKeywords(validCompetitors),
    commonEntities: extractCommonEntities(validCompetitors)
  };
}

// Helper function to extract top keywords across competitors
function extractTopKeywords(competitors) {
  const allKeywords = {};
  
  competitors.forEach(comp => {
    comp.analysis.keywords?.forEach(({ word, count }) => {
      allKeywords[word] = (allKeywords[word] || 0) + count;
    });
  });

  return Object.entries(allKeywords)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 15)
    .map(([word, totalCount]) => ({ word, totalCount }));
}

// Helper function to extract common entities
function extractCommonEntities(competitors) {
  const entityFrequency = {};
  
  competitors.forEach(comp => {
    comp.analysis.entities?.forEach(entity => {
      entityFrequency[entity] = (entityFrequency[entity] || 0) + 1;
    });
  });

  return Object.entries(entityFrequency)
    .filter(([entity, freq]) => freq >= 2)
    .sort((a, b) => b[1] - a[1])
    .slice(0, 10)
    .map(([entity]) => entity);
}

// Helper function to analyze content
function analyzeContent(content, targetKeyword) {
  const $ = cheerio.load(`<body>${content}</body>`);
  const text = $.text();
  const words = text.split(/\s+/).filter(word => word.length > 0);
  
  // Keyword density
  const keywordRegex = new RegExp(targetKeyword, 'gi');
  const keywordMatches = text.match(keywordRegex) || [];
  const keywordDensity = (keywordMatches.length / words.length) * 100;

  // Headings analysis
  const headings = {
    h1: $('h1').length,
    h2: $('h2').length,
    h3: $('h3').length,
    total: $('h1, h2, h3, h4, h5, h6').length
  };

  // Readability (simplified)
  const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);
  const avgWordsPerSentence = words.length / sentences.length;

  return {
    wordCount: words.length,
    keywordDensity: Math.round(keywordDensity * 100) / 100,
    keywordCount: keywordMatches.length,
    headings,
    sentences: sentences.length,
    avgWordsPerSentence: Math.round(avgWordsPerSentence * 10) / 10,
    paragraphs: $('p').length
  };
}

// Helper function to calculate SEO score
function calculateSEOScore(analysis) {
  let score = 100;
  const penalties = [];

  // Word count scoring
  if (analysis.wordCount < 300) {
    score -= 20;
    penalties.push('Content too short (minimum 300 words)');
  } else if (analysis.wordCount < 600) {
    score -= 10;
    penalties.push('Content could be longer for better SEO');
  }

  // Keyword density scoring
  if (analysis.keywordDensity < 0.5) {
    score -= 15;
    penalties.push('Keyword density too low');
  } else if (analysis.keywordDensity > 3) {
    score -= 20;
    penalties.push('Keyword density too high (keyword stuffing)');
  }

  // Headings scoring
  if (analysis.headings.h1 === 0) {
    score -= 15;
    penalties.push('Missing H1 heading');
  } else if (analysis.headings.h1 > 1) {
    score -= 10;
    penalties.push('Multiple H1 headings found');
  }

  if (analysis.headings.total < 3) {
    score -= 10;
    penalties.push('Insufficient heading structure');
  }

  // Readability scoring
  if (analysis.avgWordsPerSentence > 25) {
    score -= 10;
    penalties.push('Sentences too long (aim for under 25 words)');
  }

  return {
    score: Math.max(0, score),
    penalties
  };
}

// Helper function to generate recommendations
function generateRecommendations(analysis, seoScore) {
  const recommendations = [];

  if (analysis.wordCount < 600) {
    recommendations.push({
      type: 'content_length',
      priority: 'high',
      message: `Increase content length to at least 600 words (current: ${analysis.wordCount})`
    });
  }

  if (analysis.keywordDensity < 0.5 || analysis.keywordDensity > 3) {
    recommendations.push({
      type: 'keyword_density',
      priority: 'high',
      message: `Optimize keyword density to 1-2% (current: ${analysis.keywordDensity}%)`
    });
  }

  if (analysis.headings.h1 !== 1) {
    recommendations.push({
      type: 'h1_heading',
      priority: 'high',
      message: 'Ensure exactly one H1 heading exists'
    });
  }

  if (analysis.headings.total < 3) {
    recommendations.push({
      type: 'heading_structure',
      priority: 'medium',
      message: 'Add more headings to improve content structure'
    });
  }

  if (analysis.avgWordsPerSentence > 25) {
    recommendations.push({
      type: 'readability',
      priority: 'medium',
      message: 'Shorten sentences for better readability'
    });
  }

  return recommendations;
}

// Helper function to update usage tracking
async function updateUsageTracking(userId, type) {
  const today = new Date().toISOString().split('T')[0];
  
  const { data: existing } = await supabase
    .from('usage_tracking')
    .select('*')
    .eq('user_id', userId)
    .eq('date', today)
    .single();

  if (existing) {
    await supabase
      .from('usage_tracking')
      .update({ 
        [type]: existing[type] + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', existing.id);
  } else {
    await supabase
      .from('usage_tracking')
      .insert({
        user_id: userId,
        date: today,
        [type]: 1,
        created_at: new Date().toISOString()
      });
  }
}

module.exports = router;
(()=>{var e={};e.id=453,e.ids=[453],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},1017:e=>{"use strict";e.exports=require("path")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},1091:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>d,originalPathname:()=>p,pages:()=>l,routeModule:()=>x,tree:()=>c});var s=r(482),a=r(9108),i=r(2563),n=r.n(i),o=r(8300),u={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>o[e]);r.d(t,u);let c=["",{children:["auth",{children:["callback",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,5891)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/callback/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,6343)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/callback/page.tsx"],p="/auth/callback/page",d={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/callback/page",pathname:"/auth/callback",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},7617:(e,t,r)=>{Promise.resolve().then(r.bind(r,4015))},2254:(e,t,r)=>{e.exports=r(4767)},4015:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>o});var s=r(2295),a=r(3729),i=r(2254),n=r(9299);function o(){let e=(0,i.useRouter)(),t=(0,n.createSupabaseComponentClient)();return(0,a.useEffect)(()=>{(async()=>{try{let{data:r,error:s}=await t.auth.getSession();if(s){console.error("Auth callback error:",s),e.push("/auth/signin?error=callback_error");return}r.session?e.push("/dashboard"):e.push("/auth/signin")}catch(t){console.error("Unexpected error during auth callback:",t),e.push("/auth/signin?error=unexpected_error")}})()},[e,t.auth]),s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center",children:(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),s.jsx("p",{className:"mt-4 text-gray-600",children:"Completing authentication..."})]})})}},5891:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let s=(0,r(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/callback/page.tsx`),{__esModule:a,$$typeof:i}=s,n=s.default},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(337);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,972,337,129],()=>r(1091));module.exports=s})();
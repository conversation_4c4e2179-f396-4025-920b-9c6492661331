<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Settings - SEO Pro</title>
    <meta name="description" content="Configure your preferences, notifications, integrations, and team settings for optimal SEO Pro experience.">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style">
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" href="images/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <a href="index.html" class="logo">
                    <span class="logo-text">SEO Pro</span>
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <ul class="nav-menu">
                <li><a href="dashboard.html" class="nav-link">Dashboard</a></li>
                <li><a href="content-generator.html" class="nav-link">Generate</a></li>
                <li><a href="seo-analysis.html" class="nav-link">Analyze</a></li>
                <li><a href="projects.html" class="nav-link">Projects</a></li>
            </ul>
            
            <!-- User Menu -->
            <div class="nav-user">
                <div class="user-avatar">
                    <img src="images/avatars/user.jpg" alt="User Avatar" class="avatar">
                </div>
                <div class="user-menu">
                    <a href="profile.html" class="user-menu-item">Profile</a>
                    <a href="settings.html" class="user-menu-item active">Settings</a>
                    <a href="billing.html" class="user-menu-item">Billing</a>
                    <a href="login.html" class="user-menu-item">Logout</a>
                </div>
            </div>
            
            <!-- Mobile Menu Button -->
            <button class="mobile-menu-btn" aria-label="Toggle menu">
                <span class="hamburger"></span>
            </button>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Page Header -->
            <div class="page-header">
                <h1 class="page-title">Settings</h1>
                <p class="page-description">
                    Configure your preferences, notifications, and integrations to customize your SEO Pro experience.
                </p>
            </div>

            <!-- Settings Navigation -->
            <div class="settings-nav">
                <button class="settings-nav-item active" data-tab="general">General</button>
                <button class="settings-nav-item" data-tab="content">Content</button>
                <button class="settings-nav-item" data-tab="notifications">Notifications</button>
                <button class="settings-nav-item" data-tab="integrations">Integrations</button>
                <button class="settings-nav-item" data-tab="team">Team</button>
                <button class="settings-nav-item" data-tab="privacy">Privacy</button>
            </div>

            <!-- Settings Content -->
            <div class="settings-content">
                <!-- General Settings -->
                <div class="settings-tab active" id="general-tab">
                    <div class="settings-section">
                        <h2 class="section-title">General Preferences</h2>
                        <div class="settings-form">
                            <div class="form-group">
                                <label for="language" class="form-label">Language</label>
                                <select id="language" class="form-select">
                                    <option value="en" selected>English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                    <option value="de">German</option>
                                    <option value="it">Italian</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="timezone" class="form-label">Timezone</label>
                                <select id="timezone" class="form-select">
                                    <option value="UTC-8">Pacific Time (UTC-8)</option>
                                    <option value="UTC-7">Mountain Time (UTC-7)</option>
                                    <option value="UTC-6">Central Time (UTC-6)</option>
                                    <option value="UTC-5" selected>Eastern Time (UTC-5)</option>
                                    <option value="UTC+0">UTC</option>
                                    <option value="UTC+1">Central European Time (UTC+1)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="theme" class="form-label">Theme</label>
                                <div class="radio-group">
                                    <label class="radio-option">
                                        <input type="radio" name="theme" value="light" checked>
                                        <span class="radio-label">Light</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="theme" value="dark">
                                        <span class="radio-label">Dark</span>
                                    </label>
                                    <label class="radio-option">
                                        <input type="radio" name="theme" value="auto">
                                        <span class="radio-label">Auto</span>
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Settings -->
                <div class="settings-tab" id="content-tab">
                    <div class="settings-section">
                        <h2 class="section-title">Content Generation Defaults</h2>
                        <div class="settings-form">
                            <div class="form-group">
                                <label for="default-ai-model" class="form-label">Default AI Model</label>
                                <select id="default-ai-model" class="form-select">
                                    <option value="groq-llama" selected>Groq Llama 3.1 (Recommended)</option>
                                    <option value="groq-mixtral">Groq Mixtral 8x7B</option>
                                    <option value="openai-gpt4">OpenAI GPT-4</option>
                                    <option value="openai-gpt35">OpenAI GPT-3.5</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="default-tone" class="form-label">Default Tone</label>
                                <select id="default-tone" class="form-select">
                                    <option value="professional" selected>Professional</option>
                                    <option value="casual">Casual</option>
                                    <option value="friendly">Friendly</option>
                                    <option value="authoritative">Authoritative</option>
                                    <option value="conversational">Conversational</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label for="default-word-count" class="form-label">Default Word Count</label>
                                <input type="range" id="default-word-count" class="form-range" min="300" max="3000" value="1000" step="100">
                                <div class="range-labels">
                                    <span>300</span>
                                    <span id="word-count-value">1000</span>
                                    <span>3000</span>
                                </div>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked>
                                    <span class="checkbox-text">Auto-optimize for SEO</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked>
                                    <span class="checkbox-text">Include meta descriptions</span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Notifications Settings -->
                <div class="settings-tab" id="notifications-tab">
                    <div class="settings-section">
                        <h2 class="section-title">Email Notifications</h2>
                        <div class="notification-settings">
                            <div class="notification-item">
                                <div class="notification-info">
                                    <h3 class="notification-title">Content Generation Complete</h3>
                                    <p class="notification-description">Get notified when your content generation is finished</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="notification-item">
                                <div class="notification-info">
                                    <h3 class="notification-title">SEO Analysis Reports</h3>
                                    <p class="notification-description">Receive weekly SEO performance reports</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="notification-item">
                                <div class="notification-info">
                                    <h3 class="notification-title">Team Activity</h3>
                                    <p class="notification-description">Get notified about team member activities</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                            <div class="notification-item">
                                <div class="notification-info">
                                    <h3 class="notification-title">Billing & Usage Alerts</h3>
                                    <p class="notification-description">Important billing and usage limit notifications</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox" checked>
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="settings-section">
                        <h2 class="section-title">Browser Notifications</h2>
                        <div class="notification-settings">
                            <div class="notification-item">
                                <div class="notification-info">
                                    <h3 class="notification-title">Desktop Notifications</h3>
                                    <p class="notification-description">Show browser notifications for important updates</p>
                                </div>
                                <label class="toggle-switch">
                                    <input type="checkbox">
                                    <span class="toggle-slider"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Integrations Settings -->
                <div class="settings-tab" id="integrations-tab">
                    <div class="settings-section">
                        <h2 class="section-title">Connected Integrations</h2>
                        <div class="integrations-list">
                            <div class="integration-item">
                                <div class="integration-info">
                                    <div class="integration-icon">
                                        <img src="images/icons/google-analytics.png" alt="Google Analytics">
                                    </div>
                                    <div>
                                        <h3 class="integration-name">Google Analytics</h3>
                                        <p class="integration-description">Track content performance and traffic data</p>
                                    </div>
                                </div>
                                <div class="integration-status">
                                    <span class="status-badge connected">Connected</span>
                                    <button class="btn btn-outline btn-sm">Configure</button>
                                </div>
                            </div>
                            <div class="integration-item">
                                <div class="integration-info">
                                    <div class="integration-icon">
                                        <img src="images/icons/wordpress.png" alt="WordPress">
                                    </div>
                                    <div>
                                        <h3 class="integration-name">WordPress</h3>
                                        <p class="integration-description">Publish content directly to your WordPress site</p>
                                    </div>
                                </div>
                                <div class="integration-status">
                                    <span class="status-badge connected">Connected</span>
                                    <button class="btn btn-outline btn-sm">Configure</button>
                                </div>
                            </div>
                            <div class="integration-item">
                                <div class="integration-info">
                                    <div class="integration-icon">
                                        <img src="images/icons/zapier.png" alt="Zapier">
                                    </div>
                                    <div>
                                        <h3 class="integration-name">Zapier</h3>
                                        <p class="integration-description">Automate workflows with 5000+ apps</p>
                                    </div>
                                </div>
                                <div class="integration-status">
                                    <span class="status-badge not-connected">Not Connected</span>
                                    <button class="btn btn-primary btn-sm">Connect</button>
                                </div>
                            </div>
                            <div class="integration-item">
                                <div class="integration-info">
                                    <div class="integration-icon">
                                        <img src="images/icons/facebook.png" alt="Facebook">
                                    </div>
                                    <div>
                                        <h3 class="integration-name">Facebook Pages</h3>
                                        <p class="integration-description">Share content to your Facebook business pages</p>
                                    </div>
                                </div>
                                <div class="integration-status">
                                    <span class="status-badge not-connected">Not Connected</span>
                                    <button class="btn btn-primary btn-sm">Connect</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Team Settings -->
                <div class="settings-tab" id="team-tab">
                    <div class="settings-section">
                        <h2 class="section-title">Team Collaboration</h2>
                        <div class="settings-form">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked>
                                    <span class="checkbox-text">Allow team members to create projects</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked>
                                    <span class="checkbox-text">Require approval for content publishing</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox">
                                    <span class="checkbox-text">Enable real-time collaboration</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="default-permissions" class="form-label">Default Member Permissions</label>
                                <select id="default-permissions" class="form-select">
                                    <option value="viewer">Viewer</option>
                                    <option value="editor" selected>Editor</option>
                                    <option value="admin">Admin</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Privacy Settings -->
                <div class="settings-tab" id="privacy-tab">
                    <div class="settings-section">
                        <h2 class="section-title">Privacy & Data</h2>
                        <div class="settings-form">
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked>
                                    <span class="checkbox-text">Allow analytics tracking for product improvement</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox">
                                    <span class="checkbox-text">Share usage data with third-party integrations</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label class="checkbox-label">
                                    <input type="checkbox" checked>
                                    <span class="checkbox-text">Enable automatic data backups</span>
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="data-retention" class="form-label">Data Retention Period</label>
                                <select id="data-retention" class="form-select">
                                    <option value="1year">1 Year</option>
                                    <option value="2years" selected>2 Years</option>
                                    <option value="5years">5 Years</option>
                                    <option value="indefinite">Indefinite</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Save Button -->
            <div class="settings-footer">
                <button class="btn btn-primary btn-large">Save Changes</button>
                <button class="btn btn-ghost btn-large">Reset to Defaults</button>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3 class="footer-title">SEO Pro</h3>
                    <p class="footer-description">AI-powered SEO content generation platform</p>
                </div>
                <div class="footer-section">
                    <h4 class="footer-heading">Product</h4>
                    <ul class="footer-links">
                        <li><a href="features.html">Features</a></li>
                        <li><a href="pricing.html">Pricing</a></li>
                        <li><a href="dashboard.html">Dashboard</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SEO Pro. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
    <script>
        // Settings tab navigation
        document.querySelectorAll('.settings-nav-item').forEach(button => {
            button.addEventListener('click', function() {
                // Remove active class from all nav items and tabs
                document.querySelectorAll('.settings-nav-item').forEach(item => item.classList.remove('active'));
                document.querySelectorAll('.settings-tab').forEach(tab => tab.classList.remove('active'));
                
                // Add active class to clicked nav item
                this.classList.add('active');
                
                // Show corresponding tab
                const tabId = this.getAttribute('data-tab') + '-tab';
                document.getElementById(tabId).classList.add('active');
            });
        });

        // Word count slider
        const wordCountSlider = document.getElementById('default-word-count');
        const wordCountValue = document.getElementById('word-count-value');
        
        wordCountSlider.addEventListener('input', function() {
            wordCountValue.textContent = this.value;
        });
    </script>
</body>
</html>

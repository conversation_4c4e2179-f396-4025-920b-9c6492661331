const express = require('express');
const router = express.Router();
const { verifyToken, checkUsageLimit } = require('../middleware/auth');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Create new project
router.post('/', verifyToken, async (req, res) => {
  try {
    const {
      name,
      description,
      keywords = [],
      settings = {},
      status = 'active'
    } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'Project name is required' });
    }

    // Check if project name already exists for this user
    const { data: existingProject } = await supabase
      .from('projects')
      .select('id')
      .eq('user_id', req.user.id)
      .eq('name', name)
      .single();

    if (existingProject) {
      return res.status(409).json({ error: 'Project with this name already exists' });
    }

    // Create project
    const { data: project, error } = await supabase
      .from('projects')
      .insert({
        user_id: req.user.id,
        name,
        description,
        keywords,
        settings,
        status,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      project
    });

  } catch (error) {
    console.error('Create project error:', error);
    res.status(500).json({ error: 'Failed to create project' });
  }
});

// Get all projects for user
router.get('/', verifyToken, async (req, res) => {
  try {
    const { 
      page = 1, 
      limit = 20, 
      status = 'all',
      search = '',
      sort = 'updated_at',
      order = 'desc'
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Build query
    let query = supabase
      .from('projects')
      .select('*', { count: 'exact' })
      .eq('user_id', req.user.id);

    // Apply filters
    if (status !== 'all') {
      query = query.eq('status', status);
    }

    if (search) {
      query = query.or(`name.ilike.%${search}%,description.ilike.%${search}%`);
    }

    // Apply sorting and pagination
    query = query
      .order(sort, { ascending: order === 'asc' })
      .range(offset, offset + parseInt(limit) - 1);

    const { data: projects, error, count } = await query;

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      projects,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        totalPages: Math.ceil(count / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get projects error:', error);
    res.status(500).json({ error: 'Failed to retrieve projects' });
  }
});

// Get single project
router.get('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;

    const { data: project, error } = await supabase
      .from('projects')
      .select(`
        *,
        project_content (
          id,
          content_type,
          title,
          created_at,
          updated_at
        )
      `)
      .eq('id', id)
      .eq('user_id', req.user.id)
      .single();

    if (error || !project) {
      return res.status(404).json({ error: 'Project not found' });
    }

    res.json({
      success: true,
      project
    });

  } catch (error) {
    console.error('Get project error:', error);
    res.status(500).json({ error: 'Failed to retrieve project' });
  }
});

// Update project
router.put('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;
    const {
      name,
      description,
      keywords,
      settings,
      status
    } = req.body;

    // Check if project exists and belongs to user
    const { data: existingProject } = await supabase
      .from('projects')
      .select('id')
      .eq('id', id)
      .eq('user_id', req.user.id)
      .single();

    if (!existingProject) {
      return res.status(404).json({ error: 'Project not found' });
    }

    // Check for name conflict (if name is being changed)
    if (name) {
      const { data: nameConflict } = await supabase
        .from('projects')
        .select('id')
        .eq('user_id', req.user.id)
        .eq('name', name)
        .neq('id', id)
        .single();

      if (nameConflict) {
        return res.status(409).json({ error: 'Project with this name already exists' });
      }
    }

    // Update project
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (name !== undefined) updateData.name = name;
    if (description !== undefined) updateData.description = description;
    if (keywords !== undefined) updateData.keywords = keywords;
    if (settings !== undefined) updateData.settings = settings;
    if (status !== undefined) updateData.status = status;

    const { data: project, error } = await supabase
      .from('projects')
      .update(updateData)
      .eq('id', id)
      .eq('user_id', req.user.id)
      .select()
      .single();

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      project
    });

  } catch (error) {
    console.error('Update project error:', error);
    res.status(500).json({ error: 'Failed to update project' });
  }
});

// Delete project
router.delete('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;

    // Check if project exists and belongs to user
    const { data: existingProject } = await supabase
      .from('projects')
      .select('id')
      .eq('id', id)
      .eq('user_id', req.user.id)
      .single();

    if (!existingProject) {
      return res.status(404).json({ error: 'Project not found' });
    }

    // Soft delete project (update status to 'deleted')
    const { error } = await supabase
      .from('projects')
      .update({
        status: 'deleted',
        updated_at: new Date().toISOString()
      })
      .eq('id', id)
      .eq('user_id', req.user.id);

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      message: 'Project deleted successfully'
    });

  } catch (error) {
    console.error('Delete project error:', error);
    res.status(500).json({ error: 'Failed to delete project' });
  }
});

// Add content to project
router.post('/:id/content', verifyToken, async (req, res) => {
  try {
    const { id: projectId } = req.params;
    const {
      content_type,
      title,
      content,
      keyword,
      metadata = {}
    } = req.body;

    if (!content_type || !title || !content) {
      return res.status(400).json({ 
        error: 'Content type, title, and content are required' 
      });
    }

    // Check if project exists and belongs to user
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', req.user.id)
      .single();

    if (!project) {
      return res.status(404).json({ error: 'Project not found' });
    }

    // Add content to project
    const { data: projectContent, error } = await supabase
      .from('project_content')
      .insert({
        project_id: projectId,
        user_id: req.user.id,
        content_type,
        title,
        content,
        keyword,
        metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Update project's updated_at timestamp
    await supabase
      .from('projects')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', projectId);

    res.json({
      success: true,
      content: projectContent
    });

  } catch (error) {
    console.error('Add project content error:', error);
    res.status(500).json({ error: 'Failed to add content to project' });
  }
});

// Get project content
router.get('/:id/content', verifyToken, async (req, res) => {
  try {
    const { id: projectId } = req.params;
    const { 
      content_type = 'all',
      page = 1,
      limit = 20
    } = req.query;

    const offset = (parseInt(page) - 1) * parseInt(limit);

    // Check if project exists and belongs to user
    const { data: project } = await supabase
      .from('projects')
      .select('id')
      .eq('id', projectId)
      .eq('user_id', req.user.id)
      .single();

    if (!project) {
      return res.status(404).json({ error: 'Project not found' });
    }

    // Build query
    let query = supabase
      .from('project_content')
      .select('*', { count: 'exact' })
      .eq('project_id', projectId)
      .eq('user_id', req.user.id);

    if (content_type !== 'all') {
      query = query.eq('content_type', content_type);
    }

    query = query
      .order('updated_at', { ascending: false })
      .range(offset, offset + parseInt(limit) - 1);

    const { data: content, error, count } = await query;

    if (error) {
      throw error;
    }

    res.json({
      success: true,
      content,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        totalPages: Math.ceil(count / parseInt(limit))
      }
    });

  } catch (error) {
    console.error('Get project content error:', error);
    res.status(500).json({ error: 'Failed to retrieve project content' });
  }
});

// Update project content
router.put('/:projectId/content/:contentId', verifyToken, async (req, res) => {
  try {
    const { projectId, contentId } = req.params;
    const {
      title,
      content,
      keyword,
      metadata
    } = req.body;

    // Check if content exists and belongs to user
    const { data: existingContent } = await supabase
      .from('project_content')
      .select('id')
      .eq('id', contentId)
      .eq('project_id', projectId)
      .eq('user_id', req.user.id)
      .single();

    if (!existingContent) {
      return res.status(404).json({ error: 'Content not found' });
    }

    // Update content
    const updateData = {
      updated_at: new Date().toISOString()
    };

    if (title !== undefined) updateData.title = title;
    if (content !== undefined) updateData.content = content;
    if (keyword !== undefined) updateData.keyword = keyword;
    if (metadata !== undefined) updateData.metadata = metadata;

    const { data: updatedContent, error } = await supabase
      .from('project_content')
      .update(updateData)
      .eq('id', contentId)
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Update project's updated_at timestamp
    await supabase
      .from('projects')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', projectId);

    res.json({
      success: true,
      content: updatedContent
    });

  } catch (error) {
    console.error('Update project content error:', error);
    res.status(500).json({ error: 'Failed to update content' });
  }
});

// Delete project content
router.delete('/:projectId/content/:contentId', verifyToken, async (req, res) => {
  try {
    const { projectId, contentId } = req.params;

    // Check if content exists and belongs to user
    const { data: existingContent } = await supabase
      .from('project_content')
      .select('id')
      .eq('id', contentId)
      .eq('project_id', projectId)
      .eq('user_id', req.user.id)
      .single();

    if (!existingContent) {
      return res.status(404).json({ error: 'Content not found' });
    }

    // Delete content
    const { error } = await supabase
      .from('project_content')
      .delete()
      .eq('id', contentId);

    if (error) {
      throw error;
    }

    // Update project's updated_at timestamp
    await supabase
      .from('projects')
      .update({ updated_at: new Date().toISOString() })
      .eq('id', projectId);

    res.json({
      success: true,
      message: 'Content deleted successfully'
    });

  } catch (error) {
    console.error('Delete project content error:', error);
    res.status(500).json({ error: 'Failed to delete content' });
  }
});

// Export project data
router.get('/:id/export', verifyToken, async (req, res) => {
  try {
    const { id: projectId } = req.params;
    const { format = 'json' } = req.query;

    // Get project with all content
    const { data: project, error } = await supabase
      .from('projects')
      .select(`
        *,
        project_content (*)
      `)
      .eq('id', projectId)
      .eq('user_id', req.user.id)
      .single();

    if (error || !project) {
      return res.status(404).json({ error: 'Project not found' });
    }

    const exportData = {
      project: {
        name: project.name,
        description: project.description,
        keywords: project.keywords,
        settings: project.settings,
        created_at: project.created_at,
        updated_at: project.updated_at
      },
      content: project.project_content || []
    };

    if (format.toLowerCase() === 'json') {
      res.setHeader('Content-Type', 'application/json');
      res.setHeader('Content-Disposition', `attachment; filename="${project.name}-export.json"`);
      res.json(exportData);
    } else {
      return res.status(400).json({ error: 'Unsupported export format' });
    }

  } catch (error) {
    console.error('Export project error:', error);
    res.status(500).json({ error: 'Failed to export project' });
  }
});

// Duplicate project
router.post('/:id/duplicate', verifyToken, async (req, res) => {
  try {
    const { id: originalProjectId } = req.params;
    const { name } = req.body;

    if (!name) {
      return res.status(400).json({ error: 'New project name is required' });
    }

    // Get original project
    const { data: originalProject, error: fetchError } = await supabase
      .from('projects')
      .select(`
        *,
        project_content (*)
      `)
      .eq('id', originalProjectId)
      .eq('user_id', req.user.id)
      .single();

    if (fetchError || !originalProject) {
      return res.status(404).json({ error: 'Original project not found' });
    }

    // Check if new name already exists
    const { data: nameConflict } = await supabase
      .from('projects')
      .select('id')
      .eq('user_id', req.user.id)
      .eq('name', name)
      .single();

    if (nameConflict) {
      return res.status(409).json({ error: 'Project with this name already exists' });
    }

    // Create new project
    const { data: newProject, error: createError } = await supabase
      .from('projects')
      .insert({
        user_id: req.user.id,
        name,
        description: originalProject.description,
        keywords: originalProject.keywords,
        settings: originalProject.settings,
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single();

    if (createError) {
      throw createError;
    }

    // Duplicate project content
    if (originalProject.project_content && originalProject.project_content.length > 0) {
      const contentToInsert = originalProject.project_content.map(content => ({
        project_id: newProject.id,
        user_id: req.user.id,
        content_type: content.content_type,
        title: content.title,
        content: content.content,
        keyword: content.keyword,
        metadata: content.metadata,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }));

      const { error: contentError } = await supabase
        .from('project_content')
        .insert(contentToInsert);

      if (contentError) {
        console.error('Error duplicating content:', contentError);
        // Continue anyway, project created successfully
      }
    }

    res.json({
      success: true,
      project: newProject
    });

  } catch (error) {
    console.error('Duplicate project error:', error);
    res.status(500).json({ error: 'Failed to duplicate project' });
  }
});

module.exports = router;
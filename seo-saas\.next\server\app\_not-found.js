(()=>{var e={};e.id=165,e.ids=[165],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},3543:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>c,routeModule:()=>h,tree:()=>l});var s=r(482),n=r(9108),i=r(2563),o=r.n(i),a=r(8300),u={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(u[e]=()=>a[e]);r.d(t,u);let l=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"]}]},{layout:[()=>Promise.resolve().then(r.bind(r,1623)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"]}],c=[],d="/_not-found",p={require:r,loadChunk:()=>Promise.resolve()},h=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/_not-found",pathname:"/_not-found",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8775:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},6315:(e,t,r)=>{Promise.resolve().then(r.bind(r,3750))},3750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AuthProvider:()=>a,useAuth:()=>u,useAuthGuard:()=>c,useSubscription:()=>l});var s=r(2295),n=r(3729),i=r(8704);let o=(0,n.createContext)(void 0);function a({children:e}){let[t,r]=(0,n.useState)(null),[a,u]=(0,n.useState)(null),[l,c]=(0,n.useState)(null),[d,p]=(0,n.useState)(null),[h,m]=(0,n.useState)(!0),x=(0,i.R9)(),g=async e=>{try{let{data:t,error:r}=await x.from("profiles").select("*").eq("id",e).single();if(r)return console.error("Error fetching profile:",r),null;return t}catch(e){return console.error("Error fetching profile:",e),null}},f=async e=>{try{let{data:t,error:r}=await x.from("user_subscriptions").select("*").eq("user_id",e).single();if(r&&"PGRST116"!==r.code)return console.error("Error fetching subscription:",r),null;return t}catch(e){return console.error("Error fetching subscription:",e),null}};(0,n.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await x.auth.getSession();if(t){console.error("Error getting session:",t),m(!1);return}if(e?.user){u(e),r(e.user);let[t,s]=await Promise.all([g(e.user.id),f(e.user.id)]);c(t),p(s)}}catch(e){console.error("Error initializing auth:",e)}finally{m(!1)}})();let{data:{subscription:e}}=x.auth.onAuthStateChange(async(e,t)=>{if(console.log("Auth state changed:",e,t?.user?.id),u(t),r(t?.user??null),t?.user){let[e,r]=await Promise.all([g(t.user.id),f(t.user.id)]);c(e),p(r)}else c(null),p(null);m(!1)});return()=>{e.unsubscribe()}},[]);let S=async(e,t)=>{let{error:r}=await x.auth.signInWithPassword({email:e,password:t});if(r)throw Error(r.message)},y=async(e,t,r)=>{let{error:s}=await x.auth.signUp({email:e,password:t,options:{data:{full_name:r}}});if(s)throw Error(s.message)},w=async()=>{let{error:e}=await x.auth.signOut();if(e)throw Error(e.message)},P=async e=>{let{error:t}=await x.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/auth/reset-password`});if(t)throw Error(t.message)},A=async e=>{if(!t)throw Error("No user logged in");let{data:r,error:s}=await x.from("profiles").update({...e,updated_at:new Date().toISOString()}).eq("id",t.id).select().single();if(s)throw Error(s.message);c(r)},E=async()=>{t&&c(await g(t.id))},b=async()=>{t&&p(await f(t.id))};return s.jsx(o.Provider,{value:{user:t,session:a,profile:l,subscription:d,loading:h,signIn:S,signUp:y,signOut:w,resetPassword:P,updateProfile:A,refreshProfile:E,refreshSubscription:b},children:e})}function u(){let e=(0,n.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l(){let{subscription:e}=u(),t=e?.status==="active",r=e?.status==="trialing",s=e?.status==="past_due",n=e?.status==="cancelled";return{subscription:e,hasActiveSubscription:t,isOnTrial:r,isPastDue:s,isCancelled:n,canAccessFeature:r=>!!e&&("pro"===r?["pro","enterprise"].includes(e.plan_type)&&t:"enterprise"===r&&"enterprise"===e.plan_type&&t)}}function c(){let{user:e,loading:t}=u();return{isAuthenticated:!!e,isLoading:t,user:e}}},8704:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>i,R9:()=>o});var s=r(6300),n=r(7435);let i=(0,s.eI)("https://zqrmpanonghggoxdjirq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}),o=()=>(0,n.createClientComponentClient)();(0,s.eI)("https://zqrmpanonghggoxdjirq.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}})},1623:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>d});var s=r(5036),n=r(5968),i=r.n(n);r(5023);var o=r(6843);let a=(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx`),{__esModule:u,$$typeof:l}=a;a.default;let c=(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#AuthProvider`);(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useAuth`),(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useSubscription`),(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useAuthGuard`);let d={title:"SEO Content Generator - Professional SEO Content Creation",description:"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.",keywords:["SEO","content generation","AI writing","keyword optimization","competitor analysis"],authors:[{name:"SEO Content Generator"}],openGraph:{title:"SEO Content Generator - Professional SEO Content Creation",description:"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"SEO Content Generator - Professional SEO Content Creation",description:"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results."},robots:{index:!0,follow:!0},viewport:{width:"device-width",initialScale:1}};function p({children:e}){return s.jsx("html",{lang:"en",className:i().variable,children:s.jsx("body",{className:"font-sans antialiased bg-gray-50 text-gray-900",children:s.jsx(c,{children:e})})})}},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,575],()=>r(3543));module.exports=s})();
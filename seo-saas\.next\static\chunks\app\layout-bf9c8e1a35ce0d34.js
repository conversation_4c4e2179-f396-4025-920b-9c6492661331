(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185],{4299:function(e,t,r){Promise.resolve().then(r.t.bind(r,3385,23)),Promise.resolve().then(r.t.bind(r,9766,23)),Promise.resolve().then(r.bind(r,6210))},6210:function(e,t,r){"use strict";r.r(t),r.d(t,{AuthProvider:function(){return a},useAuth:function(){return u},useAuthGuard:function(){return c},useSubscription:function(){return l}});var n=r(7437),s=r(2265),i=r(4958);let o=(0,s.createContext)(void 0);function a(e){let{children:t}=e,[r,a]=(0,s.useState)(null),[u,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(null),[f,h]=(0,s.useState)(null),[p,_]=(0,s.useState)(!0),g=(0,i.R9)(),m=async e=>{try{let{data:t,error:r}=await g.from("profiles").select("*").eq("id",e).single();if(r)return console.error("Error fetching profile:",r),null;return t}catch(e){return console.error("Error fetching profile:",e),null}},v=async e=>{try{let{data:t,error:r}=await g.from("user_subscriptions").select("*").eq("user_id",e).single();if(r&&"PGRST116"!==r.code)return console.error("Error fetching subscription:",r),null;return t}catch(e){return console.error("Error fetching subscription:",e),null}};(0,s.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await g.auth.getSession();if(t){console.error("Error getting session:",t),_(!1);return}if(null==e?void 0:e.user){l(e),a(e.user);let[t,r]=await Promise.all([m(e.user.id),v(e.user.id)]);d(t),h(r)}}catch(e){console.error("Error initializing auth:",e)}finally{_(!1)}})();let{data:{subscription:e}}=g.auth.onAuthStateChange(async(e,t)=>{var r,n;if(console.log("Auth state changed:",e,null==t?void 0:null===(r=t.user)||void 0===r?void 0:r.id),l(t),a(null!==(n=null==t?void 0:t.user)&&void 0!==n?n:null),null==t?void 0:t.user){let[e,r]=await Promise.all([m(t.user.id),v(t.user.id)]);d(e),h(r)}else d(null),h(null);_(!1)});return()=>{e.unsubscribe()}},[]);let w=async(e,t)=>{let{error:r}=await g.auth.signInWithPassword({email:e,password:t});if(r)throw Error(r.message)},y=async(e,t,r)=>{let{error:n}=await g.auth.signUp({email:e,password:t,options:{data:{full_name:r}}});if(n)throw Error(n.message)},b=async()=>{let{error:e}=await g.auth.signOut();if(e)throw Error(e.message)},E=async e=>{let{error:t}=await g.auth.resetPasswordForEmail(e,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(t)throw Error(t.message)},I=async e=>{if(!r)throw Error("No user logged in");let{data:t,error:n}=await g.from("profiles").update({...e,updated_at:new Date().toISOString()}).eq("id",r.id).select().single();if(n)throw Error(n.message);d(t)},S=async()=>{r&&d(await m(r.id))},C=async()=>{r&&h(await v(r.id))};return(0,n.jsx)(o.Provider,{value:{user:r,session:u,profile:c,subscription:f,loading:p,signIn:w,signUp:y,signOut:b,resetPassword:E,updateProfile:I,refreshProfile:S,refreshSubscription:C},children:t})}function u(){let e=(0,s.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function l(){let{subscription:e}=u(),t=(null==e?void 0:e.status)==="active",r=(null==e?void 0:e.status)==="trialing",n=(null==e?void 0:e.status)==="past_due",s=(null==e?void 0:e.status)==="cancelled";return{subscription:e,hasActiveSubscription:t,isOnTrial:r,isPastDue:n,isCancelled:s,canAccessFeature:r=>!!e&&("pro"===r?["pro","enterprise"].includes(e.plan_type)&&t:"enterprise"===r&&"enterprise"===e.plan_type&&t)}}function c(){let{user:e,loading:t}=u();return{isAuthenticated:!!e,isLoading:t,user:e}}},4958:function(e,t,r){"use strict";r.d(t,{OQ:function(){return o},R9:function(){return a}});var n=r(1492),s=r(3082),i=r(2601);let o=(0,n.eI)("https://zqrmpanonghggoxdjirq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}),a=()=>(0,s.createClientComponentClient)();(0,n.eI)("https://zqrmpanonghggoxdjirq.supabase.co",i.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}})},3385:function(){},9766:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},622:function(e,t,r){"use strict";var n=r(2265),s=Symbol.for("react.element"),i=Symbol.for("react.fragment"),o=Object.prototype.hasOwnProperty,a=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,u={key:!0,ref:!0,__self:!0,__source:!0};function l(e,t,r){var n,i={},l=null,c=null;for(n in void 0!==r&&(l=""+r),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(c=t.ref),t)o.call(t,n)&&!u.hasOwnProperty(n)&&(i[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===i[n]&&(i[n]=t[n]);return{$$typeof:s,type:e,key:l,ref:c,props:i,_owner:a.current}}t.Fragment=i,t.jsx=l,t.jsxs=l},7437:function(e,t,r){"use strict";e.exports=r(622)}},function(e){e.O(0,[496,971,938,744],function(){return e(e.s=4299)}),_N_E=e.O()}]);
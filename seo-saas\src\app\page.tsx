'use client'

import * as React from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  ChartBarIcon, 
  DocumentTextIcon, 
  UserGroupIcon, 
  SparklesIcon,
  CheckCircleIcon,
  ArrowRightIcon,
  RocketLaunchIcon
} from "@heroicons/react/24/outline"

export default function HomePage() {
  const features = [
    {
      icon: SparklesIcon,
      title: "AI-Powered Content Generation",
      description: "Create SEO-optimized content with advanced AI that understands your industry and target audience.",
      color: "bg-blue-500"
    },
    {
      icon: ChartBarIcon,
      title: "Comprehensive SEO Analysis",
      description: "Get detailed insights with 6 analysis engines including keyword density, heading structure, and quality scoring.",
      color: "bg-green-500"
    },
    {
      icon: UserGroupIcon,
      title: "Competitor Intelligence",
      description: "Analyze top competitors and discover content gaps and opportunities to outrank them.",
      color: "bg-purple-500"
    },
    {
      icon: DocumentTextIcon,
      title: "Multi-Industry Support",
      description: "Optimized for 10+ industries with dynamic content adaptation and industry-specific insights.",
      color: "bg-orange-500"
    }
  ]

  const benefits = [
    "Generate 500-3000 word SEO-optimized content",
    "Real-time competitor analysis and benchmarking",
    "E-E-A-T compliant content scoring",
    "LSI keyword extraction and optimization",
    "Multi-language and geo-targeting support",
    "Advanced analytics and performance tracking"
  ]

  const stats = [
    { value: "95%", label: "SEO Score Improvement" },
    { value: "10x", label: "Faster Content Creation" },
    { value: "500+", label: "Happy Customers" },
    { value: "99.9%", label: "Uptime Guarantee" }
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="container mx-auto px-4 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <RocketLaunchIcon className="h-8 w-8 text-blue-600" />
              <span className="text-xl font-bold text-gray-900">SEO SAAS</span>
              <Badge variant="success">BETA</Badge>
            </div>
            <nav className="hidden md:flex items-center space-x-8">
              <a href="#features" className="text-gray-600 hover:text-gray-900">Features</a>
              <a href="#pricing" className="text-gray-600 hover:text-gray-900">Pricing</a>
              <a href="/auth/signin" className="text-gray-600 hover:text-gray-900">Sign In</a>
              <Button asChild>
                <a href="/auth/signup">Get Started</a>
              </Button>
            </nav>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 lg:py-32">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto text-center">
            <Badge variant="info" className="mb-4">
              🚀 World's Most Advanced SEO Content Engine
            </Badge>
            <h1 className="text-4xl lg:text-6xl font-bold text-gray-900 mb-6">
              Generate <span className="text-blue-600">SEO-Optimized</span> Content That Ranks
            </h1>
            <p className="text-xl text-gray-600 mb-8 max-w-2xl mx-auto">
              Create high-quality, SEO-optimized content with our AI-powered platform. 
              Analyze competitors, optimize for search engines, and boost your rankings.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
              <Button size="lg" className="text-lg px-8 py-3" asChild>
                <a href="/dashboard">
                  Start Creating Content
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </a>
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8 py-3" asChild>
                <a href="/dashboard/analysis">
                  Try SEO Analysis
                </a>
              </Button>
            </div>
            
            {/* Stats */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-8 max-w-2xl mx-auto">
              {stats.map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{stat.value}</div>
                  <div className="text-sm text-gray-600">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section id="features" className="py-20 bg-white">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center mb-16">
            <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
              Powerful Features for SEO Success
            </h2>
            <p className="text-xl text-gray-600">
              Everything you need to create, optimize, and rank your content
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {features.map((feature, index) => (
              <Card key={index} className="border-0 shadow-lg hover:shadow-xl transition-shadow">
                <CardHeader>
                  <div className="flex items-center space-x-3">
                    <div className={`p-3 rounded-lg ${feature.color}`}>
                      <feature.icon className="h-6 w-6 text-white" />
                    </div>
                    <CardTitle className="text-xl">{feature.title}</CardTitle>
                  </div>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-base">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="max-w-4xl mx-auto">
            <div className="text-center mb-16">
              <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
                Why Choose Our SEO Platform?
              </h2>
              <p className="text-xl text-gray-600">
                Join thousands of content creators who trust our platform
              </p>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-start space-x-3">
                  <CheckCircleIcon className="h-6 w-6 text-green-500 mt-1 flex-shrink-0" />
                  <span className="text-gray-700">{benefit}</span>
                </div>
              ))}
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600">
        <div className="container mx-auto px-4">
          <div className="max-w-3xl mx-auto text-center">
            <h2 className="text-3xl lg:text-4xl font-bold text-white mb-4">
              Ready to Boost Your SEO?
            </h2>
            <p className="text-xl text-blue-100 mb-8">
              Start creating SEO-optimized content that ranks higher and drives more traffic
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" variant="secondary" className="text-lg px-8 py-3" asChild>
                <a href="/dashboard">
                  Get Started Free
                  <ArrowRightIcon className="ml-2 h-5 w-5" />
                </a>
              </Button>
              <Button size="lg" variant="outline" className="text-lg px-8 py-3 border-white text-white hover:bg-white hover:text-blue-600" asChild>
                <a href="/dashboard/analysis">
                  Try Demo
                </a>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <RocketLaunchIcon className="h-6 w-6" />
                <span className="text-lg font-bold">SEO SAAS</span>
              </div>
              <p className="text-gray-400">
                The world's most advanced SEO content generation platform
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="/dashboard" className="hover:text-white">Dashboard</a></li>
                <li><a href="/dashboard/content" className="hover:text-white">Content Generator</a></li>
                <li><a href="/dashboard/analysis" className="hover:text-white">SEO Analysis</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">About</a></li>
                <li><a href="#" className="hover:text-white">Blog</a></li>
                <li><a href="#" className="hover:text-white">Contact</a></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><a href="#" className="hover:text-white">Help Center</a></li>
                <li><a href="#" className="hover:text-white">API Docs</a></li>
                <li><a href="#" className="hover:text-white">Status</a></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-12 pt-8 text-center text-gray-400">
            <p>&copy; 2024 SEO SAAS. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

// Professional SEO SAAS Landing Page
// Enterprise-grade landing page with modern design

import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import {
  DocumentTextIcon,
  ChartBarIcon,
  UserGroupIcon,
  SparklesIcon,
  CheckCircleIcon,
  ArrowRightIcon
} from "@heroicons/react/24/outline"

export default function Home() {
  const features = [
    {
      icon: DocumentTextIcon,
      title: "AI Content Generation",
      description: "Generate SEO-optimized content with advanced AI that understands your industry and target audience."
    },
    {
      icon: ChartBarIcon,
      title: "Competitor Analysis",
      description: "Analyze top-ranking competitors and identify content gaps to outrank them in search results."
    },
    {
      icon: UserGroupIcon,
      title: "Multi-Industry Support",
      description: "Specialized templates and optimization for technology, healthcare, finance, and 10+ other industries."
    },
    {
      icon: SparklesIcon,
      title: "Advanced SEO Engine",
      description: "Comprehensive SEO analysis with keyword density, heading optimization, and LSI keyword extraction."
    }
  ]

  const benefits = [
    "Generate 1000+ word SEO-optimized content in under 2 minutes",
    "Analyze competitor strategies and identify ranking opportunities",
    "Built-in E-E-A-T compliance for Google's quality guidelines",
    "Real-time SERP analysis and keyword research",
    "Professional content templates for all industries",
    "Advanced NLP and semantic keyword optimization"
  ]

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50">
      {/* Header */}
      <header className="border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <h1 className="text-2xl font-bold text-gray-900">SEO Pro</h1>
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/auth/signin">
                <Button variant="ghost">Sign In</Button>
              </Link>
              <Link href="/auth/signup">
                <Button>Get Started</Button>
              </Link>
            </div>
          </div>
        </div>
      </header>

      {/* Hero Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto text-center">
          <h1 className="text-5xl md:text-6xl font-bold text-gray-900 mb-6">
            Generate SEO Content That
            <span className="text-blue-600 block">Ranks #1 on Google</span>
          </h1>
          <p className="text-xl text-gray-600 mb-8 max-w-3xl mx-auto">
            Create high-quality, SEO-optimized content with our advanced AI engine. 
            Analyze competitors, optimize keywords, and generate content that outranks the competition.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link href="/auth/signup">
              <Button size="lg" className="text-lg px-8 py-3">
                Start Free Trial
                <ArrowRightIcon className="ml-2 h-5 w-5" />
              </Button>
            </Link>
            <Link href="/dashboard">
              <Button variant="outline" size="lg" className="text-lg px-8 py-3">
                View Demo
              </Button>
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-gray-900 mb-4">
              Everything You Need to Dominate Search Rankings
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our comprehensive SEO platform combines AI content generation with advanced competitor analysis
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardHeader>
                  <div className="mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4">
                    <feature.icon className="h-6 w-6 text-blue-600" />
                  </div>
                  <CardTitle className="text-xl">{feature.title}</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="text-gray-600">
                    {feature.description}
                  </CardDescription>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </section>

      {/* Benefits Section */}
      <section className="py-20 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-4xl font-bold text-gray-900 mb-6">
                Why Choose SEO Pro?
              </h2>
              <p className="text-lg text-gray-600 mb-8">
                Join thousands of content creators, marketers, and businesses who trust our platform 
                to generate high-ranking content that drives organic traffic and conversions.
              </p>
              <div className="space-y-4">
                {benefits.map((benefit, index) => (
                  <div key={index} className="flex items-start space-x-3">
                    <CheckCircleIcon className="h-6 w-6 text-green-500 flex-shrink-0 mt-0.5" />
                    <span className="text-gray-700">{benefit}</span>
                  </div>
                ))}
              </div>
            </div>
            <div className="lg:pl-8">
              <Card className="p-8 bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                <CardHeader className="text-center pb-6">
                  <CardTitle className="text-3xl font-bold text-white">
                    Ready to Get Started?
                  </CardTitle>
                  <CardDescription className="text-blue-100 text-lg">
                    Join our free trial and see the results for yourself
                  </CardDescription>
                </CardHeader>
                <CardContent className="text-center">
                  <div className="space-y-4">
                    <div className="text-4xl font-bold">$0</div>
                    <div className="text-blue-100">14-day free trial</div>
                    <div className="text-blue-100">No credit card required</div>
                    <Link href="/auth/signup">
                      <Button size="lg" variant="secondary" className="w-full mt-6">
                        Start Your Free Trial
                      </Button>
                    </Link>
                  </div>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Footer */}
      <footer className="bg-gray-900 text-white py-12">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
            <div>
              <h3 className="text-xl font-bold mb-4">SEO Pro</h3>
              <p className="text-gray-400">
                Professional SEO content generation platform powered by advanced AI.
              </p>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Product</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/features" className="hover:text-white">Features</Link></li>
                <li><Link href="/pricing" className="hover:text-white">Pricing</Link></li>
                <li><Link href="/demo" className="hover:text-white">Demo</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Company</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/about" className="hover:text-white">About</Link></li>
                <li><Link href="/contact" className="hover:text-white">Contact</Link></li>
                <li><Link href="/blog" className="hover:text-white">Blog</Link></li>
              </ul>
            </div>
            <div>
              <h4 className="font-semibold mb-4">Support</h4>
              <ul className="space-y-2 text-gray-400">
                <li><Link href="/help" className="hover:text-white">Help Center</Link></li>
                <li><Link href="/docs" className="hover:text-white">Documentation</Link></li>
                <li><Link href="/api" className="hover:text-white">API</Link></li>
              </ul>
            </div>
          </div>
          <div className="border-t border-gray-800 mt-8 pt-8 text-center text-gray-400">
            <p>&copy; 2024 SEO Pro. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  )
}

// SEO Content Preview with Highlighting
// This module provides visual SEO analysis and content preview capabilities

class SEOContentPreview {
    constructor(containerId, options = {}) {
        this.container = document.getElementById(containerId);
        this.options = {
            showLineNumbers: true,
            highlightKeywords: true,
            showSEOMetrics: true,
            realTimeAnalysis: true,
            ...options
        };
        
        this.content = '';
        this.targetKeyword = '';
        this.seoData = null;
        this.metaTags = null;
        
        this.init();
    }

    init() {
        if (!this.container) {
            console.error('SEO Preview container not found');
            return;
        }

        this.createPreviewStructure();
        this.bindEvents();
    }

    createPreviewStructure() {
        this.container.innerHTML = `
            <div class="seo-preview-wrapper">
                <!-- Preview Controls -->
                <div class="preview-controls">
                    <div class="control-group">
                        <label class="toggle-label">
                            <input type="checkbox" id="toggle-keywords" ${this.options.highlightKeywords ? 'checked' : ''}>
                            <span class="toggle-slider"></span>
                            Highlight Keywords
                        </label>
                    </div>
                    <div class="control-group">
                        <label class="toggle-label">
                            <input type="checkbox" id="toggle-seo-issues" checked>
                            <span class="toggle-slider"></span>
                            Show SEO Issues
                        </label>
                    </div>
                    <div class="control-group">
                        <label class="toggle-label">
                            <input type="checkbox" id="toggle-meta-preview" checked>
                            <span class="toggle-slider"></span>
                            Meta Preview
                        </label>
                    </div>
                    <div class="control-group">
                        <button class="btn-secondary" id="export-preview">
                            <i class="icon-download"></i> Export Preview
                        </button>
                    </div>
                </div>

                <!-- SEO Metrics Dashboard -->
                <div class="seo-metrics-dashboard" id="seo-metrics">
                    <div class="metrics-grid">
                        <div class="metric-card">
                            <div class="metric-label">SEO Score</div>
                            <div class="metric-value" id="seo-score">--</div>
                            <div class="metric-status" id="seo-status"></div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Word Count</div>
                            <div class="metric-value" id="word-count">0</div>
                            <div class="metric-target">Target: 800+</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Keyword Density</div>
                            <div class="metric-value" id="keyword-density">0%</div>
                            <div class="metric-target">Target: 1-3%</div>
                        </div>
                        <div class="metric-card">
                            <div class="metric-label">Readability</div>
                            <div class="metric-value" id="readability-score">--</div>
                            <div class="metric-target">Target: 60+</div>
                        </div>
                    </div>
                </div>

                <!-- Meta Tags Preview -->
                <div class="meta-preview-section" id="meta-preview">
                    <h3>Search Engine Preview</h3>
                    <div class="serp-preview">
                        <div class="serp-title" id="serp-title">Your Page Title Will Appear Here</div>
                        <div class="serp-url" id="serp-url">https://yourwebsite.com/page</div>
                        <div class="serp-description" id="serp-description">Your meta description will appear here. This is how your page will look in search results.</div>
                    </div>
                    
                    <!-- Social Media Preview -->
                    <div class="social-preview">
                        <h4>Social Media Preview</h4>
                        <div class="og-preview">
                            <div class="og-image-placeholder">
                                <i class="icon-image"></i>
                                <span>Open Graph Image</span>
                            </div>
                            <div class="og-content">
                                <div class="og-title" id="og-title">Page Title</div>
                                <div class="og-description" id="og-description">Description for social sharing</div>
                                <div class="og-site" id="og-site">yourwebsite.com</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Content Preview with Highlights -->
                <div class="content-preview-section">
                    <div class="preview-header">
                        <h3>Content Preview</h3>
                        <div class="highlight-legend">
                            <span class="legend-item">
                                <span class="highlight-sample keyword"></span>
                                Target Keyword
                            </span>
                            <span class="legend-item">
                                <span class="highlight-sample lsi"></span>
                                LSI Keywords
                            </span>
                            <span class="legend-item">
                                <span class="highlight-sample heading"></span>
                                Headings
                            </span>
                            <span class="legend-item">
                                <span class="highlight-sample issue"></span>
                                SEO Issues
                            </span>
                        </div>
                    </div>
                    
                    <div class="content-preview" id="content-preview">
                        <div class="preview-placeholder">
                            <i class="icon-document"></i>
                            <p>No content to preview</p>
                            <p class="placeholder-hint">Generated content will appear here with SEO highlights</p>
                        </div>
                    </div>
                </div>

                <!-- SEO Issues Panel -->
                <div class="seo-issues-panel" id="seo-issues">
                    <h3>SEO Analysis & Recommendations</h3>
                    <div class="issues-container" id="issues-container">
                        <div class="no-issues">
                            <i class="icon-check-circle"></i>
                            <p>No SEO issues detected</p>
                        </div>
                    </div>
                </div>
            </div>
        `;

        this.addStyles();
    }

    addStyles() {
        if (document.getElementById('seo-preview-styles')) return;

        const styles = document.createElement('style');
        styles.id = 'seo-preview-styles';
        styles.textContent = `
            .seo-preview-wrapper {
                background: #f9fafb;
                border-radius: 0.5rem;
                overflow: hidden;
            }

            .preview-controls {
                display: flex;
                align-items: center;
                gap: 1.5rem;
                padding: 1rem;
                background: white;
                border-bottom: 1px solid #e5e7eb;
                flex-wrap: wrap;
            }

            .control-group {
                display: flex;
                align-items: center;
            }

            .toggle-label {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                cursor: pointer;
                font-size: 0.875rem;
                color: #374151;
            }

            .toggle-slider {
                position: relative;
                width: 2rem;
                height: 1rem;
                background: #d1d5db;
                border-radius: 1rem;
                transition: background 0.2s;
            }

            .toggle-slider::before {
                content: '';
                position: absolute;
                top: 0.125rem;
                left: 0.125rem;
                width: 0.75rem;
                height: 0.75rem;
                background: white;
                border-radius: 50%;
                transition: transform 0.2s;
            }

            input[type="checkbox"]:checked + .toggle-slider {
                background: #3b82f6;
            }

            input[type="checkbox"]:checked + .toggle-slider::before {
                transform: translateX(1rem);
            }

            input[type="checkbox"] {
                display: none;
            }

            .seo-metrics-dashboard {
                padding: 1rem;
                background: white;
                border-bottom: 1px solid #e5e7eb;
            }

            .metrics-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
                gap: 1rem;
            }

            .metric-card {
                padding: 1rem;
                background: #f9fafb;
                border-radius: 0.5rem;
                border: 1px solid #e5e7eb;
            }

            .metric-label {
                font-size: 0.875rem;
                color: #6b7280;
                margin-bottom: 0.25rem;
            }

            .metric-value {
                font-size: 1.5rem;
                font-weight: 700;
                color: #111827;
                margin-bottom: 0.25rem;
            }

            .metric-target {
                font-size: 0.75rem;
                color: #9ca3af;
            }

            .metric-status {
                font-size: 0.75rem;
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                text-align: center;
                margin-top: 0.5rem;
            }

            .metric-status.excellent {
                background: #d1fae5;
                color: #065f46;
            }

            .metric-status.good {
                background: #dbeafe;
                color: #1e40af;
            }

            .metric-status.fair {
                background: #fef3c7;
                color: #92400e;
            }

            .metric-status.poor {
                background: #fee2e2;
                color: #991b1b;
            }

            .meta-preview-section {
                padding: 1rem;
                background: white;
                border-bottom: 1px solid #e5e7eb;
            }

            .meta-preview-section h3,
            .meta-preview-section h4 {
                margin: 0 0 1rem 0;
                color: #111827;
                font-size: 1.125rem;
                font-weight: 600;
            }

            .serp-preview {
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                padding: 1rem;
                background: white;
                margin-bottom: 1rem;
            }

            .serp-title {
                color: #1a0dab;
                font-size: 1.125rem;
                font-weight: 500;
                margin-bottom: 0.25rem;
                cursor: pointer;
            }

            .serp-title:hover {
                text-decoration: underline;
            }

            .serp-url {
                color: #006621;
                font-size: 0.875rem;
                margin-bottom: 0.5rem;
            }

            .serp-description {
                color: #545454;
                font-size: 0.875rem;
                line-height: 1.4;
            }

            .social-preview {
                margin-top: 1rem;
            }

            .og-preview {
                display: flex;
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                overflow: hidden;
                background: white;
                max-width: 500px;
            }

            .og-image-placeholder {
                width: 120px;
                height: 120px;
                background: #f3f4f6;
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                color: #9ca3af;
                font-size: 0.75rem;
                flex-shrink: 0;
            }

            .og-content {
                padding: 0.75rem;
                flex: 1;
            }

            .og-title {
                font-weight: 600;
                color: #111827;
                margin-bottom: 0.25rem;
                font-size: 0.875rem;
            }

            .og-description {
                color: #6b7280;
                font-size: 0.75rem;
                margin-bottom: 0.25rem;
                line-height: 1.3;
            }

            .og-site {
                color: #9ca3af;
                font-size: 0.75rem;
                text-transform: uppercase;
            }

            .content-preview-section {
                padding: 1rem;
                background: white;
                border-bottom: 1px solid #e5e7eb;
            }

            .preview-header {
                display: flex;
                justify-content: space-between;
                align-items: center;
                margin-bottom: 1rem;
                flex-wrap: wrap;
                gap: 1rem;
            }

            .preview-header h3 {
                margin: 0;
                color: #111827;
                font-size: 1.125rem;
                font-weight: 600;
            }

            .highlight-legend {
                display: flex;
                gap: 1rem;
                flex-wrap: wrap;
            }

            .legend-item {
                display: flex;
                align-items: center;
                gap: 0.5rem;
                font-size: 0.75rem;
                color: #6b7280;
            }

            .highlight-sample {
                width: 1rem;
                height: 0.5rem;
                border-radius: 0.25rem;
            }

            .highlight-sample.keyword {
                background: #fbbf24;
            }

            .highlight-sample.lsi {
                background: #60a5fa;
            }

            .highlight-sample.heading {
                background: #34d399;
            }

            .highlight-sample.issue {
                background: #f87171;
            }

            .content-preview {
                border: 1px solid #e5e7eb;
                border-radius: 0.5rem;
                padding: 1.5rem;
                background: white;
                min-height: 300px;
                max-height: 600px;
                overflow-y: auto;
                line-height: 1.6;
            }

            .preview-placeholder {
                display: flex;
                flex-direction: column;
                align-items: center;
                justify-content: center;
                height: 100%;
                color: #9ca3af;
                text-align: center;
            }

            .preview-placeholder i {
                font-size: 3rem;
                margin-bottom: 1rem;
            }

            .placeholder-hint {
                font-size: 0.875rem;
                margin-top: 0.5rem;
            }

            /* Content Highlights */
            .highlight-keyword {
                background: rgba(251, 191, 36, 0.3);
                padding: 0.125rem 0.25rem;
                border-radius: 0.25rem;
                font-weight: 600;
            }

            .highlight-lsi {
                background: rgba(96, 165, 250, 0.3);
                padding: 0.125rem 0.25rem;
                border-radius: 0.25rem;
            }

            .highlight-heading {
                background: rgba(52, 211, 153, 0.3);
                padding: 0.25rem 0.5rem;
                border-radius: 0.25rem;
                font-weight: 600;
                display: block;
                margin: 1rem 0;
            }

            .highlight-issue {
                background: rgba(248, 113, 113, 0.3);
                border-bottom: 2px wavy #f87171;
                cursor: help;
            }

            .seo-issues-panel {
                padding: 1rem;
                background: white;
            }

            .seo-issues-panel h3 {
                margin: 0 0 1rem 0;
                color: #111827;
                font-size: 1.125rem;
                font-weight: 600;
            }

            .issues-container {
                space-y: 0.5rem;
            }

            .issue-item {
                padding: 0.75rem;
                border-radius: 0.5rem;
                border-left: 4px solid;
                background: #f9fafb;
            }

            .issue-item.high {
                border-left-color: #ef4444;
                background: #fef2f2;
            }

            .issue-item.medium {
                border-left-color: #f59e0b;
                background: #fffbeb;
            }

            .issue-item.low {
                border-left-color: #3b82f6;
                background: #eff6ff;
            }

            .issue-title {
                font-weight: 600;
                color: #111827;
                margin-bottom: 0.25rem;
            }

            .issue-description {
                font-size: 0.875rem;
                color: #6b7280;
            }

            .no-issues {
                text-align: center;
                color: #059669;
                padding: 2rem;
            }

            .no-issues i {
                font-size: 2rem;
                margin-bottom: 0.5rem;
                display: block;
            }

            @media (max-width: 768px) {
                .preview-controls {
                    flex-direction: column;
                    align-items: stretch;
                }

                .control-group {
                    justify-content: space-between;
                }

                .metrics-grid {
                    grid-template-columns: repeat(2, 1fr);
                }

                .preview-header {
                    flex-direction: column;
                    align-items: stretch;
                }

                .highlight-legend {
                    justify-content: center;
                }
            }
        `;

        document.head.appendChild(styles);
    }

    bindEvents() {
        // Toggle controls
        const keywordToggle = this.container.querySelector('#toggle-keywords');
        const issuesToggle = this.container.querySelector('#toggle-seo-issues');
        const metaToggle = this.container.querySelector('#toggle-meta-preview');
        const exportBtn = this.container.querySelector('#export-preview');

        keywordToggle?.addEventListener('change', () => {
            this.options.highlightKeywords = keywordToggle.checked;
            this.updateContentPreview();
        });

        issuesToggle?.addEventListener('change', () => {
            const issuesPanel = this.container.querySelector('#seo-issues');
            if (issuesPanel) {
                issuesPanel.style.display = issuesToggle.checked ? 'block' : 'none';
            }
        });

        metaToggle?.addEventListener('change', () => {
            const metaPanel = this.container.querySelector('#meta-preview');
            if (metaPanel) {
                metaPanel.style.display = metaToggle.checked ? 'block' : 'none';
            }
        });

        exportBtn?.addEventListener('click', () => {
            this.exportPreview();
        });
    }

    // Update content and perform SEO analysis
    async updateContent(content, targetKeyword, options = {}) {
        this.content = content;
        this.targetKeyword = targetKeyword;

        if (!content || !targetKeyword) {
            this.showPlaceholder();
            return;
        }

        // Perform SEO analysis
        if (window.apiClient) {
            try {
                const analysisResult = await window.apiClient.analyzeContent(content, targetKeyword);
                if (analysisResult.success) {
                    this.seoData = analysisResult;
                }
            } catch (error) {
                console.error('SEO analysis failed:', error);
            }
        }

        // Generate meta tags
        if (window.apiClient && options.generateMeta) {
            try {
                const metaResult = await window.apiClient.generateMetaTags(content, targetKeyword, options);
                if (metaResult.success) {
                    this.metaTags = metaResult.metaTags;
                }
            } catch (error) {
                console.error('Meta tag generation failed:', error);
            }
        }

        this.updatePreview();
    }

    updatePreview() {
        this.updateMetrics();
        this.updateMetaPreview();
        this.updateContentPreview();
        this.updateIssuesPanel();
    }

    updateMetrics() {
        if (!this.seoData || !this.seoData.analysis) return;

        const metrics = this.seoData.analysis;
        
        // SEO Score
        const scoreElement = this.container.querySelector('#seo-score');
        const statusElement = this.container.querySelector('#seo-status');
        if (scoreElement && this.seoData.seoScore) {
            const score = this.seoData.seoScore.score;
            scoreElement.textContent = score;
            
            let status = 'poor';
            let statusText = 'Needs Work';
            if (score >= 90) {
                status = 'excellent';
                statusText = 'Excellent';
            } else if (score >= 75) {
                status = 'good';
                statusText = 'Good';
            } else if (score >= 60) {
                status = 'fair';
                statusText = 'Fair';
            }
            
            statusElement.textContent = statusText;
            statusElement.className = `metric-status ${status}`;
        }

        // Word Count
        const wordCountElement = this.container.querySelector('#word-count');
        if (wordCountElement && metrics.wordCount) {
            wordCountElement.textContent = metrics.wordCount.toLocaleString();
        }

        // Keyword Density
        const densityElement = this.container.querySelector('#keyword-density');
        if (densityElement && metrics.keywordDensity !== undefined) {
            densityElement.textContent = `${metrics.keywordDensity.toFixed(1)}%`;
        }

        // Readability
        const readabilityElement = this.container.querySelector('#readability-score');
        if (readabilityElement && metrics.avgWordsPerSentence) {
            // Simple readability calculation
            const score = Math.max(0, 100 - (metrics.avgWordsPerSentence - 15) * 2);
            readabilityElement.textContent = Math.round(score);
        }
    }

    updateMetaPreview() {
        if (!this.metaTags) return;

        // SERP Preview
        const titleElement = this.container.querySelector('#serp-title');
        const urlElement = this.container.querySelector('#serp-url');
        const descriptionElement = this.container.querySelector('#serp-description');

        if (titleElement && this.metaTags.title) {
            titleElement.textContent = this.metaTags.title.primary;
        }

        if (descriptionElement && this.metaTags.metaDescription) {
            descriptionElement.textContent = this.metaTags.metaDescription.primary;
        }

        // Social Media Preview
        const ogTitleElement = this.container.querySelector('#og-title');
        const ogDescriptionElement = this.container.querySelector('#og-description');

        if (ogTitleElement && this.metaTags.openGraph) {
            ogTitleElement.textContent = this.metaTags.openGraph['og:title'];
        }

        if (ogDescriptionElement && this.metaTags.openGraph) {
            ogDescriptionElement.textContent = this.metaTags.openGraph['og:description'];
        }
    }

    updateContentPreview() {
        const previewElement = this.container.querySelector('#content-preview');
        if (!previewElement || !this.content) return;

        let highlightedContent = this.content;

        if (this.options.highlightKeywords && this.targetKeyword) {
            highlightedContent = this.highlightKeywords(highlightedContent);
        }

        highlightedContent = this.highlightHeadings(highlightedContent);
        highlightedContent = this.highlightIssues(highlightedContent);

        previewElement.innerHTML = highlightedContent;
    }

    highlightKeywords(content) {
        if (!this.targetKeyword) return content;

        // Highlight main keyword
        const keywordRegex = new RegExp(`\\b(${this.escapeRegex(this.targetKeyword)})\\b`, 'gi');
        content = content.replace(keywordRegex, '<span class="highlight-keyword">$1</span>');

        // Highlight LSI keywords if available
        if (this.seoData && this.seoData.analysis && this.seoData.analysis.lsiKeywords) {
            this.seoData.analysis.lsiKeywords.forEach(keyword => {
                if (keyword !== this.targetKeyword) {
                    const lsiRegex = new RegExp(`\\b(${this.escapeRegex(keyword)})\\b`, 'gi');
                    content = content.replace(lsiRegex, '<span class="highlight-lsi">$1</span>');
                }
            });
        }

        return content;
    }

    highlightHeadings(content) {
        // Highlight HTML headings
        content = content.replace(/<(h[1-6])[^>]*>(.*?)<\/h[1-6]>/gi, 
            '<span class="highlight-heading">$2</span>');
        
        return content;
    }

    highlightIssues(content) {
        if (!this.seoData || !this.seoData.recommendations) return content;

        // This is a simplified implementation
        // In practice, you'd want more sophisticated issue detection
        
        return content;
    }

    updateIssuesPanel() {
        const issuesContainer = this.container.querySelector('#issues-container');
        if (!issuesContainer) return;

        if (!this.seoData || !this.seoData.recommendations || this.seoData.recommendations.length === 0) {
            issuesContainer.innerHTML = `
                <div class="no-issues">
                    <i class="icon-check-circle"></i>
                    <p>No SEO issues detected</p>
                </div>
            `;
            return;
        }

        const issuesHTML = this.seoData.recommendations.map(issue => {
            const priority = this.getIssuePriority(issue);
            return `
                <div class="issue-item ${priority}">
                    <div class="issue-title">${this.getIssueTitle(issue)}</div>
                    <div class="issue-description">${issue}</div>
                </div>
            `;
        }).join('');

        issuesContainer.innerHTML = issuesHTML;
    }

    getIssuePriority(issue) {
        const lowPriorityKeywords = ['improve', 'consider', 'could', 'might'];
        const highPriorityKeywords = ['missing', 'required', 'must', 'critical'];
        
        const lowerIssue = issue.toLowerCase();
        
        if (highPriorityKeywords.some(keyword => lowerIssue.includes(keyword))) {
            return 'high';
        }
        
        if (lowPriorityKeywords.some(keyword => lowerIssue.includes(keyword))) {
            return 'low';
        }
        
        return 'medium';
    }

    getIssueTitle(issue) {
        if (issue.includes('keyword')) return 'Keyword Optimization';
        if (issue.includes('heading')) return 'Heading Structure';
        if (issue.includes('content')) return 'Content Quality';
        if (issue.includes('meta')) return 'Meta Tags';
        if (issue.includes('readability')) return 'Readability';
        return 'SEO Issue';
    }

    showPlaceholder() {
        const previewElement = this.container.querySelector('#content-preview');
        if (previewElement) {
            previewElement.innerHTML = `
                <div class="preview-placeholder">
                    <i class="icon-document"></i>
                    <p>No content to preview</p>
                    <p class="placeholder-hint">Generated content will appear here with SEO highlights</p>
                </div>
            `;
        }

        // Reset metrics
        this.container.querySelector('#seo-score').textContent = '--';
        this.container.querySelector('#word-count').textContent = '0';
        this.container.querySelector('#keyword-density').textContent = '0%';
        this.container.querySelector('#readability-score').textContent = '--';
    }

    exportPreview() {
        const previewContent = this.container.querySelector('#content-preview').innerHTML;
        const metrics = {
            seoScore: this.container.querySelector('#seo-score').textContent,
            wordCount: this.container.querySelector('#word-count').textContent,
            keywordDensity: this.container.querySelector('#keyword-density').textContent,
            readability: this.container.querySelector('#readability-score').textContent
        };

        const exportData = {
            content: previewContent,
            metrics: metrics,
            keyword: this.targetKeyword,
            timestamp: new Date().toISOString()
        };

        const blob = new Blob([JSON.stringify(exportData, null, 2)], { 
            type: 'application/json' 
        });
        
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = `seo-preview-${Date.now()}.json`;
        document.body.appendChild(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    // Public API methods
    setContent(content, keyword, options = {}) {
        return this.updateContent(content, keyword, options);
    }

    getMetrics() {
        return this.seoData;
    }

    getMetaTags() {
        return this.metaTags;
    }

    show() {
        this.container.style.display = 'block';
    }

    hide() {
        this.container.style.display = 'none';
    }

    destroy() {
        if (this.container) {
            this.container.innerHTML = '';
        }
        
        const styles = document.getElementById('seo-preview-styles');
        if (styles) {
            styles.remove();
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SEOContentPreview;
} else {
    window.SEOContentPreview = SEOContentPreview;
}
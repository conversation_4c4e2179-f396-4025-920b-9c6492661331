(()=>{var e={};e.id=654,e.ids=[654],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},7335:(e,t,s)=>{"use strict";s.r(t),s.d(t,{headerHooks:()=>f,originalPathname:()=>q,patchFetch:()=>y,requestAsyncStorage:()=>h,routeModule:()=>g,serverHooks:()=>x,staticGenerationAsyncStorage:()=>_,staticGenerationBailout:()=>I});var r={};s.r(r),s.d(r,{POST:()=>m});var a=s(5419),i=s(9108),o=s(9678),n=s(8070),u=s(2045),c=s(6517),p=s(5252),l=s(2178);let d=p.Ry({email:p.Z_().email("Invalid email address"),password:p.Z_().min(6,"Password must be at least 6 characters"),fullName:p.Z_().min(2,"Full name must be at least 2 characters").optional()});async function m(e){try{let t=await e.json(),{email:s,password:r,fullName:a}=d.parse(t),i=(0,u.jq)(),{data:o,error:p}=await i.auth.signInWithPassword({email:s,password:"dummy-password"}),{data:l,error:m}=await i.auth.signUp({email:s,password:r,options:{data:{full_name:a}}});if(m)return await (0,c.Tj)({action_type:"signup_failed",action_details:{email:s,error:m.message},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),n.Z.json({error:m.message},{status:400});if(l.user)try{await c.pR.from("user_subscriptions").insert({user_id:l.user.id,plan_type:"free",status:"active",current_period_start:new Date().toISOString(),current_period_end:new Date(Date.now()+31536e6).toISOString()})}catch(e){console.error("Error creating initial subscription:",e)}return await (0,c.Tj)({user_id:l.user?.id,action_type:"signup_success",action_details:{email:s,full_name:a,method:"email_password"},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),n.Z.json({success:!0,user:l.user,session:l.session,message:l.user?.email_confirmed_at?"Account created successfully":"Please check your email to confirm your account"})}catch(e){if(console.error("Sign-up error:",e),e instanceof l.jm)return n.Z.json({error:"Invalid input",details:e.errors},{status:400});return n.Z.json({error:"Internal server error"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/signup/route",pathname:"/api/auth/signup",filename:"route",bundlePath:"app/api/auth/signup/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/auth/signup/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:_,serverHooks:x,headerHooks:f,staticGenerationBailout:I}=g,q="/api/auth/signup/route";function y(){return(0,o.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:_})}},2045:(e,t,s)=>{"use strict";s.d(t,{jq:()=>i});var r=s(7699),a=s(2455);let i=()=>(0,r.createServerComponentClient)({cookies:a.cookies})},6517:(e,t,s)=>{"use strict";s.d(t,{Tj:()=>u,pR:()=>o,xc:()=>n});var r=s(1971),a=s(7699);(0,r.eI)("https://zqrmpanonghggoxdjirq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}});let i=()=>(0,a.createClientComponentClient)(),o=(0,r.eI)("https://zqrmpanonghggoxdjirq.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}});async function n(e){let t=i(),{error:s}=await t.from("api_usage_logs").insert(e);s&&console.error("Error logging API usage:",s)}async function u(e){let t=i(),{error:s}=await t.from("user_activity_logs").insert(e);s&&console.error("Error logging user activity:",s)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,280,252],()=>s(7335));module.exports=r})();
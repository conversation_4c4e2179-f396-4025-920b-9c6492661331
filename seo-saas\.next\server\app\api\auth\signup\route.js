(()=>{var e={};e.id=654,e.ids=[654],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},7335:(e,r,t)=>{"use strict";t.r(r),t.d(r,{headerHooks:()=>v,originalPathname:()=>x,patchFetch:()=>b,requestAsyncStorage:()=>S,routeModule:()=>g,serverHooks:()=>h,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>y});var s={};t.r(s),t.d(s,{POST:()=>m});var a=t(5419),i=t(9108),n=t(9678),o=t(8070),u=t(2045),c=t(9393),l=t(5252),p=t(2178);let d=l.Ry({email:l.Z_().email("Invalid email address"),password:l.Z_().min(6,"Password must be at least 6 characters"),fullName:l.Z_().min(2,"Full name must be at least 2 characters").optional()});async function m(e){try{let r=await e.json(),{email:t,password:s,fullName:a}=d.parse(r),i=(0,u.jq)(),{data:n,error:l}=await i.auth.signInWithPassword({email:t,password:"dummy-password"}),{data:p,error:m}=await i.auth.signUp({email:t,password:s,options:{data:{full_name:a}}});if(m)return await (0,c.Tj)({action_type:"signup_failed",action_details:{email:t,error:m.message},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),o.Z.json({error:m.message},{status:400});if(p.user&&c.pR)try{await c.pR.from("user_subscriptions").insert({user_id:p.user.id,plan_type:"free",status:"active",current_period_start:new Date().toISOString(),current_period_end:new Date(Date.now()+31536e6).toISOString()})}catch(e){console.error("Error creating initial subscription:",e)}return await (0,c.Tj)({user_id:p.user?.id,action_type:"signup_success",action_details:{email:t,full_name:a,method:"email_password"},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),o.Z.json({success:!0,user:p.user,session:p.session,message:p.user?.email_confirmed_at?"Account created successfully":"Please check your email to confirm your account"})}catch(e){if(console.error("Sign-up error:",e),e instanceof p.jm)return o.Z.json({error:"Invalid input",details:e.errors},{status:400});return o.Z.json({error:"Internal server error"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/signup/route",pathname:"/api/auth/signup",filename:"route",bundlePath:"app/api/auth/signup/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/auth/signup/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:S,staticGenerationAsyncStorage:f,serverHooks:h,headerHooks:v,staticGenerationBailout:y}=g,x="/api/auth/signup/route";function b(){return(0,n.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:f})}},6843:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createProxy",{enumerable:!0,get:function(){return s}});let s=t(8195).createClientModuleProxy},482:(e,r,t)=>{"use strict";e.exports=t(399)},8195:(e,r,t)=>{"use strict";e.exports=t(482).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},2045:(e,r,t)=>{"use strict";t.d(r,{jq:()=>i});var s=t(7699),a=t(2455);let i=()=>(0,s.createServerComponentClient)({cookies:a.cookies})},9393:(e,r,t)=>{"use strict";t.d(r,{xc:()=>h,Tj:()=>v,pR:()=>S});var s=t(1971),a=t(7699),i=t(6843);let n=(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts`),{__esModule:o,$$typeof:u}=n;n.default;let c=(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#validateEnvironment`),l=(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#getEnvironment`);(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#logEnvironmentStatus`);let p=l(),d=c(),m=(e,r)=>{};d.isValid||(["Supabase configuration error:",...d.missingVars.map(e=>`- Missing: ${e}`),...d.errors.map(e=>`- Error: ${e}`)].join("\n"),m("Environment validation failed",{validation:d}));let g=null;try{p.supabaseUrl&&p.supabaseKey?(0,s.eI)(p.supabaseUrl,p.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):m("Cannot create Supabase client: missing URL or key")}catch(e){m("Failed to create Supabase client",e)}try{p.supabaseUrl&&p.supabaseServiceKey&&(g=(0,s.eI)(p.supabaseUrl,p.supabaseServiceKey,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}}))}catch(e){m("Failed to create Supabase admin client",e)}let S=g,f=()=>{try{return(0,a.createClientComponentClient)()}catch(e){throw m("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function h(e){let r=f(),{error:t}=await r.from("api_usage_logs").insert(e);t&&console.error("Error logging API usage:",t)}async function v(e){let r=f(),{error:t}=await r.from("user_activity_logs").insert(e);t&&console.error("Error logging user activity:",t)}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,280,252],()=>t(7335));module.exports=s})();
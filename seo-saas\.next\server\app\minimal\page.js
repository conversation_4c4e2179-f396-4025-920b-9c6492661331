(()=>{var e={};e.id=290,e.ids=[290],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},5311:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>o.a,__next_app__:()=>m,originalPathname:()=>p,pages:()=>c,routeModule:()=>u,tree:()=>l});var s=r(482),n=r(9108),i=r(2563),o=r.n(i),a=r(8300),d={};for(let e in a)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(d[e]=()=>a[e]);r.d(t,d);let l=["",{children:["minimal",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,901)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/minimal/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1342)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],c=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/minimal/page.tsx"],p="/minimal/page",m={require:r,loadChunk:()=>Promise.resolve()},u=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/minimal/page",pathname:"/minimal",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},8775:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},9447:()=>{},5303:()=>{},1342:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(5036);function n({children:e}){return(0,s.jsxs)("html",{lang:"en",children:[s.jsx("head",{children:s.jsx("title",{children:"SEO SAAS App"})}),s.jsx("body",{children:e})]})}r(5023)},901:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(5036);function n(){return s.jsx("div",{style:{minHeight:"100vh",display:"flex",alignItems:"center",justifyContent:"center",backgroundColor:"#f9fafb",fontFamily:"system-ui, sans-serif"},children:(0,s.jsxs)("div",{style:{textAlign:"center",padding:"2rem",backgroundColor:"white",borderRadius:"8px",boxShadow:"0 1px 3px rgba(0,0,0,0.1)"},children:[s.jsx("h1",{style:{fontSize:"2rem",fontWeight:"bold",color:"#111827",marginBottom:"1rem"},children:"Minimal Test Page"}),s.jsx("p",{style:{color:"#6b7280",marginBottom:"2rem"},children:"This page uses no external imports or components."}),s.jsx("div",{style:{padding:"1rem",backgroundColor:"#dcfce7",border:"1px solid #bbf7d0",borderRadius:"6px",marginBottom:"1rem"},children:s.jsx("strong",{style:{color:"#166534"},children:"✅ Basic React: Working"})}),s.jsx("div",{style:{padding:"1rem",backgroundColor:"#dbeafe",border:"1px solid #93c5fd",borderRadius:"6px",marginBottom:"1rem"},children:s.jsx("strong",{style:{color:"#1d4ed8"},children:"✅ Next.js Routing: Working"})}),s.jsx("div",{style:{padding:"1rem",backgroundColor:"#fef3c7",border:"1px solid #fcd34d",borderRadius:"6px"},children:s.jsx("strong",{style:{color:"#92400e"},children:"⚠️ Testing Component Imports"})})]})})}},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(337);let n=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,312,337],()=>r(5311));module.exports=s})();
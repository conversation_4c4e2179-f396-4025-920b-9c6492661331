(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[642,183],{8357:function(e,t,r){Promise.resolve().then(r.bind(r,6953))},6953:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return l}});var n=r(7437),a=r(2265),s=r(5183),i=r(5671),c=r(575),o=r(3277);function l(){let[e,t]=a.useState("testing"),[r,l]=a.useState(null),[u,d]=a.useState(null),f=async()=>{t("testing"),l(null);try{if(!s.OQ)throw Error("Supabase client not initialized");let{data:e,error:r}=await s.OQ.from("profiles").select("count",{count:"exact",head:!0});if(r)throw r;d((null==e?void 0:e.length)||0),t("connected")}catch(e){l(e.message||"Unknown error"),t("error")}};return a.useEffect(()=>{f()},[]),(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,n.jsxs)(i.Zb,{className:"w-full max-w-md",children:[(0,n.jsxs)(i.Ol,{className:"text-center",children:[(0,n.jsx)(i.ll,{children:"Supabase Connection Test"}),(0,n.jsx)(i.SZ,{children:"Testing the connection to your Supabase database"})]}),(0,n.jsxs)(i.aY,{className:"space-y-4",children:[(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)(o.Ct,{variant:(()=>{switch(e){case"connected":return"success";case"error":return"destructive";default:return"secondary"}})(),className:"text-lg px-4 py-2",children:(()=>{switch(e){case"connected":return"Connected";case"error":return"Error";default:return"Testing..."}})()})}),"connected"===e&&(0,n.jsxs)("div",{className:"text-center space-y-2",children:[(0,n.jsx)("p",{className:"text-green-600 font-medium",children:"✅ Successfully connected to Supabase!"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Database is accessible and ready for use."})]}),"error"===e&&(0,n.jsxs)("div",{className:"text-center space-y-2",children:[(0,n.jsx)("p",{className:"text-red-600 font-medium",children:"❌ Connection failed"}),(0,n.jsx)("p",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:r})]}),"testing"===e&&(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Testing connection..."})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"font-medium",children:"Environment Variables:"}),(0,n.jsxs)("div",{className:"text-sm space-y-1",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Supabase URL:"}),(0,n.jsx)(o.Ct,{variant:"outline",children:"✅ Set"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Anon Key:"}),(0,n.jsx)(o.Ct,{variant:"outline",children:"✅ Set"})]})]})]}),(0,n.jsx)(c.z,{onClick:f,className:"w-full",disabled:"testing"===e,children:"testing"===e?"Testing...":"Test Again"}),(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("a",{href:"/",className:"text-blue-600 hover:text-blue-500 text-sm",children:"← Back to Home"})})]})]})})}},3277:function(e,t,r){"use strict";r.d(t,{Ct:function(){return c},Dk:function(){return o},U2:function(){return l}});var n=r(7437);r(2265);var a=r(6061),s=r(2169);let i=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",error:"border-transparent bg-red-100 text-red-800 hover:bg-red-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function c(e){let{className:t,variant:r,...a}=e;return(0,n.jsx)("div",{className:(0,s.cn)(i({variant:r}),t),...a})}function o(e){let{score:t,className:r}=e;return(0,n.jsxs)(c,{variant:t>=80?"success":t>=60?"warning":t>=40?"error":"destructive",className:r,children:[t,"/100"]})}function l(e){let{priority:t,className:r}=e;return(0,n.jsx)(c,{variant:(e=>{switch(e){case"high":return"error";case"medium":return"warning";case"low":return"success";default:return"secondary"}})(t),className:r,children:t.toUpperCase()})}},575:function(e,t,r){"use strict";r.d(t,{z:function(){return l}});var n=r(7437),a=r(2265),s=r(4949),i=r(6061),c=r(2169);let o=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,loading:l,children:u,disabled:d,asChild:f=!1,...m}=e,h=f?s.g7:"button";return(0,n.jsxs)(h,{className:(0,c.cn)(o({variant:a,size:i,className:r})),ref:t,disabled:d||l,...m,children:[l&&(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),u]})});l.displayName="Button"},5671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return c},SZ:function(){return l},Zb:function(){return i},aY:function(){return u},ll:function(){return o}});var n=r(7437),a=r(2265),s=r(2169);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...a})});c.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...a})});l.displayName="CardDescription";let u=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...a})});u.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},5183:function(e,t,r){"use strict";r.d(t,{createSupabaseComponentClient:function(){return u},signIn:function(){return d},signInWithGitHub:function(){return h},signInWithGoogle:function(){return m},signUp:function(){return f},OQ:function(){return l}});var n=r(1492),a=r(3082);let s={supabaseUrl:"https://xpcbyzcaidfukddqniny.supabase.co",supabaseKey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A",supabaseServiceKey:""},i=function(){let e={NEXT_PUBLIC_SUPABASE_URL:"https://xpcbyzcaidfukddqniny.supabase.co",NEXT_PUBLIC_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A"},t=[],r=[];if(Object.entries(e).forEach(e=>{let[r,n]=e;n&&""!==n.trim()||t.push(r)}),e.NEXT_PUBLIC_SUPABASE_URL)try{new URL(e.NEXT_PUBLIC_SUPABASE_URL)}catch(e){r.push("NEXT_PUBLIC_SUPABASE_URL is not a valid URL")}if(e.NEXT_PUBLIC_SUPABASE_ANON_KEY){let t=e.NEXT_PUBLIC_SUPABASE_ANON_KEY;t.startsWith("eyJ")&&3===t.split(".").length||r.push("NEXT_PUBLIC_SUPABASE_ANON_KEY appears to be invalid (not a JWT token)")}return{isValid:0===t.length&&0===r.length,missingVars:t,errors:r,vars:e}}(),c=(e,t)=>{};i.isValid||(["Supabase configuration error:",...i.missingVars.map(e=>"- Missing: ".concat(e)),...i.errors.map(e=>"- Error: ".concat(e))].join("\n"),c("Environment validation failed",{validation:i}));let o=null;try{s.supabaseUrl&&s.supabaseKey?o=(0,n.eI)(s.supabaseUrl,s.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):c("Cannot create Supabase client: missing URL or key")}catch(e){c("Failed to create Supabase client",e)}try{s.supabaseUrl&&s.supabaseServiceKey}catch(e){c("Failed to create Supabase admin client",e)}let l=o,u=()=>{try{return(0,a.createClientComponentClient)()}catch(e){throw c("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function d(e,t){let r=u(),{data:n,error:a}=await r.auth.signInWithPassword({email:e,password:t});if(a)throw Error(a.message);return n}async function f(e,t,r){let n=u(),{data:a,error:s}=await n.auth.signUp({email:e,password:t,options:{data:{full_name:r},emailRedirectTo:"".concat("http://localhost:3000","/auth/callback")}});if(s)throw Error(s.message);return a}async function m(){let e=u(),{data:t,error:r}=await e.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat("http://localhost:3000","/auth/callback"),queryParams:{access_type:"offline",prompt:"consent"}}});if(r)throw Error(r.message);return t}async function h(){let e=u(),{data:t,error:r}=await e.auth.signInWithOAuth({provider:"github",options:{redirectTo:"".concat("http://localhost:3000","/auth/callback")}});if(r)throw Error(r.message);return t}},2169:function(e,t,r){"use strict";r.d(t,{KG:function(){return u},cn:function(){return s},p6:function(){return c},rl:function(){return o},uf:function(){return i},vQ:function(){return l}});var n=r(7042),a=r(4769);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}function i(e){return e.toLocaleString()}function c(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function o(e){return"".concat(e.toFixed(1),"%")}function l(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");return t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),Promise.resolve()}function u(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"text/plain",n=new Blob([e],{type:r}),a=URL.createObjectURL(n),s=document.createElement("a");s.href=a,s.download=t,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(a)}}},function(e){e.O(0,[14,82,971,938,744],function(){return e(e.s=8357)}),_N_E=e.O()}]);
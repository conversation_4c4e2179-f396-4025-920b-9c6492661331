(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[474],{1467:function(e,t,r){Promise.resolve().then(r.bind(r,1653))},1653:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return h}});var a=r(7437),s=r(2265),n=r(9897),i=r(5671),l=r(575),o=r(2782),c=r(6083),d=r(4440),m=r(4020),p=r(6610),u=r(4713);let y=[{value:"US",label:"United States"},{value:"UK",label:"United Kingdom"},{value:"CA",label:"Canada"},{value:"AU",label:"Australia"},{value:"DE",label:"Germany"},{value:"FR",label:"France"}];function h(){let[e,t]=s.useState(0),[r,h]=s.useState("url"),[x,g]=s.useState(!1),[v,f]=s.useState(null),[j,w]=s.useState(null),[N,b]=s.useState({url:"",content:"",keyword:"",location:"US",competitorUrls:[]}),[k,S]=s.useState(""),[A,C]=s.useState({}),U=()=>{var e,t;let a={};if(N.keyword.trim()||(a.keyword="Target keyword is required"),"url"===r){if(null===(e=N.url)||void 0===e?void 0:e.trim())try{new URL(N.url)}catch(e){a.url="Please enter a valid URL"}else a.url="URL is required"}else(null===(t=N.content)||void 0===t?void 0:t.trim())?N.content.length<100&&(a.content="Content must be at least 100 characters"):a.content="Content is required";return C(a),0===Object.keys(a).length},z=async e=>{if(e.preventDefault(),U()){g(!0),w(null),t(1);try{await new Promise(e=>setTimeout(e,3e3)),f({overallScore:73,keywordAnalysis:{score:68,primaryKeywordDensity:1.8,secondaryKeywordDensity:.9,recommendations:[{type:"keyword",priority:"high",description:"Increase primary keyword density to 2-3% for better optimization",impact:8},{type:"keyword",priority:"medium",description:"Add more LSI keywords to improve semantic relevance",impact:6}]},headingAnalysis:{score:82,totalHeadings:8,optimizedHeadings:6,hierarchy:{isValid:!0,issues:[]},recommendations:[{type:"structure",priority:"medium",description:"Add target keyword to H2 headings for better optimization",impact:5}]},contentQuality:{score:69,wordCount:847,readabilityScore:74,uniqueWords:312,recommendations:[{type:"content",priority:"high",description:"Expand content to 1200+ words to match top competitors",impact:9},{type:"readability",priority:"low",description:"Content readability is good, maintain current style",impact:3}]},competitorAnalysis:{averageWordCount:1250,averageScore:78,topPerformerScore:89,gaps:["Missing FAQ section that competitors have","Lack of internal linking structure","No schema markup implementation"],opportunities:[{type:"content",description:"Add comprehensive FAQ section to match competitor content depth",impact:"high"},{type:"technical",description:"Implement schema markup for better SERP features",impact:"medium"}]},lsiKeywords:[{keyword:"search engine optimization",relevanceScore:.92,frequency:3,category:"primary"},{keyword:"digital marketing",relevanceScore:.85,frequency:2,category:"secondary"},{keyword:"content strategy",relevanceScore:.78,frequency:1,category:"supporting"},{keyword:"keyword research",relevanceScore:.88,frequency:2,category:"secondary"},{keyword:"SERP ranking",relevanceScore:.82,frequency:1,category:"supporting"}],recommendations:[{type:"content",priority:"high",title:"Expand Content Length",description:"Increase content to 1200+ words to match competitor standards and improve ranking potential",expectedImpact:9},{type:"keyword",priority:"high",title:"Optimize Keyword Density",description:"Increase primary keyword density to 2-3% while maintaining natural flow",expectedImpact:8},{type:"structure",priority:"medium",title:"Add FAQ Section",description:"Include comprehensive FAQ section to match competitor content depth",expectedImpact:7},{type:"technical",priority:"medium",title:"Implement Schema Markup",description:"Add structured data markup for better SERP features and visibility",expectedImpact:6}]}),t(2)}catch(e){w("Failed to analyze content. Please try again."),console.error("Analysis error:",e)}finally{g(!1)}}},E=e=>{b(t=>({...t,competitorUrls:t.competitorUrls.filter((t,r)=>r!==e)}))},R=()=>{t(0),f(null),w(null),b({url:"",content:"",keyword:"",location:"US",competitorUrls:[]})};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold text-gray-900",children:"SEO Analysis"}),(0,a.jsx)("p",{className:"text-gray-600 mt-2",children:"Analyze your content's SEO performance and get actionable recommendations"})]}),(0,a.jsx)(d.Kl,{steps:["Configure","Analyze","Results"],currentStep:e}),j&&(0,a.jsx)(i.Zb,{className:"border-red-200 bg-red-50",children:(0,a.jsx)(i.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[(0,a.jsx)("div",{className:"text-red-600",children:"⚠️"}),(0,a.jsx)("p",{className:"text-red-800",children:j}),(0,a.jsx)(l.z,{variant:"outline",size:"sm",onClick:R,children:"Try Again"})]})})}),0===e&&(0,a.jsxs)(i.Zb,{className:"w-full max-w-4xl mx-auto",children:[(0,a.jsxs)(i.Ol,{children:[(0,a.jsx)(i.ll,{children:"Configure SEO Analysis"}),(0,a.jsx)(i.SZ,{children:"Enter your content details and target keywords for comprehensive analysis"})]}),(0,a.jsx)(i.aY,{children:(0,a.jsxs)("form",{onSubmit:z,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Analysis Type"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsx)(l.z,{type:"button",variant:"url"===r?"default":"outline",onClick:()=>h("url"),className:"h-auto p-4 justify-start",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(m.Z,{className:"h-5 w-5 mt-1"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-medium",children:"Analyze URL"}),(0,a.jsx)("div",{className:"text-sm opacity-70",children:"Analyze an existing webpage"})]})]})}),(0,a.jsx)(l.z,{type:"button",variant:"content"===r?"default":"outline",onClick:()=>h("content"),className:"h-auto p-4 justify-start",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[(0,a.jsx)(p.Z,{className:"h-5 w-5 mt-1"}),(0,a.jsxs)("div",{className:"text-left",children:[(0,a.jsx)("div",{className:"font-medium",children:"Analyze Content"}),(0,a.jsx)("div",{className:"text-sm opacity-70",children:"Paste content directly"})]})]})})]})]}),"url"===r?(0,a.jsx)(o.I,{label:"Website URL",placeholder:"https://example.com/page",value:N.url||"",onChange:e=>b(t=>({...t,url:e.target.value})),error:A.url,required:!0,helperText:"Enter the URL you want to analyze"}):(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("label",{className:"text-sm font-medium",children:"Content to Analyze"}),(0,a.jsx)("textarea",{placeholder:"Paste your content here...",value:N.content||"",onChange:e=>b(t=>({...t,content:e.target.value})),className:"w-full h-40 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0}),A.content&&(0,a.jsx)("p",{className:"text-sm text-red-600",children:A.content}),(0,a.jsx)("p",{className:"text-sm text-gray-500",children:"Minimum 100 characters required"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[(0,a.jsx)(o.I,{label:"Target Keyword",placeholder:"e.g., SEO services",value:N.keyword,onChange:e=>b(t=>({...t,keyword:e.target.value})),error:A.keyword,required:!0,helperText:"The main keyword you want to rank for"}),(0,a.jsx)(c.P,{label:"Target Location",options:y,value:N.location,onChange:e=>b(t=>({...t,location:e})),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("label",{className:"text-sm font-medium",children:["Competitor URLs (Optional)",(0,a.jsx)("span",{className:"text-gray-500 ml-1",children:"- Up to 5 URLs for comparison"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[(0,a.jsx)(o.I,{placeholder:"https://competitor.com",value:k,onChange:e=>S(e.target.value),error:A.competitorUrl,className:"flex-1"}),(0,a.jsx)(l.z,{type:"button",variant:"outline",onClick:()=>{if(k.trim()&&N.competitorUrls.length<5)try{new URL(k),b(e=>({...e,competitorUrls:[...e.competitorUrls,k.trim()]})),S("")}catch(e){C(e=>({...e,competitorUrl:"Please enter a valid URL"}))}},disabled:!k.trim()||N.competitorUrls.length>=5,children:"Add"})]}),N.competitorUrls.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)("p",{className:"text-sm text-gray-600",children:"Competitor URLs to analyze:"}),(0,a.jsx)("div",{className:"space-y-2",children:N.competitorUrls.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded",children:[(0,a.jsx)("span",{className:"text-sm truncate flex-1",children:e}),(0,a.jsx)(l.z,{type:"button",variant:"ghost",size:"sm",onClick:()=>E(t),className:"ml-2",children:"Remove"})]},t))})]})]}),(0,a.jsx)("div",{className:"flex justify-end",children:(0,a.jsx)(l.z,{type:"submit",loading:x,disabled:x,className:"min-w-[120px]",children:x?"Analyzing...":"Start Analysis"})})]})})]}),x&&(0,a.jsx)(i.Zb,{children:(0,a.jsx)(i.aY,{className:"p-8",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[(0,a.jsx)("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"}),(0,a.jsxs)("div",{className:"text-center",children:[(0,a.jsx)("h3",{className:"text-lg font-medium",children:"Analyzing Your Content..."}),(0,a.jsx)("p",{className:"text-gray-600",children:"Running comprehensive SEO analysis and competitor comparison"})]})]})})}),v&&e>=2&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)(n.D,{results:v}),(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[(0,a.jsx)(l.z,{variant:"outline",onClick:R,children:"Analyze New Content"}),(0,a.jsxs)(l.z,{children:[(0,a.jsx)(u.Z,{className:"h-4 w-4 mr-2"}),"Generate Optimized Content"]})]})]})]})}},4020:function(e,t,r){"use strict";var a=r(2265);let s=a.forwardRef(function({title:e,titleId:t,...r},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":t},r),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))});t.Z=s}},function(e){e.O(0,[696,172,217,971,938,744],function(){return e(e.s=1467)}),_N_E=e.O()}]);
// SEO Analysis API Route
// Main endpoint for comprehensive SEO content analysis

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { seoEngine } from '@/lib/services/seo-engine';
import { withRateLimit, withSubscriptionLimit, withCostLimit } from '@/lib/rate-limiter';
import { createSupabaseServerClient } from '@/lib/supabase-server';

// Request validation schema
const seoAnalysisSchema = z.object({
  keyword: z.string().min(1, 'Keyword is required').max(100, 'Keyword too long'),
  location: z.string().max(100, 'Location too long').optional().default(''),
  industry: z.string().min(1, 'Industry is required').max(50, 'Industry too long'),
  contentType: z.enum(['service', 'blog', 'product', 'landing', 'category', 'faq']),
  tone: z.enum(['professional', 'conversational', 'authoritative', 'friendly', 'technical', 'casual']),
  intent: z.enum(['informational', 'commercial', 'transactional', 'navigational']),
  targetWordCount: z.number().min(200).max(5000).optional(),
  includeCompetitorAnalysis: z.boolean().default(true),
  includeContentGeneration: z.boolean().default(true),
  customRequirements: z.array(z.string()).optional(),
});

// Response validation schema
const seoAnalysisResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    keyword: z.string(),
    location: z.string(),
    competitorAnalysis: z.object({
      difficulty: z.enum(['low', 'medium', 'high', 'very-high']),
      averageMetrics: z.object({
        wordCount: z.number(),
        keywordDensity: z.number(),
        domainAuthority: z.number(),
      }),
      topCompetitors: z.array(z.object({
        domain: z.string(),
        position: z.number(),
        title: z.string(),
        opportunityScore: z.number(),
      })),
    }),
    seoScore: z.object({
      total: z.number(),
      improvementPotential: z.number(),
    }),
    timeline: z.object({
      totalDuration: z.number(),
      steps: z.array(z.object({
        step: z.string(),
        duration: z.number(),
        status: z.enum(['completed', 'failed', 'skipped']),
      })),
    }),
  }).optional(),
  error: z.string().optional(),
  usage: z.object({
    creditsUsed: z.number(),
    costUSD: z.number(),
    rateLimitRemaining: z.number(),
  }).optional(),
});

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = seoAnalysisSchema.parse(body);

    // Get user session
    const supabase = createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user subscription tier
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier, credits_remaining, monthly_spend')
      .eq('id', user.id)
      .single();

    const subscriptionTier = profile?.subscription_tier || 'free';
    const creditsRemaining = profile?.credits_remaining || 0;
    const monthlySpend = profile?.monthly_spend || 0;

    // Estimate costs
    const estimatedCost = calculateEstimatedCost(validatedData);
    const estimatedCredits = calculateEstimatedCredits(validatedData);

    // Check subscription limits
    const subscriptionCheck = await withSubscriptionLimit(
      user.id,
      subscriptionTier,
      'daily',
      async () => ({ allowed: true })
    ).catch(error => ({ allowed: false, error: error.message }));

    if (!subscriptionCheck.allowed) {
      return NextResponse.json(
        { 
          success: false, 
          error: subscriptionCheck.error || 'Subscription limit exceeded',
          usage: {
            creditsUsed: 0,
            costUSD: 0,
            rateLimitRemaining: 0,
          }
        },
        { status: 429 }
      );
    }

    // Check cost limits
    const costCheck = await withCostLimit(
      user.id,
      subscriptionTier,
      estimatedCost,
      async () => ({ allowed: true })
    ).catch(error => ({ allowed: false, error: error.message }));

    if (!costCheck.allowed) {
      return NextResponse.json(
        { 
          success: false, 
          error: costCheck.error || 'Cost limit exceeded',
          usage: {
            creditsUsed: 0,
            costUSD: estimatedCost,
            rateLimitRemaining: 0,
          }
        },
        { status: 402 }
      );
    }

    // Check API rate limits
    const rateCheck = await withRateLimit(
      user.id,
      subscriptionTier,
      'groq',
      async () => ({ allowed: true })
    ).catch(error => ({ allowed: false, error: error.message }));

    if (!rateCheck.allowed) {
      return NextResponse.json(
        { 
          success: false, 
          error: rateCheck.error || 'Rate limit exceeded',
          usage: {
            creditsUsed: 0,
            costUSD: 0,
            rateLimitRemaining: 0,
          }
        },
        { status: 429 }
      );
    }

    // Perform SEO analysis
    console.log('Starting SEO analysis for user:', user.id, {
      keyword: validatedData.keyword,
      contentType: validatedData.contentType,
      estimatedCost,
    });

    const analysisResult = await seoEngine.analyzeSEO(validatedData, user.id);

    // Update user credits and spending
    await updateUserUsage(supabase, user.id, estimatedCredits, estimatedCost);

    // Log successful analysis
    await logAnalysisUsage(supabase, user.id, validatedData, analysisResult, estimatedCost);

    const responseTime = Date.now() - startTime;

    // Return successful response
    const response = {
      success: true,
      data: {
        keyword: analysisResult.keyword,
        location: analysisResult.location,
        competitorAnalysis: {
          difficulty: analysisResult.competitorAnalysis.difficulty,
          averageMetrics: {
            wordCount: analysisResult.competitorAnalysis.averageMetrics.wordCount,
            keywordDensity: analysisResult.competitorAnalysis.averageMetrics.keywordDensity,
            domainAuthority: analysisResult.competitorAnalysis.averageMetrics.domainAuthority,
          },
          topCompetitors: analysisResult.competitorAnalysis.topCompetitors.map(comp => ({
            domain: comp.domain,
            position: comp.position,
            title: comp.title,
            opportunityScore: comp.opportunityScore,
          })),
        },
        contentRecommendations: analysisResult.contentRecommendations,
        generatedContent: analysisResult.generatedContent,
        seoScore: {
          total: analysisResult.seoScore.total,
          breakdown: analysisResult.seoScore.breakdown,
          comparison: analysisResult.seoScore.comparison,
          improvementPotential: analysisResult.seoScore.improvementPotential,
        },
        optimization: analysisResult.optimization,
        timeline: {
          totalDuration: analysisResult.timeline.totalDuration,
          steps: analysisResult.timeline.steps,
        },
      },
      usage: {
        creditsUsed: estimatedCredits,
        costUSD: estimatedCost,
        rateLimitRemaining: 100, // This would come from rate limiter
      },
    };

    console.log('SEO analysis completed successfully', {
      userId: user.id,
      keyword: validatedData.keyword,
      responseTime,
      seoScore: analysisResult.seoScore.total,
    });

    return NextResponse.json(response, {
      status: 200,
      headers: {
        'X-Response-Time': responseTime.toString(),
        'X-SEO-Score': analysisResult.seoScore.total.toString(),
        'X-Credits-Used': estimatedCredits.toString(),
      },
    });

  } catch (error) {
    console.error('SEO analysis error:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    // Handle other errors
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    
    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        usage: {
          creditsUsed: 0,
          costUSD: 0,
          rateLimitRemaining: 0,
        },
      },
      { status: 500 }
    );
  }
}

// Helper functions

function calculateEstimatedCost(data: z.infer<typeof seoAnalysisSchema>): number {
  let cost = 0;
  
  // Base analysis cost
  cost += 0.05;
  
  // Competitor analysis cost (if enabled)
  if (data.includeCompetitorAnalysis) {
    cost += 0.03; // Serper API cost
  }
  
  // Content generation cost (if enabled)
  if (data.includeContentGeneration) {
    const wordCount = data.targetWordCount || 800;
    const tokens = Math.floor(wordCount * 1.3); // Estimate tokens from words
    cost += (tokens / 1000) * 0.05; // Groq API cost estimate
  }
  
  return Math.round(cost * 100) / 100; // Round to 2 decimal places
}

function calculateEstimatedCredits(data: z.infer<typeof seoAnalysisSchema>): number {
  let credits = 1; // Base credit for analysis
  
  if (data.includeCompetitorAnalysis) credits += 1;
  if (data.includeContentGeneration) credits += 2;
  
  return credits;
}

async function updateUserUsage(
  supabase: any,
  userId: string,
  creditsUsed: number,
  costUSD: number
) {
  const { error } = await supabase.rpc('update_user_usage', {
    user_id: userId,
    credits_used: creditsUsed,
    cost_usd: costUSD,
  });

  if (error) {
    console.error('Failed to update user usage:', error);
  }
}

async function logAnalysisUsage(
  supabase: any,
  userId: string,
  requestData: any,
  result: any,
  cost: number
) {
  const { error } = await supabase
    .from('seo_analyses')
    .insert({
      user_id: userId,
      keyword: requestData.keyword,
      location: requestData.location,
      industry: requestData.industry,
      content_type: requestData.contentType,
      seo_score: result.seoScore.total,
      difficulty: result.competitorAnalysis.difficulty,
      cost_usd: cost,
      created_at: new Date().toISOString(),
    });

  if (error) {
    console.error('Failed to log analysis usage:', error);
  }
}

// GET endpoint for analysis status/history
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = parseInt(searchParams.get('limit') || '10');
    const offset = parseInt(searchParams.get('offset') || '0');

    // Get user session
    const supabase = createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user's analysis history
    const { data: analyses, error } = await supabase
      .from('seo_analyses')
      .select(`
        id,
        keyword,
        location,
        industry,
        content_type,
        seo_score,
        difficulty,
        cost_usd,
        created_at
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      console.error('Failed to fetch analysis history:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch analysis history' },
        { status: 500 }
      );
    }

    // Get user stats
    const { data: stats } = await supabase
      .from('profiles')
      .select('credits_remaining, monthly_spend, total_analyses')
      .eq('id', user.id)
      .single();

    return NextResponse.json({
      success: true,
      data: {
        analyses,
        stats: {
          creditsRemaining: stats?.credits_remaining || 0,
          monthlySpend: stats?.monthly_spend || 0,
          totalAnalyses: stats?.total_analyses || 0,
        },
        pagination: {
          limit,
          offset,
          hasMore: analyses.length === limit,
        },
      },
    });

  } catch (error) {
    console.error('GET /api/seo/analyze error:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
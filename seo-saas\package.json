{"name": "seo-saas", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "lint:fix": "next lint --fix", "format": "prettier --write .", "format:check": "prettier --check .", "type-check": "tsc --noEmit", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage"}, "dependencies": {"react": "^19.0.0", "react-dom": "^19.0.0", "next": "15.3.5", "@supabase/supabase-js": "^2.39.0", "@supabase/auth-helpers-nextjs": "^0.8.7", "axios": "^1.6.2", "groq-sdk": "^0.3.3", "lucide-react": "^0.295.0", "recharts": "^2.8.0", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "tailwind-merge": "^2.1.0"}, "devDependencies": {"typescript": "^5", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@tailwindcss/postcss": "^4", "tailwindcss": "^4", "eslint": "^9", "eslint-config-next": "15.3.5", "@eslint/eslintrc": "^3", "prettier": "^3.1.0", "eslint-config-prettier": "^9.1.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "jest": "^29.7.0", "jest-environment-jsdom": "^29.7.0"}}
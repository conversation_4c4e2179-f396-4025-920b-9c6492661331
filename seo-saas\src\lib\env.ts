// Environment Variable Validation and Management
'use client';

// Environment validation with detailed error messages
export function validateEnvironment() {
  const requiredEnvVars = {
    NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL,
    NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
  };

  const missingVars: string[] = [];
  const errors: string[] = [];

  // Check for missing variables
  Object.entries(requiredEnvVars).forEach(([key, value]) => {
    if (!value || value.trim() === '') {
      missingVars.push(key);
    }
  });

  // Validate URL format
  if (requiredEnvVars.NEXT_PUBLIC_SUPABASE_URL) {
    try {
      new URL(requiredEnvVars.NEXT_PUBLIC_SUPABASE_URL);
    } catch {
      errors.push('NEXT_PUBLIC_SUPABASE_URL is not a valid URL');
    }
  }

  // Validate key format (basic JWT check)
  if (requiredEnvVars.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    const key = requiredEnvVars.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    if (!key.startsWith('eyJ') || key.split('.').length !== 3) {
      errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY appears to be invalid (not a JWT token)');
    }
  }

  return {
    isValid: missingVars.length === 0 && errors.length === 0,
    missingVars,
    errors,
    vars: requiredEnvVars,
  };
}

// Get environment with fallbacks
export function getEnvironment() {
  if (typeof window === 'undefined') {
    // Server-side: use process.env
    return {
      supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
      supabaseServiceKey: process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      appUrl: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
      isServer: true,
    };
  }

  // Client-side: use process.env (bundled at build time)
  return {
    supabaseUrl: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
    supabaseKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
    supabaseServiceKey: '', // Not available on client
    appUrl: process.env.NEXT_PUBLIC_APP_URL || window.location.origin,
    isServer: false,
  };
}

// Development helpers
export function logEnvironmentStatus() {
  if (process.env.NODE_ENV === 'development') {
    const validation = validateEnvironment();
    
    if (validation.isValid) {
      console.log('✅ Environment variables are properly configured');
    } else {
      console.error('❌ Environment configuration issues:');
      if (validation.missingVars.length > 0) {
        console.error('Missing variables:', validation.missingVars);
      }
      if (validation.errors.length > 0) {
        console.error('Validation errors:', validation.errors);
      }
    }
  }
}
const cheerio = require('cheerio');
const natural = require('natural');
const stopword = require('stopword');
const axios = require('axios');

class InternalLinkingSuggester {
  constructor(content, existingPages = [], options = {}) {
    this.content = content;
    this.existingPages = existingPages; // Array of {url, title, content, keywords}
    this.options = {
      maxSuggestions: 10,
      minRelevanceScore: 0.3,
      contextWindow: 50, // words around potential link
      sitemapUrl: null, // URL to sitemap.xml
      ...options
    };
    
    // Parse content
    this.$ = cheerio.load(`<div>${content}</div>`);
    this.plainText = this.$.text();
    this.words = this.getWords();
    this.sentences = this.getSentences();
    this.existingLinks = this.extractExistingLinks();
    
    // Extract LSI keywords, variations, and entities
    this.lsiKeywords = this.extractLSIKeywords();
    this.keywordVariations = this.extractKeywordVariations();
    this.entities = this.extractEntities();
  }

  // Fetch and parse sitemap.xml to get live pages
  async fetchSitemapPages(sitemapUrl) {
    try {
      console.log(`Fetching sitemap from: ${sitemapUrl}`);
      const response = await axios.get(sitemapUrl, {
        headers: {
          'User-Agent': 'Mozilla/5.0 (compatible; SEO-Bot/1.0)'
        },
        timeout: 10000
      });

      const $ = cheerio.load(response.data, { xmlMode: true });
      const pages = [];

      // Handle XML sitemap
      $('url').each((index, element) => {
        const loc = $(element).find('loc').text();
        const lastmod = $(element).find('lastmod').text();
        const priority = $(element).find('priority').text();
        
        if (loc) {
          pages.push({
            url: loc,
            lastModified: lastmod,
            priority: parseFloat(priority) || 0.5,
            title: '', // Will be fetched separately
            content: '',
            keywords: []
          });
        }
      });

      // If no XML sitemap, try to parse as HTML sitemap
      if (pages.length === 0) {
        $('a').each((index, element) => {
          const href = $(element).attr('href');
          const text = $(element).text().trim();
          
          if (href && this.isValidInternalUrl(href)) {
            pages.push({
              url: this.normalizeUrl(href, sitemapUrl),
              title: text,
              content: '',
              keywords: [],
              priority: 0.5
            });
          }
        });
      }

      console.log(`Found ${pages.length} pages in sitemap`);
      return pages;
    } catch (error) {
      console.error('Error fetching sitemap:', error.message);
      return [];
    }
  }

  // Enhanced method to generate suggestions using sitemap data
  async generateSuggestionsFromSitemap() {
    try {
      let pagesToAnalyze = this.existingPages;

      // If sitemap URL is provided, fetch live pages
      if (this.options.sitemapUrl) {
        const sitemapPages = await this.fetchSitemapPages(this.options.sitemapUrl);
        
        // Fetch page content for top priority pages (limit to prevent overload)
        const priorityPages = sitemapPages
          .sort((a, b) => b.priority - a.priority)
          .slice(0, 20); // Limit to top 20 pages

        const enrichedPages = await this.enrichPagesWithContent(priorityPages);
        pagesToAnalyze = [...pagesToAnalyze, ...enrichedPages];
      }

      // Generate linking opportunities using LSI keywords and entities
      const linkOpportunities = this.generateLSIBasedOpportunities();
      
      // Find relevant pages for each opportunity
      const suggestions = this.findRelevantPagesForOpportunities(linkOpportunities, pagesToAnalyze);
      
      // Optimize suggestions
      const optimizedSuggestions = this.optimizeSuggestions(suggestions);
      const analysis = this.analyzeCurrentLinking();

      return {
        success: true,
        suggestions: optimizedSuggestions,
        linkOpportunities,
        currentLinkAnalysis: analysis,
        recommendations: this.generateRecommendations(analysis, optimizedSuggestions),
        metadata: {
          pagesAnalyzed: pagesToAnalyze.length,
          lsiKeywordsFound: this.lsiKeywords.length,
          entitiesFound: this.entities.length,
          variationsFound: this.keywordVariations.length
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        suggestions: [],
        linkOpportunities: [],
        currentLinkAnalysis: null,
        recommendations: []
      };
    }
  }

  // Enrich pages with content by fetching their HTML
  async enrichPagesWithContent(pages) {
    const enrichedPages = [];
    
    for (const page of pages) {
      try {
        const response = await axios.get(page.url, {
          headers: {
            'User-Agent': 'Mozilla/5.0 (compatible; SEO-Bot/1.0)'
          },
          timeout: 5000,
          maxRedirects: 3
        });

        const $ = cheerio.load(response.data);
        
        // Extract title if not already set
        if (!page.title) {
          page.title = $('title').text().trim() || $('h1').first().text().trim();
        }
        
        // Extract content from main content areas
        const content = this.extractMainContent($);
        page.content = content.substring(0, 2000); // Limit content for performance
        
        // Extract keywords from meta tags and headings
        const metaKeywords = $('meta[name="keywords"]').attr('content');
        const headings = [];
        $('h1, h2, h3').each((i, elem) => {
          headings.push($(elem).text().trim());
        });
        
        page.keywords = [
          ...(metaKeywords ? metaKeywords.split(',').map(k => k.trim()) : []),
          ...headings.join(' ').toLowerCase().split(/\s+/).filter(word => word.length > 3)
        ];
        
        enrichedPages.push(page);
        
        // Add delay to avoid overwhelming the server
        await new Promise(resolve => setTimeout(resolve, 500));
        
      } catch (error) {
        console.error(`Error fetching content for ${page.url}:`, error.message);
        // Still add the page with limited data
        enrichedPages.push(page);
      }
    }
    
    return enrichedPages;
  }

  // Extract main content from HTML
  extractMainContent($) {
    // Try common content selectors
    const contentSelectors = [
      'main',
      '.content',
      '.main-content',
      '.post-content',
      '.entry-content',
      'article',
      '.article-body',
      '#content',
      '.page-content'
    ];
    
    for (const selector of contentSelectors) {
      const content = $(selector).text();
      if (content && content.length > 100) {
        return content;
      }
    }
    
    // Fallback to body content, excluding nav, footer, sidebar
    const excludeSelectors = 'nav, footer, .nav, .navigation, .sidebar, .menu, .header, .footer, script, style';
    $('body').find(excludeSelectors).remove();
    
    return $('body').text() || '';
  }

  // Generate LSI-based linking opportunities
  generateLSIBasedOpportunities() {
    const opportunities = [];
    
    // Create opportunities from LSI keywords
    this.lsiKeywords.forEach(keyword => {
      const contexts = this.findWordContexts(keyword);
      contexts.forEach(context => {
        opportunities.push({
          phrase: keyword,
          originalPhrase: keyword,
          type: 'lsi_keyword',
          context,
          score: 0.8,
          priority: 'high',
          source: 'lsi_analysis'
        });
      });
    });
    
    // Create opportunities from keyword variations
    this.keywordVariations.forEach(variation => {
      const contexts = this.findWordContexts(variation);
      contexts.forEach(context => {
        opportunities.push({
          phrase: variation,
          originalPhrase: variation,
          type: 'keyword_variation',
          context,
          score: 0.7,
          priority: 'medium',
          source: 'variation_analysis'
        });
      });
    });
    
    // Create opportunities from entities
    this.entities.forEach(entity => {
      const contexts = this.findWordContexts(entity);
      contexts.forEach(context => {
        opportunities.push({
          phrase: entity,
          originalPhrase: entity,
          type: 'entity',
          context,
          score: 0.9,
          priority: 'high',
          source: 'entity_extraction'
        });
      });
    });
    
    // Remove duplicates and sort by score
    return this.deduplicateAndScore(opportunities)
      .filter(opp => opp.score > 0.5)
      .sort((a, b) => b.score - a.score)
      .slice(0, 30); // Limit to top 30 opportunities
  }

  // Find relevant pages for each linking opportunity
  findRelevantPagesForOpportunities(opportunities, pages) {
    const suggestions = [];
    
    opportunities.forEach(opportunity => {
      const { phrase, originalPhrase, context, type, score, source } = opportunity;
      
      // Skip if phrase is already linked
      if (this.isAlreadyLinked(phrase)) {
        return;
      }
      
      // Find matching pages using enhanced relevance calculation
      const relevantPages = pages
        .map(page => ({
          ...page,
          relevanceScore: this.calculateEnhancedRelevanceScore(phrase, page, type)
        }))
        .filter(page => page.relevanceScore > 0.4)
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, 2); // Top 2 most relevant pages
      
      if (relevantPages.length > 0) {
        relevantPages.forEach(page => {
          suggestions.push({
            anchorText: originalPhrase,
            targetUrl: page.url,
            targetTitle: page.title,
            relevanceScore: page.relevanceScore,
            phraseScore: score,
            overallScore: (page.relevanceScore + score) / 2,
            context,
            type,
            source,
            recommendation: this.generateEnhancedLinkRecommendation(opportunity, page)
          });
        });
      }
    });
    
    return suggestions;
  }

  // Enhanced relevance score calculation for LSI-based opportunities
  calculateEnhancedRelevanceScore(phrase, page, type) {
    let score = 0;
    const phraseWords = phrase.toLowerCase().split(/\s+/);
    
    // Base relevance calculation
    const baseScore = this.calculateRelevanceScore(phrase, page);
    score += baseScore * 0.5;
    
    // Type-specific scoring
    if (type === 'entity') {
      // Entities get higher weight for title matches
      const titleScore = this.calculateTextRelevance(phraseWords, page.title?.toLowerCase() || '');
      score += titleScore * 0.3;
    } else if (type === 'lsi_keyword') {
      // LSI keywords get higher weight for content matches
      const contentScore = this.calculateTextRelevance(phraseWords, page.content?.toLowerCase() || '');
      score += contentScore * 0.3;
    } else if (type === 'keyword_variation') {
      // Variations get balanced scoring
      const titleScore = this.calculateTextRelevance(phraseWords, page.title?.toLowerCase() || '');
      const contentScore = this.calculateTextRelevance(phraseWords, page.content?.toLowerCase() || '');
      score += (titleScore + contentScore) * 0.15;
    }
    
    // Boost score for semantic similarity
    const semanticScore = this.calculateSemanticSimilarity(phrase, page);
    score += semanticScore * 0.2;
    
    return Math.min(score, 1.0);
  }

  // Calculate semantic similarity between phrase and page
  calculateSemanticSimilarity(phrase, page) {
    const phraseWords = phrase.toLowerCase().split(/\s+/);
    const pageText = `${page.title} ${page.content}`.toLowerCase();
    const pageWords = pageText.split(/\s+/);
    
    // Simple semantic similarity using word co-occurrence
    let semanticMatches = 0;
    
    phraseWords.forEach(word => {
      // Look for related words (simple stemming and variations)
      const relatedWords = this.getRelatedWords(word);
      const matches = pageWords.filter(pageWord => 
        relatedWords.some(relatedWord => 
          pageWord.includes(relatedWord) || relatedWord.includes(pageWord)
        )
      );
      semanticMatches += matches.length;
    });
    
    return Math.min(semanticMatches / (phraseWords.length * 5), 1.0);
  }

  // Get semantically related words (simplified)
  getRelatedWords(word) {
    const relatedWords = [word];
    
    // Add simple variations
    if (word.endsWith('ing')) {
      relatedWords.push(word.slice(0, -3)); // Remove 'ing'
    }
    if (word.endsWith('ed')) {
      relatedWords.push(word.slice(0, -2)); // Remove 'ed'
    }
    if (word.endsWith('s')) {
      relatedWords.push(word.slice(0, -1)); // Remove 's'
    }
    
    // Add with common suffixes
    relatedWords.push(word + 's', word + 'ing', word + 'ed');
    
    return relatedWords.filter(w => w.length > 2);
  }

  // Generate enhanced link recommendation
  generateEnhancedLinkRecommendation(opportunity, page) {
    return {
      action: 'add_link',
      confidence: opportunity.score * page.relevanceScore,
      reasoning: `"${opportunity.originalPhrase}" (${opportunity.type}) is highly relevant to "${page.title}" (${Math.round(page.relevanceScore * 100)}% match)`,
      source: opportunity.source,
      linkingStrategy: this.getLinkingStrategy(opportunity.type),
      bestPractices: [
        'Use natural anchor text that flows with the content',
        'Ensure link adds genuine value for readers',
        'Place link contextually within relevant content',
        'Avoid over-optimization with exact match anchors'
      ]
    };
  }

  // Get linking strategy based on opportunity type
  getLinkingStrategy(type) {
    const strategies = {
      'lsi_keyword': 'Use LSI keywords as natural anchor text to improve topical relevance',
      'keyword_variation': 'Leverage keyword variations to create diverse anchor text profile',
      'entity': 'Link entities to authoritative pages about those specific topics',
      'noun_phrase': 'Use descriptive noun phrases for contextual linking',
      'topic_cluster': 'Connect related topic clusters to improve site architecture'
    };
    
    return strategies[type] || 'Use natural, contextual anchor text';
  }

  // Main method to generate internal linking suggestions
  generateSuggestions() {
    try {
      const linkablePhrases = this.identifyLinkablePhrases();
      const suggestions = this.findRelevantPages(linkablePhrases);
      const optimizedSuggestions = this.optimizeSuggestions(suggestions);
      const analysis = this.analyzeCurrentLinking();

      return {
        success: true,
        suggestions: optimizedSuggestions,
        linkablePhrases: linkablePhrases.slice(0, 20),
        currentLinkAnalysis: analysis,
        recommendations: this.generateRecommendations(analysis, optimizedSuggestions)
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        suggestions: [],
        linkablePhrases: [],
        currentLinkAnalysis: null,
        recommendations: []
      };
    }
  }

  // Identify phrases that could be turned into internal links
  identifyLinkablePhrases() {
    const phrases = [];
    
    // Extract noun phrases and key terms
    const nounPhrases = this.extractNounPhrases();
    const keyTerms = this.extractKeyTerms();
    const topicClusters = this.identifyTopicClusters();
    
    // Combine and score phrases
    const allPhrases = [...nounPhrases, ...keyTerms, ...topicClusters];
    
    // Remove duplicates and score by relevance
    const uniquePhrases = this.deduplicateAndScore(allPhrases);
    
    return uniquePhrases
      .filter(phrase => phrase.score > 0.3)
      .sort((a, b) => b.score - a.score)
      .slice(0, 50);
  }

  // Extract noun phrases from content
  extractNounPhrases() {
    const phrases = [];
    const sentences = this.sentences;
    
    sentences.forEach(sentence => {
      // Simple noun phrase extraction using POS patterns
      const words = sentence.split(/\s+/);
      const tagged = this.simplePoSTagging(words);
      
      // Look for patterns like "Adjective + Noun" or "Noun + Noun"
      for (let i = 0; i < tagged.length - 1; i++) {
        const current = tagged[i];
        const next = tagged[i + 1];
        
        if (this.isLinkablePhrase(current, next)) {
          const phrase = `${current.word} ${next.word}`;
          const context = this.getContext(sentence, i, 2);
          
          phrases.push({
            phrase: phrase.toLowerCase(),
            originalPhrase: phrase,
            type: 'noun_phrase',
            context,
            position: sentence.indexOf(phrase),
            frequency: 1,
            score: 0.7
          });
        }
      }
    });
    
    return phrases;
  }

  // Extract key terms and important concepts
  extractKeyTerms() {
    const terms = [];
    
    // TF-IDF-like scoring for term importance
    const wordFreq = this.calculateWordFrequency();
    const importantWords = Object.entries(wordFreq)
      .filter(([word, freq]) => {
        return word.length > 3 && 
               freq > 1 && 
               !this.isCommonWord(word) &&
               !this.isStopWord(word);
      })
      .sort((a, b) => b[1] - a[1])
      .slice(0, 30);

    importantWords.forEach(([word, freq]) => {
      const contexts = this.findWordContexts(word);
      contexts.forEach(context => {
        terms.push({
          phrase: word.toLowerCase(),
          originalPhrase: word,
          type: 'key_term',
          context,
          frequency: freq,
          score: Math.min(freq / 10, 1.0)
        });
      });
    });
    
    return terms;
  }

  // Identify topic clusters within content
  identifyTopicClusters() {
    const clusters = [];
    const headings = this.extractHeadings();
    
    headings.forEach(heading => {
      const headingWords = heading.text.split(/\s+/)
        .filter(word => word.length > 3 && !this.isStopWord(word));
      
      if (headingWords.length >= 2) {
        // Create clusters from heading combinations
        for (let i = 0; i < headingWords.length - 1; i++) {
          const cluster = `${headingWords[i]} ${headingWords[i + 1]}`;
          clusters.push({
            phrase: cluster.toLowerCase(),
            originalPhrase: cluster,
            type: 'topic_cluster',
            context: heading.text,
            headingLevel: heading.level,
            score: 0.8 - (heading.level * 0.1) // Higher score for higher-level headings
          });
        }
      }
    });
    
    return clusters;
  }

  // Find relevant pages for each linkable phrase
  findRelevantPages(linkablePhrases) {
    const suggestions = [];
    
    linkablePhrases.forEach(phraseObj => {
      const { phrase, originalPhrase, context, type, score } = phraseObj;
      
      // Skip if phrase is already linked
      if (this.isAlreadyLinked(phrase)) {
        return;
      }
      
      // Find matching pages
      const relevantPages = this.existingPages
        .map(page => ({
          ...page,
          relevanceScore: this.calculateRelevanceScore(phrase, page)
        }))
        .filter(page => page.relevanceScore > this.options.minRelevanceScore)
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, 3); // Top 3 most relevant pages
      
      if (relevantPages.length > 0) {
        relevantPages.forEach(page => {
          suggestions.push({
            anchorText: originalPhrase,
            targetUrl: page.url,
            targetTitle: page.title,
            relevanceScore: page.relevanceScore,
            phraseScore: score,
            overallScore: (page.relevanceScore + score) / 2,
            context,
            type,
            recommendation: this.generateLinkRecommendation(phraseObj, page)
          });
        });
      }
    });
    
    return suggestions;
  }

  // Calculate relevance score between phrase and page
  calculateRelevanceScore(phrase, page) {
    let score = 0;
    const phraseWords = phrase.toLowerCase().split(/\s+/);
    
    // Check title relevance (highest weight)
    const titleScore = this.calculateTextRelevance(phraseWords, page.title?.toLowerCase() || '');
    score += titleScore * 0.4;
    
    // Check URL relevance
    const urlScore = this.calculateTextRelevance(phraseWords, page.url?.toLowerCase() || '');
    score += urlScore * 0.2;
    
    // Check content relevance
    const contentScore = this.calculateTextRelevance(phraseWords, page.content?.toLowerCase() || '');
    score += contentScore * 0.3;
    
    // Check keyword relevance
    if (page.keywords) {
      const keywordScore = this.calculateTextRelevance(phraseWords, page.keywords.join(' ').toLowerCase());
      score += keywordScore * 0.1;
    }
    
    return Math.min(score, 1.0);
  }

  // Calculate text relevance using word matching
  calculateTextRelevance(phraseWords, text) {
    if (!text || phraseWords.length === 0) return 0;
    
    const textWords = text.split(/\s+/);
    let matches = 0;
    
    phraseWords.forEach(word => {
      if (textWords.includes(word)) {
        matches++;
      } else {
        // Check for partial matches or stems
        const partialMatches = textWords.filter(textWord => 
          textWord.includes(word) || word.includes(textWord)
        );
        if (partialMatches.length > 0) {
          matches += 0.5;
        }
      }
    });
    
    return matches / phraseWords.length;
  }

  // Optimize suggestions to avoid over-linking
  optimizeSuggestions(suggestions) {
    // Remove duplicates and low-quality suggestions
    const deduped = this.removeDuplicateSuggestions(suggestions);
    
    // Limit suggestions per paragraph
    const optimized = this.limitSuggestionsPerSection(deduped);
    
    // Ensure anchor text diversity
    const diversified = this.ensureAnchorTextDiversity(optimized);
    
    // Sort by overall score and limit
    return diversified
      .sort((a, b) => b.overallScore - a.overallScore)
      .slice(0, this.options.maxSuggestions);
  }

  // Analyze current internal linking structure
  analyzeCurrentLinking() {
    const analysis = {
      totalLinks: this.existingLinks.length,
      internalLinks: this.existingLinks.filter(link => this.isInternalLink(link.href)).length,
      externalLinks: this.existingLinks.filter(link => !this.isInternalLink(link.href)).length,
      anchorTextAnalysis: this.analyzeAnchorTexts(),
      linkDensity: this.calculateLinkDensity(),
      linkDistribution: this.analyzeLinkDistribution(),
      issues: []
    };
    
    // Identify issues
    if (analysis.linkDensity > 5) {
      analysis.issues.push('Link density too high (>5%). Consider reducing number of links.');
    }
    
    if (analysis.internalLinks < 3) {
      analysis.issues.push('Too few internal links. Add more to improve site structure.');
    }
    
    if (analysis.anchorTextAnalysis.exactMatchRatio > 0.6) {
      analysis.issues.push('Too many exact match anchor texts. Diversify anchor text.');
    }
    
    return analysis;
  }

  // Generate recommendations based on analysis
  generateRecommendations(analysis, suggestions) {
    const recommendations = [];
    
    // Link quantity recommendations
    if (analysis.internalLinks < 3) {
      recommendations.push({
        type: 'quantity',
        priority: 'high',
        message: `Add ${3 - analysis.internalLinks} more internal links to improve site structure`,
        suggestedActions: suggestions.slice(0, 3 - analysis.internalLinks)
      });
    }
    
    // Link distribution recommendations
    if (analysis.linkDistribution.unbalanced) {
      recommendations.push({
        type: 'distribution',
        priority: 'medium',
        message: 'Internal links are unevenly distributed. Spread links throughout content.',
        suggestedActions: this.getDistributionSuggestions()
      });
    }
    
    // Anchor text recommendations
    if (analysis.anchorTextAnalysis.exactMatchRatio > 0.6) {
      recommendations.push({
        type: 'anchor_text',
        priority: 'medium',
        message: 'Diversify anchor text to include branded, partial match, and generic terms',
        suggestedActions: this.getAnchorTextSuggestions(suggestions)
      });
    }
    
    // Quality recommendations
    const highQualitySuggestions = suggestions.filter(s => s.overallScore > 0.7);
    if (highQualitySuggestions.length > 0) {
      recommendations.push({
        type: 'quality',
        priority: 'high',
        message: `${highQualitySuggestions.length} high-quality internal linking opportunities found`,
        suggestedActions: highQualitySuggestions.slice(0, 5)
      });
    }
    
    return recommendations;
  }

  // Helper methods
  getWords() {
    return this.plainText
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 0);
  }

  getSentences() {
    return this.plainText
      .split(/[.!?]+/)
      .filter(sentence => sentence.trim().length > 0)
      .map(sentence => sentence.trim());
  }

  extractExistingLinks() {
    const links = [];
    this.$('a').each((i, elem) => {
      const $link = this.$(elem);
      links.push({
        href: $link.attr('href') || '',
        text: $link.text().trim(),
        title: $link.attr('title') || ''
      });
    });
    return links;
  }

  extractHeadings() {
    const headings = [];
    this.$('h1, h2, h3, h4, h5, h6').each((i, elem) => {
      const $heading = this.$(elem);
      headings.push({
        level: parseInt(elem.tagName.substring(1)),
        text: $heading.text().trim()
      });
    });
    return headings;
  }

  calculateWordFrequency() {
    const frequency = {};
    this.words.forEach(word => {
      if (word.length > 2) {
        frequency[word] = (frequency[word] || 0) + 1;
      }
    });
    return frequency;
  }

  isInternalLink(href) {
    if (!href) return false;
    return !href.startsWith('http') || 
           href.includes(window?.location?.hostname) ||
           href.startsWith('/') || 
           href.startsWith('#');
  }

  isAlreadyLinked(phrase) {
    return this.existingLinks.some(link => 
      link.text.toLowerCase().includes(phrase.toLowerCase())
    );
  }

  isCommonWord(word) {
    const commonWords = [
      'content', 'website', 'page', 'link', 'article', 'blog', 'post',
      'information', 'data', 'user', 'system', 'service', 'company'
    ];
    return commonWords.includes(word.toLowerCase());
  }

  isStopWord(word) {
    return stopword.removeStopwords([word]).length === 0;
  }

  simplePoSTagging(words) {
    // Simplified POS tagging
    return words.map(word => ({
      word,
      pos: this.guessPartOfSpeech(word)
    }));
  }

  guessPartOfSpeech(word) {
    const lowerWord = word.toLowerCase();
    
    // Simple heuristics for POS tagging
    if (word.endsWith('ing')) return 'VBG';
    if (word.endsWith('ed')) return 'VBD';
    if (word.endsWith('ly')) return 'RB';
    if (word.endsWith('tion') || word.endsWith('sion')) return 'NN';
    if (/^[A-Z]/.test(word)) return 'NNP'; // Proper noun
    
    return 'NN'; // Default to noun
  }

  isLinkablePhrase(current, next) {
    const linkablePatterns = [
      ['JJ', 'NN'],   // Adjective + Noun
      ['NN', 'NN'],   // Noun + Noun
      ['NNP', 'NN'],  // Proper Noun + Noun
      ['NN', 'NNP']   // Noun + Proper Noun
    ];
    
    return linkablePatterns.some(([pos1, pos2]) => 
      current.pos === pos1 && next.pos === pos2
    );
  }

  getContext(sentence, position, wordCount) {
    const words = sentence.split(/\s+/);
    const start = Math.max(0, position - wordCount);
    const end = Math.min(words.length, position + wordCount + 2);
    return words.slice(start, end).join(' ');
  }

  findWordContexts(word) {
    const contexts = [];
    const regex = new RegExp(`\\b${word}\\b`, 'gi');
    
    this.sentences.forEach(sentence => {
      if (regex.test(sentence)) {
        const index = sentence.toLowerCase().indexOf(word.toLowerCase());
        const context = this.getContext(sentence, 
          sentence.substring(0, index).split(/\s+/).length - 1, 
          this.options.contextWindow / 2
        );
        contexts.push(context);
      }
    });
    
    return contexts;
  }

  deduplicateAndScore(phrases) {
    const phraseMap = new Map();
    
    phrases.forEach(phraseObj => {
      const key = phraseObj.phrase.toLowerCase();
      if (phraseMap.has(key)) {
        const existing = phraseMap.get(key);
        existing.frequency += phraseObj.frequency || 1;
        existing.score = Math.max(existing.score, phraseObj.score);
      } else {
        phraseMap.set(key, { ...phraseObj });
      }
    });
    
    return Array.from(phraseMap.values());
  }

  removeDuplicateSuggestions(suggestions) {
    const seen = new Set();
    return suggestions.filter(suggestion => {
      const key = `${suggestion.anchorText}-${suggestion.targetUrl}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  limitSuggestionsPerSection(suggestions) {
    // Implement logic to limit suggestions per content section
    // This is a simplified version
    return suggestions.slice(0, this.options.maxSuggestions);
  }

  ensureAnchorTextDiversity(suggestions) {
    const anchorCounts = new Map();
    
    return suggestions.filter(suggestion => {
      const anchor = suggestion.anchorText.toLowerCase();
      const count = anchorCounts.get(anchor) || 0;
      
      if (count < 2) { // Allow maximum 2 uses of same anchor text
        anchorCounts.set(anchor, count + 1);
        return true;
      }
      return false;
    });
  }

  analyzeAnchorTexts() {
    if (this.existingLinks.length === 0) {
      return { exactMatchRatio: 0, diversity: 0, avgLength: 0 };
    }

    const anchorTexts = this.existingLinks.map(link => link.text.toLowerCase());
    const uniqueAnchors = new Set(anchorTexts);
    
    // Calculate exact match ratio (simplified)
    const exactMatches = anchorTexts.filter(text => 
      this.words.some(word => text === word)
    ).length;
    
    return {
      exactMatchRatio: exactMatches / anchorTexts.length,
      diversity: uniqueAnchors.size / anchorTexts.length,
      avgLength: anchorTexts.reduce((sum, text) => sum + text.length, 0) / anchorTexts.length
    };
  }

  calculateLinkDensity() {
    const wordCount = this.words.length;
    const linkCount = this.existingLinks.length;
    return wordCount > 0 ? (linkCount / wordCount) * 100 : 0;
  }

  analyzeLinkDistribution() {
    // Simplified link distribution analysis
    return {
      unbalanced: this.existingLinks.length > 0 && this.existingLinks.length < 3
    };
  }

  generateLinkRecommendation(phraseObj, page) {
    return {
      action: 'add_link',
      confidence: phraseObj.score * page.relevanceScore,
      reasoning: `"${phraseObj.originalPhrase}" is highly relevant to "${page.title}" (${Math.round(page.relevanceScore * 100)}% match)`,
      bestPractices: [
        'Use natural anchor text',
        'Ensure link adds value to readers',
        'Avoid over-optimization'
      ]
    };
  }

  getDistributionSuggestions() {
    return [
      'Add internal links throughout the content, not just at the beginning or end',
      'Include links in different content sections',
      'Balance links across paragraphs'
    ];
  }

  getAnchorTextSuggestions(suggestions) {
    return suggestions.slice(0, 3).map(suggestion => ({
      current: suggestion.anchorText,
      alternatives: this.generateAnchorTextAlternatives(suggestion.anchorText),
      recommendation: 'Use varied anchor text for better SEO'
    }));
  }

  generateAnchorTextAlternatives(anchorText) {
    // Generate alternative anchor texts
    const alternatives = [];
    const words = anchorText.split(/\s+/);
    
    if (words.length > 1) {
      alternatives.push(words.slice(0, -1).join(' ')); // Remove last word
      alternatives.push(words.slice(1).join(' '));     // Remove first word
    }
    
    alternatives.push(`learn about ${anchorText}`);
    alternatives.push(`${anchorText} guide`);
    alternatives.push(`more on ${anchorText}`);
    
    return alternatives.filter(alt => alt !== anchorText).slice(0, 3);
  }

  // Extract LSI keywords from content
  extractLSIKeywords() {
    const lsiKeywords = [];
    const words = this.words.filter(word => 
      word.length > 4 && 
      !this.isStopWord(word) && 
      !this.isCommonWord(word)
    );
    
    // Use term frequency and co-occurrence analysis
    const wordFreq = this.calculateWordFrequency();
    const importantWords = Object.entries(wordFreq)
      .filter(([word, freq]) => freq > 1 && words.includes(word))
      .sort((a, b) => b[1] - a[1])
      .slice(0, 15)
      .map(([word]) => word);
    
    // Add bi-grams and tri-grams that include important words
    for (let i = 0; i < this.words.length - 1; i++) {
      const bigram = `${this.words[i]} ${this.words[i + 1]}`;
      if (importantWords.some(word => bigram.includes(word))) {
        lsiKeywords.push(bigram);
      }
      
      if (i < this.words.length - 2) {
        const trigram = `${this.words[i]} ${this.words[i + 1]} ${this.words[i + 2]}`;
        if (importantWords.some(word => trigram.includes(word))) {
          lsiKeywords.push(trigram);
        }
      }
    }
    
    return [...new Set([...importantWords, ...lsiKeywords])].slice(0, 20);
  }

  // Extract keyword variations
  extractKeywordVariations() {
    const variations = [];
    const words = this.words;
    
    // Find variations based on stemming and morphology
    words.forEach(word => {
      if (word.length > 4) {
        const stemmed = natural.PorterStemmer.stem(word);
        
        // Find words with same stem
        const sameStems = words.filter(w => 
          natural.PorterStemmer.stem(w) === stemmed && w !== word
        );
        
        if (sameStems.length > 0) {
          variations.push(...sameStems);
        }
      }
    });
    
    return [...new Set(variations)].slice(0, 15);
  }

  // Extract entities (proper nouns, brands, locations, etc.)
  extractEntities() {
    const entities = [];
    
    // Simple entity extraction based on capitalization patterns
    const sentences = this.sentences;
    
    sentences.forEach(sentence => {
      const words = sentence.split(/\s+/);
      
      for (let i = 0; i < words.length; i++) {
        const word = words[i].replace(/[^\w]/g, '');
        
        // Check for capitalized words (potential entities)
        if (word.match(/^[A-Z][a-z]+$/)) {
          // Look for multi-word entities
          let entity = word;
          for (let j = i + 1; j < words.length && j < i + 3; j++) {
            const nextWord = words[j].replace(/[^\w]/g, '');
            if (nextWord.match(/^[A-Z][a-z]+$/)) {
              entity += ` ${nextWord}`;
            } else {
              break;
            }
          }
          
          // Only include entities with 2+ characters
          if (entity.length > 2 && !this.isCommonWord(entity.toLowerCase())) {
            entities.push(entity);
          }
        }
      }
    });
    
    return [...new Set(entities)].slice(0, 10);
  }

  // Helper method to check if URL is valid for internal linking
  isValidInternalUrl(url) {
    if (!url) return false;
    
    // Exclude external links, anchors, and non-web protocols
    return !url.startsWith('http://') && 
           !url.startsWith('https://') && 
           !url.startsWith('mailto:') && 
           !url.startsWith('tel:') && 
           !url.startsWith('javascript:') && 
           !url.startsWith('#') &&
           url.length > 0;
  }

  // Helper method to normalize URLs
  normalizeUrl(url, baseUrl) {
    try {
      if (url.startsWith('http')) {
        return url;
      }
      
      const base = new URL(baseUrl);
      if (url.startsWith('/')) {
        return `${base.protocol}//${base.host}${url}`;
      } else {
        return `${base.protocol}//${base.host}/${url}`;
      }
    } catch (error) {
      return url;
    }
  }
}

module.exports = InternalLinkingSuggester;
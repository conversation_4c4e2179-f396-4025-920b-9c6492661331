<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - SEO Pro</title>
    <meta name="description" content="Manage your user profile, account settings, subscription details, and team collaboration preferences for SEO Pro platform.">
    
    <!-- Preload Critical Resources -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style">
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
    
    <!-- Favicon -->
    <link rel="icon" href="images/favicon.ico">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <a href="index.html" class="logo">
                    <span class="logo-text">SEO Pro</span>
                </a>
            </div>
            
            <!-- Desktop Navigation -->
            <ul class="nav-menu">
                <li><a href="dashboard.html" class="nav-link">Dashboard</a></li>
                <li><a href="content-generator.html" class="nav-link">Generate</a></li>
                <li><a href="seo-analysis.html" class="nav-link">Analyze</a></li>
                <li><a href="projects.html" class="nav-link">Projects</a></li>
            </ul>
            
            <!-- User Menu -->
            <div class="nav-user">
                <div class="user-avatar">
                    <img src="images/avatars/user.jpg" alt="User Avatar" class="avatar">
                </div>
                <div class="user-menu">
                    <a href="profile.html" class="user-menu-item active">Profile</a>
                    <a href="settings.html" class="user-menu-item">Settings</a>
                    <a href="billing.html" class="user-menu-item">Billing</a>
                    <a href="login.html" class="user-menu-item">Logout</a>
                </div>
            </div>
            
            <!-- Mobile Menu Button -->
            <button class="mobile-menu-btn" aria-label="Toggle menu">
                <span class="hamburger"></span>
            </button>
        </nav>
    </header>

    <!-- Main Content -->
    <main class="main">
        <div class="container">
            <!-- Profile Header -->
            <div class="profile-header">
                <div class="profile-avatar-section">
                    <div class="profile-avatar">
                        <img src="images/avatars/user.jpg" alt="Profile Avatar" class="avatar-large">
                        <button class="avatar-edit-btn">
                            <svg viewBox="0 0 20 20" fill="currentColor">
                                <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"/>
                            </svg>
                        </button>
                    </div>
                </div>
                <div class="profile-info">
                    <h1 class="profile-name">John Smith</h1>
                    <p class="profile-email"><EMAIL></p>
                    <div class="profile-badges">
                        <span class="badge badge-primary">Professional Plan</span>
                        <span class="badge badge-success">Verified</span>
                    </div>
                </div>
                <div class="profile-actions">
                    <button class="btn btn-outline">Edit Profile</button>
                    <button class="btn btn-primary">Upgrade Plan</button>
                </div>
            </div>

            <!-- Profile Stats -->
            <div class="profile-stats">
                <div class="stat-item">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">247</h3>
                        <p class="stat-label">Content Generated</p>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">89</h3>
                        <p class="stat-label">Avg SEO Score</p>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">12</h3>
                        <p class="stat-label">Active Projects</p>
                    </div>
                </div>
                <div class="stat-item">
                    <div class="stat-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <div class="stat-content">
                        <h3 class="stat-value">5</h3>
                        <p class="stat-label">Team Members</p>
                    </div>
                </div>
            </div>

            <!-- Profile Content -->
            <div class="profile-content">
                <!-- Personal Information -->
                <div class="profile-section">
                    <div class="section-header">
                        <h2 class="section-title">Personal Information</h2>
                        <button class="btn btn-ghost btn-sm">Edit</button>
                    </div>
                    <div class="section-content">
                        <form class="profile-form">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="first-name" class="form-label">First Name</label>
                                    <input type="text" id="first-name" class="form-input" value="John" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="last-name" class="form-label">Last Name</label>
                                    <input type="text" id="last-name" class="form-input" value="Smith" readonly>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="email" class="form-label">Email Address</label>
                                    <input type="email" id="email" class="form-input" value="<EMAIL>" readonly>
                                </div>
                                <div class="form-group">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" id="phone" class="form-input" value="+****************" readonly>
                                </div>
                            </div>
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="company" class="form-label">Company</label>
                                    <input type="text" id="company" class="form-input" value="Tech Solutions Inc." readonly>
                                </div>
                                <div class="form-group">
                                    <label for="job-title" class="form-label">Job Title</label>
                                    <input type="text" id="job-title" class="form-input" value="Marketing Director" readonly>
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="bio" class="form-label">Bio</label>
                                <textarea id="bio" class="form-textarea" rows="3" readonly>Experienced marketing professional with 10+ years in digital marketing and SEO. Passionate about data-driven content strategies and team collaboration.</textarea>
                            </div>
                        </form>
                    </div>
                </div>

                <!-- Account Security -->
                <div class="profile-section">
                    <div class="section-header">
                        <h2 class="section-title">Account Security</h2>
                    </div>
                    <div class="section-content">
                        <div class="security-items">
                            <div class="security-item">
                                <div class="security-info">
                                    <h3 class="security-title">Password</h3>
                                    <p class="security-description">Last changed 3 months ago</p>
                                </div>
                                <button class="btn btn-outline btn-sm">Change Password</button>
                            </div>
                            <div class="security-item">
                                <div class="security-info">
                                    <h3 class="security-title">Two-Factor Authentication</h3>
                                    <p class="security-description">Add an extra layer of security to your account</p>
                                </div>
                                <button class="btn btn-primary btn-sm">Enable 2FA</button>
                            </div>
                            <div class="security-item">
                                <div class="security-info">
                                    <h3 class="security-title">Login Sessions</h3>
                                    <p class="security-description">Manage your active login sessions</p>
                                </div>
                                <button class="btn btn-ghost btn-sm">View Sessions</button>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- API Access -->
                <div class="profile-section">
                    <div class="section-header">
                        <h2 class="section-title">API Access</h2>
                        <button class="btn btn-primary btn-sm">Generate New Key</button>
                    </div>
                    <div class="section-content">
                        <div class="api-keys">
                            <div class="api-key-item">
                                <div class="api-key-info">
                                    <h3 class="api-key-name">Production API Key</h3>
                                    <p class="api-key-value">sk-proj-***************************</p>
                                    <p class="api-key-meta">Created on Dec 15, 2023 • Last used 2 hours ago</p>
                                </div>
                                <div class="api-key-actions">
                                    <button class="btn btn-ghost btn-sm">Copy</button>
                                    <button class="btn btn-outline btn-sm">Regenerate</button>
                                    <button class="btn btn-danger btn-sm">Delete</button>
                                </div>
                            </div>
                            <div class="api-key-item">
                                <div class="api-key-info">
                                    <h3 class="api-key-name">Development API Key</h3>
                                    <p class="api-key-value">sk-dev-***************************</p>
                                    <p class="api-key-meta">Created on Nov 28, 2023 • Last used 1 week ago</p>
                                </div>
                                <div class="api-key-actions">
                                    <button class="btn btn-ghost btn-sm">Copy</button>
                                    <button class="btn btn-outline btn-sm">Regenerate</button>
                                    <button class="btn btn-danger btn-sm">Delete</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Data & Privacy -->
                <div class="profile-section">
                    <div class="section-header">
                        <h2 class="section-title">Data & Privacy</h2>
                    </div>
                    <div class="section-content">
                        <div class="privacy-items">
                            <div class="privacy-item">
                                <div class="privacy-info">
                                    <h3 class="privacy-title">Export Data</h3>
                                    <p class="privacy-description">Download a copy of your account data</p>
                                </div>
                                <button class="btn btn-outline btn-sm">Request Export</button>
                            </div>
                            <div class="privacy-item">
                                <div class="privacy-info">
                                    <h3 class="privacy-title">Delete Account</h3>
                                    <p class="privacy-description">Permanently delete your account and all data</p>
                                </div>
                                <button class="btn btn-danger btn-sm">Delete Account</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </main>

    <!-- Footer -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div class="footer-section">
                    <h3 class="footer-title">SEO Pro</h3>
                    <p class="footer-description">AI-powered SEO content generation platform</p>
                </div>
                <div class="footer-section">
                    <h4 class="footer-heading">Product</h4>
                    <ul class="footer-links">
                        <li><a href="features.html">Features</a></li>
                        <li><a href="pricing.html">Pricing</a></li>
                        <li><a href="dashboard.html">Dashboard</a></li>
                    </ul>
                </div>
            </div>
            <div class="footer-bottom">
                <p>&copy; 2024 SEO Pro. All rights reserved.</p>
            </div>
        </div>
    </footer>

    <script src="js/main.js"></script>
</body>
</html>

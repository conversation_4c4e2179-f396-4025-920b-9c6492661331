const express = require('express');
const router = express.Router();
const axios = require('axios');
const { verifyToken, checkSubscription, checkUsageLimit } = require('../middleware/auth');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Generate content endpoint
router.post('/generate', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const {
      keyword,
      contentType,
      tone,
      wordCount,
      industry,
      location,
      searchEngine,
      intent,
      competitorData
    } = req.body;

    if (!keyword || !contentType) {
      return res.status(400).json({ error: 'Keyword and content type are required' });
    }

    // Build the optimized prompt
    const prompt = buildContentPrompt({
      keyword,
      contentType,
      tone: tone || 'professional',
      wordCount: wordCount || '800-1200',
      industry: industry || 'general',
      intent: intent || 'informational',
      competitorData
    });

    // Call Groq API
    const groqResponse = await axios.post(
      'https://api.groq.com/openai/v1/chat/completions',
      {
        model: 'llama3-70b-8192',
        messages: [
          {
            role: 'system',
            content: 'You are an expert SEO content writer specializing in creating high-quality, optimized content that follows E-E-A-T guidelines and NLP best practices.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const generatedContent = groqResponse.data.choices[0].message.content;

    // Save to database
    const { data: savedContent, error: saveError } = await supabase
      .from('generated_content')
      .insert({
        user_id: req.user.id,
        keyword,
        content_type: contentType,
        content: generatedContent,
        metadata: {
          tone,
          wordCount,
          industry,
          location,
          searchEngine,
          intent
        },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (saveError) {
      console.error('Save error:', saveError);
    }

    // Update usage tracking
    await updateUsageTracking(req.user.id, 'content_generations');

    res.json({
      content: generatedContent,
      id: savedContent?.id,
      metadata: {
        keyword,
        contentType,
        tone,
        wordCount: generatedContent.split(' ').length,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Content generation error:', error);
    res.status(500).json({ error: 'Failed to generate content' });
  }
});

// Get user's generated content
router.get('/list', verifyToken, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { data: content, error, count } = await supabase
      .from('generated_content')
      .select('*', { count: 'exact' })
      .eq('user_id', req.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw error;
    }

    res.json({
      content,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('List content error:', error);
    res.status(500).json({ error: 'Failed to retrieve content' });
  }
});

// Get single content item
router.get('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;

    const { data: content, error } = await supabase
      .from('generated_content')
      .select('*')
      .eq('id', id)
      .eq('user_id', req.user.id)
      .single();

    if (error || !content) {
      return res.status(404).json({ error: 'Content not found' });
    }

    res.json(content);
  } catch (error) {
    console.error('Get content error:', error);
    res.status(500).json({ error: 'Failed to retrieve content' });
  }
});

// Helper function to build optimized prompts
function buildContentPrompt({ keyword, contentType, tone, wordCount, industry, intent, competitorData }) {
  const wordCountRange = {
    '500-800': { min: 500, max: 800 },
    '800-1200': { min: 800, max: 1200 },
    '1200-2000': { min: 1200, max: 2000 },
    '2000+': { min: 2000, max: 3000 }
  };

  const range = wordCountRange[wordCount] || wordCountRange['800-1200'];

  let prompt = `Create a comprehensive ${contentType} about "${keyword}" for the ${industry} industry.

REQUIREMENTS:
- Word count: ${range.min}-${range.max} words
- Tone: ${tone}
- Intent: ${intent}
- Follow E-E-A-T guidelines (Experience, Expertise, Authoritativeness, Trustworthiness)
- Use clear, direct language avoiding complex or abstract terms
- Include relevant LSI keywords and entities naturally
- Structure with proper headings (H1, H2, H3)
- Optimize for featured snippets where applicable`;

  if (competitorData) {
    prompt += `\n\nCOMPETITOR INSIGHTS:
- Average word count: ${competitorData.avgWordCount}
- Average headings: ${competitorData.avgHeadings}
- Key topics covered: ${competitorData.topics?.join(', ')}
- LSI keywords to include: ${competitorData.lsiKeywords?.join(', ')}`;
  }

  prompt += `\n\nFORMATTING:
- Start with an engaging introduction
- Use short paragraphs (2-3 sentences)
- Include bullet points or numbered lists where appropriate
- End with a strong conclusion
- Naturally incorporate the keyword "${keyword}" with optimal density`;

  return prompt;
}

// Helper function to update usage tracking
async function updateUsageTracking(userId, type) {
  const today = new Date().toISOString().split('T')[0];
  
  const { data: existing } = await supabase
    .from('usage_tracking')
    .select('*')
    .eq('user_id', userId)
    .eq('date', today)
    .single();

  if (existing) {
    await supabase
      .from('usage_tracking')
      .update({ 
        [type]: existing[type] + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', existing.id);
  } else {
    await supabase
      .from('usage_tracking')
      .insert({
        user_id: userId,
        date: today,
        [type]: 1,
        created_at: new Date().toISOString()
      });
  }
}

module.exports = router;
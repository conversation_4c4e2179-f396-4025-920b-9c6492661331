const express = require('express');
const router = express.Router();
const axios = require('axios');
const { verifyToken, checkSubscription, checkUsageLimit } = require('../middleware/auth');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Generate content endpoint
router.post('/generate', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const {
      keyword,
      contentType,
      tone,
      wordCount,
      industry,
      location,
      searchEngine,
      intent,
      competitorData
    } = req.body;

    if (!keyword || !contentType) {
      return res.status(400).json({ error: 'Keyword and content type are required' });
    }

    // Build the optimized prompt
    const prompt = buildContentPrompt({
      keyword,
      contentType,
      tone: tone || 'professional',
      wordCount: wordCount || '800-1200',
      industry: industry || 'general',
      intent: intent || 'informational',
      competitorData
    });

    // Call Groq API
    const groqResponse = await axios.post(
      'https://api.groq.com/openai/v1/chat/completions',
      {
        model: 'llama3-70b-8192',
        messages: [
          {
            role: 'system',
            content: 'You are an expert SEO content writer specializing in creating high-quality, optimized content that follows E-E-A-T guidelines and NLP best practices.'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        temperature: 0.7,
        max_tokens: 4000
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const generatedContent = groqResponse.data.choices[0].message.content;

    // Save to database
    const { data: savedContent, error: saveError } = await supabase
      .from('generated_content')
      .insert({
        user_id: req.user.id,
        keyword,
        content_type: contentType,
        content: generatedContent,
        metadata: {
          tone,
          wordCount,
          industry,
          location,
          searchEngine,
          intent
        },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (saveError) {
      console.error('Save error:', saveError);
    }

    // Update usage tracking
    await updateUsageTracking(req.user.id, 'content_generations');

    res.json({
      content: generatedContent,
      id: savedContent?.id,
      metadata: {
        keyword,
        contentType,
        tone,
        wordCount: generatedContent.split(' ').length,
        generatedAt: new Date().toISOString()
      }
    });
  } catch (error) {
    console.error('Content generation error:', error);
    res.status(500).json({ error: 'Failed to generate content' });
  }
});

// Get user's generated content
router.get('/list', verifyToken, async (req, res) => {
  try {
    const { page = 1, limit = 10 } = req.query;
    const offset = (page - 1) * limit;

    const { data: content, error, count } = await supabase
      .from('generated_content')
      .select('*', { count: 'exact' })
      .eq('user_id', req.user.id)
      .order('created_at', { ascending: false })
      .range(offset, offset + limit - 1);

    if (error) {
      throw error;
    }

    res.json({
      content,
      pagination: {
        page: parseInt(page),
        limit: parseInt(limit),
        total: count,
        pages: Math.ceil(count / limit)
      }
    });
  } catch (error) {
    console.error('List content error:', error);
    res.status(500).json({ error: 'Failed to retrieve content' });
  }
});

// Get single content item
router.get('/:id', verifyToken, async (req, res) => {
  try {
    const { id } = req.params;

    const { data: content, error } = await supabase
      .from('generated_content')
      .select('*')
      .eq('id', id)
      .eq('user_id', req.user.id)
      .single();

    if (error || !content) {
      return res.status(404).json({ error: 'Content not found' });
    }

    res.json(content);
  } catch (error) {
    console.error('Get content error:', error);
    res.status(500).json({ error: 'Failed to retrieve content' });
  }
});

// Helper function to build optimized prompts
function buildContentPrompt({ keyword, contentType, tone, wordCount, industry, intent, competitorData }) {
  const wordCountRange = {
    '500-800': { min: 500, max: 800 },
    '800-1200': { min: 800, max: 1200 },
    '1200-2000': { min: 1200, max: 2000 },
    '2000+': { min: 2000, max: 3000 }
  };

  const range = wordCountRange[wordCount] || wordCountRange['800-1200'];

  // E-E-A-T specific guidelines based on content type and industry
  const eatGuidelines = {
    'blog-post': 'Include personal insights, real examples, and actionable advice. Cite credible sources and include author expertise.',
    'article': 'Provide comprehensive coverage with expert analysis. Reference authoritative sources and industry standards.',
    'landing-page': 'Build trust through testimonials, certifications, and clear value propositions. Include contact information and credentials.',
    'product-description': 'Highlight unique features, benefits, and use cases. Include specifications and compatibility information.'
  };

  const industryExpertise = {
    'healthcare': 'medical studies, peer-reviewed research, healthcare professionals, FDA guidelines',
    'finance': 'financial regulations, market data, certified financial advisors, government sources',
    'technology': 'technical specifications, industry standards, expert reviews, official documentation',
    'legal': 'legal precedents, statutory requirements, licensed attorneys, court decisions',
    'education': 'academic research, educational institutions, certified educators, curriculum standards'
  };

  let prompt = `Create a comprehensive, expert-level ${contentType} about "${keyword}" for the ${industry} industry.

E-E-A-T OPTIMIZATION REQUIREMENTS:
- EXPERIENCE: Include real-world examples, case studies, and practical applications
- EXPERTISE: Demonstrate deep knowledge through technical details and industry insights
- AUTHORITATIVENESS: Reference credible sources, statistics, and established authorities
- TRUSTWORTHINESS: Use factual, well-researched information with proper citations

CONTENT SPECIFICATIONS:
- Word count: ${range.min}-${range.max} words
- Tone: ${tone}
- Search intent: ${intent}
- Industry focus: ${industry}

NLP OPTIMIZATION RULES:
- Use clear subject-verb-object sentence structure
- Avoid abstract terms like: meticulous, navigating, complexities, realm, bespoke, endeavor
- Write in direct, accessible language that's easily interpretable
- Use concrete, specific terms instead of vague descriptors
- Maintain readability score above 60 (Flesch Reading Ease)

CONTENT STRUCTURE:
- H1: Include primary keyword naturally
- H2-H3: Use semantic variations and related terms
- Optimal keyword density: 1-2% for primary keyword
- Include LSI keywords and entity variations naturally
- Structure for featured snippet opportunities`;

  // Add industry-specific expertise requirements
  if (industryExpertise[industry]) {
    prompt += `\n\nINDUSTRY EXPERTISE SOURCES:
Reference authoritative sources such as: ${industryExpertise[industry]}`;
  }

  // Add content-type specific E-E-A-T guidelines
  if (eatGuidelines[contentType]) {
    prompt += `\n\nCONTENT TYPE GUIDELINES:
${eatGuidelines[contentType]}`;
  }

  if (competitorData) {
    prompt += `\n\nCOMPETITOR INSIGHTS (use to exceed competition):
- Average word count: ${competitorData.avgWordCount} (aim to exceed by 10-20%)
- Average headings: ${competitorData.avgHeadings}
- Top keywords to include: ${competitorData.topKeywords?.slice(0, 10).map(k => k.word).join(', ')}
- Common entities: ${competitorData.commonEntities?.join(', ')}
- Content gaps to fill: Provide unique value not covered by competitors`;
  }

  prompt += `\n\nFORMATTING REQUIREMENTS:
- Start with an engaging hook that addresses user intent
- Use short paragraphs (2-3 sentences maximum)
- Include bullet points or numbered lists for scannability
- Add relevant statistics and data points
- Include actionable takeaways and next steps
- End with a strong conclusion that reinforces key points
- Naturally incorporate "${keyword}" with 1-2% density
- Use semantic variations throughout content

OUTPUT QUALITY STANDARDS:
- Provide unique insights not available elsewhere
- Include specific, actionable advice
- Use current, up-to-date information
- Maintain professional credibility throughout
- Ensure content serves user search intent completely`;

  return prompt;
}

// Helper function to update usage tracking
async function updateUsageTracking(userId, type) {
  const today = new Date().toISOString().split('T')[0];
  
  const { data: existing } = await supabase
    .from('usage_tracking')
    .select('*')
    .eq('user_id', userId)
    .eq('date', today)
    .single();

  if (existing) {
    await supabase
      .from('usage_tracking')
      .update({ 
        [type]: existing[type] + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', existing.id);
  } else {
    await supabase
      .from('usage_tracking')
      .insert({
        user_id: userId,
        date: today,
        [type]: 1,
        created_at: new Date().toISOString()
      });
  }
}

module.exports = router;
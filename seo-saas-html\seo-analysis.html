<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Analysis - SEO Pro</title>
    <meta name="description" content="Comprehensive SEO analysis tool with keyword density, heading structure, and competitor analysis">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    <link href="css/professional-fixes.css" rel="stylesheet">
    
    <style>
        /* SEO Analysis Layout - Professional & Wide */
        .page-container {
            min-height: 100vh;
            background: #f9fafb;
        }

        .analysis-wrapper {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
        }

        .analysis-header {
            text-align: center;
            margin-bottom: 3rem;
            background: white;
            padding: 3rem 2rem;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #f3f4f6;
        }

        .analysis-title {
            font-size: 2.5rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 1rem;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .analysis-subtitle {
            font-size: 1.25rem;
            color: #6b7280;
            margin: 0;
            max-width: 600px;
            margin: 0 auto;
        }

        /* Analysis Form - Wide Layout */
        .analysis-form {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #f3f4f6;
            padding: 2rem;
            margin-bottom: 2rem;
        }

        .form-section {
            margin-bottom: 2rem;
        }

        .form-section-title {
            font-size: 1.25rem;
            font-weight: 600;
            color: #111827;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid #f3f4f6;
        }

        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin-bottom: 1.5rem;
        }

        .form-group-wide {
            margin-bottom: 1.5rem;
        }

        .form-input-wide, .form-textarea-wide, .form-select-wide {
            width: 100%;
            padding: 1rem 1.25rem;
            font-size: 0.875rem;
            border: 1px solid #d1d5db;
            border-radius: 0.5rem;
            min-height: 3rem;
            background: white;
            transition: all 0.3s ease;
        }

        .form-input-wide:focus, .form-textarea-wide:focus, .form-select-wide:focus {
            border-color: #3b82f6;
            box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
            outline: none;
        }

        .form-textarea-wide {
            min-height: 8rem;
            resize: vertical;
        }

        .form-label-wide {
            display: block;
            font-size: 0.875rem;
            font-weight: 500;
            color: #374151;
            margin-bottom: 0.5rem;
        }

        /* Analysis Results */
        .analysis-results {
            display: none;
        }

        .analysis-results.active {
            display: block;
        }

        /* Overall Score Section */
        .score-section {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #f3f4f6;
            padding: 3rem 2rem;
            text-align: center;
            margin-bottom: 2rem;
        }

        .score-circle {
            width: 10rem;
            height: 10rem;
            position: relative;
            margin: 0 auto 2rem;
            border-radius: 50%;
            background: conic-gradient(#3b82f6 0deg 252deg, #f3f4f6 252deg 360deg);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .score-circle::before {
            content: '';
            width: 8rem;
            height: 8rem;
            background: white;
            border-radius: 50%;
            position: absolute;
        }

        .score-text {
            position: relative;
            z-index: 1;
            text-align: center;
        }

        .score-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #111827;
            margin-bottom: 0.25rem;
        }

        .score-label {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
        }

        .score-description {
            font-size: 1.125rem;
            color: #6b7280;
            margin-top: 1rem;
        }

        /* Module Scores Grid */
        .module-scores {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 1.5rem;
            margin: 2rem 0;
        }

        .module-card {
            background: white;
            border: 1px solid #f3f4f6;
            border-radius: 0.75rem;
            padding: 1.5rem;
            text-align: center;
            transition: all 0.3s ease;
        }

        .module-card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }

        .module-score {
            font-size: 2rem;
            font-weight: 700;
            color: #3b82f6;
            margin-bottom: 0.5rem;
        }

        .module-name {
            font-size: 0.875rem;
            color: #6b7280;
            font-weight: 500;
            text-transform: uppercase;
            letter-spacing: 0.05em;
        }

        /* Analysis Details */
        .analysis-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 2rem;
            margin-top: 2rem;
        }

        .detail-card {
            background: white;
            border-radius: 0.75rem;
            box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
            border: 1px solid #f3f4f6;
            overflow: hidden;
        }

        .detail-header {
            padding: 1.5rem 2rem;
            background: #f9fafb;
            border-bottom: 1px solid #f3f4f6;
        }

        .detail-title {
            font-size: 1.125rem;
            font-weight: 600;
            color: #111827;
            margin: 0;
        }

        .detail-content {
            padding: 2rem;
        }

        /* Progress Indicator */
        .analysis-progress {
            text-align: center;
            padding: 3rem;
            display: none;
        }

        .analysis-progress.active {
            display: block;
        }

        .progress-circle {
            width: 4rem;
            height: 4rem;
            border: 3px solid #f3f4f6;
            border-top: 3px solid #3b82f6;
            border-radius: 50%;
            margin: 0 auto 1rem;
            animation: spin 1s linear infinite;
        }

        .progress-text {
            font-size: 1.125rem;
            color: #6b7280;
            margin-bottom: 0.5rem;
        }

        .progress-step {
            font-size: 0.875rem;
            color: #9ca3af;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* Mobile Responsive */
        @media (max-width: 768px) {
            .analysis-wrapper {
                padding: 1rem;
            }

            .analysis-header {
                padding: 2rem 1rem;
            }

            .analysis-title {
                font-size: 2rem;
            }

            .form-grid {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .module-scores {
                grid-template-columns: 1fr;
                gap: 1rem;
            }

            .analysis-details {
                grid-template-columns: 1fr;
                gap: 1.5rem;
            }

            .score-circle {
                width: 8rem;
                height: 8rem;
            }

            .score-circle::before {
                width: 6rem;
                height: 6rem;
            }

            .score-number {
                font-size: 2rem;
            }
        }

        /* Utility Classes */
        .space-y-4 > * + * {
            margin-top: 1rem;
        }

        .space-y-3 > * + * {
            margin-top: 0.75rem;
        }

        .text-green-600 {
            color: #059669;
        }

        .text-yellow-600 {
            color: #d97706;
        }

        .text-red-600 {
            color: #dc2626;
        }

        .text-blue-600 {
            color: #2563eb;
        }

        .text-red-800 {
            color: #991b1b;
        }

        .text-red-700 {
            color: #b91c1c;
        }

        .text-yellow-800 {
            color: #92400e;
        }

        .text-yellow-700 {
            color: #a16207;
        }

        .text-blue-800 {
            color: #1e40af;
        }

        .text-blue-700 {
            color: #1d4ed8;
        }

        .bg-red-50 {
            background-color: #fef2f2;
        }

        .bg-yellow-50 {
            background-color: #fffbeb;
        }

        .bg-blue-50 {
            background-color: #eff6ff;
        }

        .border-red-200 {
            border-color: #fecaca;
        }

        .border-yellow-200 {
            border-color: #fde68a;
        }

        .border-blue-200 {
            border-color: #bfdbfe;
        }

        .rounded {
            border-radius: 0.25rem;
        }

        .font-semibold {
            font-weight: 600;
        }

        .text-lg {
            font-size: 1.125rem;
        }

        .text-xl {
            font-size: 1.25rem;
        }

        .text-2xl {
            font-size: 1.5rem;
        }

        .text-3xl {
            font-size: 1.875rem;
        }

        .text-sm {
            font-size: 0.875rem;
        }

        .p-3 {
            padding: 0.75rem;
        }

        .border {
            border-width: 1px;
        }

        .mt-8 {
            margin-top: 2rem;
        }

        .mt-1 {
            margin-top: 0.25rem;
        }

        .mb-4 {
            margin-bottom: 1rem;
        }

        .flex {
            display: flex;
        }

        .justify-between {
            justify-content: space-between;
        }

        .justify-center {
            justify-content: center;
        }

        .items-center {
            align-items: center;
        }

        .flex-wrap {
            flex-wrap: wrap;
        }

        .gap-4 {
            gap: 1rem;
        }
    </style>
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container-fluid px-6">
            <div class="flex items-center">
                <button class="mobile-menu-btn mr-4 lg:hidden" onclick="toggleSidebar()" aria-label="Toggle sidebar">
                    <span class="hamburger"></span>
                </button>
                <a href="index.html" class="logo">
                    <span class="logo-text">SEO Pro</span>
                </a>
            </div>
            
            <div class="flex items-center gap-4">
                <div class="hidden md:block">
                    <div class="input-group">
                        <input type="search" class="form-input" placeholder="Search..." style="width: 300px;">
                    </div>
                </div>
                
                <button class="relative p-2 text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                    </svg>
                    <span class="absolute top-0 right-0 w-2 h-2 bg-error rounded-full"></span>
                </button>
                
                <div class="relative">
                    <button class="flex items-center gap-2 p-2" onclick="toggleUserMenu()">
                        <div class="w-8 h-8 bg-gradient rounded-full flex items-center justify-center text-white font-semibold">
                            JD
                        </div>
                        <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 hidden">
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Profile</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Settings</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Billing</a>
                        <hr class="my-2">
                        <a href="login.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Sign Out</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <div class="flex">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" id="sidebar" style="width: 280px; background: var(--white); border-right: 1px solid var(--gray-200); height: calc(100vh - 4rem); position: fixed; top: 4rem; left: 0; overflow-y: auto; transition: transform 0.3s ease;">
            <nav class="p-6">
                <a href="dashboard.html" class="sidebar-link">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Dashboard
                </a>
                
                <a href="content-generator.html" class="sidebar-link">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Content Generator
                </a>
                
                <a href="seo-analysis.html" class="sidebar-link active">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    SEO Analysis
                </a>
            </nav>
        </aside>
        
    </div>

    <!-- Main Content - Full Width Layout -->
    <div class="page-container">
        <div class="analysis-wrapper">
            <!-- Header -->
            <div class="analysis-header">
                <h1 class="analysis-title">SEO Analysis</h1>
                <p class="analysis-subtitle">Comprehensive analysis of your content's SEO performance and optimization opportunities</p>
            </div>

            <!-- Analysis Form -->
            <div class="analysis-form">
                <div class="form-section">
                    <h2 class="form-section-title">Start New Analysis</h2>

                    <form id="analysisForm" onsubmit="handleAnalysis(event)">
                        <div class="form-grid">
                            <div class="form-group-wide">
                                <label class="form-label-wide">Target URL *</label>
                                <input type="url" class="form-input-wide" placeholder="https://example.com/page-to-analyze" required>
                                <p class="text-sm text-gray-600 mt-1">Enter the URL you want to analyze for SEO optimization</p>
                            </div>

                            <div class="form-group-wide">
                                <label class="form-label-wide">Analysis Type</label>
                                <select class="form-select-wide">
                                    <option value="comprehensive">Comprehensive Analysis</option>
                                    <option value="quick">Quick Scan</option>
                                    <option value="competitor">Competitor Analysis</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group-wide">
                            <label class="form-label-wide">Target Keywords *</label>
                            <input type="text" class="form-input-wide" placeholder="SEO optimization, content marketing, digital strategy" required>
                            <p class="text-sm text-gray-600 mt-1">Keywords you want to rank for (comma-separated)</p>
                        </div>

                        <div class="form-grid">
                            <div class="form-group-wide">
                                <label class="form-label-wide">Content Type</label>
                                <select class="form-select-wide">
                                    <option value="blog-post">Blog Post</option>
                                    <option value="product-page">Product Page</option>
                                    <option value="landing-page">Landing Page</option>
                                    <option value="homepage">Homepage</option>
                                    <option value="category-page">Category Page</option>
                                </select>
                            </div>

                            <div class="form-group-wide">
                                <label class="form-label-wide">Industry</label>
                                <select class="form-select-wide">
                                    <option value="technology">Technology</option>
                                    <option value="healthcare">Healthcare</option>
                                    <option value="finance">Finance</option>
                                    <option value="ecommerce">E-commerce</option>
                                    <option value="education">Education</option>
                                    <option value="other">Other</option>
                                </select>
                            </div>
                        </div>

                        <div class="form-group-wide">
                            <label class="form-label-wide">Competitor URLs (Optional)</label>
                            <textarea class="form-textarea-wide" placeholder="Enter competitor URLs to compare against, one per line"></textarea>
                            <p class="text-sm text-gray-600 mt-1">We'll analyze how your content compares to these competitors</p>
                        </div>

                        <div class="text-center">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Start Analysis
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Analysis Progress -->
            <div class="analysis-progress" id="analysis-progress">
                <div class="progress-circle"></div>
                <div class="progress-text">Analyzing your content...</div>
                <div class="progress-step" id="progress-step">Fetching page content</div>
            </div>

            <!-- Analysis Results -->
            <div class="analysis-results" id="analysis-results">
                <!-- Overall Score -->
                <div class="score-section">
                    <div class="score-circle">
                        <div class="score-text">
                            <div class="score-number">85</div>
                            <div class="score-label">SEO Score</div>
                        </div>
                    </div>
                    <div class="score-description">
                        Your content has good SEO optimization with room for improvement in technical aspects.
                    </div>
                </div>

                <!-- Module Scores -->
                <div class="module-scores">
                    <div class="module-card">
                        <div class="module-score">92</div>
                        <div class="module-name">Content Quality</div>
                    </div>

                    <div class="module-card">
                        <div class="module-score">78</div>
                        <div class="module-name">Technical SEO</div>
                    </div>

                    <div class="module-card">
                        <div class="module-score">85</div>
                        <div class="module-name">Keyword Optimization</div>
                    </div>

                    <div class="module-card">
                        <div class="module-score">71</div>
                        <div class="module-name">Page Performance</div>
                    </div>

                    <div class="module-card">
                        <div class="module-score">89</div>
                        <div class="module-name">User Experience</div>
                    </div>

                    <div class="module-card">
                        <div class="module-score">76</div>
                        <div class="module-name">Mobile Optimization</div>
                    </div>
                </div>

                <!-- Detailed Analysis -->
                <div class="analysis-details">
                    <div class="detail-card">
                        <div class="detail-header">
                            <h3 class="detail-title">Content Analysis</h3>
                        </div>
                        <div class="detail-content">
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span>Word Count</span>
                                    <span class="font-semibold">1,247 words</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Reading Level</span>
                                    <span class="font-semibold">Grade 8</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Keyword Density</span>
                                    <span class="font-semibold">2.3%</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Headings Structure</span>
                                    <span class="font-semibold text-green-600">Good</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-card">
                        <div class="detail-header">
                            <h3 class="detail-title">Technical SEO</h3>
                        </div>
                        <div class="detail-content">
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span>Meta Title</span>
                                    <span class="font-semibold text-green-600">Optimized</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Meta Description</span>
                                    <span class="font-semibold text-yellow-600">Needs Work</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>URL Structure</span>
                                    <span class="font-semibold text-green-600">Good</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Schema Markup</span>
                                    <span class="font-semibold text-red-600">Missing</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-card">
                        <div class="detail-header">
                            <h3 class="detail-title">Performance Metrics</h3>
                        </div>
                        <div class="detail-content">
                            <div class="space-y-4">
                                <div class="flex justify-between items-center">
                                    <span>Page Load Speed</span>
                                    <span class="font-semibold">2.3s</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Core Web Vitals</span>
                                    <span class="font-semibold text-yellow-600">Needs Improvement</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>Mobile Friendly</span>
                                    <span class="font-semibold text-green-600">Yes</span>
                                </div>
                                <div class="flex justify-between items-center">
                                    <span>HTTPS</span>
                                    <span class="font-semibold text-green-600">Secure</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <div class="detail-card">
                        <div class="detail-header">
                            <h3 class="detail-title">Recommendations</h3>
                        </div>
                        <div class="detail-content">
                            <div class="space-y-3">
                                <div class="p-3 bg-red-50 border border-red-200 rounded">
                                    <div class="font-semibold text-red-800">High Priority</div>
                                    <div class="text-red-700">Add schema markup for better search visibility</div>
                                </div>
                                <div class="p-3 bg-yellow-50 border border-yellow-200 rounded">
                                    <div class="font-semibold text-yellow-800">Medium Priority</div>
                                    <div class="text-yellow-700">Optimize meta description length (currently too short)</div>
                                </div>
                                <div class="p-3 bg-blue-50 border border-blue-200 rounded">
                                    <div class="font-semibold text-blue-800">Low Priority</div>
                                    <div class="text-blue-700">Consider adding more internal links</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="text-center mt-8">
                    <div class="flex justify-center gap-4 flex-wrap">
                        <button class="btn btn-secondary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                            </svg>
                            Export Report
                        </button>

                        <button class="btn btn-secondary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                            </svg>
                            Copy Results
                        </button>

                        <button class="btn btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                            </svg>
                            Generate Optimized Content
                        </button>

                        <button class="btn btn-primary">
                            <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                            </svg>
                            Re-analyze
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function handleAnalysis(event) {
            event.preventDefault();

            // Hide form and show progress
            document.querySelector('.analysis-form').style.display = 'none';
            document.getElementById('analysis-progress').classList.add('active');

            // Simulate analysis process
            const steps = [
                'Fetching page content...',
                'Analyzing HTML structure...',
                'Checking meta tags...',
                'Evaluating keyword density...',
                'Testing page performance...',
                'Comparing with competitors...',
                'Generating recommendations...'
            ];

            let stepIndex = 0;
            const progressInterval = setInterval(() => {
                if (stepIndex < steps.length) {
                    document.getElementById('progress-step').textContent = steps[stepIndex];
                    stepIndex++;
                } else {
                    clearInterval(progressInterval);
                    showResults();
                }
            }, 1000);
        }

        function showResults() {
            // Hide progress and show results
            document.getElementById('analysis-progress').classList.remove('active');
            document.getElementById('analysis-results').classList.add('active');
        }

        function toggleUserMenu() {
            const userMenu = document.getElementById('userMenu');
            userMenu.classList.toggle('hidden');
        }

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('userMenu');
            const userMenuButton = event.target.closest('button[onclick="toggleUserMenu()"]');

            if (!userMenu.contains(event.target) && !userMenuButton) {
                userMenu.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
                    
                    <form id="analysisForm" onsubmit="handleAnalysis(event)">
                        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                            <div class="form-group">
                                <label for="targetUrl" class="form-label form-label-required">Target URL</label>
                                <input 
                                    type="url" 
                                    id="targetUrl" 
                                    name="targetUrl" 
                                    class="form-input" 
                                    placeholder="https://example.com/page-to-analyze"
                                    required
                                >
                                <p class="form-help">Enter the URL you want to analyze for SEO optimization</p>
                            </div>
                            
                            <div class="form-group">
                                <label for="targetKeywords" class="form-label form-label-required">Target Keywords</label>
                                <input 
                                    type="text" 
                                    id="targetKeywords" 
                                    name="targetKeywords" 
                                    class="form-input" 
                                    placeholder="SEO optimization, content marketing"
                                    required
                                >
                                <p class="form-help">Keywords you want to rank for (comma-separated)</p>
                            </div>
                            
                            <div class="form-group">
                                <label for="competitorUrls" class="form-label">Competitor URLs</label>
                                <textarea 
                                    id="competitorUrls" 
                                    name="competitorUrls" 
                                    class="form-textarea" 
                                    placeholder="https://competitor1.com/page&#10;https://competitor2.com/page"
                                    rows="3"
                                ></textarea>
                                <p class="form-help">URLs of competing pages (one per line)</p>
                            </div>
                            
                            <div class="form-group">
                                <label for="analysisType" class="form-label">Analysis Type</label>
                                <select id="analysisType" name="analysisType" class="form-select">
                                    <option value="comprehensive">Comprehensive Analysis</option>
                                    <option value="quick">Quick Scan</option>
                                    <option value="technical">Technical SEO</option>
                                    <option value="content">Content Analysis</option>
                                    <option value="competitor">Competitor Comparison</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="flex justify-end mt-6">
                            <button type="submit" class="btn btn-primary">
                                <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                </svg>
                                Start Analysis
                            </button>
                        </div>
                    </form>
                </div>
                
                <!-- Analysis Loading -->
                <div class="analysis-loading" id="analysisLoading">
                    <div class="spinner mx-auto mb-4"></div>
                    <h3 class="text-xl font-semibold mb-2">Analyzing Your Page...</h3>
                    <p class="text-gray-600">This may take 30-60 seconds depending on the page size</p>
                </div>
                
                <!-- Analysis Results -->
                <div class="analysis-results" id="analysisResults">
                    <!-- Overall Score -->
                    <div class="analysis-module">
                        <div class="text-center">
                            <h2 class="text-2xl font-bold mb-6">Overall SEO Score</h2>
                            
                            <div class="score-circle">
                                <svg width="120" height="120" class="circular-progress">
                                    <circle cx="60" cy="60" r="54" stroke="#e5e7eb" stroke-width="8" fill="none"></circle>
                                    <circle cx="60" cy="60" r="54" stroke="url(#scoreGradient)" stroke-width="8" fill="none" 
                                            stroke-dasharray="339.3" stroke-dashoffset="67.9" stroke-linecap="round"
                                            transform="rotate(-90 60 60)"></circle>
                                    <defs>
                                        <linearGradient id="scoreGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                            <stop offset="0%" style="stop-color:#3b82f6"/>
                                            <stop offset="100%" style="stop-color:#7c3aed"/>
                                        </linearGradient>
                                    </defs>
                                </svg>
                                <div class="score-text">
                                    <div class="score-number">82</div>
                                    <div class="score-label">out of 100</div>
                                </div>
                            </div>
                            
                            <div class="module-score excellent">Excellent</div>
                            <p class="text-gray-600 mt-2">Your page is well-optimized with room for improvement</p>
                        </div>
                    </div>
                    
                    <!-- Analysis Modules Grid -->
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Keyword Density Analysis -->
                        <div class="analysis-module">
                            <div class="module-header">
                                <div class="module-icon bg-blue-100">
                                    <svg class="w-6 h-6 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="module-title">Keyword Density</h3>
                                </div>
                                <div class="module-score good">Good (85/100)</div>
                            </div>
                            
                            <div class="space-y-3">
                                <div class="keyword-density-item">
                                    <span class="font-medium">SEO optimization</span>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-600 mr-2">2.3%</span>
                                        <div class="density-bar">
                                            <div class="density-fill" style="width: 77%"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="keyword-density-item">
                                    <span class="font-medium">content marketing</span>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-600 mr-2">1.8%</span>
                                        <div class="density-bar">
                                            <div class="density-fill" style="width: 60%"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="keyword-density-item">
                                    <span class="font-medium">search engine</span>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-600 mr-2">1.2%</span>
                                        <div class="density-bar">
                                            <div class="density-fill" style="width: 40%"></div>
                                        </div>
                                    </div>
                                </div>
                                
                                <div class="keyword-density-item">
                                    <span class="font-medium">digital marketing</span>
                                    <div class="flex items-center">
                                        <span class="text-sm text-gray-600 mr-2">0.9%</span>
                                        <div class="density-bar">
                                            <div class="density-fill" style="width: 30%"></div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Heading Structure -->
                        <div class="analysis-module">
                            <div class="module-header">
                                <div class="module-icon bg-green-100">
                                    <svg class="w-6 h-6 text-success" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 10h16M4 14h16M4 18h16"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="module-title">Heading Structure</h3>
                                </div>
                                <div class="module-score excellent">Excellent (95/100)</div>
                            </div>
                            
                            <div class="space-y-2">
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="font-medium">H1 Tags</span>
                                    <span class="text-success font-semibold">1 ✓</span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="font-medium">H2 Tags</span>
                                    <span class="text-success font-semibold">4 ✓</span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="font-medium">H3 Tags</span>
                                    <span class="text-success font-semibold">7 ✓</span>
                                </div>
                                <div class="flex items-center justify-between p-2 bg-gray-50 rounded">
                                    <span class="font-medium">H4-H6 Tags</span>
                                    <span class="text-warning font-semibold">2 ⚠</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Content Quality -->
                        <div class="analysis-module">
                            <div class="module-header">
                                <div class="module-icon bg-purple-100">
                                    <svg class="w-6 h-6 text-secondary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="module-title">Content Quality</h3>
                                </div>
                                <div class="module-score excellent">Excellent (88/100)</div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4">
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-primary">2,456</div>
                                    <div class="text-sm text-gray-600">Words</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-success">8.2</div>
                                    <div class="text-sm text-gray-600">Reading Level</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-warning">92%</div>
                                    <div class="text-sm text-gray-600">Uniqueness</div>
                                </div>
                                <div class="text-center">
                                    <div class="text-2xl font-bold text-success">45</div>
                                    <div class="text-sm text-gray-600">LSI Keywords</div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Technical SEO -->
                        <div class="analysis-module">
                            <div class="module-header">
                                <div class="module-icon bg-orange-100">
                                    <svg class="w-6 h-6 text-warning" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="module-title">Technical SEO</h3>
                                </div>
                                <div class="module-score good">Good (78/100)</div>
                            </div>
                            
                            <div class="space-y-2">
                                <div class="flex items-center justify-between">
                                    <span>Page Speed</span>
                                    <span class="text-success">85/100 ✓</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span>Mobile Friendly</span>
                                    <span class="text-success">Yes ✓</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span>SSL Certificate</span>
                                    <span class="text-success">Valid ✓</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span>Meta Description</span>
                                    <span class="text-warning">Too Long ⚠</span>
                                </div>
                                <div class="flex items-center justify-between">
                                    <span>Image Alt Tags</span>
                                    <span class="text-error">Missing ✗</span>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Competitor Comparison -->
                        <div class="analysis-module lg:col-span-2">
                            <div class="module-header">
                                <div class="module-icon bg-red-100">
                                    <svg class="w-6 h-6 text-error" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="module-title">Competitor Comparison</h3>
                                </div>
                                <div class="module-score fair">Needs Work (65/100)</div>
                            </div>
                            
                            <div class="overflow-x-auto">
                                <table class="table">
                                    <thead>
                                        <tr>
                                            <th>Metric</th>
                                            <th>Your Page</th>
                                            <th>Competitor 1</th>
                                            <th>Competitor 2</th>
                                            <th>Average</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <tr>
                                            <td>Word Count</td>
                                            <td class="font-semibold">2,456</td>
                                            <td>3,120</td>
                                            <td>2,890</td>
                                            <td>2,822</td>
                                        </tr>
                                        <tr>
                                            <td>Keyword Density</td>
                                            <td class="font-semibold">2.3%</td>
                                            <td>1.8%</td>
                                            <td>2.1%</td>
                                            <td>2.1%</td>
                                        </tr>
                                        <tr>
                                            <td>Backlinks</td>
                                            <td class="font-semibold text-error">45</td>
                                            <td>156</td>
                                            <td>98</td>
                                            <td>100</td>
                                        </tr>
                                        <tr>
                                            <td>Page Speed</td>
                                            <td class="font-semibold text-success">85</td>
                                            <td>72</td>
                                            <td>68</td>
                                            <td>75</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                        
                        <!-- E-E-A-T Compliance -->
                        <div class="analysis-module lg:col-span-2">
                            <div class="module-header">
                                <div class="module-icon bg-indigo-100">
                                    <svg class="w-6 h-6 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z"></path>
                                    </svg>
                                </div>
                                <div class="flex-1">
                                    <h3 class="module-title">E-E-A-T Compliance</h3>
                                </div>
                                <div class="module-score good">Good (80/100)</div>
                            </div>
                            
                            <div class="grid grid-cols-2 lg:grid-cols-4 gap-4">
                                <div class="text-center p-4 bg-gray-50 rounded-lg">
                                    <div class="text-2xl font-bold text-success mb-2">85</div>
                                    <div class="text-sm font-medium">Experience</div>
                                    <div class="text-xs text-gray-600">First-hand experience evident</div>
                                </div>
                                
                                <div class="text-center p-4 bg-gray-50 rounded-lg">
                                    <div class="text-2xl font-bold text-warning mb-2">75</div>
                                    <div class="text-sm font-medium">Expertise</div>
                                    <div class="text-xs text-gray-600">Good domain knowledge</div>
                                </div>
                                
                                <div class="text-center p-4 bg-gray-50 rounded-lg">
                                    <div class="text-2xl font-bold text-warning mb-2">78</div>
                                    <div class="text-sm font-medium">Authoritativeness</div>
                                    <div class="text-xs text-gray-600">Need more citations</div>
                                </div>
                                
                                <div class="text-center p-4 bg-gray-50 rounded-lg">
                                    <div class="text-2xl font-bold text-success mb-2">82</div>
                                    <div class="text-sm font-medium">Trustworthiness</div>
                                    <div class="text-xs text-gray-600">Good trust signals</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Recommendations -->
                    <div class="analysis-module">
                        <h3 class="text-xl font-semibold mb-6">Recommendations</h3>
                        
                        <div class="space-y-4">
                            <div class="recommendation-item high">
                                <span class="priority-badge priority-high">High</span>
                                <div>
                                    <h4 class="font-semibold mb-1">Add Alt Text to Images</h4>
                                    <p class="text-sm text-gray-600">15 images are missing alt text. This affects accessibility and SEO rankings.</p>
                                </div>
                            </div>
                            
                            <div class="recommendation-item high">
                                <span class="priority-badge priority-high">High</span>
                                <div>
                                    <h4 class="font-semibold mb-1">Optimize Meta Description</h4>
                                    <p class="text-sm text-gray-600">Your meta description is 183 characters. Keep it under 160 characters for better SERP display.</p>
                                </div>
                            </div>
                            
                            <div class="recommendation-item medium">
                                <span class="priority-badge priority-medium">Medium</span>
                                <div>
                                    <h4 class="font-semibold mb-1">Increase Content Length</h4>
                                    <p class="text-sm text-gray-600">Competitors have 15-20% more content. Consider adding 400-600 more words of valuable content.</p>
                                </div>
                            </div>
                            
                            <div class="recommendation-item medium">
                                <span class="priority-badge priority-medium">Medium</span>
                                <div>
                                    <h4 class="font-semibold mb-1">Build More Backlinks</h4>
                                    <p class="text-sm text-gray-600">Your page has 55% fewer backlinks than top competitors. Focus on earning quality backlinks.</p>
                                </div>
                            </div>
                            
                            <div class="recommendation-item low">
                                <span class="priority-badge priority-low">Low</span>
                                <div>
                                    <h4 class="font-semibold mb-1">Add Schema Markup</h4>
                                    <p class="text-sm text-gray-600">Implement structured data to help search engines better understand your content.</p>
                                </div>
                            </div>
                            
                            <div class="recommendation-item low">
                                <span class="priority-badge priority-low">Low</span>
                                <div>
                                    <h4 class="font-semibold mb-1">Optimize Internal Linking</h4>
                                    <p class="text-sm text-gray-600">Add 3-5 more internal links to related pages to improve site architecture.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Export Options -->
                    <div class="analysis-module">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-xl font-semibold">Export Analysis</h3>
                                <p class="text-gray-600">Download your analysis report in various formats</p>
                            </div>
                            
                            <div class="flex gap-3">
                                <button class="btn btn-secondary" onclick="exportReport('pdf')">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    PDF Report
                                </button>
                                
                                <button class="btn btn-secondary" onclick="exportReport('excel')">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 17v-2m3 2v-4m3 4v-6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Excel Report
                                </button>
                                
                                <button class="btn btn-primary" onclick="shareReport()">
                                    <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z"></path>
                                    </svg>
                                    Share Link
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Mobile Sidebar Overlay -->
    <div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 hidden lg:hidden z-30" onclick="toggleSidebar()"></div>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            sidebar.classList.toggle('active');
            overlay.classList.toggle('hidden');
            menuBtn.classList.toggle('active');
        }
        
        function toggleUserMenu() {
            const userMenu = document.getElementById('userMenu');
            userMenu.classList.toggle('hidden');
        }
        
        function handleAnalysis(event) {
            event.preventDefault();
            
            // Hide form and show loading
            document.querySelector('.analysis-form').style.display = 'none';
            document.getElementById('analysisLoading').classList.add('active');
            
            // Simulate analysis process
            setTimeout(() => {
                document.getElementById('analysisLoading').classList.remove('active');
                document.getElementById('analysisResults').classList.add('active');
                
                // Scroll to results
                document.getElementById('analysisResults').scrollIntoView({
                    behavior: 'smooth',
                    block: 'start'
                });
            }, 3000);
        }
        
        function exportReport(format) {
            console.log(`Exporting report as ${format}`);
            alert(`${format.toUpperCase()} report export would be initiated here`);
        }
        
        function shareReport() {
            const shareUrl = window.location.href + '?report=abc123';
            if (navigator.share) {
                navigator.share({
                    title: 'SEO Analysis Report',
                    text: 'Check out this SEO analysis report',
                    url: shareUrl
                });
            } else {
                // Fallback to copying to clipboard
                navigator.clipboard.writeText(shareUrl).then(() => {
                    alert('Report link copied to clipboard!');
                });
            }
        }
        
        // Reset form to start new analysis
        function startNewAnalysis() {
            document.querySelector('.analysis-form').style.display = 'block';
            document.getElementById('analysisResults').classList.remove('active');
            document.getElementById('analysisForm').reset();
        }
        
        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('userMenu');
            const userMenuButton = event.target.closest('button[onclick="toggleUserMenu()"]');
            
            if (!userMenu.contains(event.target) && !userMenuButton) {
                userMenu.classList.add('hidden');
            }
        });
    </script>

    <!-- Core JavaScript -->
    <script src="js/config.js"></script>
    <script src="js/supabase.js"></script>
    <script src="js/api.js"></script>
    <script src="js/seo-analyzer.js"></script>
    <script src="js/app-init.js"></script>
    <script src="js/main.js"></script>
</body>
</html>
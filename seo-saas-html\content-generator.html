<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Content Generator - SEO Pro</title>
    <meta name="description" content="Generate high-quality, SEO-optimized content with AI">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    
    <style>
        /* Content Generator Specific Styles */
        .form-step-content {
            display: none;
        }
        
        .form-step-content.active {
            display: block;
        }
        
        .generated-content {
            background: var(--white);
            border-radius: var(--radius-lg);
            padding: var(--space-6);
            border: 1px solid var(--gray-200);
            margin-top: var(--space-6);
        }
        
        .content-toolbar {
            display: flex;
            gap: var(--space-2);
            margin-bottom: var(--space-4);
            flex-wrap: wrap;
        }
        
        .content-editor {
            min-height: 400px;
            max-height: 600px;
            overflow-y: auto;
            padding: var(--space-4);
            border: 1px solid var(--gray-200);
            border-radius: var(--radius-md);
            background: var(--white);
            line-height: 1.6;
            font-family: var(--font-primary);
        }
        
        .content-editor:focus {
            outline: 2px solid var(--primary-blue);
            outline-offset: 2px;
        }
        
        .word-count {
            color: var(--gray-600);
            font-size: var(--text-sm);
            margin-top: var(--space-2);
        }
        
        .generation-progress {
            display: none;
            text-align: center;
            padding: var(--space-8);
        }
        
        .generation-progress.active {
            display: block;
        }
        
        .progress-steps {
            max-width: 400px;
            margin: 0 auto var(--space-6);
        }
        
        .progress-step {
            display: flex;
            align-items: center;
            margin-bottom: var(--space-4);
            opacity: 0.4;
            transition: all var(--transition-normal);
        }
        
        .progress-step.active {
            opacity: 1;
        }
        
        .progress-step.completed {
            opacity: 1;
        }
        
        .progress-step-icon {
            width: 2rem;
            height: 2rem;
            border-radius: var(--radius-full);
            background: var(--gray-200);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--space-3);
            transition: all var(--transition-normal);
        }
        
        .progress-step.active .progress-step-icon {
            background: var(--primary-gradient);
            color: var(--white);
        }
        
        .progress-step.completed .progress-step-icon {
            background: var(--success-green);
            color: var(--white);
        }
        
        .seo-score {
            display: inline-flex;
            align-items: center;
            padding: var(--space-2) var(--space-4);
            border-radius: var(--radius-full);
            font-weight: var(--font-semibold);
            font-size: var(--text-sm);
        }
        
        .seo-score.excellent {
            background: rgba(16, 185, 129, 0.1);
            color: var(--success-green);
        }
        
        .seo-score.good {
            background: rgba(245, 158, 11, 0.1);
            color: var(--warning-orange);
        }
        
        .seo-score.poor {
            background: rgba(239, 68, 68, 0.1);
            color: var(--error-red);
        }
    </style>
</head>
<body>
    <!-- Include the same header and sidebar from dashboard.html -->
    <header class="header">
        <nav class="nav container-fluid px-6">
            <div class="flex items-center">
                <button class="mobile-menu-btn mr-4 lg:hidden" onclick="toggleSidebar()" aria-label="Toggle sidebar">
                    <span class="hamburger"></span>
                </button>
                <a href="index.html" class="logo">
                    <span class="logo-text">SEO Pro</span>
                </a>
            </div>
            
            <div class="flex items-center gap-4">
                <div class="hidden md:block">
                    <div class="input-group">
                        <input type="search" class="form-input" placeholder="Search..." style="width: 300px;">
                    </div>
                </div>
                
                <button class="relative p-2 text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                    </svg>
                    <span class="absolute top-0 right-0 w-2 h-2 bg-error rounded-full"></span>
                </button>
                
                <div class="relative">
                    <button class="flex items-center gap-2 p-2" onclick="toggleUserMenu()">
                        <div class="w-8 h-8 bg-gradient rounded-full flex items-center justify-center text-white font-semibold">
                            JD
                        </div>
                        <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 hidden">
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Profile</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Settings</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Billing</a>
                        <hr class="my-2">
                        <a href="login.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Sign Out</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <div class="flex">
        <!-- Sidebar (same as dashboard) -->
        <aside class="dashboard-sidebar" id="sidebar" style="width: 280px; background: var(--white); border-right: 1px solid var(--gray-200); height: calc(100vh - 4rem); position: fixed; top: 4rem; left: 0; overflow-y: auto; transition: transform 0.3s ease;">
            <nav class="p-6">
                <a href="dashboard.html" class="sidebar-link">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Dashboard
                </a>
                
                <a href="content-generator.html" class="sidebar-link active">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Content Generator
                </a>
                
                <a href="seo-analysis.html" class="sidebar-link">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    SEO Analysis
                </a>
                
                <a href="#" class="sidebar-link">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                    </svg>
                    Keyword Research
                </a>
                
                <a href="#" class="sidebar-link">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                    </svg>
                    Projects
                </a>
                
                <a href="#" class="sidebar-link">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Analytics
                </a>
                
                <hr class="my-4 border-gray-200">
                
                <a href="#" class="sidebar-link">
                    <svg class="w-5 h-5 mr-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Settings
                </a>
            </nav>
        </aside>
        
        <!-- Main Content -->
        <main class="dashboard-main" style="margin-left: 280px; min-height: calc(100vh - 4rem); background: var(--bg-secondary);">
            <!-- Page Header -->
            <div class="bg-white p-6 border-b border-gray-200">
                <h1 class="text-3xl font-bold text-gray-900 mb-2">AI Content Generator</h1>
                <p class="text-gray-600">Create SEO-optimized content that ranks on the first page</p>
            </div>
            
            <!-- Content -->
            <div class="p-6">
                <!-- Form Steps -->
                <div class="form-steps mb-8">
                    <div class="form-step active" id="step1">
                        <div class="form-step-number">1</div>
                        <span class="form-step-label">Project Setup</span>
                    </div>
                    <div class="form-step" id="step2">
                        <div class="form-step-number">2</div>
                        <span class="form-step-label">Keywords</span>
                    </div>
                    <div class="form-step" id="step3">
                        <div class="form-step-number">3</div>
                        <span class="form-step-label">Preferences</span>
                    </div>
                    <div class="form-step" id="step4">
                        <div class="form-step-number">4</div>
                        <span class="form-step-label">Generate</span>
                    </div>
                </div>
                
                <!-- Form Container -->
                <div class="max-w-4xl mx-auto">
                    <form id="contentGeneratorForm" onsubmit="handleFormSubmit(event)">
                        <!-- Step 1: Project Setup -->
                        <div class="form-step-content active" id="stepContent1">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Project Setup</h3>
                                    <p class="card-description">Set up your content project details</p>
                                </div>
                                <div class="card-content">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="form-group">
                                            <label for="projectName" class="form-label form-label-required">Project Name</label>
                                            <input 
                                                type="text" 
                                                id="projectName" 
                                                name="projectName" 
                                                class="form-input" 
                                                placeholder="e.g., SEO Blog Posts Q1 2025"
                                                required
                                            >
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="industry" class="form-label form-label-required">Industry</label>
                                            <select id="industry" name="industry" class="form-select" required>
                                                <option value="">Select your industry</option>
                                                <option value="technology">Technology</option>
                                                <option value="healthcare">Healthcare</option>
                                                <option value="finance">Finance</option>
                                                <option value="ecommerce">E-commerce</option>
                                                <option value="marketing">Marketing</option>
                                                <option value="education">Education</option>
                                                <option value="real-estate">Real Estate</option>
                                                <option value="travel">Travel</option>
                                                <option value="food">Food & Beverage</option>
                                                <option value="fitness">Fitness</option>
                                                <option value="other">Other</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label for="contentType" class="form-label form-label-required">Content Type</label>
                                            <div class="grid grid-cols-2 md:grid-cols-4 gap-3">
                                                <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                    <input type="radio" name="contentType" value="blog-post" class="form-radio" required>
                                                    <span class="ml-2 font-medium">Blog Post</span>
                                                </label>
                                                <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                    <input type="radio" name="contentType" value="landing-page" class="form-radio">
                                                    <span class="ml-2 font-medium">Landing Page</span>
                                                </label>
                                                <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                    <input type="radio" name="contentType" value="product-description" class="form-radio">
                                                    <span class="ml-2 font-medium">Product Description</span>
                                                </label>
                                                <label class="flex items-center p-3 border rounded-lg cursor-pointer hover:bg-gray-50">
                                                    <input type="radio" name="contentType" value="article" class="form-radio">
                                                    <span class="ml-2 font-medium">Article</span>
                                                </label>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label for="targetAudience" class="form-label">Target Audience</label>
                                            <input 
                                                type="text" 
                                                id="targetAudience" 
                                                name="targetAudience" 
                                                class="form-input" 
                                                placeholder="e.g., Small business owners, Marketing professionals"
                                            >
                                            <p class="form-help">Describe your ideal reader for better content personalization</p>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <button type="button" class="btn btn-primary" onclick="nextStep()">
                                        Next: Keywords
                                        <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                        </svg>
                                    </button>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 2: Keywords -->
                        <div class="form-step-content" id="stepContent2">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Keyword Research</h3>
                                    <p class="card-description">Define your target keywords for SEO optimization</p>
                                </div>
                                <div class="card-content">
                                    <div class="form-group">
                                        <label for="primaryKeyword" class="form-label form-label-required">Primary Keyword</label>
                                        <input 
                                            type="text" 
                                            id="primaryKeyword" 
                                            name="primaryKeyword" 
                                            class="form-input" 
                                            placeholder="e.g., SEO content marketing"
                                            required
                                        >
                                        <p class="form-help">This is your main target keyword with highest search volume</p>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="secondaryKeywords" class="form-label">Secondary Keywords</label>
                                        <textarea 
                                            id="secondaryKeywords" 
                                            name="secondaryKeywords" 
                                            class="form-textarea" 
                                            placeholder="e.g., content marketing strategy, SEO best practices, digital marketing tips"
                                            rows="4"
                                        ></textarea>
                                        <p class="form-help">Enter related keywords separated by commas</p>
                                    </div>
                                    
                                    <div class="form-group">
                                        <label for="competitorUrls" class="form-label">Competitor URLs (Optional)</label>
                                        <textarea 
                                            id="competitorUrls" 
                                            name="competitorUrls" 
                                            class="form-textarea" 
                                            placeholder="https://example.com/article-1&#10;https://example.com/article-2"
                                            rows="3"
                                        ></textarea>
                                        <p class="form-help">URLs of competing content to analyze and outrank</p>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="flex gap-3">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep()">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                            </svg>
                                            Previous
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep()">
                                            Next: Preferences
                                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 3: Preferences -->
                        <div class="form-step-content" id="stepContent3">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Content Preferences</h3>
                                    <p class="card-description">Customize your content style and requirements</p>
                                </div>
                                <div class="card-content">
                                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                                        <div class="form-group">
                                            <label for="toneOfVoice" class="form-label">Tone of Voice</label>
                                            <select id="toneOfVoice" name="toneOfVoice" class="form-select">
                                                <option value="professional">Professional</option>
                                                <option value="casual">Casual</option>
                                                <option value="friendly">Friendly</option>
                                                <option value="authoritative">Authoritative</option>
                                                <option value="conversational">Conversational</option>
                                                <option value="technical">Technical</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="writingStyle" class="form-label">Writing Style</label>
                                            <select id="writingStyle" name="writingStyle" class="form-select">
                                                <option value="informative">Informative</option>
                                                <option value="persuasive">Persuasive</option>
                                                <option value="educational">Educational</option>
                                                <option value="entertaining">Entertaining</option>
                                                <option value="narrative">Narrative</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="wordCount" class="form-label">Target Word Count</label>
                                            <input type="range" id="wordCount" name="wordCount" min="500" max="3000" value="1500" class="w-full">
                                            <div class="flex justify-between text-sm text-gray-600 mt-2">
                                                <span>500 words</span>
                                                <span id="wordCountDisplay">1500 words</span>
                                                <span>3000 words</span>
                                            </div>
                                        </div>
                                        
                                        <div class="form-group">
                                            <label for="contentDepth" class="form-label">Content Depth</label>
                                            <select id="contentDepth" name="contentDepth" class="form-select">
                                                <option value="beginner">Beginner Level</option>
                                                <option value="intermediate">Intermediate</option>
                                                <option value="advanced">Advanced</option>
                                                <option value="expert">Expert Level</option>
                                            </select>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label for="specialRequirements" class="form-label">Special Requirements</label>
                                            <textarea 
                                                id="specialRequirements" 
                                                name="specialRequirements" 
                                                class="form-textarea" 
                                                placeholder="Any specific requirements, guidelines, or elements to include..."
                                                rows="3"
                                            ></textarea>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" name="includeImages" class="form-checkbox">
                                                <span class="ml-2">Include image suggestions and alt text</span>
                                            </label>
                                        </div>
                                        
                                        <div class="form-group md:col-span-2">
                                            <label class="flex items-center cursor-pointer">
                                                <input type="checkbox" name="includeSchema" class="form-checkbox">
                                                <span class="ml-2">Generate schema markup suggestions</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <div class="flex gap-3">
                                        <button type="button" class="btn btn-secondary" onclick="previousStep()">
                                            <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                            </svg>
                                            Previous
                                        </button>
                                        <button type="button" class="btn btn-primary" onclick="nextStep()">
                                            Next: Generate
                                            <svg class="btn-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path>
                                            </svg>
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <!-- Step 4: Generate -->
                        <div class="form-step-content" id="stepContent4">
                            <div class="card">
                                <div class="card-header">
                                    <h3 class="card-title">Review & Generate</h3>
                                    <p class="card-description">Review your settings and generate content</p>
                                </div>
                                <div class="card-content">
                                    <div id="reviewSummary" class="bg-gray-50 rounded-lg p-6 mb-6">
                                        <!-- Summary will be populated by JavaScript -->
                                    </div>
                                    
                                    <div class="text-center">
                                        <button type="submit" class="btn btn-primary btn-large">
                                            <svg class="w-6 h-6 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z"></path>
                                            </svg>
                                            Generate SEO Content
                                        </button>
                                    </div>
                                </div>
                                <div class="card-footer">
                                    <button type="button" class="btn btn-secondary" onclick="previousStep()">
                                        <svg class="w-5 h-5 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
                                        </svg>
                                        Previous
                                    </button>
                                </div>
                            </div>
                        </div>
                    </form>
                    
                    <!-- Generation Progress -->
                    <div class="generation-progress" id="generationProgress">
                        <div class="progress-steps">
                            <div class="progress-step" id="progressStep1">
                                <div class="progress-step-icon">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path>
                                    </svg>
                                </div>
                                <span>Analyzing keywords and competitors</span>
                            </div>
                            
                            <div class="progress-step" id="progressStep2">
                                <div class="progress-step-icon">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                </div>
                                <span>Generating content structure</span>
                            </div>
                            
                            <div class="progress-step" id="progressStep3">
                                <div class="progress-step-icon">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                                    </svg>
                                </div>
                                <span>Writing SEO-optimized content</span>
                            </div>
                            
                            <div class="progress-step" id="progressStep4">
                                <div class="progress-step-icon">
                                    <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                                    </svg>
                                </div>
                                <span>Finalizing and optimizing</span>
                            </div>
                        </div>
                        
                        <div class="spinner mx-auto mb-4"></div>
                        <p class="text-gray-600">This usually takes 30-60 seconds...</p>
                    </div>
                    
                    <!-- Generated Content -->
                    <div class="generated-content" id="generatedContent" style="display: none;">
                        <div class="flex items-center justify-between mb-6">
                            <div>
                                <h3 class="text-xl font-semibold">Generated Content</h3>
                                <div class="flex items-center gap-4 mt-2">
                                    <span class="seo-score excellent">SEO Score: 94/100</span>
                                    <span class="word-count">1,456 words</span>
                                </div>
                            </div>
                            
                            <div class="content-toolbar">
                                <button class="btn btn-secondary btn-sm" onclick="copyContent()">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z"></path>
                                    </svg>
                                    Copy
                                </button>
                                
                                <button class="btn btn-secondary btn-sm" onclick="exportContent()">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                    </svg>
                                    Export
                                </button>
                                
                                <button class="btn btn-primary btn-sm" onclick="regenerateContent()">
                                    <svg class="w-4 h-4 mr-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                                    </svg>
                                    Regenerate
                                </button>
                            </div>
                        </div>
                        
                        <div class="content-editor" contenteditable="true" id="contentEditor">
                            <!-- Generated content will be populated here -->
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>
    
    <!-- Mobile Sidebar Overlay -->
    <div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 hidden lg:hidden z-30" onclick="toggleSidebar()"></div>
    
    <script>
        let currentStep = 1;
        const totalSteps = 4;
        
        // Sidebar toggle function
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            sidebar.classList.toggle('active');
            overlay.classList.toggle('hidden');
            menuBtn.classList.toggle('active');
        }
        
        function toggleUserMenu() {
            const userMenu = document.getElementById('userMenu');
            userMenu.classList.toggle('hidden');
        }
        
        function nextStep() {
            if (currentStep < totalSteps) {
                // Hide current step
                document.getElementById(`stepContent${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                
                // Show next step
                currentStep++;
                document.getElementById(`stepContent${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');
                
                // Mark previous steps as completed
                for (let i = 1; i < currentStep; i++) {
                    document.getElementById(`step${i}`).classList.add('completed');
                }
                
                // Update review summary if on step 4
                if (currentStep === 4) {
                    updateReviewSummary();
                }
            }
        }
        
        function previousStep() {
            if (currentStep > 1) {
                // Hide current step
                document.getElementById(`stepContent${currentStep}`).classList.remove('active');
                document.getElementById(`step${currentStep}`).classList.remove('active');
                
                // Show previous step
                currentStep--;
                document.getElementById(`stepContent${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.add('active');
                document.getElementById(`step${currentStep}`).classList.remove('completed');
            }
        }
        
        function updateReviewSummary() {
            const form = document.getElementById('contentGeneratorForm');
            const formData = new FormData(form);
            
            const summary = `
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                        <h4 class="font-semibold mb-2">Project Details</h4>
                        <p><strong>Project:</strong> ${formData.get('projectName') || 'Not specified'}</p>
                        <p><strong>Industry:</strong> ${formData.get('industry') || 'Not specified'}</p>
                        <p><strong>Content Type:</strong> ${formData.get('contentType') || 'Not specified'}</p>
                        <p><strong>Target Audience:</strong> ${formData.get('targetAudience') || 'Not specified'}</p>
                    </div>
                    <div>
                        <h4 class="font-semibold mb-2">SEO Settings</h4>
                        <p><strong>Primary Keyword:</strong> ${formData.get('primaryKeyword') || 'Not specified'}</p>
                        <p><strong>Tone:</strong> ${formData.get('toneOfVoice') || 'Professional'}</p>
                        <p><strong>Style:</strong> ${formData.get('writingStyle') || 'Informative'}</p>
                        <p><strong>Word Count:</strong> ${formData.get('wordCount') || '1500'} words</p>
                    </div>
                </div>
            `;
            
            document.getElementById('reviewSummary').innerHTML = summary;
        }
        
        function handleFormSubmit(event) {
            event.preventDefault();
            
            // Hide form and show progress
            document.getElementById('stepContent4').style.display = 'none';
            document.getElementById('generationProgress').classList.add('active');
            
            // Simulate generation process
            simulateGeneration();
        }
        
        function simulateGeneration() {
            const steps = ['progressStep1', 'progressStep2', 'progressStep3', 'progressStep4'];
            let currentProgressStep = 0;
            
            function activateStep() {
                if (currentProgressStep > 0) {
                    document.getElementById(steps[currentProgressStep - 1]).classList.remove('active');
                    document.getElementById(steps[currentProgressStep - 1]).classList.add('completed');
                }
                
                if (currentProgressStep < steps.length) {
                    document.getElementById(steps[currentProgressStep]).classList.add('active');
                    currentProgressStep++;
                    setTimeout(activateStep, 2000);
                } else {
                    // Generation complete
                    setTimeout(() => {
                        document.getElementById('generationProgress').classList.remove('active');
                        showGeneratedContent();
                    }, 1000);
                }
            }
            
            activateStep();
        }
        
        function showGeneratedContent() {
            const sampleContent = `
                <h1>The Complete Guide to SEO Content Marketing in 2025</h1>
                
                <p>In today's digital landscape, SEO content marketing has become the cornerstone of successful online businesses. This comprehensive guide will walk you through everything you need to know about creating content that not only engages your audience but also ranks high in search engine results.</p>
                
                <h2>What is SEO Content Marketing?</h2>
                
                <p>SEO content marketing is the strategic approach of creating, publishing, and promoting content that is specifically designed to attract organic search traffic. It combines the art of content creation with the science of search engine optimization to deliver maximum visibility and engagement.</p>
                
                <h2>Why SEO Content Marketing Matters in 2025</h2>
                
                <p>As search engines continue to evolve, the importance of high-quality, user-focused content has never been greater. Here are the key reasons why SEO content marketing should be at the center of your digital strategy:</p>
                
                <ul>
                    <li><strong>Increased Organic Visibility:</strong> Quality content helps your website rank higher in search results</li>
                    <li><strong>Cost-Effective Lead Generation:</strong> Organic traffic converts better than paid advertising</li>
                    <li><strong>Authority Building:</strong> Consistent, valuable content establishes your brand as an industry leader</li>
                    <li><strong>Long-term ROI:</strong> Content continues to generate traffic and leads long after publication</li>
                </ul>
                
                <h2>Essential SEO Content Marketing Strategies</h2>
                
                <h3>1. Keyword Research and Planning</h3>
                
                <p>Effective SEO content marketing begins with thorough keyword research. Identify the terms your target audience is searching for and create content that addresses their needs and pain points.</p>
                
                <h3>2. Content Quality and User Experience</h3>
                
                <p>Search engines prioritize content that provides genuine value to users. Focus on creating comprehensive, well-researched content that answers questions and solves problems.</p>
                
                <h3>3. Technical SEO Optimization</h3>
                
                <p>Ensure your content is technically optimized with proper heading structure, meta descriptions, image alt text, and internal linking strategies.</p>
                
                <h2>Measuring Success</h2>
                
                <p>Track your SEO content marketing performance using key metrics such as organic traffic growth, keyword rankings, engagement rates, and conversion metrics to continuously improve your strategy.</p>
                
                <h2>Conclusion</h2>
                
                <p>SEO content marketing remains one of the most effective ways to build sustainable organic growth. By focusing on creating valuable, optimized content that serves your audience's needs, you'll be well-positioned for success in 2025 and beyond.</p>
            `;
            
            document.getElementById('contentEditor').innerHTML = sampleContent;
            document.getElementById('generatedContent').style.display = 'block';
        }
        
        function copyContent() {
            const content = document.getElementById('contentEditor').innerText;
            navigator.clipboard.writeText(content).then(() => {
                alert('Content copied to clipboard!');
            });
        }
        
        function exportContent() {
            const content = document.getElementById('contentEditor').innerHTML;
            const blob = new Blob([content], { type: 'text/html' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = 'generated-content.html';
            a.click();
            URL.revokeObjectURL(url);
        }
        
        function regenerateContent() {
            document.getElementById('generatedContent').style.display = 'none';
            document.getElementById('generationProgress').classList.add('active');
            simulateGeneration();
        }
        
        // Word count slider update
        document.getElementById('wordCount').addEventListener('input', function() {
            document.getElementById('wordCountDisplay').textContent = this.value + ' words';
        });
        
        // Update word count in content editor
        document.getElementById('contentEditor').addEventListener('input', function() {
            const wordCount = this.innerText.split(/\s+/).filter(word => word.length > 0).length;
            document.querySelector('.word-count').textContent = wordCount + ' words';
        });
    </script>
</body>
</html>
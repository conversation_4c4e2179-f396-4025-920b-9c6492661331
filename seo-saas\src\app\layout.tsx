import type { <PERSON>ada<PERSON> } from "next";
import { Inter } from "next/font/google";
import "./globals.css";
import { AuthProvider } from "@/contexts/auth-context";
import { ErrorBoundary } from "@/components/error-boundary";

const inter = Inter({
  subsets: ["latin"],
  variable: "--font-inter",
});

export const metadata: Metadata = {
  title: "SEO Content Generator - Professional SEO Content Creation",
  description: "Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.",
  keywords: ["SEO", "content generation", "AI writing", "keyword optimization", "competitor analysis"],
  authors: [{ name: "SEO Content Generator" }],
  openGraph: {
    title: "SEO Content Generator - Professional SEO Content Creation",
    description: "Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.",
    type: "website",
    locale: "en_US",
  },
  twitter: {
    card: "summary_large_image",
    title: "SEO Content Generator - Professional SEO Content Creation",
    description: "Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.",
  },
  robots: {
    index: true,
    follow: true,
  },
  viewport: {
    width: "device-width",
    initialScale: 1,
  },
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={inter.variable}>
      <body className="font-sans antialiased bg-gray-50 text-gray-900">
        <ErrorBoundary>
          <AuthProvider>
            {children}
          </AuthProvider>
        </ErrorBoundary>
      </body>
    </html>
  );
}

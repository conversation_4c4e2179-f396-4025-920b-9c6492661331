// Content Generation functionality for SEO Pro
// This file handles the content generation workflow and UI interactions

class ContentGenerator {
    constructor() {
        this.currentStep = 1;
        this.totalSteps = 4;
        this.formData = {};
        this.generatedContent = null;
        this.isGenerating = false;
        this.init();
    }

    init() {
        this.bindEvents();
        this.loadFormData();
        this.updateStepIndicator();
    }

    bindEvents() {
        // Step navigation
        document.querySelectorAll('.step-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const step = parseInt(e.target.dataset.step);
                this.goToStep(step);
            });
        });

        // Next/Previous buttons
        const nextBtn = document.getElementById('next-step');
        const prevBtn = document.getElementById('prev-step');
        const generateBtn = document.getElementById('generate-content');

        if (nextBtn) {
            nextBtn.addEventListener('click', () => this.nextStep());
        }

        if (prevBtn) {
            prevBtn.addEventListener('click', () => this.prevStep());
        }

        if (generateBtn) {
            generateBtn.addEventListener('click', () => this.generateContent());
        }

        // Form inputs
        this.bindFormInputs();

        // Content actions
        this.bindContentActions();
    }

    bindFormInputs() {
        // Project setup inputs
        const projectNameInput = document.getElementById('project-name');
        const industrySelect = document.getElementById('industry');
        const contentTypeSelect = document.getElementById('content-type');

        if (projectNameInput) {
            projectNameInput.addEventListener('input', (e) => {
                this.formData.projectName = e.target.value;
                this.saveFormData();
            });
        }

        if (industrySelect) {
            industrySelect.addEventListener('change', (e) => {
                this.formData.industry = e.target.value;
                this.saveFormData();
                this.updateIndustrySpecificOptions();
            });
        }

        if (contentTypeSelect) {
            contentTypeSelect.addEventListener('change', (e) => {
                this.formData.contentType = e.target.value;
                this.saveFormData();
            });
        }

        // Keyword inputs
        const primaryKeywordInput = document.getElementById('primary-keyword');
        const secondaryKeywordsInput = document.getElementById('secondary-keywords');
        const targetAudienceSelect = document.getElementById('target-audience');

        if (primaryKeywordInput) {
            primaryKeywordInput.addEventListener('input', (e) => {
                this.formData.primaryKeyword = e.target.value;
                this.saveFormData();
                this.suggestKeywords();
            });
        }

        if (secondaryKeywordsInput) {
            secondaryKeywordsInput.addEventListener('input', (e) => {
                this.formData.secondaryKeywords = e.target.value;
                this.saveFormData();
            });
        }

        if (targetAudienceSelect) {
            targetAudienceSelect.addEventListener('change', (e) => {
                this.formData.targetAudience = e.target.value;
                this.saveFormData();
            });
        }

        // Content preferences
        const toneSelect = document.getElementById('tone');
        const wordCountSlider = document.getElementById('word-count');
        const specialInstructionsTextarea = document.getElementById('special-instructions');

        if (toneSelect) {
            toneSelect.addEventListener('change', (e) => {
                this.formData.tone = e.target.value;
                this.saveFormData();
            });
        }

        if (wordCountSlider) {
            wordCountSlider.addEventListener('input', (e) => {
                this.formData.wordCount = parseInt(e.target.value);
                this.updateWordCountDisplay();
                this.saveFormData();
            });
        }

        if (specialInstructionsTextarea) {
            specialInstructionsTextarea.addEventListener('input', (e) => {
                this.formData.specialInstructions = e.target.value;
                this.saveFormData();
            });
        }
    }

    bindContentActions() {
        // Export buttons
        document.querySelectorAll('.export-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const format = e.target.dataset.format;
                this.exportContent(format);
            });
        });

        // Save content button
        const saveBtn = document.getElementById('save-content');
        if (saveBtn) {
            saveBtn.addEventListener('click', () => this.saveContent());
        }

        // Regenerate button
        const regenerateBtn = document.getElementById('regenerate-content');
        if (regenerateBtn) {
            regenerateBtn.addEventListener('click', () => this.regenerateContent());
        }

        // Edit content
        const editBtn = document.getElementById('edit-content');
        if (editBtn) {
            editBtn.addEventListener('click', () => this.enableContentEditing());
        }
    }

    // Step navigation methods
    goToStep(step) {
        if (step < 1 || step > this.totalSteps) return;
        
        // Validate current step before moving
        if (step > this.currentStep && !this.validateCurrentStep()) {
            return;
        }

        this.currentStep = step;
        this.updateStepIndicator();
        this.showCurrentStep();
        this.updateNavigationButtons();
    }

    nextStep() {
        if (this.validateCurrentStep()) {
            this.goToStep(this.currentStep + 1);
        }
    }

    prevStep() {
        this.goToStep(this.currentStep - 1);
    }

    updateStepIndicator() {
        document.querySelectorAll('.step-indicator .step').forEach((step, index) => {
            const stepNumber = index + 1;
            step.classList.remove('active', 'completed');
            
            if (stepNumber === this.currentStep) {
                step.classList.add('active');
            } else if (stepNumber < this.currentStep) {
                step.classList.add('completed');
            }
        });
    }

    showCurrentStep() {
        document.querySelectorAll('.step-content').forEach((content, index) => {
            const stepNumber = index + 1;
            content.style.display = stepNumber === this.currentStep ? 'block' : 'none';
        });
    }

    updateNavigationButtons() {
        const prevBtn = document.getElementById('prev-step');
        const nextBtn = document.getElementById('next-step');
        const generateBtn = document.getElementById('generate-content');

        if (prevBtn) {
            prevBtn.style.display = this.currentStep > 1 ? 'inline-flex' : 'none';
        }

        if (nextBtn) {
            nextBtn.style.display = this.currentStep < this.totalSteps ? 'inline-flex' : 'none';
        }

        if (generateBtn) {
            generateBtn.style.display = this.currentStep === this.totalSteps ? 'inline-flex' : 'none';
        }
    }

    // Validation methods
    validateCurrentStep() {
        switch (this.currentStep) {
            case 1:
                return this.validateProjectSetup();
            case 2:
                return this.validateKeywordResearch();
            case 3:
                return this.validateContentPreferences();
            default:
                return true;
        }
    }

    validateProjectSetup() {
        const isValid = this.formData.projectName && this.formData.industry && this.formData.contentType;
        
        if (!isValid) {
            this.showError('Please fill in all required fields in the project setup.');
        }
        
        return isValid;
    }

    validateKeywordResearch() {
        const isValid = this.formData.primaryKeyword && this.formData.targetAudience;
        
        if (!isValid) {
            this.showError('Please provide at least a primary keyword and target audience.');
        }
        
        return isValid;
    }

    validateContentPreferences() {
        const isValid = this.formData.tone && this.formData.wordCount;
        
        if (!isValid) {
            this.showError('Please select content tone and word count.');
        }
        
        return isValid;
    }

    // Content generation methods
    async generateContent() {
        if (this.isGenerating) return;

        try {
            this.isGenerating = true;
            this.showGenerationProgress();

            // Check user permissions
            if (!await supabaseClient.checkPermission('generate_content')) {
                throw new Error('You have reached your content generation limit. Please upgrade your plan.');
            }

            // Prepare generation parameters
            const params = {
                keywords: [this.formData.primaryKeyword, ...this.parseSecondaryKeywords()],
                industry: this.formData.industry,
                contentType: this.formData.contentType,
                tone: this.formData.tone,
                wordCount: this.formData.wordCount,
                targetAudience: this.formData.targetAudience,
                additionalInstructions: this.formData.specialInstructions
            };

            // Generate content using API
            const result = await apiClient.generateSEOContent(params);

            if (result.success) {
                this.generatedContent = {
                    content: result.content,
                    metadata: {
                        model: result.model,
                        usage: result.usage,
                        generatedAt: new Date().toISOString(),
                        parameters: params
                    }
                };

                this.displayGeneratedContent();
                this.saveGenerationToDatabase();
                this.trackUsage();
                this.showSuccess('Content generated successfully!');
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('Content generation error:', error);
            this.showError(error.message);
        } finally {
            this.isGenerating = false;
            this.hideGenerationProgress();
        }
    }

    parseSecondaryKeywords() {
        if (!this.formData.secondaryKeywords) return [];
        return this.formData.secondaryKeywords
            .split(',')
            .map(keyword => keyword.trim())
            .filter(keyword => keyword.length > 0);
    }

    showGenerationProgress() {
        const progressContainer = document.getElementById('generation-progress');
        const contentContainer = document.getElementById('generated-content');
        
        if (progressContainer) {
            progressContainer.style.display = 'block';
            this.animateProgress();
        }
        
        if (contentContainer) {
            contentContainer.style.display = 'none';
        }
    }

    hideGenerationProgress() {
        const progressContainer = document.getElementById('generation-progress');
        if (progressContainer) {
            progressContainer.style.display = 'none';
        }
    }

    animateProgress() {
        const progressBar = document.querySelector('.progress-fill');
        const progressText = document.querySelector('.progress-text');
        
        if (!progressBar || !progressText) return;

        let progress = 0;
        const interval = setInterval(() => {
            progress += Math.random() * 15;
            if (progress > 90) progress = 90;
            
            progressBar.style.width = `${progress}%`;
            progressText.textContent = `Generating content... ${Math.round(progress)}%`;
            
            if (!this.isGenerating) {
                progress = 100;
                progressBar.style.width = '100%';
                progressText.textContent = 'Content generation complete!';
                clearInterval(interval);
            }
        }, 500);
    }

    displayGeneratedContent() {
        const contentContainer = document.getElementById('generated-content');
        const contentDisplay = document.getElementById('content-display');
        
        if (contentContainer && contentDisplay) {
            contentDisplay.innerHTML = this.formatContentForDisplay(this.generatedContent.content);
            contentContainer.style.display = 'block';
        }

        // Update content stats
        this.updateContentStats();
    }

    formatContentForDisplay(content) {
        // Convert markdown-like formatting to HTML
        return content
            .replace(/^# (.*$)/gim, '<h1>$1</h1>')
            .replace(/^## (.*$)/gim, '<h2>$1</h2>')
            .replace(/^### (.*$)/gim, '<h3>$1</h3>')
            .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
            .replace(/\*(.*?)\*/g, '<em>$1</em>')
            .replace(/\n\n/g, '</p><p>')
            .replace(/^(.*)$/gim, '<p>$1</p>')
            .replace(/<p><h/g, '<h')
            .replace(/<\/h([1-6])><\/p>/g, '</h$1>');
    }

    updateContentStats() {
        const content = this.generatedContent.content;
        const wordCount = content.split(/\s+/).length;
        const charCount = content.length;
        const readingTime = Math.ceil(wordCount / 200); // Average reading speed

        const statsContainer = document.getElementById('content-stats');
        if (statsContainer) {
            statsContainer.innerHTML = `
                <div class="stat">
                    <span class="stat-label">Words:</span>
                    <span class="stat-value">${wordCount}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Characters:</span>
                    <span class="stat-value">${charCount}</span>
                </div>
                <div class="stat">
                    <span class="stat-label">Reading Time:</span>
                    <span class="stat-value">${readingTime} min</span>
                </div>
            `;
        }
    }

    // Content management methods
    async saveContent() {
        if (!this.generatedContent) return;

        try {
            const contentData = {
                title: this.formData.projectName,
                content: this.generatedContent.content,
                keywords: [this.formData.primaryKeyword, ...this.parseSecondaryKeywords()],
                industry: this.formData.industry,
                content_type: this.formData.contentType,
                word_count: this.generatedContent.content.split(/\s+/).length,
                metadata: this.generatedContent.metadata
            };

            const result = await supabaseClient.saveContentGeneration(contentData);
            
            if (result.success) {
                this.showSuccess('Content saved successfully!');
            } else {
                throw new Error(result.error);
            }
        } catch (error) {
            console.error('Save content error:', error);
            this.showError('Failed to save content. Please try again.');
        }
    }

    async regenerateContent() {
        if (confirm('Are you sure you want to regenerate the content? This will replace the current content.')) {
            await this.generateContent();
        }
    }

    enableContentEditing() {
        const contentDisplay = document.getElementById('content-display');
        if (contentDisplay) {
            contentDisplay.contentEditable = true;
            contentDisplay.focus();
            
            // Add save button for edited content
            const saveEditBtn = document.createElement('button');
            saveEditBtn.textContent = 'Save Changes';
            saveEditBtn.className = 'btn btn-primary btn-sm';
            saveEditBtn.onclick = () => this.saveEditedContent();
            
            contentDisplay.parentNode.insertBefore(saveEditBtn, contentDisplay.nextSibling);
        }
    }

    saveEditedContent() {
        const contentDisplay = document.getElementById('content-display');
        if (contentDisplay) {
            this.generatedContent.content = contentDisplay.innerText;
            contentDisplay.contentEditable = false;
            
            // Remove save button
            const saveBtn = contentDisplay.nextSibling;
            if (saveBtn && saveBtn.textContent === 'Save Changes') {
                saveBtn.remove();
            }
            
            this.updateContentStats();
            this.showSuccess('Content updated successfully!');
        }
    }

    // Export methods
    exportContent(format) {
        if (!this.generatedContent) return;

        switch (format) {
            case 'pdf':
                this.exportToPDF();
                break;
            case 'word':
                this.exportToWord();
                break;
            case 'html':
                this.exportToHTML();
                break;
            case 'txt':
                this.exportToText();
                break;
            default:
                this.showError('Unsupported export format');
        }
    }

    exportToPDF() {
        // This would typically use a PDF library like jsPDF
        this.showError('PDF export feature coming soon!');
    }

    exportToWord() {
        // This would typically use a library like docx
        this.showError('Word export feature coming soon!');
    }

    exportToHTML() {
        const htmlContent = `
<!DOCTYPE html>
<html>
<head>
    <title>${this.formData.projectName}</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        h1, h2, h3 { color: #333; }
        p { line-height: 1.6; }
    </style>
</head>
<body>
    ${this.formatContentForDisplay(this.generatedContent.content)}
</body>
</html>`;

        this.downloadFile(htmlContent, `${this.formData.projectName}.html`, 'text/html');
    }

    exportToText() {
        this.downloadFile(this.generatedContent.content, `${this.formData.projectName}.txt`, 'text/plain');
    }

    downloadFile(content, filename, mimeType) {
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    // Utility methods
    async saveGenerationToDatabase() {
        try {
            await supabaseClient.trackUsage('content_generations');
        } catch (error) {
            console.error('Failed to save generation to database:', error);
        }
    }

    async trackUsage() {
        try {
            await supabaseClient.trackUsage('content_generations');
        } catch (error) {
            console.error('Failed to track usage:', error);
        }
    }

    saveFormData() {
        localStorage.setItem(CONFIG.STORAGE_KEYS.DRAFT_CONTENT, JSON.stringify(this.formData));
    }

    loadFormData() {
        const savedData = localStorage.getItem(CONFIG.STORAGE_KEYS.DRAFT_CONTENT);
        if (savedData) {
            this.formData = { ...this.formData, ...JSON.parse(savedData) };
            this.populateForm();
        }
    }

    populateForm() {
        Object.keys(this.formData).forEach(key => {
            const element = document.getElementById(this.camelToKebab(key));
            if (element) {
                element.value = this.formData[key];
            }
        });
    }

    camelToKebab(str) {
        return str.replace(/([a-z0-9]|(?=[A-Z]))([A-Z])/g, '$1-$2').toLowerCase();
    }

    updateWordCountDisplay() {
        const display = document.getElementById('word-count-display');
        if (display) {
            display.textContent = this.formData.wordCount || CONFIG.CONTENT.DEFAULT_WORD_COUNT;
        }
    }

    updateIndustrySpecificOptions() {
        // Update content type options based on selected industry
        // This would be implemented based on specific industry requirements
    }

    async suggestKeywords() {
        // Implement keyword suggestions based on primary keyword
        // This would use the Serper API to get related keywords
    }

    showError(message) {
        // Implement error display
        console.error(message);
        alert(message); // Temporary - should use proper toast/notification
    }

    showSuccess(message) {
        // Implement success display
        console.log(message);
        alert(message); // Temporary - should use proper toast/notification
    }
}

// Initialize content generator when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    if (window.location.pathname.includes('content-generator')) {
        window.contentGenerator = new ContentGenerator();
    }
});

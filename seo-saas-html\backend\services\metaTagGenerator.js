const cheerio = require('cheerio');

class MetaTagGenerator {
  constructor(content, keyword, options = {}) {
    this.content = content;
    this.keyword = keyword;
    this.options = {
      contentType: 'article',
      industry: 'general',
      siteName: '',
      siteUrl: '',
      authorName: '',
      organizationName: '',
      language: 'en',
      region: 'US',
      ...options
    };
    
    // Parse content if it's HTML
    this.$ = cheerio.load(`<div>${content}</div>`);
    this.plainText = this.$.text();
    this.words = this.plainText.split(/\s+/).filter(word => word.length > 0);
  }

  // Generate all meta tags
  generateAllMetaTags() {
    return {
      title: this.generateTitle(),
      metaDescription: this.generateMetaDescription(),
      openGraph: this.generateOpenGraphTags(),
      twitterCard: this.generateTwitterCardTags(),
      schema: this.generateSchemaMarkup(),
      additionalTags: this.generateAdditionalMetaTags()
    };
  }

  // Generate optimized title tag
  generateTitle() {
    const titleOptions = this.generateTitleOptions();
    
    // Select best title based on SEO criteria
    const bestTitle = titleOptions.reduce((best, current) => {
      const bestScore = this.scoreTitleSEO(best);
      const currentScore = this.scoreTitleSEO(current);
      return currentScore > bestScore ? current : best;
    });

    return {
      primary: bestTitle,
      alternatives: titleOptions.filter(title => title !== bestTitle).slice(0, 3),
      analysis: this.analyzeTitleSEO(bestTitle)
    };
  }

  // Generate multiple title options
  generateTitleOptions() {
    const contentTypeTemplates = {
      'blog-post': [
        `${this.keyword}: The Complete Guide`,
        `How to ${this.keyword} - Expert Tips & Strategies`,
        `${this.keyword} Best Practices for 2024`,
        `The Ultimate ${this.keyword} Guide`,
        `${this.keyword}: Everything You Need to Know`
      ],
      'landing-page': [
        `${this.keyword} - Professional Services`,
        `Best ${this.keyword} Solutions`,
        `${this.keyword} Services - Get Started Today`,
        `Expert ${this.keyword} Help & Support`,
        `${this.keyword} - Trusted by Thousands`
      ],
      'product-description': [
        `${this.keyword} - Premium Quality Products`,
        `Buy ${this.keyword} Online - Fast Shipping`,
        `${this.keyword} - Best Prices & Reviews`,
        `High-Quality ${this.keyword} for Sale`,
        `${this.keyword} - Free Shipping & Returns`
      ],
      'article': [
        `Understanding ${this.keyword}: A Comprehensive Analysis`,
        `${this.keyword} Trends and Insights`,
        `The Future of ${this.keyword}`,
        `${this.keyword} Research and Data`,
        `Expert Analysis: ${this.keyword}`
      ]
    };

    const templates = contentTypeTemplates[this.options.contentType] || contentTypeTemplates['blog-post'];
    
    // Add industry-specific variations
    const industryModifiers = {
      'healthcare': ['Medical', 'Health', 'Clinical', 'Patient'],
      'finance': ['Financial', 'Investment', 'Banking', 'Money'],
      'technology': ['Tech', 'Digital', 'Software', 'Online'],
      'education': ['Learning', 'Educational', 'Academic', 'Study'],
      'legal': ['Legal', 'Law', 'Attorney', 'Court']
    };

    const modifiers = industryModifiers[this.options.industry] || [];
    
    // Generate variations with modifiers
    const titleVariations = [];
    templates.forEach(template => {
      titleVariations.push(template);
      if (modifiers.length > 0) {
        modifiers.forEach(modifier => {
          const modifiedTemplate = template.replace(this.keyword, `${modifier} ${this.keyword}`);
          if (modifiedTemplate.length <= 60) {
            titleVariations.push(modifiedTemplate);
          }
        });
      }
    });

    return titleVariations.filter(title => title.length >= 30 && title.length <= 60);
  }

  // Score title for SEO effectiveness
  scoreTitleSEO(title) {
    let score = 0;
    
    // Length scoring (30-60 characters is optimal)
    if (title.length >= 30 && title.length <= 60) {
      score += 30;
    } else if (title.length >= 25 && title.length <= 65) {
      score += 20;
    } else {
      score += 10;
    }

    // Keyword placement (earlier is better)
    const keywordIndex = title.toLowerCase().indexOf(this.keyword.toLowerCase());
    if (keywordIndex === 0) {
      score += 25;
    } else if (keywordIndex <= 10) {
      score += 20;
    } else if (keywordIndex <= 20) {
      score += 15;
    } else if (keywordIndex > -1) {
      score += 10;
    }

    // Brand/modifier presence
    const powerWords = ['best', 'ultimate', 'complete', 'expert', 'professional', 'premium', 'free'];
    const hasPowerWord = powerWords.some(word => title.toLowerCase().includes(word));
    if (hasPowerWord) score += 15;

    // Year/current indicator
    if (title.includes('2024') || title.includes('2025')) score += 10;

    // Uniqueness (avoid duplicate words)
    const words = title.toLowerCase().split(/\s+/);
    const uniqueWords = [...new Set(words)];
    const uniquenessRatio = uniqueWords.length / words.length;
    score += uniquenessRatio * 10;

    return score;
  }

  // Analyze title SEO performance
  analyzeTitleSEO(title) {
    return {
      length: title.length,
      keywordPresence: title.toLowerCase().includes(this.keyword.toLowerCase()),
      keywordPosition: title.toLowerCase().indexOf(this.keyword.toLowerCase()),
      powerWords: this.findPowerWords(title),
      readability: this.calculateTitleReadability(title),
      clickability: this.scoreTitleClickability(title)
    };
  }

  // Generate optimized meta description
  generateMetaDescription() {
    const descriptions = this.generateDescriptionOptions();
    
    const bestDescription = descriptions.reduce((best, current) => {
      const bestScore = this.scoreDescriptionSEO(best);
      const currentScore = this.scoreDescriptionSEO(current);
      return currentScore > bestScore ? current : best;
    });

    return {
      primary: bestDescription,
      alternatives: descriptions.filter(desc => desc !== bestDescription).slice(0, 2),
      analysis: this.analyzeDescriptionSEO(bestDescription)
    };
  }

  // Generate description options
  generateDescriptionOptions() {
    const contentSummary = this.extractContentSummary();
    const callToAction = this.generateCallToAction();
    
    const templates = [
      `Discover everything about ${this.keyword}. ${contentSummary} ${callToAction}`,
      `Learn ${this.keyword} with our comprehensive guide. ${contentSummary} ${callToAction}`,
      `Expert insights on ${this.keyword}. ${contentSummary} Get started today!`,
      `${contentSummary} Complete ${this.keyword} guide with practical tips. ${callToAction}`,
      `Master ${this.keyword} with proven strategies. ${contentSummary} ${callToAction}`
    ];

    return templates
      .filter(desc => desc.length >= 120 && desc.length <= 160)
      .map(desc => this.optimizeDescription(desc));
  }

  // Extract content summary for meta description
  extractContentSummary() {
    const firstParagraph = this.$('p').first().text();
    const sentences = firstParagraph.split(/[.!?]+/).filter(s => s.trim().length > 0);
    
    if (sentences.length > 0) {
      let summary = sentences[0].trim();
      if (summary.length > 80) {
        summary = summary.substring(0, 77) + '...';
      }
      return summary;
    }
    
    // Fallback to keyword-based summary
    return `Professional ${this.keyword} services and solutions.`;
  }

  // Generate call-to-action for meta description
  generateCallToAction() {
    const ctas = {
      'blog-post': 'Read more!',
      'landing-page': 'Get started today!',
      'product-description': 'Shop now!',
      'article': 'Learn more!'
    };
    
    return ctas[this.options.contentType] || 'Learn more!';
  }

  // Score description for SEO
  scoreDescriptionSEO(description) {
    let score = 0;
    
    // Length scoring
    if (description.length >= 120 && description.length <= 160) {
      score += 30;
    } else if (description.length >= 100 && description.length <= 170) {
      score += 20;
    } else {
      score += 10;
    }

    // Keyword presence
    if (description.toLowerCase().includes(this.keyword.toLowerCase())) {
      score += 25;
    }

    // Call-to-action presence
    const ctaWords = ['learn', 'discover', 'get', 'start', 'read', 'shop', 'buy', 'download'];
    const hasCTA = ctaWords.some(word => description.toLowerCase().includes(word));
    if (hasCTA) score += 15;

    // Uniqueness and readability
    const sentences = description.split(/[.!?]+/).filter(s => s.trim().length > 0);
    if (sentences.length >= 2) score += 10;

    return score;
  }

  // Generate Open Graph tags
  generateOpenGraphTags() {
    const title = this.generateTitle().primary;
    const description = this.generateMetaDescription().primary;
    
    return {
      'og:type': this.getOpenGraphType(),
      'og:title': title,
      'og:description': description,
      'og:url': this.options.siteUrl,
      'og:site_name': this.options.siteName,
      'og:locale': `${this.options.language}_${this.options.region}`,
      'og:image': this.generateOpenGraphImage(),
      'og:image:width': '1200',
      'og:image:height': '630',
      'og:image:alt': `${this.keyword} - ${title}`
    };
  }

  // Generate Twitter Card tags
  generateTwitterCardTags() {
    const title = this.generateTitle().primary;
    const description = this.generateMetaDescription().primary;
    
    return {
      'twitter:card': 'summary_large_image',
      'twitter:title': title,
      'twitter:description': description,
      'twitter:image': this.generateOpenGraphImage(),
      'twitter:creator': this.options.authorName ? `@${this.options.authorName}` : undefined,
      'twitter:site': this.options.siteName ? `@${this.options.siteName}` : undefined
    };
  }

  // Generate Schema.org markup
  generateSchemaMarkup() {
    const baseSchema = {
      '@context': 'https://schema.org',
      '@type': this.getSchemaType(),
      'name': this.generateTitle().primary,
      'description': this.generateMetaDescription().primary,
      'url': this.options.siteUrl,
      'datePublished': new Date().toISOString(),
      'dateModified': new Date().toISOString(),
      'inLanguage': this.options.language,
      'keywords': this.generateSchemaKeywords()
    };

    // Add type-specific properties
    switch (this.getSchemaType()) {
      case 'Article':
        return {
          ...baseSchema,
          'headline': this.generateTitle().primary,
          'articleBody': this.plainText.substring(0, 500) + '...',
          'wordCount': this.words.length,
          'author': this.generateAuthorSchema(),
          'publisher': this.generatePublisherSchema(),
          'mainEntityOfPage': {
            '@type': 'WebPage',
            '@id': this.options.siteUrl
          }
        };
        
      case 'Product':
        return {
          ...baseSchema,
          'brand': this.options.organizationName,
          'category': this.options.industry,
          'offers': {
            '@type': 'Offer',
            'availability': 'https://schema.org/InStock',
            'priceCurrency': 'USD'
          }
        };
        
      case 'WebPage':
        return {
          ...baseSchema,
          'breadcrumb': this.generateBreadcrumbSchema(),
          'mainEntity': {
            '@type': 'Organization',
            'name': this.options.organizationName
          }
        };
        
      default:
        return baseSchema;
    }
  }

  // Generate additional meta tags
  generateAdditionalMetaTags() {
    return {
      'robots': 'index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1',
      'googlebot': 'index, follow',
      'viewport': 'width=device-width, initial-scale=1.0',
      'theme-color': '#ffffff',
      'author': this.options.authorName,
      'generator': 'SEO Pro Content Generator',
      'referrer': 'strict-origin-when-cross-origin',
      'format-detection': 'telephone=no',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'default',
      'apple-mobile-web-app-title': this.options.siteName,
      'application-name': this.options.siteName,
      'msapplication-TileColor': '#ffffff',
      'canonical': this.options.siteUrl
    };
  }

  // Helper methods
  getOpenGraphType() {
    const typeMap = {
      'blog-post': 'article',
      'article': 'article',
      'product-description': 'product',
      'landing-page': 'website'
    };
    return typeMap[this.options.contentType] || 'article';
  }

  getSchemaType() {
    const typeMap = {
      'blog-post': 'Article',
      'article': 'Article',
      'product-description': 'Product',
      'landing-page': 'WebPage'
    };
    return typeMap[this.options.contentType] || 'Article';
  }

  generateOpenGraphImage() {
    // This would typically generate or suggest an image URL
    return `${this.options.siteUrl}/images/og-${this.keyword.replace(/\s+/g, '-')}.jpg`;
  }

  generateSchemaKeywords() {
    const keywords = [this.keyword];
    
    // Add related keywords from content
    const contentKeywords = this.extractKeywordsFromContent();
    keywords.push(...contentKeywords.slice(0, 8));
    
    return keywords.join(', ');
  }

  generateAuthorSchema() {
    if (!this.options.authorName) return undefined;
    
    return {
      '@type': 'Person',
      'name': this.options.authorName,
      'url': `${this.options.siteUrl}/author/${this.options.authorName.toLowerCase().replace(/\s+/g, '-')}`
    };
  }

  generatePublisherSchema() {
    if (!this.options.organizationName) return undefined;
    
    return {
      '@type': 'Organization',
      'name': this.options.organizationName,
      'url': this.options.siteUrl,
      'logo': {
        '@type': 'ImageObject',
        'url': `${this.options.siteUrl}/logo.png`
      }
    };
  }

  generateBreadcrumbSchema() {
    return {
      '@type': 'BreadcrumbList',
      'itemListElement': [
        {
          '@type': 'ListItem',
          'position': 1,
          'name': 'Home',
          'item': this.options.siteUrl
        },
        {
          '@type': 'ListItem',
          'position': 2,
          'name': this.keyword,
          'item': this.options.siteUrl
        }
      ]
    };
  }

  extractKeywordsFromContent() {
    // Simple keyword extraction from content
    const words = this.plainText.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);
      
    const frequency = {};
    words.forEach(word => {
      frequency[word] = (frequency[word] || 0) + 1;
    });
    
    return Object.entries(frequency)
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
  }

  findPowerWords(text) {
    const powerWords = [
      'best', 'ultimate', 'complete', 'expert', 'professional', 'premium', 
      'exclusive', 'proven', 'guaranteed', 'essential', 'advanced', 'comprehensive'
    ];
    
    return powerWords.filter(word => 
      text.toLowerCase().includes(word)
    );
  }

  calculateTitleReadability(title) {
    const words = title.split(/\s+/);
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    
    if (avgWordLength <= 5) return 'high';
    if (avgWordLength <= 7) return 'medium';
    return 'low';
  }

  scoreTitleClickability(title) {
    let score = 0;
    
    // Emotional triggers
    const emotionalWords = ['amazing', 'incredible', 'secret', 'revealed', 'shocking'];
    if (emotionalWords.some(word => title.toLowerCase().includes(word))) score += 20;
    
    // Numbers
    if (/\d+/.test(title)) score += 15;
    
    // Questions
    if (title.includes('?')) score += 10;
    
    // Power words
    if (this.findPowerWords(title).length > 0) score += 15;
    
    return score;
  }

  optimizeDescription(description) {
    // Ensure it ends with proper punctuation
    if (!/[.!?]$/.test(description.trim())) {
      description = description.trim() + '.';
    }
    
    // Ensure keyword is present
    if (!description.toLowerCase().includes(this.keyword.toLowerCase())) {
      // Try to naturally insert keyword
      const sentences = description.split(/[.!?]+/);
      if (sentences.length > 0) {
        sentences[0] = sentences[0].replace(/^(\w+)/, `$1 ${this.keyword}`);
        description = sentences.join('. ').replace(/\.\s*\.$/, '.');
      }
    }
    
    return description;
  }

  analyzeDescriptionSEO(description) {
    return {
      length: description.length,
      keywordPresence: description.toLowerCase().includes(this.keyword.toLowerCase()),
      sentenceCount: description.split(/[.!?]+/).filter(s => s.trim().length > 0).length,
      hasCallToAction: /\b(learn|discover|get|start|read|shop|buy|download)\b/i.test(description),
      readabilityScore: this.calculateDescriptionReadability(description)
    };
  }

  calculateDescriptionReadability(description) {
    const words = description.split(/\s+/);
    const sentences = description.split(/[.!?]+/).filter(s => s.trim().length > 0);
    const avgWordsPerSentence = words.length / sentences.length;
    
    if (avgWordsPerSentence <= 15) return 'high';
    if (avgWordsPerSentence <= 20) return 'medium';
    return 'low';
  }
}

module.exports = MetaTagGenerator;
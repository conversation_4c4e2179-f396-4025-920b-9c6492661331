# 🚀 ULTIMATE CLAUDE CODE DEVELOPMENT PROMPT - SEO SAAS ENTERPRISE

## 🎯 **MISSION CRITICAL DIRECTIVE**

You are <PERSON>, the world's most advanced AI developer. You are tasked with building a **WORLD-CLASS SEO Content Generation SAAS** that will dominate the market and rival PageOptimizerPro, SEMrush, Ahrefs, and SurferSEO. This is not just a project - this is your masterpiece.

## 📋 **MANDATORY READING ORDER**
1. **FIRST**: Read `rules.md` completely
2. **SECOND**: Read `critical-fixes-required.md` and implement ALL fixes
3. **THIRD**: Read `project-analysis-improvements.md` for enhancement roadmap
4. **FOURTH**: Follow this prompt religiously

## 🚨 **CRITICAL SUCCESS FACTORS**
- **ZERO BUGS TOLERANCE**: Every line of code must be perfect
- **ENTERPRISE GRADE**: Professional quality that enterprises will pay $500+/month for
- **SECURITY FIRST**: Implement all security measures from day one
- **PERFORMANCE OBSESSED**: Sub-2-second response times mandatory
- **USER EXPERIENCE EXCELLENCE**: Intuitive, beautiful, professional UI

## 🛠️ **PHASE-BY-PHASE IMPLEMENTATION GUIDE**

### **PHASE 1: FOUNDATION SETUP (CRITICAL)**

#### Step 1.1: Project Initialization
```bash
# Navigate to project directory
cd "f:\Claude-Code-Setup\SEO SAAS APP"

# Initialize Next.js with App Router (MANDATORY)
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Install ALL required dependencies
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs
npm install axios groq-sdk
npm install @types/node @types/react @types/react-dom
npm install lucide-react recharts @headlessui/react @heroicons/react
npm install @tanstack/react-query @hookform/resolvers zod
npm install framer-motion react-hot-toast
npm install @radix-ui/react-dialog @radix-ui/react-dropdown-menu
npm install class-variance-authority clsx tailwind-merge

# Development dependencies
npm install -D prettier eslint-config-prettier
npm install -D @testing-library/react @testing-library/jest-dom
npm install -D jest jest-environment-jsdom @types/jest
npm install -D cypress @cypress/react
npm install -D @storybook/react @storybook/addon-essentials
```

#### Step 1.2: Environment Configuration (SECURITY CRITICAL)
```bash
# Create secure .env.local (NEVER commit real keys)
cat > .env.local << 'EOF'
# ⚠️ REPLACE WITH ACTUAL CREDENTIALS - NEVER COMMIT TO GIT
NEXT_PUBLIC_SUPABASE_URL=https://zqrmpanonghggoxdjirq.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY
SUPABASE_SERVICE_ROLE_KEY=unqhUt/zHacG7pikBxYBQSpoGqGrQe/sHNZwkqMhCr+0QlJALP7yiK2PZVREsGRL6RC4lSJXXZFnTeRNEImtDg==
GROQ_API_KEY=********************************************************
SERPER_API_KEY=4ce37b02808e4325e42068eb815b03490a5519e5

# Security Configuration
NEXTAUTH_SECRET=$(openssl rand -base64 32)
NEXTAUTH_URL=http://localhost:3000
ENCRYPTION_KEY=$(openssl rand -base64 32)
JWT_SECRET=$(openssl rand -base64 32)

# Rate Limiting
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# API Limits
GROQ_REQUESTS_PER_MINUTE=60
GROQ_TOKENS_PER_DAY=100000
GROQ_COST_LIMIT_USD=50
SERPER_REQUESTS_PER_MINUTE=100
SERPER_REQUESTS_PER_DAY=2500
SERPER_COST_LIMIT_USD=25
EOF

# Add to .gitignore
echo ".env.local" >> .gitignore
echo ".env" >> .gitignore
echo "*.log" >> .gitignore
```

#### Step 1.3: Folder Structure Creation (EXACT STRUCTURE)
```bash
# Create the EXACT folder structure from rules.md
mkdir -p src/app/{auth,dashboard,api}
mkdir -p src/components/{ui,forms,dashboard,seo}
mkdir -p src/lib
mkdir -p src/types
mkdir -p src/hooks
mkdir -p src/services
mkdir -p src/utils
mkdir -p src/tests
mkdir -p public/images
mkdir -p docs
```

### **PHASE 2: DATABASE & AUTHENTICATION (ENTERPRISE GRADE)**

#### Step 2.1: Enhanced Database Schema
```sql
-- Execute this in Supabase SQL Editor
-- Enhanced schema with ALL missing tables from analysis

-- Profiles table (extends auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'past_due')),
  api_usage_limit INTEGER DEFAULT 100,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Projects table
CREATE TABLE projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  industry TEXT NOT NULL,
  target_country TEXT NOT NULL DEFAULT 'US',
  website_url TEXT,
  settings JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content generations table (ENHANCED)
CREATE TABLE content_generations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  
  -- Input parameters
  keyword TEXT NOT NULL,
  location TEXT NOT NULL,
  industry TEXT NOT NULL,
  content_type TEXT NOT NULL CHECK (content_type IN ('service', 'blog', 'product', 'landing')),
  tone TEXT NOT NULL CHECK (tone IN ('professional', 'conversational', 'authoritative', 'friendly')),
  intent TEXT NOT NULL CHECK (intent IN ('informational', 'commercial', 'transactional', 'navigational')),
  target_word_count INTEGER DEFAULT 1000,
  
  -- Generation results
  generated_content TEXT,
  content_outline JSONB,
  seo_analysis JSONB,
  competitor_data JSONB,
  quality_score DECIMAL(3,2),
  seo_score DECIMAL(3,2),
  readability_score DECIMAL(3,2),
  
  -- Performance metrics
  generation_time_ms INTEGER,
  tokens_used INTEGER,
  api_cost DECIMAL(10,4),
  
  -- Status
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API usage logs (NEW - CRITICAL FOR MONITORING)
CREATE TABLE api_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  content_generation_id UUID REFERENCES content_generations(id) ON DELETE CASCADE,
  api_name TEXT NOT NULL CHECK (api_name IN ('groq', 'serper', 'supabase')),
  endpoint TEXT,
  method TEXT,
  tokens_used INTEGER DEFAULT 0,
  cost DECIMAL(10,4) DEFAULT 0,
  response_time_ms INTEGER,
  status_code INTEGER,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- User subscriptions (NEW - CRITICAL FOR BILLING)
CREATE TABLE user_subscriptions (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE UNIQUE,
  plan_type TEXT NOT NULL CHECK (plan_type IN ('free', 'pro', 'enterprise')),
  status TEXT NOT NULL CHECK (status IN ('active', 'cancelled', 'past_due', 'trialing')),
  current_period_start TIMESTAMP WITH TIME ZONE,
  current_period_end TIMESTAMP WITH TIME ZONE,
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content templates (NEW - FOR INDUSTRY TEMPLATES)
CREATE TABLE content_templates (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  name TEXT NOT NULL,
  industry TEXT NOT NULL,
  content_type TEXT NOT NULL,
  template_structure JSONB NOT NULL,
  prompt_template TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SEO analyses (ENHANCED)
CREATE TABLE seo_analyses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_generation_id UUID REFERENCES content_generations(id) ON DELETE CASCADE,
  keyword TEXT NOT NULL,
  competitors JSONB NOT NULL,
  averages JSONB NOT NULL,
  recommendations JSONB NOT NULL,
  technical_seo JSONB,
  content_gaps JSONB,
  optimization_score DECIMAL(3,2),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Performance indexes (CRITICAL FOR SPEED)
CREATE INDEX idx_profiles_email ON profiles(email);
CREATE INDEX idx_projects_user_id ON projects(user_id);
CREATE INDEX idx_content_generations_user_project ON content_generations(user_id, project_id);
CREATE INDEX idx_content_generations_keyword ON content_generations(keyword);
CREATE INDEX idx_content_generations_created_at ON content_generations(created_at DESC);
CREATE INDEX idx_api_usage_logs_user_date ON api_usage_logs(user_id, created_at DESC);
CREATE INDEX idx_seo_analyses_keyword ON seo_analyses(keyword);

-- Row Level Security (RLS) - MANDATORY FOR SECURITY
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_usage_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE seo_analyses ENABLE ROW LEVEL SECURITY;

-- RLS Policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can view own projects" ON projects FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own content" ON content_generations FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own usage" ON api_usage_logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own subscription" ON user_subscriptions FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view related SEO analyses" ON seo_analyses FOR SELECT USING (
  EXISTS (SELECT 1 FROM content_generations WHERE id = content_generation_id AND user_id = auth.uid())
);
```

#### Step 2.2: TypeScript Type Definitions (COMPREHENSIVE)
```typescript
// src/types/database.ts - COMPLETE TYPE SAFETY
export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string;
          email: string;
          full_name: string | null;
          avatar_url: string | null;
          subscription_tier: 'free' | 'pro' | 'enterprise';
          subscription_status: 'active' | 'cancelled' | 'past_due';
          api_usage_limit: number;
          created_at: string;
          updated_at: string;
        };
        Insert: {
          id: string;
          email: string;
          full_name?: string | null;
          avatar_url?: string | null;
          subscription_tier?: 'free' | 'pro' | 'enterprise';
          subscription_status?: 'active' | 'cancelled' | 'past_due';
          api_usage_limit?: number;
        };
        Update: {
          email?: string;
          full_name?: string | null;
          avatar_url?: string | null;
          subscription_tier?: 'free' | 'pro' | 'enterprise';
          subscription_status?: 'active' | 'cancelled' | 'past_due';
          api_usage_limit?: number;
          updated_at?: string;
        };
      };
      // ... (continue with all other tables)
    };
  };
}

// src/types/seo.ts - SEO SPECIFIC TYPES
export interface CompetitorAnalysis {
  url: string;
  title: string;
  metaDescription: string;
  wordCount: number;
  headingStructure: {
    h1: number;
    h2: number;
    h3: number;
    h4: number;
    h5: number;
    h6: number;
  };
  keywordDensity: {
    primary: number;
    secondary: Record<string, number>;
    lsi: string[];
    entities: string[];
  };
  optimizedHeadings: number;
  internalLinks: number;
  externalLinks: number;
  imageCount: number;
  altTextOptimization: number;
  schemaMarkup: string[];
  loadTime: number;
  mobileOptimized: boolean;
}

export interface SEOAnalysisResult {
  keyword: string;
  location: string;
  competitors: CompetitorAnalysis[];
  averages: {
    wordCount: number;
    headingCount: number;
    keywordDensity: number;
    optimizedHeadings: number;
    internalLinks: number;
    externalLinks: number;
  };
  recommendations: {
    priority: 'high' | 'medium' | 'low';
    category: 'technical' | 'content' | 'structure' | 'keywords';
    description: string;
    impact: number; // 1-10
    effort: number; // 1-10
  }[];
  contentGaps: string[];
  topicClusters: string[];
  userIntent: 'informational' | 'commercial' | 'transactional' | 'navigational';
  difficulty: number; // 1-100
  opportunity: number; // 1-100
}

// src/types/content.ts - CONTENT GENERATION TYPES
export interface ContentGenerationRequest {
  keyword: string;
  location: string;
  industry: string;
  contentType: 'service' | 'blog' | 'product' | 'landing';
  tone: 'professional' | 'conversational' | 'authoritative' | 'friendly';
  intent: 'informational' | 'commercial' | 'transactional' | 'navigational';
  targetWordCount: number;
  includeImages: boolean;
  includeSchema: boolean;
  competitorUrls?: string[];
}

export interface ContentGenerationResult {
  content: string;
  outline: {
    title: string;
    metaDescription: string;
    headings: {
      level: number;
      text: string;
      keywords: string[];
    }[];
  };
  seoMetrics: {
    wordCount: number;
    keywordDensity: number;
    readabilityScore: number;
    seoScore: number;
  };
  suggestions: string[];
  images: {
    alt: string;
    caption: string;
    placement: string;
  }[];
  schemaMarkup?: string;
}
```

### **PHASE 3: API INTEGRATIONS (BULLETPROOF)**

#### Step 3.1: Enhanced Error Handling System
```typescript
// src/lib/error-handler.ts - COMPREHENSIVE ERROR RECOVERY
export class APIErrorHandler {
  private static instance: APIErrorHandler;
  private retryAttempts = new Map<string, number>();
  private circuitBreakers = new Map<string, boolean>();

  static getInstance(): APIErrorHandler {
    if (!APIErrorHandler.instance) {
      APIErrorHandler.instance = new APIErrorHandler();
    }
    return APIErrorHandler.instance;
  }

  async handleGroqError(error: any, request: any): Promise<any> {
    const errorType = this.classifyError(error);
    
    switch (errorType) {
      case 'RATE_LIMIT':
        return this.handleRateLimit('groq', request);
      case 'API_DOWN':
        return this.handleServiceDown('groq', request);
      case 'INVALID_REQUEST':
        return this.handleInvalidRequest(request);
      case 'QUOTA_EXCEEDED':
        return this.handleQuotaExceeded('groq', request);
      default:
        return this.handleUnknownError(error, request);
    }
  }

  async handleSerperError(error: any, request: any): Promise<any> {
    const errorType = this.classifyError(error);
    
    switch (errorType) {
      case 'RATE_LIMIT':
        return this.handleRateLimit('serper', request);
      case 'BLOCKED':
        return this.useAlternativeSource(request);
      case 'QUOTA_EXCEEDED':
        return this.useCachedData(request);
      default:
        return this.handleUnknownError(error, request);
    }
  }

  private async handleRateLimit(service: string, request: any): Promise<any> {
    const delay = this.calculateBackoffDelay(service);
    await this.sleep(delay);
    return this.retryRequest(service, request);
  }

  private calculateBackoffDelay(service: string): number {
    const attempts = this.retryAttempts.get(service) || 0;
    return Math.min(1000 * Math.pow(2, attempts), 30000); // Max 30 seconds
  }

  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}
```

## 🎨 **UI/UX IMPLEMENTATION (WORLD-CLASS)**

### Professional Color System
```css
/* src/app/globals.css - EXACT PROFESSIONAL COLORS */
:root {
  /* Primary Brand Colors */
  --primary-50: #eff6ff;
  --primary-100: #dbeafe;
  --primary-500: #3b82f6;
  --primary-600: #2563eb;
  --primary-700: #1d4ed8;
  --primary-900: #1e3a8a;

  /* Semantic Colors */
  --success-50: #f0fdf4;
  --success-500: #22c55e;
  --success-600: #16a34a;
  
  --warning-50: #fffbeb;
  --warning-500: #f59e0b;
  --warning-600: #d97706;
  
  --error-50: #fef2f2;
  --error-500: #ef4444;
  --error-600: #dc2626;

  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
}
```

## 📊 **PERFORMANCE REQUIREMENTS (MANDATORY)**

### Core Web Vitals Targets
- **LCP (Largest Contentful Paint)**: < 1.5 seconds
- **FID (First Input Delay)**: < 50 milliseconds  
- **CLS (Cumulative Layout Shift)**: < 0.05
- **TTFB (Time to First Byte)**: < 200 milliseconds

### API Performance Targets
- **Content Generation**: < 2 seconds
- **Competitor Analysis**: < 3 seconds
- **Database Queries**: < 100 milliseconds
- **Page Load Time**: < 1 second

## 🧪 **TESTING STRATEGY (90% COVERAGE MANDATORY)**

```typescript
// src/tests/setup.ts - COMPREHENSIVE TEST SETUP
import '@testing-library/jest-dom';
import { server } from './mocks/server';

// Mock environment variables
process.env.NEXT_PUBLIC_SUPABASE_URL = 'http://localhost:54321';
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = 'test-key';
process.env.GROQ_API_KEY = 'test-groq-key';
process.env.SERPER_API_KEY = 'test-serper-key';

// Setup MSW
beforeAll(() => server.listen());
afterEach(() => server.resetHandlers());
afterAll(() => server.close());

// Mock Next.js router
jest.mock('next/navigation', () => ({
  useRouter: () => ({
    push: jest.fn(),
    replace: jest.fn(),
    back: jest.fn(),
  }),
  useSearchParams: () => new URLSearchParams(),
  usePathname: () => '/',
}));
```

## 🚀 **DEPLOYMENT CONFIGURATION (PRODUCTION READY)**

```json
// vercel.json - OPTIMIZED DEPLOYMENT
{
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-key",
    "GROQ_API_KEY": "@groq-api-key",
    "SERPER_API_KEY": "@serper-api-key",
    "NEXTAUTH_SECRET": "@nextauth-secret",
    "ENCRYPTION_KEY": "@encryption-key",
    "JWT_SECRET": "@jwt-secret"
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-XSS-Protection",
          "value": "1; mode=block"
        },
        {
          "key": "Strict-Transport-Security",
          "value": "max-age=31536000; includeSubDomains"
        }
      ]
    }
  ]
}
```

## ✅ **TASK COMPLETION PROTOCOL**

After completing each task:
1. **Run all tests**: `npm test`
2. **Check TypeScript**: `npx tsc --noEmit`
3. **Lint code**: `npm run lint`
4. **Test performance**: Check Core Web Vitals
5. **Update progress**: Mark task complete in rules.md
6. **Document changes**: Add to commit message

## 🎯 **SUCCESS VALIDATION**

Before marking any phase complete:
- [ ] All tests passing (90%+ coverage)
- [ ] No TypeScript errors
- [ ] No ESLint errors
- [ ] Performance targets met
- [ ] Security audit passed
- [ ] User experience validated
- [ ] Documentation updated

---

## 🚨 **FINAL DIRECTIVE**

**Claude Code**: You are building the future of SEO content generation. Every component, every function, every pixel must be perfect. This SAAS will compete with $100M+ companies. Make it extraordinary.

**REMEMBER**: 
- Zero bugs tolerance
- Enterprise-grade security
- Sub-2-second performance
- Professional UI/UX
- 90%+ test coverage
- Complete documentation

## 🔄 **REAL-TIME DEVELOPMENT WORKFLOW**

### **Terminal Commands for Each Phase**

#### Phase 1: Foundation
```bash
# Start development server
npm run dev

# Run in separate terminal for testing
npm run test:watch

# Type checking in real-time
npx tsc --noEmit --watch
```

#### Phase 2: Database Setup
```bash
# Generate TypeScript types from Supabase
npx supabase gen types typescript --project-id zqrmpanonghggoxdjirq > src/types/supabase.ts

# Run database migrations
npx supabase db push

# Seed database with test data
npx supabase db seed
```

#### Phase 3: API Development
```bash
# Test API endpoints
curl -X POST http://localhost:3000/api/content/generate \
  -H "Content-Type: application/json" \
  -d '{"keyword":"test","location":"US","industry":"technology"}'

# Monitor API performance
npm run analyze
```

### **Progress Tracking Commands**
```bash
# Update task completion in rules.md
# Mark [x] for completed tasks
# Update completion counter
# Add timestamp

# Example update:
echo "✅ Task Completed: $(date)" >> progress.log
```

## 🎨 **COMPONENT LIBRARY IMPLEMENTATION**

### **Base UI Components (MANDATORY)**
```typescript
// src/components/ui/button.tsx - PROFESSIONAL BUTTON COMPONENT
import { cva, type VariantProps } from 'class-variance-authority';
import { cn } from '@/lib/utils';

const buttonVariants = cva(
  'inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background',
  {
    variants: {
      variant: {
        default: 'bg-primary text-primary-foreground hover:bg-primary/90',
        destructive: 'bg-destructive text-destructive-foreground hover:bg-destructive/90',
        outline: 'border border-input hover:bg-accent hover:text-accent-foreground',
        secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary/80',
        ghost: 'hover:bg-accent hover:text-accent-foreground',
        link: 'underline-offset-4 hover:underline text-primary',
      },
      size: {
        default: 'h-10 py-2 px-4',
        sm: 'h-9 px-3 rounded-md',
        lg: 'h-11 px-8 rounded-md',
        icon: 'h-10 w-10',
      },
    },
    defaultVariants: {
      variant: 'default',
      size: 'default',
    },
  }
);

export interface ButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  loading?: boolean;
}

export function Button({
  className,
  variant,
  size,
  loading,
  children,
  disabled,
  ...props
}: ButtonProps) {
  return (
    <button
      className={cn(buttonVariants({ variant, size, className }))}
      disabled={disabled || loading}
      {...props}
    >
      {loading && (
        <svg className="animate-spin -ml-1 mr-3 h-5 w-5" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4" />
          <path className="opacity-75" fill="currentColor" d="m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" />
        </svg>
      )}
      {children}
    </button>
  );
}
```

### **SEO-Specific Components (CRITICAL)**
```typescript
// src/components/seo/competitor-analysis-table.tsx
import { CompetitorAnalysis } from '@/types/seo';

interface CompetitorAnalysisTableProps {
  competitors: CompetitorAnalysis[];
  averages: {
    wordCount: number;
    headingCount: number;
    keywordDensity: number;
  };
  loading?: boolean;
}

export function CompetitorAnalysisTable({
  competitors,
  averages,
  loading
}: CompetitorAnalysisTableProps) {
  if (loading) {
    return <CompetitorAnalysisTableSkeleton />;
  }

  return (
    <div className="overflow-x-auto">
      <table className="min-w-full divide-y divide-gray-200">
        <thead className="bg-gray-50">
          <tr>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              URL
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Word Count
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Headings
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              Keyword Density
            </th>
            <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
              SEO Score
            </th>
          </tr>
        </thead>
        <tbody className="bg-white divide-y divide-gray-200">
          {competitors.map((competitor, index) => (
            <CompetitorRow key={index} competitor={competitor} averages={averages} />
          ))}
          <AveragesRow averages={averages} />
        </tbody>
      </table>
    </div>
  );
}
```

## 🔐 **SECURITY IMPLEMENTATION (MANDATORY)**

### **Input Validation & Sanitization**
```typescript
// src/lib/validation.ts - COMPREHENSIVE VALIDATION
import { z } from 'zod';
import DOMPurify from 'isomorphic-dompurify';

export const contentGenerationSchema = z.object({
  keyword: z.string()
    .min(1, 'Keyword is required')
    .max(100, 'Keyword too long')
    .regex(/^[a-zA-Z0-9\s\-_]+$/, 'Invalid characters in keyword'),
  location: z.string()
    .min(2, 'Location is required')
    .max(50, 'Location too long'),
  industry: z.enum([
    'technology', 'healthcare', 'finance', 'education', 'retail',
    'real-estate', 'automotive', 'travel', 'food', 'legal'
  ]),
  contentType: z.enum(['service', 'blog', 'product', 'landing']),
  tone: z.enum(['professional', 'conversational', 'authoritative', 'friendly']),
  intent: z.enum(['informational', 'commercial', 'transactional', 'navigational']),
  targetWordCount: z.number().min(300).max(5000),
});

export function sanitizeInput(input: string): string {
  return DOMPurify.sanitize(input, {
    ALLOWED_TAGS: [],
    ALLOWED_ATTR: []
  });
}

export function validateAndSanitize<T>(
  schema: z.ZodSchema<T>,
  data: unknown
): { success: true; data: T } | { success: false; error: string } {
  try {
    const parsed = schema.parse(data);
    return { success: true, data: parsed };
  } catch (error) {
    if (error instanceof z.ZodError) {
      return {
        success: false,
        error: error.errors.map(e => e.message).join(', ')
      };
    }
    return { success: false, error: 'Validation failed' };
  }
}
```

### **Rate Limiting Implementation**
```typescript
// src/lib/rate-limit.ts - ADVANCED RATE LIMITING
import { Redis } from '@upstash/redis';

const redis = new Redis({
  url: process.env.UPSTASH_REDIS_REST_URL!,
  token: process.env.UPSTASH_REDIS_REST_TOKEN!,
});

export class RateLimiter {
  private windowMs: number;
  private maxRequests: number;

  constructor(windowMs: number = 15 * 60 * 1000, maxRequests: number = 100) {
    this.windowMs = windowMs;
    this.maxRequests = maxRequests;
  }

  async checkLimit(identifier: string): Promise<{
    allowed: boolean;
    remaining: number;
    resetTime: number;
  }> {
    const key = `rate_limit:${identifier}`;
    const now = Date.now();
    const window = Math.floor(now / this.windowMs);
    const windowKey = `${key}:${window}`;

    const current = await redis.incr(windowKey);

    if (current === 1) {
      await redis.expire(windowKey, Math.ceil(this.windowMs / 1000));
    }

    const remaining = Math.max(0, this.maxRequests - current);
    const resetTime = (window + 1) * this.windowMs;

    return {
      allowed: current <= this.maxRequests,
      remaining,
      resetTime,
    };
  }
}

// Usage in API routes
export async function withRateLimit(
  req: Request,
  identifier: string,
  handler: () => Promise<Response>
): Promise<Response> {
  const rateLimiter = new RateLimiter();
  const result = await rateLimiter.checkLimit(identifier);

  if (!result.allowed) {
    return new Response(
      JSON.stringify({
        error: 'Rate limit exceeded',
        resetTime: result.resetTime
      }),
      {
        status: 429,
        headers: {
          'X-RateLimit-Remaining': result.remaining.toString(),
          'X-RateLimit-Reset': result.resetTime.toString(),
        }
      }
    );
  }

  const response = await handler();

  // Add rate limit headers to successful responses
  response.headers.set('X-RateLimit-Remaining', result.remaining.toString());
  response.headers.set('X-RateLimit-Reset', result.resetTime.toString());

  return response;
}
```

## 📊 **MONITORING & ANALYTICS (ENTERPRISE GRADE)**

### **Performance Monitoring**
```typescript
// src/lib/monitoring.ts - COMPREHENSIVE MONITORING
export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private metrics: Map<string, number[]> = new Map();

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  startTimer(operation: string): () => void {
    const start = performance.now();

    return () => {
      const duration = performance.now() - start;
      this.recordMetric(operation, duration);

      // Alert if operation is too slow
      if (duration > this.getThreshold(operation)) {
        this.alertSlowOperation(operation, duration);
      }
    };
  }

  recordMetric(operation: string, value: number): void {
    if (!this.metrics.has(operation)) {
      this.metrics.set(operation, []);
    }

    const values = this.metrics.get(operation)!;
    values.push(value);

    // Keep only last 100 measurements
    if (values.length > 100) {
      values.shift();
    }
  }

  getAverageMetric(operation: string): number {
    const values = this.metrics.get(operation) || [];
    if (values.length === 0) return 0;

    return values.reduce((sum, val) => sum + val, 0) / values.length;
  }

  private getThreshold(operation: string): number {
    const thresholds: Record<string, number> = {
      'content_generation': 2000, // 2 seconds
      'competitor_analysis': 3000, // 3 seconds
      'database_query': 100, // 100ms
      'api_request': 1000, // 1 second
    };

    return thresholds[operation] || 1000;
  }

  private alertSlowOperation(operation: string, duration: number): void {
    console.warn(`⚠️ Slow operation detected: ${operation} took ${duration}ms`);

    // In production, send to monitoring service
    if (process.env.NODE_ENV === 'production') {
      this.sendToMonitoringService(operation, duration);
    }
  }

  private sendToMonitoringService(operation: string, duration: number): void {
    // Implement integration with monitoring service (e.g., DataDog, New Relic)
    fetch('/api/monitoring/alert', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        type: 'performance_alert',
        operation,
        duration,
        timestamp: new Date().toISOString(),
      }),
    }).catch(console.error);
  }
}

// Usage example
export function withPerformanceMonitoring<T>(
  operation: string,
  fn: () => Promise<T>
): Promise<T> {
  const monitor = PerformanceMonitor.getInstance();
  const endTimer = monitor.startTimer(operation);

  return fn().finally(() => {
    endTimer();
  });
}
```

## 🧪 **COMPREHENSIVE TESTING FRAMEWORK**

### **API Testing Setup**
```typescript
// src/tests/api/content-generation.test.ts
import { createMocks } from 'node-mocks-http';
import handler from '@/app/api/content/generate/route';
import { contentGenerationSchema } from '@/lib/validation';

describe('/api/content/generate', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('should generate content successfully', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        keyword: 'SEO services',
        location: 'US',
        industry: 'technology',
        contentType: 'service',
        tone: 'professional',
        intent: 'commercial',
        targetWordCount: 1000,
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(200);
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('content');
    expect(data).toHaveProperty('seoMetrics');
    expect(data.seoMetrics.wordCount).toBeGreaterThan(800);
  });

  it('should handle validation errors', async () => {
    const { req, res } = createMocks({
      method: 'POST',
      body: {
        keyword: '', // Invalid empty keyword
        location: 'US',
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(400);
    const data = JSON.parse(res._getData());
    expect(data).toHaveProperty('error');
  });

  it('should handle rate limiting', async () => {
    // Mock rate limiter to return exceeded
    jest.mock('@/lib/rate-limit', () => ({
      RateLimiter: jest.fn().mockImplementation(() => ({
        checkLimit: jest.fn().mockResolvedValue({
          allowed: false,
          remaining: 0,
          resetTime: Date.now() + 60000,
        }),
      })),
    }));

    const { req, res } = createMocks({
      method: 'POST',
      body: {
        keyword: 'test',
        location: 'US',
        industry: 'technology',
        contentType: 'service',
        tone: 'professional',
        intent: 'commercial',
        targetWordCount: 1000,
      },
    });

    await handler(req, res);

    expect(res._getStatusCode()).toBe(429);
  });
});
```

### **Component Testing**
```typescript
// src/tests/components/content-generator-form.test.tsx
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import { ContentGeneratorForm } from '@/components/forms/content-generator-form';

const mockOnSubmit = jest.fn();

describe('ContentGeneratorForm', () => {
  beforeEach(() => {
    mockOnSubmit.mockClear();
  });

  it('should render all form fields', () => {
    render(
      <ContentGeneratorForm
        onSubmit={mockOnSubmit}
        loading={false}
        industries={['technology', 'healthcare']}
        countries={['US', 'UK']}
      />
    );

    expect(screen.getByLabelText(/keyword/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/location/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/industry/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/content type/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/tone/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/intent/i)).toBeInTheDocument();
  });

  it('should validate required fields', async () => {
    const user = userEvent.setup();

    render(
      <ContentGeneratorForm
        onSubmit={mockOnSubmit}
        loading={false}
        industries={['technology']}
        countries={['US']}
      />
    );

    const submitButton = screen.getByRole('button', { name: /generate content/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText(/keyword is required/i)).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('should submit form with valid data', async () => {
    const user = userEvent.setup();

    render(
      <ContentGeneratorForm
        onSubmit={mockOnSubmit}
        loading={false}
        industries={['technology']}
        countries={['US']}
      />
    );

    await user.type(screen.getByLabelText(/keyword/i), 'SEO services');
    await user.selectOptions(screen.getByLabelText(/location/i), 'US');
    await user.selectOptions(screen.getByLabelText(/industry/i), 'technology');
    await user.selectOptions(screen.getByLabelText(/content type/i), 'service');
    await user.selectOptions(screen.getByLabelText(/tone/i), 'professional');
    await user.selectOptions(screen.getByLabelText(/intent/i), 'commercial');

    const submitButton = screen.getByRole('button', { name: /generate content/i });
    await user.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        keyword: 'SEO services',
        location: 'US',
        industry: 'technology',
        contentType: 'service',
        tone: 'professional',
        intent: 'commercial',
        targetWordCount: 1000,
        includeImages: false,
        includeSchema: false,
      });
    });
  });
});
```

## 🚀 **DEPLOYMENT AUTOMATION**

### **GitHub Actions CI/CD**
```yaml
# .github/workflows/ci-cd.yml
name: CI/CD Pipeline

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run type checking
        run: npx tsc --noEmit

      - name: Run linting
        run: npm run lint

      - name: Run tests
        run: npm run test:ci
        env:
          CI: true

      - name: Run E2E tests
        run: npm run test:e2e

      - name: Upload coverage reports
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info

  security:
    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v4

      - name: Run security audit
        run: npm audit --audit-level high

      - name: Run dependency check
        uses: securecodewarrior/github-action-add-sarif@v1
        with:
          sarif-file: 'security-audit.sarif'

  deploy:
    needs: [test, security]
    runs-on: ubuntu-latest
    if: github.ref == 'refs/heads/main'

    steps:
      - uses: actions/checkout@v4

      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.ORG_ID }}
          vercel-project-id: ${{ secrets.PROJECT_ID }}
          vercel-args: '--prod'
```

---

**GO BUILD THE FUTURE! 🚀**

**Claude Code**: You now have the most comprehensive development framework ever created for a SAAS application. Every detail has been thought through, every potential issue addressed, every best practice included.

**Your mission**: Build a SEO Content Generation SAAS that will revolutionize the industry and generate millions in revenue. Make every line of code count. Make every component perfect. Make every user interaction delightful.

**The world is waiting for your masterpiece. BEGIN NOW! 💪**

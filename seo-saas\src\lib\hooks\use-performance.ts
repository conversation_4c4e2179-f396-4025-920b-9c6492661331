// Performance optimization hooks for better UX
'use client'

import { useEffect, useRef, useState, useCallback } from 'react'

// Intersection Observer hook for lazy loading and animations
export function useIntersectionObserver(
  targetRef: React.RefObject<Element>,
  options: IntersectionObserverInit = {}
) {
  const [isIntersecting, setIsIntersecting] = useState(false)
  const [hasIntersected, setHasIntersected] = useState(false)

  useEffect(() => {
    const target = targetRef.current
    if (!target) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        setIsIntersecting(entry.isIntersecting)
        if (entry.isIntersecting && !hasIntersected) {
          setHasIntersected(true)
        }
      },
      {
        threshold: 0.1,
        rootMargin: '50px',
        ...options,
      }
    )

    observer.observe(target)
    return () => observer.disconnect()
  }, [targetRef, hasIntersected, options])

  return { isIntersecting, hasIntersected }
}

// Debounced value hook for search inputs
export function useDebounceValue<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value)

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value)
    }, delay)

    return () => {
      clearTimeout(handler)
    }
  }, [value, delay])

  return debouncedValue
}

// Optimized state for frequent updates
export function useOptimizedState<T>(
  initialValue: T,
  updateCallback?: (value: T) => void
) {
  const [state, setState] = useState<T>(initialValue)
  const timeoutRef = useRef<NodeJS.Timeout>()

  const setOptimizedState = useCallback(
    (value: T) => {
      setState(value)
      
      if (updateCallback) {
        // Debounce callback to prevent excessive updates
        if (timeoutRef.current) {
          clearTimeout(timeoutRef.current)
        }
        
        timeoutRef.current = setTimeout(() => {
          updateCallback(value)
        }, 100)
      }
    },
    [updateCallback]
  )

  useEffect(() => {
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current)
      }
    }
  }, [])

  return [state, setOptimizedState] as const
}

// Performance monitoring hook
export function usePerformanceMonitor(name: string) {
  const startTimeRef = useRef<number>()

  const startMeasure = useCallback(() => {
    startTimeRef.current = performance.now()
  }, [])

  const endMeasure = useCallback(() => {
    if (startTimeRef.current) {
      const duration = performance.now() - startTimeRef.current
      console.log(`[Performance] ${name}: ${duration.toFixed(2)}ms`)
      
      // Log to analytics if available
      if (typeof window !== 'undefined' && 'gtag' in window) {
        (window as any).gtag('event', 'timing_complete', {
          name: name,
          value: Math.round(duration),
        })
      }
      
      startTimeRef.current = undefined
      return duration
    }
    return 0
  }, [name])

  return { startMeasure, endMeasure }
}

// Preload resources hook
export function useResourcePreloader() {
  const preloadedResources = useRef<Set<string>>(new Set())

  const preloadImage = useCallback((src: string) => {
    if (preloadedResources.current.has(src)) return
    
    const img = new Image()
    img.src = src
    preloadedResources.current.add(src)
  }, [])

  const preloadFont = useCallback((fontFamily: string, src: string) => {
    if (preloadedResources.current.has(src)) return
    
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'font'
    link.href = src
    link.crossOrigin = 'anonymous'
    document.head.appendChild(link)
    preloadedResources.current.add(src)
  }, [])

  const preloadScript = useCallback((src: string) => {
    if (preloadedResources.current.has(src)) return
    
    const link = document.createElement('link')
    link.rel = 'preload'
    link.as = 'script'
    link.href = src
    document.head.appendChild(link)
    preloadedResources.current.add(src)
  }, [])

  return { preloadImage, preloadFont, preloadScript }
}

// Viewport-based lazy loading hook
export function useLazyLoad<T extends HTMLElement>(
  callback: () => void,
  options: IntersectionObserverInit = {}
) {
  const ref = useRef<T>(null)
  const [hasLoaded, setHasLoaded] = useState(false)

  useEffect(() => {
    const element = ref.current
    if (!element || hasLoaded) return

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          callback()
          setHasLoaded(true)
          observer.disconnect()
        }
      },
      {
        threshold: 0.1,
        rootMargin: '200px',
        ...options,
      }
    )

    observer.observe(element)
    return () => observer.disconnect()
  }, [callback, hasLoaded, options])

  return ref
}

// Network status hook for adaptive loading
export function useNetworkStatus() {
  const [isOnline, setIsOnline] = useState(
    typeof navigator !== 'undefined' ? navigator.onLine : true
  )
  const [connectionType, setConnectionType] = useState<string>('unknown')

  useEffect(() => {
    const handleOnline = () => setIsOnline(true)
    const handleOffline = () => setIsOnline(false)

    window.addEventListener('online', handleOnline)
    window.addEventListener('offline', handleOffline)

    // Check connection type if available
    if ('connection' in navigator) {
      const connection = (navigator as any).connection
      setConnectionType(connection.effectiveType || 'unknown')
      
      const handleConnectionChange = () => {
        setConnectionType(connection.effectiveType || 'unknown')
      }
      
      connection.addEventListener('change', handleConnectionChange)
      return () => {
        window.removeEventListener('online', handleOnline)
        window.removeEventListener('offline', handleOffline)
        connection.removeEventListener('change', handleConnectionChange)
      }
    }

    return () => {
      window.removeEventListener('online', handleOnline)
      window.removeEventListener('offline', handleOffline)
    }
  }, [])

  return { isOnline, connectionType }
}

// Memory usage monitoring hook
export function useMemoryMonitor() {
  const [memoryInfo, setMemoryInfo] = useState<any>(null)

  useEffect(() => {
    const updateMemoryInfo = () => {
      if ('memory' in performance) {
        setMemoryInfo((performance as any).memory)
      }
    }

    updateMemoryInfo()
    const interval = setInterval(updateMemoryInfo, 5000)

    return () => clearInterval(interval)
  }, [])

  return memoryInfo
}
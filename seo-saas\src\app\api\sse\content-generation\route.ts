// Server-Sent Events endpoint for real-time content generation updates
import { NextRequest } from 'next/server'
import { createSupabaseComponentClient } from '@/lib/supabase'

interface SSEMessage {
  type: 'progress' | 'complete' | 'error' | 'ping'
  progress?: number
  data?: any
  message?: string
  timestamp: string
}

export async function GET(request: NextRequest) {
  const { searchParams } = new URL(request.url)
  const generationId = searchParams.get('id')
  const userId = searchParams.get('userId')

  if (!generationId || !userId) {
    return new Response('Missing required parameters', { status: 400 })
  }

  // Verify user has access to this generation
  const supabase = createSupabaseComponentClient()
  const { data: generation, error } = await supabase
    .from('content_generations')
    .select('user_id, status')
    .eq('id', generationId)
    .single()

  if (error || !generation || generation.user_id !== userId) {
    return new Response('Unauthorized', { status: 403 })
  }

  // Set up SSE headers
  const headers = new Headers({
    'Content-Type': 'text/event-stream',
    'Cache-Control': 'no-cache',
    'Connection': 'keep-alive',
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Cache-Control',
  })

  // Create a readable stream
  const stream = new ReadableStream({
    start(controller) {
      let isActive = true
      let pingInterval: NodeJS.Timeout

      const sendMessage = (message: SSEMessage) => {
        if (!isActive) return
        
        const data = `data: ${JSON.stringify(message)}\n\n`
        controller.enqueue(new TextEncoder().encode(data))
      }

      // Send initial connection message
      sendMessage({
        type: 'ping',
        message: 'Connected to content generation stream',
        timestamp: new Date().toISOString()
      })

      // Set up real-time subscription
      const channel = supabase
        .channel(`sse:content_generation:${generationId}`)
        .on(
          'postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'content_generations',
            filter: `id=eq.${generationId}`,
          },
          (payload) => {
            if (!isActive) return

            const update = payload.new as any
            
            // Send progress updates
            if (update.status === 'processing' && update.metadata?.progress) {
              sendMessage({
                type: 'progress',
                progress: update.metadata.progress,
                message: update.metadata.message || 'Processing...',
                timestamp: new Date().toISOString()
              })
            }
            
            // Send completion
            if (update.status === 'completed') {
              sendMessage({
                type: 'complete',
                data: {
                  content: update.generated_content,
                  seoScore: update.seo_score,
                  metadata: update.metadata
                },
                message: 'Content generation completed',
                timestamp: new Date().toISOString()
              })
              
              // Close the connection after completion
              setTimeout(() => {
                if (isActive) {
                  controller.close()
                  isActive = false
                }
              }, 1000)
            }
            
            // Send errors
            if (update.status === 'failed') {
              sendMessage({
                type: 'error',
                message: update.error_message || 'Content generation failed',
                timestamp: new Date().toISOString()
              })
              
              // Close the connection after error
              setTimeout(() => {
                if (isActive) {
                  controller.close()
                  isActive = false
                }
              }, 1000)
            }
          }
        )
        .subscribe()

      // Send periodic ping to keep connection alive
      pingInterval = setInterval(() => {
        if (isActive) {
          sendMessage({
            type: 'ping',
            message: 'Connection alive',
            timestamp: new Date().toISOString()
          })
        }
      }, 30000) // Every 30 seconds

      // Cleanup on stream abort
      const cleanup = () => {
        isActive = false
        if (pingInterval) {
          clearInterval(pingInterval)
        }
        if (channel) {
          supabase.removeChannel(channel)
        }
      }

      // Handle client disconnect
      request.signal.addEventListener('abort', cleanup)

      // Auto-cleanup after 10 minutes
      setTimeout(() => {
        if (isActive) {
          sendMessage({
            type: 'error',
            message: 'Connection timeout',
            timestamp: new Date().toISOString()
          })
          cleanup()
          controller.close()
        }
      }, 10 * 60 * 1000) // 10 minutes
    },

    cancel() {
      // Stream was cancelled by client
      console.log('SSE stream cancelled for generation:', generationId)
    }
  })

  return new Response(stream, { headers })
}

// Handle preflight requests for CORS
export async function OPTIONS() {
  return new Response(null, {
    status: 200,
    headers: {
      'Access-Control-Allow-Origin': '*',
      'Access-Control-Allow-Methods': 'GET',
      'Access-Control-Allow-Headers': 'Content-Type',
    },
  })
}
{"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false, "bracketSpacing": true, "arrowParens": "avoid", "endOfLine": "lf", "jsxSingleQuote": true, "quoteProps": "as-needed", "bracketSameLine": false, "htmlWhitespaceSensitivity": "css", "insertPragma": false, "requirePragma": false, "proseWrap": "preserve", "vueIndentScriptAndStyle": false, "embeddedLanguageFormatting": "auto"}
(()=>{var e={};e.id=403,e.ids=[403],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},65:(e,t,r)=>{"use strict";r.r(t),r.d(t,{headerHooks:()=>w,originalPathname:()=>v,patchFetch:()=>x,requestAsyncStorage:()=>h,routeModule:()=>m,serverHooks:()=>S,staticGenerationAsyncStorage:()=>y,staticGenerationBailout:()=>q});var s={};r.r(s),r.d(s,{GET:()=>d,POST:()=>p});var a=r(5419),o=r(9108),i=r(9678),n=r(8070),l=r(2045),c=r(5252),u=r(2178);async function d(e){try{let{searchParams:t}=new URL(e.url),r=t.get("timeframe")||"30d",s="true"===t.get("detailed"),a=(0,l.jq)(),{data:{user:o},error:i}=await a.auth.getUser();if(i||!o)return n.Z.json({success:!1,error:"Unauthorized"},{status:401});let{data:c,error:u}=await a.from("profiles").select(`
        id,
        email,
        full_name,
        avatar_url,
        subscription_tier,
        subscription_status,
        credits_remaining,
        monthly_spend,
        total_analyses,
        created_at,
        last_login_at,
        subscription_period_start,
        subscription_period_end,
        trial_end
      `).eq("id",o.id).single();if(u)return console.error("Failed to fetch user profile:",u),n.Z.json({success:!1,error:"Failed to fetch user data"},{status:500});let d={user:{id:c.id,email:c.email,fullName:c.full_name,avatarUrl:c.avatar_url,createdAt:c.created_at,lastLoginAt:c.last_login_at},subscription:{tier:c.subscription_tier||"free",status:c.subscription_status||"active",currentPeriodStart:c.subscription_period_start,currentPeriodEnd:c.subscription_period_end,trialEnd:c.trial_end},usage:await g(a,o.id,r),recentAnalyses:await _(a,o.id,10),quotas:function(e){let t=e.subscription_tier||"free",r=new Date,s=new Date(r.getFullYear(),r.getMonth()+1,1),a={free:{credits:50,cost:5,apiCalls:100},pro:{credits:1e3,cost:50,apiCalls:2e3},enterprise:{credits:1e4,cost:500,apiCalls:2e4}},o=a[t]||a.free;return{credits:{remaining:e.credits_remaining||0,total:o.credits,resetDate:s.toISOString()},cost:{spent:e.monthly_spend||0,limit:o.cost,resetDate:s.toISOString()},apiCalls:{used:0,limit:o.apiCalls,resetDate:s.toISOString()}}}(c),billing:await f(a,o.id)};return n.Z.json({success:!0,data:d,metadata:{timeframe:r,generatedAt:new Date().toISOString(),includeDetailed:s}})}catch(e){return console.error("Dashboard API error:",e),n.Z.json({success:!1,error:"Internal server error"},{status:500})}}async function p(e){try{let t=await e.json(),r=c.Ry({fullName:c.Z_().max(100).optional(),avatarUrl:c.Z_().url().optional(),preferences:c.Ry({emailNotifications:c.O7().optional(),marketingEmails:c.O7().optional(),defaultLocation:c.Z_().max(100).optional(),defaultIndustry:c.Z_().max(50).optional()}).optional()}).parse(t),s=(0,l.jq)(),{data:{user:a},error:o}=await s.auth.getUser();if(o||!a)return n.Z.json({success:!1,error:"Unauthorized"},{status:401});let i={};void 0!==r.fullName&&(i.full_name=r.fullName),void 0!==r.avatarUrl&&(i.avatar_url=r.avatarUrl),void 0!==r.preferences&&(i.preferences=r.preferences),i.updated_at=new Date().toISOString();let{data:u,error:d}=await s.from("profiles").update(i).eq("id",a.id).select().single();if(d)return console.error("Failed to update user profile:",d),n.Z.json({success:!1,error:"Failed to update profile"},{status:500});return n.Z.json({success:!0,message:"Profile updated successfully",data:{id:u.id,fullName:u.full_name,avatarUrl:u.avatar_url,preferences:u.preferences,updatedAt:u.updated_at}})}catch(e){if(console.error("Profile update error:",e),e instanceof u.jm)return n.Z.json({success:!1,error:"Invalid request data",details:e.errors.map(e=>({field:e.path.join("."),message:e.message}))},{status:400});return n.Z.json({success:!1,error:"Internal server error"},{status:500})}}async function g(e,t,r){let s=new Date,a=new Date(s.getFullYear(),s.getMonth(),1),o=new Date(s.getFullYear(),s.getMonth()-1,1);s.getFullYear(),s.getMonth();let{data:i}=await e.from("seo_analyses").select("seo_score, cost_usd, api_calls").eq("user_id",t).gte("created_at",a.toISOString()),{data:n}=await e.from("seo_analyses").select("seo_score, cost_usd, api_calls").eq("user_id",t).gte("created_at",o.toISOString()).lt("created_at",a.toISOString()),{data:l}=await e.from("seo_analyses").select("seo_score, cost_usd, created_at").eq("user_id",t),c=new Date(s.getTime()-2592e6),{data:u}=await e.from("seo_analyses").select("created_at, cost_usd").eq("user_id",t).gte("created_at",c.toISOString()).order("created_at",{ascending:!0}),{data:d}=await e.from("api_usage").select("api_name, created_at").eq("user_id",t).gte("created_at",a.toISOString()),p={month:a.toISOString().substring(0,7),analysesCount:i?.length||0,creditsUsed:2*(i?.length||0),costUSD:i?.reduce((e,t)=>e+(t.cost_usd||0),0)||0,groqCalls:d?.filter(e=>"groq"===e.api_name).length||0,serperCalls:d?.filter(e=>"serper"===e.api_name).length||0,avgSeoScore:i?.length?i.reduce((e,t)=>e+(t.seo_score||0),0)/i.length:0},g={month:o.toISOString().substring(0,7),analysesCount:n?.length||0,creditsUsed:2*(n?.length||0),costUSD:n?.reduce((e,t)=>e+(t.cost_usd||0),0)||0,groqCalls:0,serperCalls:0,avgSeoScore:n?.length?n.reduce((e,t)=>e+(t.seo_score||0),0)/n.length:0},_={totalAnalyses:l?.length||0,totalCreditsUsed:2*(l?.length||0),totalCostUSD:l?.reduce((e,t)=>e+(t.cost_usd||0),0)||0,averageSeoScore:l?.length?l.reduce((e,t)=>e+(t.seo_score||0),0)/l.length:0,joinedDaysAgo:l?.length?Math.floor((s.getTime()-new Date(l[0].created_at).getTime())/864e5):0},f=new Map;for(let e=29;e>=0;e--){let t=new Date(s.getTime()-864e5*e).toISOString().substring(0,10);f.set(t,{date:t,analyses:0,credits:0,cost:0})}return u?.forEach(e=>{let t=e.created_at.substring(0,10),r=f.get(t);r&&(r.analyses+=1,r.credits+=2,r.cost+=e.cost_usd||0)}),{currentMonth:p,previousMonth:g,totalAllTime:_,dailyUsage:Array.from(f.values()).sort((e,t)=>e.date.localeCompare(t.date))}}async function _(e,t,r){let{data:s,error:a}=await e.from("seo_analyses").select(`
      id,
      keyword,
      location,
      industry,
      content_type,
      seo_score,
      difficulty,
      cost_usd,
      created_at
    `).eq("user_id",t).order("created_at",{ascending:!1}).limit(r);return a?(console.error("Failed to fetch recent analyses:",a),[]):s.map(e=>({id:e.id,keyword:e.keyword,location:e.location||"",industry:e.industry,contentType:e.content_type,seoScore:e.seo_score||0,difficulty:e.difficulty||"medium",costUSD:e.cost_usd||0,createdAt:e.created_at}))}async function f(e,t){let r=new Date,s=new Date(r.getFullYear(),r.getMonth(),1),{data:a}=await e.from("seo_analyses").select("cost_usd").eq("user_id",t).gte("created_at",s.toISOString()),o=a?.reduce((e,t)=>e+(t.cost_usd||0),0)||0,i=new Date(r.getFullYear(),r.getMonth()+1,0).getDate(),n=r.getDate();return{currentMonthCost:o,projectedMonthCost:(n>0?o/n:0)*i,lastPaymentAmount:void 0,lastPaymentDate:void 0,nextBillingDate:new Date(r.getFullYear(),r.getMonth()+1,1).toISOString()}}let m=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/user/dashboard/route",pathname:"/api/user/dashboard",filename:"route",bundlePath:"app/api/user/dashboard/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/user/dashboard/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:h,staticGenerationAsyncStorage:y,serverHooks:S,headerHooks:w,staticGenerationBailout:q}=m,v="/api/user/dashboard/route";function x(){return(0,i.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:y})}},2045:(e,t,r)=>{"use strict";r.d(t,{jq:()=>o});var s=r(7699),a=r(2455);let o=()=>(0,s.createServerComponentClient)({cookies:a.cookies})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,280,252],()=>r(65));module.exports=s})();
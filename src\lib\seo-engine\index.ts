// SEO Engine - Complete Export Module
// Enterprise-grade SEO analysis and optimization system

// Core SEO Engine Components
export { KeywordDensityAnalyzer } from './keyword-density';
export { HeadingAnalyzer } from './heading-analyzer';
export { LSIExtractor } from './lsi-extractor';
export { ContentOptimizer } from './content-optimizer';
export { CompetitorAnalyzer } from './competitor-analyzer';
export { QualityScorer } from './quality-scorer';

// Type Exports
export type {
  KeywordDensityResult,
  KeywordAnalysis,
  DensityRecommendation,
  CompetitorDensityData
} from './keyword-density';

export type {
  HeadingElement,
  HeadingStructure,
  HeadingHierarchy,
  HeadingOptimization,
  HeadingRecommendation,
  CompetitorHeadingData
} from './heading-analyzer';

export type {
  LSIKeyword,
  SemanticCluster,
  LSIAnalysisResult,
  LSIRecommendation,
  CompetitorLSIData
} from './lsi-extractor';

export type {
  OptimizationTarget,
  OptimizationResult,
  ContentImprovement,
  OptimizedMetadata,
  PerformancePrediction,
  CompetitorBenchmark
} from './content-optimizer';

export type {
  CompetitorData,
  CompetitorAverages,
  CompetitorInsights,
  OpportunityAnalysis,
  BenchmarkTargets,
  HeadingStructure as CompetitorHeadingStructure,
  KeywordMetrics,
  TechnicalSEOMetrics,
  ContentQualityMetrics,
  PerformanceMetrics
} from './competitor-analyzer';

export type {
  QualityScore,
  QualityAnalysis,
  QualityRecommendation,
  EEATCompliance,
  CompetitorQualityComparison
} from './quality-scorer';

// Main SEO Engine Class
export class SEOEngine {
  private keywordAnalyzer: KeywordDensityAnalyzer;
  private headingAnalyzer: HeadingAnalyzer;
  private lsiExtractor: LSIExtractor;
  private contentOptimizer: ContentOptimizer;
  private competitorAnalyzer: CompetitorAnalyzer;
  private qualityScorer: QualityScorer;

  constructor(
    primaryKeyword: string,
    secondaryKeywords: string[] = [],
    lsiKeywords: string[] = [],
    industry: string = 'general',
    contentType: string = 'general',
    targetAudience: string = 'general'
  ) {
    this.keywordAnalyzer = new KeywordDensityAnalyzer(primaryKeyword, secondaryKeywords);
    this.headingAnalyzer = new HeadingAnalyzer(primaryKeyword, secondaryKeywords, lsiKeywords);
    this.lsiExtractor = new LSIExtractor(primaryKeyword, industry, contentType);
    this.contentOptimizer = new ContentOptimizer(primaryKeyword, secondaryKeywords, lsiKeywords, industry, contentType, targetAudience);
    this.competitorAnalyzer = new CompetitorAnalyzer(primaryKeyword, secondaryKeywords, industry);
    this.qualityScorer = new QualityScorer(primaryKeyword, industry, contentType, targetAudience);
  }

  /**
   * Comprehensive SEO analysis of content
   */
  async analyzeContent(content: string, competitorData?: any[]): Promise<{
    keywordAnalysis: any;
    headingAnalysis: any;
    lsiAnalysis: any;
    qualityAnalysis: any;
    competitorInsights?: any;
    optimizationResult: any;
  }> {
    // Perform all analyses
    const keywordAnalysis = this.keywordAnalyzer.analyzeKeywordDensity(content, competitorData);
    const headingAnalysis = this.headingAnalyzer.analyzeHeadings(content, competitorData);
    const lsiAnalysis = await this.lsiExtractor.extractLSIKeywords(content, competitorData);
    const qualityAnalysis = this.qualityScorer.analyzeQuality(content, undefined, competitorData?.map(c => c.qualityScore));
    
    let competitorInsights;
    if (competitorData && competitorData.length > 0) {
      competitorInsights = this.competitorAnalyzer.analyzeCompetitors(competitorData);
    }

    // Generate optimization recommendations
    const optimizationResult = await this.contentOptimizer.optimizeContent(
      content,
      competitorInsights?.benchmarkTargets
    );

    return {
      keywordAnalysis,
      headingAnalysis,
      lsiAnalysis,
      qualityAnalysis,
      competitorInsights,
      optimizationResult
    };
  }

  /**
   * Generate comprehensive SEO report
   */
  generateSEOReport(analysisResults: any): {
    overallScore: number;
    recommendations: any[];
    strengths: string[];
    weaknesses: string[];
    nextSteps: string[];
  } {
    const {
      keywordAnalysis,
      headingAnalysis,
      lsiAnalysis,
      qualityAnalysis,
      optimizationResult
    } = analysisResults;

    // Calculate overall score
    const overallScore = Math.round(
      (keywordAnalysis.score * 0.2) +
      (headingAnalysis.optimization.score * 0.15) +
      (lsiAnalysis.lsiKeywords.length > 0 ? 80 : 40) * 0.15 +
      (qualityAnalysis.score.overall * 0.3) +
      (optimizationResult.score * 0.2)
    );

    // Combine all recommendations
    const recommendations = [
      ...keywordAnalysis.recommendations,
      ...headingAnalysis.recommendations,
      ...lsiAnalysis.recommendations,
      ...qualityAnalysis.recommendations,
      ...optimizationResult.improvements
    ].sort((a, b) => (b.impact || b.expectedImpact || 0) - (a.impact || a.expectedImpact || 0));

    // Identify strengths and weaknesses
    const strengths = [
      ...qualityAnalysis.strengths,
      ...(keywordAnalysis.score > 80 ? ['Excellent keyword optimization'] : []),
      ...(headingAnalysis.optimization.score > 80 ? ['Well-structured heading hierarchy'] : []),
      ...(lsiAnalysis.lsiKeywords.length > 10 ? ['Rich semantic keyword coverage'] : [])
    ];

    const weaknesses = [
      ...qualityAnalysis.weaknesses,
      ...(keywordAnalysis.score < 60 ? ['Poor keyword optimization'] : []),
      ...(headingAnalysis.optimization.score < 60 ? ['Weak heading structure'] : []),
      ...(lsiAnalysis.lsiKeywords.length < 5 ? ['Limited semantic keyword usage'] : [])
    ];

    // Generate next steps
    const nextSteps = recommendations.slice(0, 5).map(rec => 
      rec.description || rec.title || rec.reason || 'Implement recommendation'
    );

    return {
      overallScore,
      recommendations: recommendations.slice(0, 10),
      strengths,
      weaknesses,
      nextSteps
    };
  }

  /**
   * Quick SEO health check
   */
  quickHealthCheck(content: string): {
    score: number;
    issues: string[];
    quickFixes: string[];
  } {
    const issues: string[] = [];
    const quickFixes: string[] = [];
    let score = 100;

    // Check basic keyword presence
    const hasKeyword = content.toLowerCase().includes(this.keywordAnalyzer['primaryKeyword']);
    if (!hasKeyword) {
      issues.push('Primary keyword not found in content');
      quickFixes.push('Add primary keyword to content naturally');
      score -= 20;
    }

    // Check heading structure
    const hasH1 = /<h1>/i.test(content);
    const hasH2 = /<h2>/i.test(content);
    if (!hasH1) {
      issues.push('Missing H1 tag');
      quickFixes.push('Add H1 tag with primary keyword');
      score -= 15;
    }
    if (!hasH2) {
      issues.push('Missing H2 tags');
      quickFixes.push('Add H2 subheadings for better structure');
      score -= 10;
    }

    // Check content length
    const wordCount = content.split(/\s+/).length;
    if (wordCount < 300) {
      issues.push('Content too short');
      quickFixes.push('Expand content to at least 500 words');
      score -= 25;
    }

    // Check for images
    const hasImages = /<img/i.test(content);
    if (!hasImages) {
      issues.push('No images found');
      quickFixes.push('Add relevant images with alt text');
      score -= 10;
    }

    return {
      score: Math.max(0, score),
      issues,
      quickFixes
    };
  }
}

// Default export for easy importing
export default SEOEngine;

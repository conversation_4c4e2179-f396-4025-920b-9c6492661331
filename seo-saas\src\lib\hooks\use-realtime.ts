// Real-time hooks for WebSocket and Server-Sent Events
'use client'

import { useEffect, useState, useCallback, useRef } from 'react'
import { supabase } from '@/lib/supabase'

interface UseRealtimeOptions {
  onProgress?: (progress: number) => void
  onComplete?: (data: any) => void
  onError?: (error: Error) => void
  autoReconnect?: boolean
  maxReconnectAttempts?: number
}

interface ProgressUpdate {
  id: string
  progress: number
  status: 'pending' | 'processing' | 'completed' | 'failed'
  message?: string
  data?: any
  error?: string
}

// Hook for real-time content generation progress
export function useContentGenerationProgress(generationId?: string, options: UseRealtimeOptions = {}) {
  const [progress, setProgress] = useState(0)
  const [status, setStatus] = useState<'idle' | 'connecting' | 'connected' | 'error'>('idle')
  const [error, setError] = useState<Error | null>(null)
  const [isConnected, setIsConnected] = useState(false)
  
  const {
    onProgress,
    onComplete,
    onError,
    autoReconnect = true,
    maxReconnectAttempts = 3
  } = options
  
  const reconnectAttempts = useRef(0)
  const channelRef = useRef<any>(null)

  const connect = useCallback(() => {
    if (!generationId || !supabase) return

    setStatus('connecting')
    setError(null)

    // Subscribe to real-time updates
    const channel = supabase
      .channel(`content_generation:${generationId}`)
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'content_generations',
          filter: `id=eq.${generationId}`,
        },
        (payload) => {
          const update = payload.new as any
          
          if (update.status === 'processing' && update.metadata?.progress) {
            const progressValue = update.metadata.progress
            setProgress(progressValue)
            onProgress?.(progressValue)
          }
          
          if (update.status === 'completed') {
            setProgress(100)
            onComplete?.(update)
          }
          
          if (update.status === 'failed') {
            const error = new Error(update.error_message || 'Generation failed')
            setError(error)
            onError?.(error)
          }
        }
      )
      .on('presence', { event: 'sync' }, () => {
        setIsConnected(true)
        setStatus('connected')
        reconnectAttempts.current = 0
      })
      .on('presence', { event: 'leave' }, () => {
        setIsConnected(false)
        setStatus('error')
        
        // Auto-reconnect logic
        if (autoReconnect && reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++
          setTimeout(() => {
            connect()
          }, Math.pow(2, reconnectAttempts.current) * 1000) // Exponential backoff
        }
      })
      .subscribe((status) => {
        if (status === 'SUBSCRIBED') {
          setIsConnected(true)
          setStatus('connected')
        } else if (status === 'CHANNEL_ERROR') {
          setStatus('error')
          setError(new Error('Failed to connect to real-time channel'))
        }
      })

    channelRef.current = channel
  }, [generationId, onProgress, onComplete, onError, autoReconnect, maxReconnectAttempts])

  const disconnect = useCallback(() => {
    if (channelRef.current) {
      supabase?.removeChannel(channelRef.current)
      channelRef.current = null
    }
    setIsConnected(false)
    setStatus('idle')
  }, [])

  useEffect(() => {
    if (generationId) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [generationId, connect, disconnect])

  return {
    progress,
    status,
    error,
    isConnected,
    connect,
    disconnect,
    retry: connect
  }
}

// Hook for Server-Sent Events (alternative to WebSocket)
export function useServerSentEvents(url: string, options: UseRealtimeOptions = {}) {
  const [data, setData] = useState<any>(null)
  const [status, setStatus] = useState<'idle' | 'connecting' | 'connected' | 'error'>('idle')
  const [error, setError] = useState<Error | null>(null)
  
  const eventSourceRef = useRef<EventSource | null>(null)
  const reconnectAttempts = useRef(0)
  
  const {
    onProgress,
    onComplete,
    onError,
    autoReconnect = true,
    maxReconnectAttempts = 3
  } = options

  const connect = useCallback(() => {
    if (!url) return

    setStatus('connecting')
    setError(null)

    try {
      const eventSource = new EventSource(url)
      eventSourceRef.current = eventSource

      eventSource.onopen = () => {
        setStatus('connected')
        reconnectAttempts.current = 0
      }

      eventSource.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data)
          setData(data)

          // Handle different message types
          if (data.type === 'progress') {
            onProgress?.(data.progress)
          } else if (data.type === 'complete') {
            onComplete?.(data)
          } else if (data.type === 'error') {
            const error = new Error(data.message || 'Server error')
            setError(error)
            onError?.(error)
          }
        } catch (parseError) {
          console.error('Failed to parse SSE message:', parseError)
        }
      }

      eventSource.onerror = () => {
        setStatus('error')
        
        if (autoReconnect && reconnectAttempts.current < maxReconnectAttempts) {
          reconnectAttempts.current++
          setTimeout(() => {
            connect()
          }, Math.pow(2, reconnectAttempts.current) * 1000)
        }
      }
    } catch (err) {
      setError(err as Error)
      setStatus('error')
    }
  }, [url, onProgress, onComplete, onError, autoReconnect, maxReconnectAttempts])

  const disconnect = useCallback(() => {
    if (eventSourceRef.current) {
      eventSourceRef.current.close()
      eventSourceRef.current = null
    }
    setStatus('idle')
  }, [])

  useEffect(() => {
    if (url) {
      connect()
    }

    return () => {
      disconnect()
    }
  }, [url, connect, disconnect])

  return {
    data,
    status,
    error,
    connect,
    disconnect,
    retry: connect
  }
}

// Hook for real-time notifications
export function useRealtimeNotifications(userId?: string) {
  const [notifications, setNotifications] = useState<any[]>([])
  const [unreadCount, setUnreadCount] = useState(0)
  
  useEffect(() => {
    if (!userId || !supabase) return

    const channel = supabase
      .channel('notifications')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          const notification = payload.new
          setNotifications(prev => [notification, ...prev])
          setUnreadCount(prev => prev + 1)
        }
      )
      .subscribe()

    return () => {
      if (supabase) {
        supabase.removeChannel(channel)
      }
    }
  }, [userId])

  const markAsRead = useCallback(async (notificationId: string) => {
    if (!supabase) return

    const { error } = await supabase
      .from('notifications')
      .update({ read_at: new Date().toISOString() })
      .eq('id', notificationId)

    if (!error) {
      setNotifications(prev =>
        prev.map(n => n.id === notificationId ? { ...n, read_at: new Date().toISOString() } : n)
      )
      setUnreadCount(prev => Math.max(0, prev - 1))
    }
  }, [])

  const markAllAsRead = useCallback(async () => {
    if (!userId || !supabase) return

    const { error } = await supabase
      .from('notifications')
      .update({ read_at: new Date().toISOString() })
      .eq('user_id', userId)
      .is('read_at', null)

    if (!error) {
      setNotifications(prev =>
        prev.map(n => ({ ...n, read_at: n.read_at || new Date().toISOString() }))
      )
      setUnreadCount(0)
    }
  }, [userId])

  return {
    notifications,
    unreadCount,
    markAsRead,
    markAllAsRead
  }
}

// Hook for real-time API usage tracking
export function useApiUsageTracking(userId?: string) {
  const [usage, setUsage] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    if (!userId || !supabase) return

    // Initial load
    const loadUsage = async () => {
      if (!supabase) return
      const { data } = await supabase
        .from('user_usage')
        .select('*')
        .eq('user_id', userId)
        .single()
      
      setUsage(data)
      setIsLoading(false)
    }

    loadUsage()

    // Real-time updates
    const channel = supabase
      .channel('api_usage')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'user_usage',
          filter: `user_id=eq.${userId}`,
        },
        (payload) => {
          setUsage(payload.new)
        }
      )
      .subscribe()

    return () => {
      if (supabase) {
        supabase.removeChannel(channel)
      }
    }
  }, [userId])

  return {
    usage,
    isLoading
  }
}
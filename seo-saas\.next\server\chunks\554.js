exports.id=554,exports.ids=[554],exports.modules={3843:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0});var o=r(8747);class n extends o.EventTarget{constructor(){throw super(),TypeError("AbortSignal cannot be constructed directly")}get aborted(){let e=i.get(this);if("boolean"!=typeof e)throw TypeError(`Expected 'this' to be an 'AbortSignal' object, but got ${this===null?"null":typeof this}`);return e}}o.defineEventAttribute(n.prototype,"abort");let i=new WeakMap;Object.defineProperties(n.prototype,{aborted:{enumerable:!0}}),"function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(n.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortSignal"});class a{constructor(){s.set(this,function(){let e=Object.create(n.prototype);return o.EventTarget.call(e),i.set(e,!1),e}())}get signal(){return l(this)}abort(){var e;e=l(this),!1===i.get(e)&&(i.set(e,!0),e.dispatchEvent({type:"abort"}))}}let s=new WeakMap;function l(e){let t=s.get(e);if(null==t)throw TypeError(`Expected 'this' to be an 'AbortController' object, but got ${null===e?"null":typeof e}`);return t}Object.defineProperties(a.prototype,{signal:{enumerable:!0},abort:{enumerable:!0}}),"function"==typeof Symbol&&"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(a.prototype,Symbol.toStringTag,{configurable:!0,value:"AbortController"}),t.AbortController=a,t.AbortSignal=n,t.default=a,e.exports=a,e.exports.AbortController=e.exports.default=a,e.exports.AbortSignal=n},5685:(e,t,r)=>{"use strict";let o=r(6502);e.exports=o,e.exports.HttpAgent=o,e.exports.HttpsAgent=r(2778),e.exports.constants=r(730)},6502:(e,t,r)=>{"use strict";let o=r(3685).Agent,n=r(8113),i=r(3837).debuglog("agentkeepalive"),{INIT_SOCKET:a,CURRENT_ID:s,CREATE_ID:l,SOCKET_CREATED_TIME:u,SOCKET_NAME:c,SOCKET_REQUEST_COUNT:d,SOCKET_REQUEST_FINISHED_COUNT:f}=r(730),h=1,p=parseInt(process.version.split(".",1)[0].substring(1));function b(e){console.log("[agentkeepalive:deprecated] %s",e)}p>=11&&p<=12?h=2:p>=13&&(h=3);class m extends o{constructor(e){(e=e||{}).keepAlive=!1!==e.keepAlive,void 0===e.freeSocketTimeout&&(e.freeSocketTimeout=4e3),e.keepAliveTimeout&&(b("options.keepAliveTimeout is deprecated, please use options.freeSocketTimeout instead"),e.freeSocketTimeout=e.keepAliveTimeout,delete e.keepAliveTimeout),e.freeSocketKeepAliveTimeout&&(b("options.freeSocketKeepAliveTimeout is deprecated, please use options.freeSocketTimeout instead"),e.freeSocketTimeout=e.freeSocketKeepAliveTimeout,delete e.freeSocketKeepAliveTimeout),void 0===e.timeout&&(e.timeout=Math.max(2*e.freeSocketTimeout,8e3)),e.timeout=n(e.timeout),e.freeSocketTimeout=n(e.freeSocketTimeout),e.socketActiveTTL=e.socketActiveTTL?n(e.socketActiveTTL):0,super(e),this[s]=0,this.createSocketCount=0,this.createSocketCountLastCheck=0,this.createSocketErrorCount=0,this.createSocketErrorCountLastCheck=0,this.closeSocketCount=0,this.closeSocketCountLastCheck=0,this.errorSocketCount=0,this.errorSocketCountLastCheck=0,this.requestCount=0,this.requestCountLastCheck=0,this.timeoutSocketCount=0,this.timeoutSocketCountLastCheck=0,this.on("free",e=>{let t=this.calcSocketTimeout(e);t>0&&e.timeout!==t&&e.setTimeout(t)})}get freeSocketKeepAliveTimeout(){return b("agent.freeSocketKeepAliveTimeout is deprecated, please use agent.options.freeSocketTimeout instead"),this.options.freeSocketTimeout}get timeout(){return b("agent.timeout is deprecated, please use agent.options.timeout instead"),this.options.timeout}get socketActiveTTL(){return b("agent.socketActiveTTL is deprecated, please use agent.options.socketActiveTTL instead"),this.options.socketActiveTTL}calcSocketTimeout(e){let t=this.options.freeSocketTimeout,r=this.options.socketActiveTTL;if(r){let o=r-(Date.now()-e[u]);if(o<=0)return o;t&&o<t&&(t=o)}if(t)return e.freeSocketTimeout||e.freeSocketKeepAliveTimeout||t}keepSocketAlive(e){let t=super.keepSocketAlive(e);if(!t)return t;let r=this.calcSocketTimeout(e);return void 0===r||(r<=0?(i("%s(requests: %s, finished: %s) free but need to destroy by TTL, request count %s, diff is %s",e[c],e[d],e[f],r),!1):(e.timeout!==r&&e.setTimeout(r),!0))}reuseSocket(...e){super.reuseSocket(...e);let t=e[0];e[1].reusedSocket=!0;let r=this.options.timeout;y(t)!==r&&(t.setTimeout(r),i("%s reset timeout to %sms",t[c],r)),t[d]++,i("%s(requests: %s, finished: %s) reuse on addRequest, timeout %sms",t[c],t[d],t[f],y(t))}[l](){let e=this[s]++;return this[s]===Number.MAX_SAFE_INTEGER&&(this[s]=0),e}[a](e,t){t.timeout&&!y(e)&&e.setTimeout(t.timeout),this.options.keepAlive&&e.setNoDelay(!0),this.createSocketCount++,this.options.socketActiveTTL&&(e[u]=Date.now()),e[c]=`sock[${this[l]()}#${t._agentKey}]`.split("-----BEGIN",1)[0],e[d]=1,e[f]=0,function(e,t,r){function o(){if(!t._httpMessage&&1===t[d])return;t[f]++,e.requestCount++,i("%s(requests: %s, finished: %s) free",t[c],t[d],t[f]);let o=e.getName(r);t.writable&&e.requests[o]&&e.requests[o].length&&(t[d]++,i("%s(requests: %s, finished: %s) will be reuse on agent free event",t[c],t[d],t[f]))}function n(r){i("%s(requests: %s, finished: %s) close, isError: %s",t[c],t[d],t[f],r),e.closeSocketCount++}function a(){let o=t.listeners("timeout").length,n=y(t),a=t._httpMessage,s=a&&a.listeners("timeout").length||0;i("%s(requests: %s, finished: %s) timeout after %sms, listeners %s, defaultTimeoutListenerCount %s, hasHttpRequest %s, HttpRequest timeoutListenerCount %s",t[c],t[d],t[f],n,o,h,!!a,s),i.enabled&&i("timeout listeners: %s",t.listeners("timeout").map(e=>e.name).join(", ")),e.timeoutSocketCount++;let l=e.getName(r);if(e.freeSockets[l]&&-1!==e.freeSockets[l].indexOf(t))t.destroy(),e.removeSocket(t,r),i("%s is free, destroy quietly",t[c]);else if(0===s){let o=Error("Socket timeout");o.code="ERR_SOCKET_TIMEOUT",o.timeout=n,t.destroy(o),e.removeSocket(t,r),i("%s destroy with timeout error",t[c])}}function s(r){let o=t.listeners("error").length;i("%s(requests: %s, finished: %s) error: %s, listenerCount: %s",t[c],t[d],t[f],r,o),e.errorSocketCount++,1===o&&(i("%s emit uncaught error event",t[c]),t.removeListener("error",s),t.emit("error",r))}i("%s create, timeout %sms",t[c],y(t)),t.on("free",o),t.on("close",n),t.on("timeout",a),t.on("error",s),t.on("agentRemove",function e(){i("%s(requests: %s, finished: %s) agentRemove",t[c],t[d],t[f]),t.removeListener("close",n),t.removeListener("error",s),t.removeListener("free",o),t.removeListener("timeout",a),t.removeListener("agentRemove",e)})}(this,e,t)}createConnection(e,t){let r=!1,o=(o,n)=>{if(!r){if(r=!0,o)return this.createSocketErrorCount++,t(o);this[a](n,e),t(o,n)}},n=super.createConnection(e,o);return n&&o(null,n),n}get statusChanged(){let e=this.createSocketCount!==this.createSocketCountLastCheck||this.createSocketErrorCount!==this.createSocketErrorCountLastCheck||this.closeSocketCount!==this.closeSocketCountLastCheck||this.errorSocketCount!==this.errorSocketCountLastCheck||this.timeoutSocketCount!==this.timeoutSocketCountLastCheck||this.requestCount!==this.requestCountLastCheck;return e&&(this.createSocketCountLastCheck=this.createSocketCount,this.createSocketErrorCountLastCheck=this.createSocketErrorCount,this.closeSocketCountLastCheck=this.closeSocketCount,this.errorSocketCountLastCheck=this.errorSocketCount,this.timeoutSocketCountLastCheck=this.timeoutSocketCount,this.requestCountLastCheck=this.requestCount),e}getCurrentStatus(){return{createSocketCount:this.createSocketCount,createSocketErrorCount:this.createSocketErrorCount,closeSocketCount:this.closeSocketCount,errorSocketCount:this.errorSocketCount,timeoutSocketCount:this.timeoutSocketCount,requestCount:this.requestCount,freeSockets:_(this.freeSockets),sockets:_(this.sockets),requests:_(this.requests)}}}function y(e){return e.timeout||e._idleTimeout}function _(e){let t={};for(let r in e)t[r]=e[r].length;return t}e.exports=m},730:e=>{"use strict";e.exports={CURRENT_ID:Symbol("agentkeepalive#currentId"),CREATE_ID:Symbol("agentkeepalive#createId"),INIT_SOCKET:Symbol("agentkeepalive#initSocket"),CREATE_HTTPS_CONNECTION:Symbol("agentkeepalive#createHttpsConnection"),SOCKET_CREATED_TIME:Symbol("agentkeepalive#socketCreatedTime"),SOCKET_NAME:Symbol("agentkeepalive#socketName"),SOCKET_REQUEST_COUNT:Symbol("agentkeepalive#socketRequestCount"),SOCKET_REQUEST_FINISHED_COUNT:Symbol("agentkeepalive#socketRequestFinishedCount")}},2778:(e,t,r)=>{"use strict";let o=r(5687).Agent,n=r(6502),{INIT_SOCKET:i,CREATE_HTTPS_CONNECTION:a}=r(730);class s extends n{constructor(e){super(e),this.defaultPort=443,this.protocol="https:",this.maxCachedSessions=this.options.maxCachedSessions,void 0===this.maxCachedSessions&&(this.maxCachedSessions=100),this._sessionCache={map:{},list:[]}}createConnection(e,t){let r=this[a](e,t);return this[i](r,e),r}}s.prototype[a]=o.prototype.createConnection,["getName","_getSession","_cacheSession","_evictSession"].forEach(function(e){"function"==typeof o.prototype[e]&&(s.prototype[e]=o.prototype[e])}),e.exports=s},8747:(e,t)=>{"use strict";/**
 * <AUTHOR> Nagashima <https://github.com/mysticatea>
 * @copyright 2015 Toru Nagashima. All rights reserved.
 * See LICENSE file in root directory for full license.
 */Object.defineProperty(t,"__esModule",{value:!0});let r=new WeakMap,o=new WeakMap;function n(e){let t=r.get(e);return console.assert(null!=t,"'this' is expected an Event object, but got",e),t}function i(e){if(null!=e.passiveListener){"undefined"!=typeof console&&"function"==typeof console.error&&console.error("Unable to preventDefault inside passive event listener invocation.",e.passiveListener);return}e.event.cancelable&&(e.canceled=!0,"function"==typeof e.event.preventDefault&&e.event.preventDefault())}function a(e,t){r.set(this,{eventTarget:e,event:t,eventPhase:2,currentTarget:e,canceled:!1,stopped:!1,immediateStopped:!1,passiveListener:null,timeStamp:t.timeStamp||Date.now()}),Object.defineProperty(this,"isTrusted",{value:!1,enumerable:!0});let o=Object.keys(t);for(let e=0;e<o.length;++e){let t=o[e];t in this||Object.defineProperty(this,t,s(t))}}function s(e){return{get(){return n(this).event[e]},set(t){n(this).event[e]=t},configurable:!0,enumerable:!0}}function l(e,t){n(e).passiveListener=t}a.prototype={get type(){return n(this).event.type},get target(){return n(this).eventTarget},get currentTarget(){return n(this).currentTarget},composedPath(){let e=n(this).currentTarget;return null==e?[]:[e]},get NONE(){return 0},get CAPTURING_PHASE(){return 1},get AT_TARGET(){return 2},get BUBBLING_PHASE(){return 3},get eventPhase(){return n(this).eventPhase},stopPropagation(){let e=n(this);e.stopped=!0,"function"==typeof e.event.stopPropagation&&e.event.stopPropagation()},stopImmediatePropagation(){let e=n(this);e.stopped=!0,e.immediateStopped=!0,"function"==typeof e.event.stopImmediatePropagation&&e.event.stopImmediatePropagation()},get bubbles(){return!!n(this).event.bubbles},get cancelable(){return!!n(this).event.cancelable},preventDefault(){i(n(this))},get defaultPrevented(){return n(this).canceled},get composed(){return!!n(this).event.composed},get timeStamp(){return n(this).timeStamp},get srcElement(){return n(this).eventTarget},get cancelBubble(){return n(this).stopped},set cancelBubble(value){if(!value)return;let e=n(this);e.stopped=!0,"boolean"==typeof e.event.cancelBubble&&(e.event.cancelBubble=!0)},get returnValue(){return!n(this).canceled},set returnValue(value){value||i(n(this))},initEvent(){}},Object.defineProperty(a.prototype,"constructor",{value:a,configurable:!0,writable:!0});let u=new WeakMap;function c(e){return null!==e&&"object"==typeof e}function d(e){let t=u.get(e);if(null==t)throw TypeError("'this' is expected an EventTarget object, but got another value.");return t}function f(e,t){Object.defineProperty(e,`on${t}`,{get(){let e=d(this).get(t);for(;null!=e;){if(3===e.listenerType)return e.listener;e=e.next}return null},set(e){"function"==typeof e||c(e)||(e=null);let r=d(this),o=null,n=r.get(t);for(;null!=n;)3===n.listenerType?null!==o?o.next=n.next:null!==n.next?r.set(t,n.next):r.delete(t):o=n,n=n.next;if(null!==e){let n={listener:e,listenerType:3,passive:!1,once:!1,next:null};null===o?r.set(t,n):o.next=n}},configurable:!0,enumerable:!0})}function h(e){function t(){p.call(this)}t.prototype=Object.create(p.prototype,{constructor:{value:t,configurable:!0,writable:!0}});for(let r=0;r<e.length;++r)f(t.prototype,e[r]);return t}function p(){if(this instanceof p){u.set(this,new Map);return}if(1==arguments.length&&Array.isArray(arguments[0]))return h(arguments[0]);if(arguments.length>0){let e=Array(arguments.length);for(let t=0;t<arguments.length;++t)e[t]=arguments[t];return h(e)}throw TypeError("Cannot call a class as a function")}p.prototype={addEventListener(e,t,r){if(null==t)return;if("function"!=typeof t&&!c(t))throw TypeError("'listener' should be a function or an object.");let o=d(this),n=c(r),i=(n?r.capture:r)?1:2,a={listener:t,listenerType:i,passive:n&&!!r.passive,once:n&&!!r.once,next:null},s=o.get(e);if(void 0===s){o.set(e,a);return}let l=null;for(;null!=s;){if(s.listener===t&&s.listenerType===i)return;l=s,s=s.next}l.next=a},removeEventListener(e,t,r){if(null==t)return;let o=d(this),n=(c(r)?r.capture:r)?1:2,i=null,a=o.get(e);for(;null!=a;){if(a.listener===t&&a.listenerType===n){null!==i?i.next=a.next:null!==a.next?o.set(e,a.next):o.delete(e);return}i=a,a=a.next}},dispatchEvent(e){if(null==e||"string"!=typeof e.type)throw TypeError('"event.type" should be a string.');let t=d(this),r=e.type,i=t.get(r);if(null==i)return!0;let u=new(function e(t){if(null==t||t===Object.prototype)return a;let r=o.get(t);return null==r&&(r=function(e,t){let r=Object.keys(t);if(0===r.length)return e;function o(t,r){e.call(this,t,r)}o.prototype=Object.create(e.prototype,{constructor:{value:o,configurable:!0,writable:!0}});for(let i=0;i<r.length;++i){let a=r[i];if(!(a in e.prototype)){let e="function"==typeof Object.getOwnPropertyDescriptor(t,a).value;Object.defineProperty(o.prototype,a,e?function(e){return{value(){let t=n(this).event;return t[e].apply(t,arguments)},configurable:!0,enumerable:!0}}(a):s(a))}}return o}(e(Object.getPrototypeOf(t)),t),o.set(t,r)),r}(Object.getPrototypeOf(e)))(this,e),c=null;for(;null!=i;){if(i.once?null!==c?c.next=i.next:null!==i.next?t.set(r,i.next):t.delete(r):c=i,l(u,i.passive?i.listener:null),"function"==typeof i.listener)try{i.listener.call(this,u)}catch(e){"undefined"!=typeof console&&"function"==typeof console.error&&console.error(e)}else 3!==i.listenerType&&"function"==typeof i.listener.handleEvent&&i.listener.handleEvent(u);if(n(u).immediateStopped)break;i=i.next}return l(u,null),n(u).eventPhase=0,n(u).currentTarget=null,!u.defaultPrevented}},Object.defineProperty(p.prototype,"constructor",{value:p,configurable:!0,writable:!0}),t.defineEventAttribute=f,t.EventTarget=p,t.default=p,e.exports=p,e.exports.EventTarget=e.exports.default=p,e.exports.defineEventAttribute=f},8113:(e,t,r)=>{"use strict";/*!
 * humanize-ms - index.js
 * Copyright(c) 2014 dead_horse <<EMAIL>>
 * MIT Licensed
 */var o=r(3837),n=r(7553);e.exports=function(e){if("number"==typeof e)return e;var t=n(e);return void 0===t&&console.warn(Error(o.format("humanize-ms(%j) result undefined",e)).stack),t}},7553:e=>{function t(e,t,r,o){return Math.round(e/r)+" "+o+(t>=1.5*r?"s":"")}e.exports=function(e,r){r=r||{};var o,n,i=typeof e;if("string"===i&&e.length>0)return function(e){if(!((e=String(e)).length>100)){var t=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(t){var r=parseFloat(t[1]);switch((t[2]||"ms").toLowerCase()){case"years":case"year":case"yrs":case"yr":case"y":return 315576e5*r;case"weeks":case"week":case"w":return 6048e5*r;case"days":case"day":case"d":return 864e5*r;case"hours":case"hour":case"hrs":case"hr":case"h":return 36e5*r;case"minutes":case"minute":case"mins":case"min":case"m":return 6e4*r;case"seconds":case"second":case"secs":case"sec":case"s":return 1e3*r;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return r;default:return}}}}(e);if("number"===i&&isFinite(e))return r.long?(o=Math.abs(e))>=864e5?t(e,o,864e5,"day"):o>=36e5?t(e,o,36e5,"hour"):o>=6e4?t(e,o,6e4,"minute"):o>=1e3?t(e,o,1e3,"second"):e+" ms":(n=Math.abs(e))>=864e5?Math.round(e/864e5)+"d":n>=36e5?Math.round(e/36e5)+"h":n>=6e4?Math.round(e/6e4)+"m":n>=1e3?Math.round(e/1e3)+"s":e+"ms";throw Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))}},6789:function(e,t){/**
 * @license
 * web-streams-polyfill v3.3.3
 * Copyright 2024 Mattias Buelens, Diwank Singh Tomer and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */(function(e){"use strict";var t,r,o;function n(){}function i(e){return"object"==typeof e&&null!==e||"function"==typeof e}function a(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}let s=Promise,l=Promise.prototype.then,u=Promise.reject.bind(s);function c(e){return new s(e)}function d(e){return c(t=>t(e))}function f(e,t,r){return l.call(e,t,r)}function h(e,t,r){f(f(e,t,r),void 0,n)}function p(e,t){h(e,void 0,t)}function b(e){f(e,void 0,n)}let m=e=>{if("function"==typeof queueMicrotask)m=queueMicrotask;else{let e=d(void 0);m=t=>f(e,t)}return m(e)};function y(e,t,r){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function _(e,t,r){try{return d(y(e,t,r))}catch(e){return u(e)}}class g{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){let t=this._back,r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){let e=this._front,t=e,r=this._cursor,o=r+1,n=e._elements,i=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;(t!==o.length||void 0!==r._next)&&(t!==o.length||(o=(r=r._next)._elements,t=0,0!==o.length));)e(o[t]),++t}peek(){let e=this._front,t=this._cursor;return e._elements[t]}}let v=Symbol("[[AbortSteps]]"),S=Symbol("[[ErrorSteps]]"),w=Symbol("[[CancelSteps]]"),T=Symbol("[[PullSteps]]"),R=Symbol("[[ReleaseSteps]]");function E(e,t){var r;e._ownerReadableStream=t,t._reader=e,"readable"===t._state?q(e):"closed"===t._state?(q(e),O(e)):(r=t._storedError,q(e),A(e,r))}function P(e,t){return t1(e._ownerReadableStream,t)}function k(e){var t,r;let o=e._ownerReadableStream;"readable"===o._state?A(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):(t=e,r=TypeError("Reader was released and can no longer be used to monitor the stream's closedness"),q(t),A(t,r)),o._readableStreamController[R](),o._reader=void 0,e._ownerReadableStream=void 0}function C(e){return TypeError("Cannot "+e+" a stream using a released reader")}function q(e){e._closedPromise=c((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}function A(e,t){void 0!==e._closedPromise_reject&&(b(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function O(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let j=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},x=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function L(e,t){if(void 0!==e&&!("object"==typeof e||"function"==typeof e))throw TypeError(`${t} is not an object.`)}function W(e,t){if("function"!=typeof e)throw TypeError(`${t} is not a function.`)}function B(e,t){if(!("object"==typeof e&&null!==e||"function"==typeof e))throw TypeError(`${t} is not an object.`)}function $(e,t,r){if(void 0===e)throw TypeError(`Parameter ${t} is required in '${r}'.`)}function I(e,t,r){if(void 0===e)throw TypeError(`${t} is required in '${r}'.`)}function z(e){return Number(e)}function F(e,t){var r,o;let n=Number.MAX_SAFE_INTEGER,i=Number(e);if(!j(i=0===(r=i)?0:r))throw TypeError(`${t} is not a finite number`);if((i=0===(o=x(i))?0:o)<0||i>n)throw TypeError(`${t} is outside the accepted range of 0 to ${n}, inclusive`);return j(i)&&0!==i?i:0}function M(e,t){if(!tZ(e))throw TypeError(`${t} is not a ReadableStream.`)}function D(e){return new Q(e)}function N(e,t){e._reader._readRequests.push(t)}function U(e,t,r){let o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function H(e){return e._reader._readRequests.length}function V(e){let t=e._reader;return!!(void 0!==t&&X(t))}class Q{constructor(e){if($(e,1,"ReadableStreamDefaultReader"),M(e,"First parameter"),t0(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");E(this,e),this._readRequests=new g}get closed(){return X(this)?this._closedPromise:u(K("closed"))}cancel(e){return X(this)?void 0===this._ownerReadableStream?u(C("cancel")):P(this,e):u(K("cancel"))}read(){let e,t;if(!X(this))return u(K("read"));if(void 0===this._ownerReadableStream)return u(C("read from"));let r=c((r,o)=>{e=r,t=o});return Y(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!X(this))throw K("releaseLock");if(void 0!==this._ownerReadableStream)k(this),G(this,TypeError("Reader was released"))}}function X(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_readRequests"))&&e instanceof Q}function Y(e,t){let r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[T](t)}function G(e,t){let r=e._readRequests;e._readRequests=new g,r.forEach(e=>{e._errorSteps(t)})}function K(e){return TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(Q.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(Q.prototype.cancel,"cancel"),a(Q.prototype.read,"read"),a(Q.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(Q.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});let J=Object.getPrototypeOf(Object.getPrototypeOf(async function*(){}).prototype);class Z{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){let e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?f(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){let t=()=>this._returnSteps(e);return this._ongoingPromise?f(this._ongoingPromise,t,t):t()}_nextSteps(){let e,t;if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let r=this._reader,o=c((r,o)=>{e=r,t=o});return Y(r,{_chunkSteps:t=>{this._ongoingPromise=void 0,m(()=>e({value:t,done:!1}))},_closeSteps:()=>{this._ongoingPromise=void 0,this._isFinished=!0,k(r),e({value:void 0,done:!0})},_errorSteps:e=>{this._ongoingPromise=void 0,this._isFinished=!0,k(r),t(e)}}),o}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;let t=this._reader;if(!this._preventCancel){let r=P(t,e);return k(t),f(r,()=>({value:e,done:!0}),void 0)}return k(t),d({value:e,done:!0})}}let ee={next(){return et(this)?this._asyncIteratorImpl.next():u(er("next"))},return(e){return et(this)?this._asyncIteratorImpl.return(e):u(er("return"))}};function et(e){if(!i(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof Z}catch(e){return!1}}function er(e){return TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}Object.setPrototypeOf(ee,J);let eo=Number.isNaN||function(e){return e!=e};function en(e){return e.slice()}function ei(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}let ea=e=>(ea="function"==typeof e.transfer?e=>e.transfer():"function"==typeof structuredClone?e=>structuredClone(e,{transfer:[e]}):e=>e)(e),es=e=>(es="boolean"==typeof e.detached?e=>e.detached:e=>0===e.byteLength)(e);function el(e,t,r){if(e.slice)return e.slice(t,r);let o=r-t,n=new ArrayBuffer(o);return ei(n,0,e,t,o),n}function eu(e,t){let r=e[t];if(null!=r){if("function"!=typeof r)throw TypeError(`${String(t)} is not a function`);return r}}let ec=null!==(o=null!==(t=Symbol.asyncIterator)&&void 0!==t?t:null===(r=Symbol.for)||void 0===r?void 0:r.call(Symbol,"Symbol.asyncIterator"))&&void 0!==o?o:"@@asyncIterator";function ed(e){let t=el(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function ef(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function eh(e,t,r){if(!(!("number"!=typeof r||eo(r))&&!(r<0))||r===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function ep(e){e._queue=new g,e._queueTotalSize=0}function eb(e){return e===DataView}class em{constructor(){throw TypeError("Illegal constructor")}get view(){if(!eg(this))throw eU("view");return this._view}respond(e){if(!eg(this))throw eU("respond");if($(e,1,"respond"),e=F(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(es(this._view.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be used as a response");eM(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!eg(this))throw eU("respondWithNewView");if($(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");if(es(e.buffer))throw TypeError("The given view's buffer has been detached and so cannot be used as a response");eD(this._associatedReadableByteStreamController,e)}}Object.defineProperties(em.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),a(em.prototype.respond,"respond"),a(em.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(em.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class ey{constructor(){throw TypeError("Illegal constructor")}get byobRequest(){if(!e_(this))throw eH("byobRequest");return ez(this)}get desiredSize(){if(!e_(this))throw eH("desiredSize");return eF(this)}close(){if(!e_(this))throw eH("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");let e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);eW(this)}enqueue(e){if(!e_(this))throw eH("enqueue");if($(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");let t=this._controlledReadableByteStream._state;if("readable"!==t)throw TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);eB(this,e)}error(e){if(!e_(this))throw eH("error");e$(this,e)}[w](e){eS(this),ep(this);let t=this._cancelAlgorithm(e);return eL(this),t}[T](e){let t=this._controlledReadableByteStream;if(this._queueTotalSize>0){eI(this,e);return}let r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){e._errorSteps(t);return}let o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,minimumFill:1,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}N(t,e),ev(this)}[R](){if(this._pendingPullIntos.length>0){let e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new g,this._pendingPullIntos.push(e)}}}function e_(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream"))&&e instanceof ey}function eg(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController"))&&e instanceof em}function ev(e){if(function(e){let t=e._controlledReadableByteStream;return"readable"===t._state&&!e._closeRequested&&!!e._started&&!!(V(t)&&H(t)>0||eX(t)&&eQ(t)>0||eF(e)>0)}(e)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,h(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ev(e)),null),t=>(e$(e,t),null))}}function eS(e){eA(e),e._pendingPullIntos=new g}function ew(e,t){let r=!1;"closed"===e._state&&(r=!0);let o=eT(t);"default"===t.readerType?U(e,o,r):function(e,t,r){let o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t)}(e,o,r)}function eT(e){let t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function eR(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function eE(e,t,r,o){let n;try{n=el(t,r,r+o)}catch(t){throw e$(e,t),t}eR(e,n,0,o)}function eP(e,t){t.bytesFilled>0&&eE(e,t.buffer,t.byteOffset,t.bytesFilled),ex(e)}function ek(e,t){let r=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),o=t.bytesFilled+r,n=r,i=!1,a=o%t.elementSize,s=o-a;s>=t.minimumFill&&(n=s-t.bytesFilled,i=!0);let l=e._queue;for(;n>0;){let r=l.peek(),o=Math.min(n,r.byteLength),i=t.byteOffset+t.bytesFilled;ei(t.buffer,i,r.buffer,r.byteOffset,o),r.byteLength===o?l.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,eC(e,o,t),n-=o}return i}function eC(e,t,r){r.bytesFilled+=t}function eq(e){0===e._queueTotalSize&&e._closeRequested?(eL(e),t3(e._controlledReadableByteStream)):ev(e)}function eA(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function eO(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;let t=e._pendingPullIntos.peek();ek(e,t)&&(ex(e),ew(e._controlledReadableByteStream,t))}}function ej(e,t){let r=e._pendingPullIntos.peek();eA(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&ex(e);let r=e._controlledReadableByteStream;if(eX(r))for(;eQ(r)>0;)ew(r,ex(e))}(e,r):function(e,t,r){if(eC(e,t,r),"none"===r.readerType){eP(e,r),eO(e);return}if(r.bytesFilled<r.minimumFill)return;ex(e);let o=r.bytesFilled%r.elementSize;if(o>0){let t=r.byteOffset+r.bytesFilled;eE(e,r.buffer,t-o,o)}r.bytesFilled-=o,ew(e._controlledReadableByteStream,r),eO(e)}(e,t,r),ev(e)}function ex(e){return e._pendingPullIntos.shift()}function eL(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function eW(e){let t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){if(e._queueTotalSize>0){e._closeRequested=!0;return}if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();if(t.bytesFilled%t.elementSize!=0){let t=TypeError("Insufficient bytes to fill elements in the given buffer");throw e$(e,t),t}}eL(e),t3(t)}}function eB(e,t){let r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;let{buffer:o,byteOffset:n,byteLength:i}=t;if(es(o))throw TypeError("chunk's buffer is detached and so cannot be enqueued");let a=ea(o);if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();if(es(t.buffer))throw TypeError("The BYOB request's buffer has been detached and so cannot be filled with an enqueued chunk");eA(e),t.buffer=ea(t.buffer),"none"===t.readerType&&eP(e,t)}V(r)?(function(e){let t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;eI(e,t._readRequests.shift())}}(e),0===H(r)?eR(e,a,n,i):(e._pendingPullIntos.length>0&&ex(e),U(r,new Uint8Array(a,n,i),!1))):eX(r)?(eR(e,a,n,i),eO(e)):eR(e,a,n,i),ev(e)}function e$(e,t){let r=e._controlledReadableByteStream;"readable"===r._state&&(eS(e),ep(e),eL(e),t8(r,t))}function eI(e,t){let r=e._queue.shift();e._queueTotalSize-=r.byteLength,eq(e);let o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function ez(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(em.prototype);o._associatedReadableByteStreamController=e,o._view=r,e._byobRequest=o}return e._byobRequest}function eF(e){let t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function eM(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw RangeError("bytesWritten out of range")}r.buffer=ea(r.buffer),ej(e,t)}function eD(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw RangeError("The region specified by view is larger than byobRequest");let o=t.byteLength;r.buffer=ea(t.buffer),ej(e,o)}function eN(e,t,r,o,n,i,a){t._controlledReadableByteStream=e,t._pullAgain=!1,t._pulling=!1,t._byobRequest=null,t._queue=t._queueTotalSize=void 0,ep(t),t._closeRequested=!1,t._started=!1,t._strategyHWM=i,t._pullAlgorithm=o,t._cancelAlgorithm=n,t._autoAllocateChunkSize=a,t._pendingPullIntos=new g,e._readableStreamController=t,h(d(r()),()=>(t._started=!0,ev(t),null),e=>(e$(t,e),null))}function eU(e){return TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function eH(e){return TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function eV(e,t){e._reader._readIntoRequests.push(t)}function eQ(e){return e._reader._readIntoRequests.length}function eX(e){let t=e._reader;return!!(void 0!==t&&eG(t))}Object.defineProperties(ey.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),a(ey.prototype.close,"close"),a(ey.prototype.enqueue,"enqueue"),a(ey.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ey.prototype,Symbol.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class eY{constructor(e){if($(e,1,"ReadableStreamBYOBReader"),M(e,"First parameter"),t0(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!e_(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");E(this,e),this._readIntoRequests=new g}get closed(){return eG(this)?this._closedPromise:u(eZ("closed"))}cancel(e){return eG(this)?void 0===this._ownerReadableStream?u(C("cancel")):P(this,e):u(eZ("cancel"))}read(e,t={}){let r,o,n;if(!eG(this))return u(eZ("read"));if(!ArrayBuffer.isView(e))return u(TypeError("view must be an array buffer view"));if(0===e.byteLength)return u(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return u(TypeError("view's buffer must have non-zero byteLength"));if(es(e.buffer))return u(TypeError("view's buffer has been detached"));try{var i,a;i="options",L(t,i),r={min:F(null!==(a=null==t?void 0:t.min)&&void 0!==a?a:1,`${i} has member 'min' that`)}}catch(e){return u(e)}let s=r.min;if(0===s)return u(TypeError("options.min must be greater than 0"));if(eb(e.constructor)){if(s>e.byteLength)return u(RangeError("options.min must be less than or equal to view's byteLength"))}else if(s>e.length)return u(RangeError("options.min must be less than or equal to view's length"));if(void 0===this._ownerReadableStream)return u(C("read from"));let l=c((e,t)=>{o=e,n=t});return eK(this,e,s,{_chunkSteps:e=>o({value:e,done:!1}),_closeSteps:e=>o({value:e,done:!0}),_errorSteps:e=>n(e)}),l}releaseLock(){if(!eG(this))throw eZ("releaseLock");if(void 0!==this._ownerReadableStream)k(this),eJ(this,TypeError("Reader was released"))}}function eG(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_readIntoRequests"))&&e instanceof eY}function eK(e,t,r,o){let n=e._ownerReadableStream;n._disturbed=!0,"errored"===n._state?o._errorSteps(n._storedError):function(e,t,r,o){let n;let i=e._controlledReadableByteStream,a=t.constructor,s=eb(a)?1:a.BYTES_PER_ELEMENT,{byteOffset:l,byteLength:u}=t;try{n=ea(t.buffer)}catch(e){o._errorSteps(e);return}let c={buffer:n,bufferByteLength:n.byteLength,byteOffset:l,byteLength:u,bytesFilled:0,minimumFill:r*s,elementSize:s,viewConstructor:a,readerType:"byob"};if(e._pendingPullIntos.length>0){e._pendingPullIntos.push(c),eV(i,o);return}if("closed"===i._state){let e=new a(c.buffer,c.byteOffset,0);o._closeSteps(e);return}if(e._queueTotalSize>0){if(ek(e,c)){let t=eT(c);eq(e),o._chunkSteps(t);return}if(e._closeRequested){let t=TypeError("Insufficient bytes to fill elements in the given buffer");e$(e,t),o._errorSteps(t);return}}e._pendingPullIntos.push(c),eV(i,o),ev(e)}(n._readableStreamController,t,r,o)}function eJ(e,t){let r=e._readIntoRequests;e._readIntoRequests=new g,r.forEach(e=>{e._errorSteps(t)})}function eZ(e){return TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function e0(e,t){let{highWaterMark:r}=e;if(void 0===r)return t;if(eo(r)||r<0)throw RangeError("Invalid highWaterMark");return r}function e1(e){let{size:t}=e;return t||(()=>1)}function e3(e,t){L(e,t);let r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:z(r),size:void 0===o?void 0:(W(o,`${t} has member 'size' that`),e=>z(o(e)))}}function e8(e,t){if(!e5(e))throw TypeError(`${t} is not a WritableStream.`)}Object.defineProperties(eY.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(eY.prototype.cancel,"cancel"),a(eY.prototype.read,"read"),a(eY.prototype.releaseLock,"releaseLock"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(eY.prototype,Symbol.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});let e2="function"==typeof AbortController;class e6{constructor(e={},t={}){void 0===e?e=null:B(e,"First parameter");let r=e3(t,"Second parameter"),o=function(e,t){L(e,t);let r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,i=null==e?void 0:e.type,a=null==e?void 0:e.write;return{abort:void 0===r?void 0:(W(r,`${t} has member 'abort' that`),t=>_(r,e,[t])),close:void 0===o?void 0:(W(o,`${t} has member 'close' that`),()=>_(o,e,[])),start:void 0===n?void 0:(W(n,`${t} has member 'start' that`),t=>y(n,e,[t])),write:void 0===a?void 0:(W(a,`${t} has member 'write' that`),(t,r)=>_(a,e,[t,r])),type:i}}(e,"First parameter");if(e4(this),void 0!==o.type)throw RangeError("Invalid type is specified");let n=e1(r);(function(e,t,r,o){let n,i;let a=Object.create(tp.prototype);n=void 0!==t.start?()=>t.start(a):()=>void 0,i=void 0!==t.write?e=>t.write(e,a):()=>d(void 0),tm(e,a,n,i,void 0!==t.close?()=>t.close():()=>d(void 0),void 0!==t.abort?e=>t.abort(e):()=>d(void 0),r,o)})(this,o,e0(r,1),n)}get locked(){if(!e5(this))throw tw("locked");return e7(this)}abort(e){return e5(this)?e7(this)?u(TypeError("Cannot abort a stream that already has a writer")):e9(this,e):u(tw("abort"))}close(){return e5(this)?e7(this)?u(TypeError("Cannot close a stream that already has a writer")):tn(this)?u(TypeError("Cannot close an already-closing stream")):te(this):u(tw("close"))}getWriter(){if(!e5(this))throw tw("getWriter");return new ts(this)}}function e4(e){e._state="writable",e._storedError=void 0,e._writer=void 0,e._writableStreamController=void 0,e._writeRequests=new g,e._inFlightWriteRequest=void 0,e._closeRequest=void 0,e._inFlightCloseRequest=void 0,e._pendingAbortRequest=void 0,e._backpressure=!1}function e5(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_writableStreamController"))&&e instanceof e6}function e7(e){return void 0!==e._writer}function e9(e,t){var r;if("closed"===e._state||"errored"===e._state)return d(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);let o=e._state;if("closed"===o||"errored"===o)return d(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);let i=c((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}});return e._pendingAbortRequest._promise=i,n||tr(e,t),i}function te(e){var t;let r=e._state;if("closed"===r||"errored"===r)return u(TypeError(`The stream (in ${r} state) is not in the writable state and cannot be closed`));let o=c((t,r)=>{e._closeRequest={_resolve:t,_reject:r}}),n=e._writer;return void 0!==n&&e._backpressure&&"writable"===r&&tj(n),eh(t=e._writableStreamController,th,0),tg(t),o}function tt(e,t){if("writable"===e._state){tr(e,t);return}to(e)}function tr(e,t){let r=e._writableStreamController;e._state="erroring",e._storedError=t;let o=e._writer;void 0!==o&&tc(o,t),!(void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest)&&r._started&&to(e)}function to(e){e._state="errored",e._writableStreamController[S]();let t=e._storedError;if(e._writeRequests.forEach(e=>{e._reject(t)}),e._writeRequests=new g,void 0===e._pendingAbortRequest){ti(e);return}let r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring){r._reject(t),ti(e);return}h(e._writableStreamController[v](r._reason),()=>(r._resolve(),ti(e),null),t=>(r._reject(t),ti(e),null))}function tn(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function ti(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;void 0!==t&&tk(t,e._storedError)}function ta(e,t){let r=e._writer;void 0!==r&&t!==e._backpressure&&(t?tq(r):tj(r)),e._backpressure=t}Object.defineProperties(e6.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),a(e6.prototype.abort,"abort"),a(e6.prototype.close,"close"),a(e6.prototype.getWriter,"getWriter"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(e6.prototype,Symbol.toStringTag,{value:"WritableStream",configurable:!0});class ts{constructor(e){if($(e,1,"WritableStreamDefaultWriter"),e8(e,"First parameter"),e7(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;let t=e._state;if("writable"===t)!tn(e)&&e._backpressure?tq(this):(tq(this),tj(this)),tP(this);else if("erroring"===t)tA(this,e._storedError),tP(this);else if("closed"===t)tq(this),tj(this),tP(this),tC(this);else{let t=e._storedError;tA(this,t),tP(this),tk(this,t)}}get closed(){return tl(this)?this._closedPromise:u(tR("closed"))}get desiredSize(){if(!tl(this))throw tR("desiredSize");if(void 0===this._ownerWritableStream)throw tE("desiredSize");return function(e){let t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:t_(t._writableStreamController)}(this)}get ready(){return tl(this)?this._readyPromise:u(tR("ready"))}abort(e){return tl(this)?void 0===this._ownerWritableStream?u(tE("abort")):e9(this._ownerWritableStream,e):u(tR("abort"))}close(){if(!tl(this))return u(tR("close"));let e=this._ownerWritableStream;return void 0===e?u(tE("close")):tn(e)?u(TypeError("Cannot close an already-closing stream")):tu(this)}releaseLock(){if(!tl(this))throw tR("releaseLock");void 0!==this._ownerWritableStream&&td(this)}write(e){return tl(this)?void 0===this._ownerWritableStream?u(tE("write to")):tf(this,e):u(tR("write"))}}function tl(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream"))&&e instanceof ts}function tu(e){return te(e._ownerWritableStream)}function tc(e,t){"pending"===e._readyPromiseState?tO(e,t):tA(e,t)}function td(e){var t,r;let o=e._ownerWritableStream,n=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");tc(e,n),"pending"===e._closedPromiseState?tk(e,n):(t=e,r=n,tP(t),tk(t,r)),o._writer=void 0,e._ownerWritableStream=void 0}function tf(e,t){let r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return tv(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return u(tE("write to"));let i=r._state;if("errored"===i)return u(r._storedError);if(tn(r)||"closed"===i)return u(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return u(r._storedError);let a=c((e,t)=>{r._writeRequests.push({_resolve:e,_reject:t})});return function(e,t,r){try{eh(e,t,r)}catch(t){tv(e,t);return}let o=e._controlledWritableStream;tn(o)||"writable"!==o._state||ta(o,0>=t_(e)),tg(e)}(o,t,n),a}Object.defineProperties(ts.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),a(ts.prototype.abort,"abort"),a(ts.prototype.close,"close"),a(ts.prototype.releaseLock,"releaseLock"),a(ts.prototype.write,"write"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(ts.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});let th={};class tp{constructor(){throw TypeError("Illegal constructor")}get abortReason(){if(!tb(this))throw tT("abortReason");return this._abortReason}get signal(){if(!tb(this))throw tT("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e){if(!tb(this))throw tT("error");"writable"===this._controlledWritableStream._state&&tS(this,e)}[v](e){let t=this._abortAlgorithm(e);return ty(this),t}[S](){ep(this)}}function tb(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream"))&&e instanceof tp}function tm(e,t,r,o,n,i,a,s){t._controlledWritableStream=e,e._writableStreamController=t,t._queue=void 0,t._queueTotalSize=void 0,ep(t),t._abortReason=void 0,t._abortController=function(){if(e2)return new AbortController}(),t._started=!1,t._strategySizeAlgorithm=s,t._strategyHWM=a,t._writeAlgorithm=o,t._closeAlgorithm=n,t._abortAlgorithm=i,ta(e,0>=t_(t)),h(d(r()),()=>(t._started=!0,tg(t),null),r=>(t._started=!0,tt(e,r),null))}function ty(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function t_(e){return e._strategyHWM-e._queueTotalSize}function tg(e){let t=e._controlledWritableStream;if(!e._started||void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state){to(t);return}if(0===e._queue.length)return;let r=e._queue.peek().value;r===th?function(e){let t=e._controlledWritableStream;t._inFlightCloseRequest=t._closeRequest,t._closeRequest=void 0,ef(e);let r=e._closeAlgorithm();ty(e),h(r,()=>((function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let t=e._writer;void 0!==t&&tC(t)})(t),null),e=>(t._inFlightCloseRequest._reject(e),t._inFlightCloseRequest=void 0,void 0!==t._pendingAbortRequest&&(t._pendingAbortRequest._reject(e),t._pendingAbortRequest=void 0),tt(t,e),null))}(e):function(e,t){let r=e._controlledWritableStream;r._inFlightWriteRequest=r._writeRequests.shift(),h(e._writeAlgorithm(t),()=>{r._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0;let t=r._state;return ef(e),tn(r)||"writable"!==t||ta(r,0>=t_(e)),tg(e),null},t=>("writable"===r._state&&ty(e),r._inFlightWriteRequest._reject(t),r._inFlightWriteRequest=void 0,tt(r,t),null))}(e,r)}function tv(e,t){"writable"===e._controlledWritableStream._state&&tS(e,t)}function tS(e,t){let r=e._controlledWritableStream;ty(e),tr(r,t)}function tw(e){return TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function tT(e){return TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function tR(e){return TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function tE(e){return TypeError("Cannot "+e+" a stream using a released writer")}function tP(e){e._closedPromise=c((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}function tk(e,t){void 0!==e._closedPromise_reject&&(b(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function tC(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function tq(e){e._readyPromise=c((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function tA(e,t){tq(e),tO(e,t)}function tO(e,t){void 0!==e._readyPromise_reject&&(b(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function tj(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(tp.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tp.prototype,Symbol.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});let tx="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof self?self:"undefined"!=typeof global?global:void 0,tL=function(){let e=null==tx?void 0:tx.DOMException;return!function(e){if(!("function"==typeof e||"object"==typeof e)||"DOMException"!==e.name)return!1;try{return new e,!0}catch(e){return!1}}(e)?void 0:e}()||function(){let e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return a(e,"DOMException"),e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}();function tW(e,t,r,o,i,a){let s=D(e),l=new ts(t);e._disturbed=!0;let m=!1,y=d(void 0);return c((_,g)=>{var v,S;let w;if(void 0!==a){if(w=()=>{let r=void 0!==a.reason?a.reason:new tL("Aborted","AbortError"),n=[];o||n.push(()=>"writable"===t._state?e9(t,r):d(void 0)),i||n.push(()=>"readable"===e._state?t1(e,r):d(void 0)),E(()=>Promise.all(n.map(e=>e())),!0,r)},a.aborted){w();return}a.addEventListener("abort",w)}if(R(e,s._closedPromise,e=>(o?P(!0,e):E(()=>e9(t,e),!0,e),null)),R(t,l._closedPromise,t=>(i?P(!0,t):E(()=>t1(e,t),!0,t),null)),v=s._closedPromise,S=()=>(r?P():E(()=>(function(e){let t=e._ownerWritableStream,r=t._state;return tn(t)||"closed"===r?d(void 0):"errored"===r?u(t._storedError):tu(e)})(l)),null),"closed"===e._state?S():h(v,S),tn(t)||"closed"===t._state){let t=TypeError("the destination writable stream closed before all data could be piped to it");i?P(!0,t):E(()=>t1(e,t),!0,t)}function T(){let e=y;return f(y,()=>e!==y?T():void 0)}function R(e,t,r){"errored"===e._state?r(e._storedError):p(t,r)}function E(e,r,o){!m&&((m=!0,"writable"!==t._state||tn(t))?n():h(T(),n));function n(){return h(e(),()=>C(r,o),e=>C(!0,e)),null}}function P(e,r){!m&&((m=!0,"writable"!==t._state||tn(t))?C(e,r):h(T(),()=>C(e,r)))}function C(e,t){return td(l),k(s),void 0!==a&&a.removeEventListener("abort",w),e?g(t):_(void 0),null}b(c((e,t)=>{!function r(o){o?e():f(m?d(!0):f(l._readyPromise,()=>c((e,t)=>{Y(s,{_chunkSteps:t=>{y=f(tf(l,t),void 0,n),e(!1)},_closeSteps:()=>e(!0),_errorSteps:t})})),r,t)}(!1)}))})}class tB{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!t$(this))throw tQ("desiredSize");return tU(this)}close(){if(!t$(this))throw tQ("close");if(!tH(this))throw TypeError("The stream is not in a state that permits close");tM(this)}enqueue(e){if(!t$(this))throw tQ("enqueue");if(!tH(this))throw TypeError("The stream is not in a state that permits enqueue");return tD(this,e)}error(e){if(!t$(this))throw tQ("error");tN(this,e)}[w](e){ep(this);let t=this._cancelAlgorithm(e);return tF(this),t}[T](e){let t=this._controlledReadableStream;if(this._queue.length>0){let r=ef(this);this._closeRequested&&0===this._queue.length?(tF(this),t3(t)):tI(this),e._chunkSteps(r)}else N(t,e),tI(this)}[R](){}}function t$(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream"))&&e instanceof tB}function tI(e){if(tz(e)){if(e._pulling){e._pullAgain=!0;return}e._pulling=!0,h(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,tI(e)),null),t=>(tN(e,t),null))}}function tz(e){let t=e._controlledReadableStream;return!!tH(e)&&!!e._started&&!!(t0(t)&&H(t)>0||tU(e)>0)}function tF(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function tM(e){if(!tH(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(tF(e),t3(t))}function tD(e,t){if(!tH(e))return;let r=e._controlledReadableStream;if(t0(r)&&H(r)>0)U(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw tN(e,t),t}try{eh(e,t,r)}catch(t){throw tN(e,t),t}}tI(e)}function tN(e,t){let r=e._controlledReadableStream;"readable"===r._state&&(ep(e),tF(e),t8(r,t))}function tU(e){let t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function tH(e){let t=e._controlledReadableStream._state;return!e._closeRequested&&"readable"===t}function tV(e,t,r,o,n,i,a){t._controlledReadableStream=e,t._queue=void 0,t._queueTotalSize=void 0,ep(t),t._started=!1,t._closeRequested=!1,t._pullAgain=!1,t._pulling=!1,t._strategySizeAlgorithm=a,t._strategyHWM=i,t._pullAlgorithm=o,t._cancelAlgorithm=n,e._readableStreamController=t,h(d(r()),()=>(t._started=!0,tI(t),null),e=>(tN(t,e),null))}function tQ(e){return TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function tX(e,t){L(e,t);let r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!o,preventClose:!!n,signal:i}}Object.defineProperties(tB.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),a(tB.prototype.close,"close"),a(tB.prototype.enqueue,"enqueue"),a(tB.prototype.error,"error"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tB.prototype,Symbol.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class tY{constructor(e={},t={}){void 0===e?e=null:B(e,"First parameter");let r=e3(t,"Second parameter"),o=function(e,t){L(e,t);let r=null==e?void 0:e.autoAllocateChunkSize,o=null==e?void 0:e.cancel,n=null==e?void 0:e.pull,i=null==e?void 0:e.start,a=null==e?void 0:e.type;return{autoAllocateChunkSize:void 0===r?void 0:F(r,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===o?void 0:(W(o,`${t} has member 'cancel' that`),t=>_(o,e,[t])),pull:void 0===n?void 0:(W(n,`${t} has member 'pull' that`),t=>_(n,e,[t])),start:void 0===i?void 0:(W(i,`${t} has member 'start' that`),t=>y(i,e,[t])),type:void 0===a?void 0:function(e,t){if("bytes"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}(a,`${t} has member 'type' that`)}}(e,"First parameter");if(tJ(this),"bytes"===o.type){if(void 0!==r.size)throw RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){let o,n,i;let a=Object.create(ey.prototype);o=void 0!==t.start?()=>t.start(a):()=>void 0,n=void 0!==t.pull?()=>t.pull(a):()=>d(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0);let s=t.autoAllocateChunkSize;if(0===s)throw TypeError("autoAllocateChunkSize must be greater than 0");eN(e,a,o,n,i,r,s)}(this,o,e0(r,0))}else{let e=e1(r);!function(e,t,r,o){let n,i;let a=Object.create(tB.prototype);n=void 0!==t.start?()=>t.start(a):()=>void 0,i=void 0!==t.pull?()=>t.pull(a):()=>d(void 0),tV(e,a,n,i,void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0),r,o)}(this,o,e0(r,1),e)}}get locked(){if(!tZ(this))throw t2("locked");return t0(this)}cancel(e){return tZ(this)?t0(this)?u(TypeError("Cannot cancel a stream that already has a reader")):t1(this,e):u(t2("cancel"))}getReader(e){if(!tZ(this))throw t2("getReader");return void 0===function(e,t){L(e,t);let r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:function(e,t){if("byob"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?D(this):new eY(this)}pipeThrough(e,t={}){if(!tZ(this))throw t2("pipeThrough");$(e,1,"pipeThrough");let r=function(e,t){L(e,t);let r=null==e?void 0:e.readable;I(r,"readable","ReadableWritablePair"),M(r,`${t} has member 'readable' that`);let o=null==e?void 0:e.writable;return I(o,"writable","ReadableWritablePair"),e8(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=tX(t,"Second parameter");if(t0(this))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(e7(r.writable))throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return b(tW(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){let r;if(!tZ(this))return u(t2("pipeTo"));if(void 0===e)return u("Parameter 1 is required in 'pipeTo'.");if(!e5(e))return u(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{r=tX(t,"Second parameter")}catch(e){return u(e)}return t0(this)?u(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):e7(e)?u(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):tW(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!tZ(this))throw t2("tee");let e=e_(this._readableStreamController)?function(e){let t,r,o,n,i,a=D(e),s=!1,l=!1,u=!1,f=!1,h=!1,b=c(e=>{i=e});function y(e){p(e._closedPromise,t=>(e!==a||(e$(o._readableStreamController,t),e$(n._readableStreamController,t),f&&h||i(void 0)),null))}function _(){eG(a)&&(k(a),y(a=D(e))),Y(a,{_chunkSteps:t=>{m(()=>{l=!1,u=!1;let r=t;if(!f&&!h)try{r=ed(t)}catch(t){e$(o._readableStreamController,t),e$(n._readableStreamController,t),i(t1(e,t));return}f||eB(o._readableStreamController,t),h||eB(n._readableStreamController,r),s=!1,l?v():u&&S()})},_closeSteps:()=>{s=!1,f||eW(o._readableStreamController),h||eW(n._readableStreamController),o._readableStreamController._pendingPullIntos.length>0&&eM(o._readableStreamController,0),n._readableStreamController._pendingPullIntos.length>0&&eM(n._readableStreamController,0),f&&h||i(void 0)},_errorSteps:()=>{s=!1}})}function g(t,r){X(a)&&(k(a),y(a=new eY(e)));let c=r?n:o,d=r?o:n;eK(a,t,1,{_chunkSteps:t=>{m(()=>{l=!1,u=!1;let o=r?h:f;if(r?f:h)o||eD(c._readableStreamController,t);else{let r;try{r=ed(t)}catch(t){e$(c._readableStreamController,t),e$(d._readableStreamController,t),i(t1(e,t));return}o||eD(c._readableStreamController,t),eB(d._readableStreamController,r)}s=!1,l?v():u&&S()})},_closeSteps:e=>{s=!1;let t=r?h:f,o=r?f:h;t||eW(c._readableStreamController),o||eW(d._readableStreamController),void 0!==e&&(t||eD(c._readableStreamController,e),!o&&d._readableStreamController._pendingPullIntos.length>0&&eM(d._readableStreamController,0)),t&&o||i(void 0)},_errorSteps:()=>{s=!1}})}function v(){if(s)return l=!0,d(void 0);s=!0;let e=ez(o._readableStreamController);return null===e?_():g(e._view,!1),d(void 0)}function S(){if(s)return u=!0,d(void 0);s=!0;let e=ez(n._readableStreamController);return null===e?_():g(e._view,!0),d(void 0)}function w(){}return o=tK(w,v,function(o){if(f=!0,t=o,h){let o=t1(e,en([t,r]));i(o)}return b}),n=tK(w,S,function(o){if(h=!0,r=o,f){let o=t1(e,en([t,r]));i(o)}return b}),y(a),[o,n]}(this):function(e,t){let r,o,n,i,a;let s=D(e),l=!1,u=!1,f=!1,h=!1,b=c(e=>{a=e});function y(){return l?u=!0:(l=!0,Y(s,{_chunkSteps:e=>{m(()=>{u=!1,f||tD(n._readableStreamController,e),h||tD(i._readableStreamController,e),l=!1,u&&y()})},_closeSteps:()=>{l=!1,f||tM(n._readableStreamController),h||tM(i._readableStreamController),f&&h||a(void 0)},_errorSteps:()=>{l=!1}})),d(void 0)}function _(){}return n=tG(_,y,function(t){if(f=!0,r=t,h){let t=t1(e,en([r,o]));a(t)}return b}),i=tG(_,y,function(t){if(h=!0,o=t,f){let t=t1(e,en([r,o]));a(t)}return b}),p(s._closedPromise,e=>(tN(n._readableStreamController,e),tN(i._readableStreamController,e),f&&h||a(void 0),null)),[n,i]}(this);return en(e)}values(e){if(!tZ(this))throw t2("values");return function(e,t){let r=new Z(D(e),t),o=Object.create(ee);return o._asyncIteratorImpl=r,o}(this,(L(e,"First parameter"),{preventCancel:!!(null==e?void 0:e.preventCancel)}).preventCancel)}[ec](e){return this.values(e)}static from(e){var t;let r;return i(e)&&void 0!==e.getReader?(t=e.getReader(),r=tG(n,function(){let e;try{e=t.read()}catch(e){return u(e)}return f(e,e=>{if(!i(e))throw TypeError("The promise returned by the reader.read() method must fulfill with an object");if(e.done)tM(r._readableStreamController);else{let t=e.value;tD(r._readableStreamController,t)}},void 0)},function(e){try{return d(t.cancel(e))}catch(e){return u(e)}},0)):function(e){let t;let r=function e(t,r="sync",o){if(void 0===o){if("async"===r){if(void 0===(o=eu(t,ec))){let r=eu(t,Symbol.iterator);return function(e){let t={[Symbol.iterator]:()=>e.iterator},r=async function*(){return yield*t}(),o=r.next;return{iterator:r,nextMethod:o,done:!1}}(e(t,"sync",r))}}else o=eu(t,Symbol.iterator)}if(void 0===o)throw TypeError("The object is not iterable");let n=y(o,t,[]);if(!i(n))throw TypeError("The iterator method must return an object");let a=n.next;return{iterator:n,nextMethod:a,done:!1}}(e,"async");return t=tG(n,function(){let e;try{e=function(e){let t=y(e.nextMethod,e.iterator,[]);if(!i(t))throw TypeError("The iterator.next() method must return an object");return t}(r)}catch(e){return u(e)}return f(d(e),e=>{if(!i(e))throw TypeError("The promise returned by the iterator.next() method must fulfill with an object");if(e.done)tM(t._readableStreamController);else{let r=e.value;tD(t._readableStreamController,r)}},void 0)},function(e){let t,o;let n=r.iterator;try{t=eu(n,"return")}catch(e){return u(e)}if(void 0===t)return d(void 0);try{o=y(t,n,[e])}catch(e){return u(e)}return f(d(o),e=>{if(!i(e))throw TypeError("The promise returned by the iterator.return() method must fulfill with an object")},void 0)},0)}(e)}}function tG(e,t,r,o=1,n=()=>1){let i=Object.create(tY.prototype);return tJ(i),tV(i,Object.create(tB.prototype),e,t,r,o,n),i}function tK(e,t,r){let o=Object.create(tY.prototype);return tJ(o),eN(o,Object.create(ey.prototype),e,t,r,0,void 0),o}function tJ(e){e._state="readable",e._reader=void 0,e._storedError=void 0,e._disturbed=!1}function tZ(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_readableStreamController"))&&e instanceof tY}function t0(e){return void 0!==e._reader}function t1(e,t){if(e._disturbed=!0,"closed"===e._state)return d(void 0);if("errored"===e._state)return u(e._storedError);t3(e);let r=e._reader;if(void 0!==r&&eG(r)){let e=r._readIntoRequests;r._readIntoRequests=new g,e.forEach(e=>{e._closeSteps(void 0)})}return f(e._readableStreamController[w](t),n,void 0)}function t3(e){e._state="closed";let t=e._reader;if(void 0!==t&&(O(t),X(t))){let e=t._readRequests;t._readRequests=new g,e.forEach(e=>{e._closeSteps()})}}function t8(e,t){e._state="errored",e._storedError=t;let r=e._reader;void 0!==r&&(A(r,t),X(r)?G(r,t):eJ(r,t))}function t2(e){return TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function t6(e,t){L(e,t);let r=null==e?void 0:e.highWaterMark;return I(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:z(r)}}Object.defineProperties(tY,{from:{enumerable:!0}}),Object.defineProperties(tY.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),a(tY.from,"from"),a(tY.prototype.cancel,"cancel"),a(tY.prototype.getReader,"getReader"),a(tY.prototype.pipeThrough,"pipeThrough"),a(tY.prototype.pipeTo,"pipeTo"),a(tY.prototype.tee,"tee"),a(tY.prototype.values,"values"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(tY.prototype,Symbol.toStringTag,{value:"ReadableStream",configurable:!0}),Object.defineProperty(tY.prototype,ec,{value:tY.prototype.values,writable:!0,configurable:!0});let t4=e=>e.byteLength;a(t4,"size");class t5{constructor(e){$(e,1,"ByteLengthQueuingStrategy"),e=t6(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!t9(this))throw t7("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!t9(this))throw t7("size");return t4}}function t7(e){return TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function t9(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark"))&&e instanceof t5}Object.defineProperties(t5.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(t5.prototype,Symbol.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});let re=()=>1;a(re,"size");class rt{constructor(e){$(e,1,"CountQueuingStrategy"),e=t6(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!ro(this))throw rr("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!ro(this))throw rr("size");return re}}function rr(e){return TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function ro(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark"))&&e instanceof rt}Object.defineProperties(rt.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rt.prototype,Symbol.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class rn{constructor(e={},t={},r={}){let o;void 0===e&&(e=null);let n=e3(t,"Second parameter"),i=e3(r,"Third parameter"),a=function(e,t){L(e,t);let r=null==e?void 0:e.cancel,o=null==e?void 0:e.flush,n=null==e?void 0:e.readableType,i=null==e?void 0:e.start,a=null==e?void 0:e.transform,s=null==e?void 0:e.writableType;return{cancel:void 0===r?void 0:(W(r,`${t} has member 'cancel' that`),t=>_(r,e,[t])),flush:void 0===o?void 0:(W(o,`${t} has member 'flush' that`),t=>_(o,e,[t])),readableType:n,start:void 0===i?void 0:(W(i,`${t} has member 'start' that`),t=>y(i,e,[t])),transform:void 0===a?void 0:(W(a,`${t} has member 'transform' that`),(t,r)=>_(a,e,[t,r])),writableType:s}}(e,"First parameter");if(void 0!==a.readableType)throw RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw RangeError("Invalid writableType specified");let s=e0(i,0),l=e1(i),p=e0(n,1),b=e1(n);(function(e,t,r,o,n,i){function a(){return t}e._writable=function(e,t,r,o,n=1,i=()=>1){let a=Object.create(e6.prototype);return e4(a),tm(a,Object.create(tp.prototype),e,t,r,o,n,i),a}(a,function(t){return function(e,t){let r=e._transformStreamController;return e._backpressure?f(e._backpressureChangePromise,()=>{let o=e._writable;if("erroring"===o._state)throw o._storedError;return rp(r,t)},void 0):rp(r,t)}(e,t)},function(){return function(e){let t=e._transformStreamController;if(void 0!==t._finishPromise)return t._finishPromise;let r=e._readable;t._finishPromise=c((e,r)=>{t._finishPromise_resolve=e,t._finishPromise_reject=r});let o=t._flushAlgorithm();return rf(t),h(o,()=>("errored"===r._state?ry(t,r._storedError):(tM(r._readableStreamController),rm(t)),null),e=>(tN(r._readableStreamController,e),ry(t,e),null)),t._finishPromise}(e)},function(t){return function(e,t){let r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;let o=e._readable;r._finishPromise=c((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t});let n=r._cancelAlgorithm(t);return rf(r),h(n,()=>("errored"===o._state?ry(r,o._storedError):(tN(o._readableStreamController,t),rm(r)),null),e=>(tN(o._readableStreamController,e),ry(r,e),null)),r._finishPromise}(e,t)},r,o),e._readable=tG(a,function(){return ru(e,!1),e._backpressureChangePromise},function(t){return function(e,t){let r=e._transformStreamController;if(void 0!==r._finishPromise)return r._finishPromise;let o=e._writable;r._finishPromise=c((e,t)=>{r._finishPromise_resolve=e,r._finishPromise_reject=t});let n=r._cancelAlgorithm(t);return rf(r),h(n,()=>("errored"===o._state?ry(r,o._storedError):(tv(o._writableStreamController,t),rl(e),rm(r)),null),t=>(tv(o._writableStreamController,t),rl(e),ry(r,t),null)),r._finishPromise}(e,t)},n,i),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,ru(e,!0),e._transformStreamController=void 0})(this,c(e=>{o=e}),p,b,s,l),function(e,t){let r,o,n;let i=Object.create(rc.prototype);r=void 0!==t.transform?e=>t.transform(e,i):e=>{try{return rh(i,e),d(void 0)}catch(e){return u(e)}},o=void 0!==t.flush?()=>t.flush(i):()=>d(void 0),n=void 0!==t.cancel?e=>t.cancel(e):()=>d(void 0),i._controlledTransformStream=e,e._transformStreamController=i,i._transformAlgorithm=r,i._flushAlgorithm=o,i._cancelAlgorithm=n,i._finishPromise=void 0,i._finishPromise_resolve=void 0,i._finishPromise_reject=void 0}(this,a),void 0!==a.start?o(a.start(this._transformStreamController)):o(void 0)}get readable(){if(!ri(this))throw r_("readable");return this._readable}get writable(){if(!ri(this))throw r_("writable");return this._writable}}function ri(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_transformStreamController"))&&e instanceof rn}function ra(e,t){tN(e._readable._readableStreamController,t),rs(e,t)}function rs(e,t){rf(e._transformStreamController),tv(e._writable._writableStreamController,t),rl(e)}function rl(e){e._backpressure&&ru(e,!1)}function ru(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=c(t=>{e._backpressureChangePromise_resolve=t}),e._backpressure=t}Object.defineProperties(rn.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rn.prototype,Symbol.toStringTag,{value:"TransformStream",configurable:!0});class rc{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!rd(this))throw rb("desiredSize");return tU(this._controlledTransformStream._readable._readableStreamController)}enqueue(e){if(!rd(this))throw rb("enqueue");rh(this,e)}error(e){if(!rd(this))throw rb("error");ra(this._controlledTransformStream,e)}terminate(){if(!rd(this))throw rb("terminate");(function(e){let t=e._controlledTransformStream;tM(t._readable._readableStreamController),rs(t,TypeError("TransformStream terminated"))})(this)}}function rd(e){return!!(i(e)&&Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream"))&&e instanceof rc}function rf(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0,e._cancelAlgorithm=void 0}function rh(e,t){let r=e._controlledTransformStream,o=r._readable._readableStreamController;if(!tH(o))throw TypeError("Readable side is not in a state that permits enqueue");try{tD(o,t)}catch(e){throw rs(r,e),r._readable._storedError}!tz(o)!==r._backpressure&&ru(r,!0)}function rp(e,t){return f(e._transformAlgorithm(t),void 0,t=>{throw ra(e._controlledTransformStream,t),t})}function rb(e){return TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function rm(e){void 0!==e._finishPromise_resolve&&(e._finishPromise_resolve(),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function ry(e,t){void 0!==e._finishPromise_reject&&(b(e._finishPromise),e._finishPromise_reject(t),e._finishPromise_resolve=void 0,e._finishPromise_reject=void 0)}function r_(e){return TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}Object.defineProperties(rc.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),a(rc.prototype.enqueue,"enqueue"),a(rc.prototype.error,"error"),a(rc.prototype.terminate,"terminate"),"symbol"==typeof Symbol.toStringTag&&Object.defineProperty(rc.prototype,Symbol.toStringTag,{value:"TransformStreamDefaultController",configurable:!0}),e.ByteLengthQueuingStrategy=t5,e.CountQueuingStrategy=rt,e.ReadableByteStreamController=ey,e.ReadableStream=tY,e.ReadableStreamBYOBReader=eY,e.ReadableStreamBYOBRequest=em,e.ReadableStreamDefaultController=tB,e.ReadableStreamDefaultReader=Q,e.TransformStream=rn,e.TransformStreamDefaultController=rc,e.WritableStream=e6,e.WritableStreamDefaultController=tp,e.WritableStreamDefaultWriter=ts})(t)},3687:(e,t,r)=>{"use strict";r.d(t,{t:()=>rn});/**
 * @license
 * web-streams-polyfill v4.0.0-beta.3
 * Copyright 2021 Mattias Buelens, Diwank Singh Tomer and other contributors.
 * This code is released under the MIT license.
 * SPDX-License-Identifier: MIT
 */let o="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?Symbol:e=>`Symbol(${e})`;function n(){}function i(e){return"object"==typeof e&&null!==e||"function"==typeof e}function a(e,t){try{Object.defineProperty(e,"name",{value:t,configurable:!0})}catch(e){}}let s=Promise,l=Promise.prototype.then,u=Promise.resolve.bind(s),c=Promise.reject.bind(s);function d(e){return new s(e)}function f(e,t,r){return l.call(e,t,r)}function h(e,t,r){f(f(e,t,r),void 0,n)}function p(e){f(e,void 0,n)}let b=e=>{if("function"==typeof queueMicrotask)b=queueMicrotask;else{let e=u(void 0);b=t=>f(e,t)}return b(e)};function m(e,t,r){if("function"!=typeof e)throw TypeError("Argument is not a function");return Function.prototype.apply.call(e,t,r)}function y(e,t,r){try{var o;return o=m(e,t,r),u(o)}catch(e){return c(e)}}class _{constructor(){this._cursor=0,this._size=0,this._front={_elements:[],_next:void 0},this._back=this._front,this._cursor=0,this._size=0}get length(){return this._size}push(e){let t=this._back,r=t;16383===t._elements.length&&(r={_elements:[],_next:void 0}),t._elements.push(e),r!==t&&(this._back=r,t._next=r),++this._size}shift(){let e=this._front,t=e,r=this._cursor,o=r+1,n=e._elements,i=n[r];return 16384===o&&(t=e._next,o=0),--this._size,this._cursor=o,e!==t&&(this._front=t),n[r]=void 0,i}forEach(e){let t=this._cursor,r=this._front,o=r._elements;for(;!(t===o.length&&void 0===r._next||t===o.length&&(o=(r=r._next)._elements,t=0,0===o.length));)e(o[t]),++t}peek(){let e=this._front,t=this._cursor;return e._elements[t]}}let g=o("[[AbortSteps]]"),v=o("[[ErrorSteps]]"),S=o("[[CancelSteps]]"),w=o("[[PullSteps]]"),T=o("[[ReleaseSteps]]");function R(e,t){var r;e._ownerReadableStream=t,t._reader=e,"readable"===t._state?C(e):"closed"===t._state?(C(e),A(e)):(r=t._storedError,C(e),q(e,r))}function E(e,t){return tP(e._ownerReadableStream,t)}function P(e){var t;let r=e._ownerReadableStream;"readable"===r._state?q(e,TypeError("Reader was released and can no longer be used to monitor the stream's closedness")):(t=TypeError("Reader was released and can no longer be used to monitor the stream's closedness"),C(e),q(e,t)),r._readableStreamController[T](),r._reader=void 0,e._ownerReadableStream=void 0}function k(e){return TypeError("Cannot "+e+" a stream using a released reader")}function C(e){e._closedPromise=d((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r})}function q(e,t){void 0!==e._closedPromise_reject&&(p(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}function A(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0)}let O=Number.isFinite||function(e){return"number"==typeof e&&isFinite(e)},j=Math.trunc||function(e){return e<0?Math.ceil(e):Math.floor(e)};function x(e,t){var r;if(void 0!==e&&"object"!=typeof(r=e)&&"function"!=typeof r)throw TypeError(`${t} is not an object.`)}function L(e,t){if("function"!=typeof e)throw TypeError(`${t} is not a function.`)}function W(e,t){if(!("object"==typeof e&&null!==e||"function"==typeof e))throw TypeError(`${t} is not an object.`)}function B(e,t,r){if(void 0===e)throw TypeError(`Parameter ${t} is required in '${r}'.`)}function $(e,t,r){if(void 0===e)throw TypeError(`${t} is required in '${r}'.`)}function I(e){return Number(e)}function z(e,t){var r,o;let n=Number.MAX_SAFE_INTEGER,i=Number(e);if(!O(i=0===(r=i)?0:r))throw TypeError(`${t} is not a finite number`);if((i=0===(o=j(i))?0:o)<0||i>n)throw TypeError(`${t} is outside the accepted range of 0 to ${n}, inclusive`);return O(i)&&0!==i?i:0}function F(e){if(!i(e)||"function"!=typeof e.getReader)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function M(e){if(!i(e)||"function"!=typeof e.getWriter)return!1;try{return"boolean"==typeof e.locked}catch(e){return!1}}function D(e,t){if(!tR(e))throw TypeError(`${t} is not a ReadableStream.`)}function N(e,t){e._reader._readRequests.push(t)}function U(e,t,r){let o=e._reader._readRequests.shift();r?o._closeSteps():o._chunkSteps(t)}function H(e){return e._reader._readRequests.length}function V(e){let t=e._reader;return void 0!==t&&!!X(t)}class Q{constructor(e){if(B(e,1,"ReadableStreamDefaultReader"),D(e,"First parameter"),tE(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");R(this,e),this._readRequests=new _}get closed(){return X(this)?this._closedPromise:c(G("closed"))}cancel(e){return X(this)?void 0===this._ownerReadableStream?c(k("cancel")):E(this,e):c(G("cancel"))}read(){let e,t;if(!X(this))return c(G("read"));if(void 0===this._ownerReadableStream)return c(k("read from"));let r=d((r,o)=>{e=r,t=o});return function(e,t){let r=e._ownerReadableStream;r._disturbed=!0,"closed"===r._state?t._closeSteps():"errored"===r._state?t._errorSteps(r._storedError):r._readableStreamController[w](t)}(this,{_chunkSteps:t=>e({value:t,done:!1}),_closeSteps:()=>e({value:void 0,done:!0}),_errorSteps:e=>t(e)}),r}releaseLock(){if(!X(this))throw G("releaseLock");void 0!==this._ownerReadableStream&&(P(this),Y(this,TypeError("Reader was released")))}}function X(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readRequests")&&e instanceof Q}function Y(e,t){let r=e._readRequests;e._readRequests=new _,r.forEach(e=>{e._errorSteps(t)})}function G(e){return TypeError(`ReadableStreamDefaultReader.prototype.${e} can only be used on a ReadableStreamDefaultReader`)}Object.defineProperties(Q.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(Q.prototype.cancel,"cancel"),a(Q.prototype.read,"read"),a(Q.prototype.releaseLock,"releaseLock"),"symbol"==typeof o.toStringTag&&Object.defineProperty(Q.prototype,o.toStringTag,{value:"ReadableStreamDefaultReader",configurable:!0});class K{constructor(e,t){this._ongoingPromise=void 0,this._isFinished=!1,this._reader=e,this._preventCancel=t}next(){let e=()=>this._nextSteps();return this._ongoingPromise=this._ongoingPromise?f(this._ongoingPromise,e,e):e(),this._ongoingPromise}return(e){let t=()=>this._returnSteps(e);return this._ongoingPromise?f(this._ongoingPromise,t,t):t()}_nextSteps(){if(this._isFinished)return Promise.resolve({value:void 0,done:!0});let e=this._reader;return void 0===e?c(k("iterate")):f(e.read(),e=>{var t;return this._ongoingPromise=void 0,e.done&&(this._isFinished=!0,null===(t=this._reader)||void 0===t||t.releaseLock(),this._reader=void 0),e},e=>{var t;throw this._ongoingPromise=void 0,this._isFinished=!0,null===(t=this._reader)||void 0===t||t.releaseLock(),this._reader=void 0,e})}_returnSteps(e){if(this._isFinished)return Promise.resolve({value:e,done:!0});this._isFinished=!0;let t=this._reader;if(void 0===t)return c(k("finish iterating"));if(this._reader=void 0,!this._preventCancel){let r=t.cancel(e);return t.releaseLock(),f(r,()=>({value:e,done:!0}),void 0)}return t.releaseLock(),u({value:e,done:!0})}}let J={next(){return Z(this)?this._asyncIteratorImpl.next():c(ee("next"))},return(e){return Z(this)?this._asyncIteratorImpl.return(e):c(ee("return"))}};function Z(e){if(!i(e)||!Object.prototype.hasOwnProperty.call(e,"_asyncIteratorImpl"))return!1;try{return e._asyncIteratorImpl instanceof K}catch(e){return!1}}function ee(e){return TypeError(`ReadableStreamAsyncIterator.${e} can only be used on a ReadableSteamAsyncIterator`)}"symbol"==typeof o.asyncIterator&&Object.defineProperty(J,o.asyncIterator,{value(){return this},writable:!0,configurable:!0});let et=Number.isNaN||function(e){return e!=e};function er(e,t,r,o,n){new Uint8Array(e).set(new Uint8Array(r,o,n),t)}function eo(e){let t=function(e,t,r){if(e.slice)return e.slice(t,r);let o=r-t,n=new ArrayBuffer(o);return er(n,0,e,t,o),n}(e.buffer,e.byteOffset,e.byteOffset+e.byteLength);return new Uint8Array(t)}function en(e){let t=e._queue.shift();return e._queueTotalSize-=t.size,e._queueTotalSize<0&&(e._queueTotalSize=0),t.value}function ei(e,t,r){if("number"!=typeof r||et(r)||r<0||r===1/0)throw RangeError("Size must be a finite, non-NaN, non-negative number.");e._queue.push({value:t,size:r}),e._queueTotalSize+=r}function ea(e){e._queue=new _,e._queueTotalSize=0}class es{constructor(){throw TypeError("Illegal constructor")}get view(){if(!ec(this))throw eq("view");return this._view}respond(e){if(!ec(this))throw eq("respond");if(B(e,1,"respond"),e=z(e,"First parameter"),void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");this._view.buffer,function(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t)throw TypeError("bytesWritten must be 0 when calling respond() on a closed stream")}else{if(0===t)throw TypeError("bytesWritten must be greater than 0 when calling respond() on a readable stream");if(r.bytesFilled+t>r.byteLength)throw RangeError("bytesWritten out of range")}r.buffer=r.buffer,eT(e,t)}(this._associatedReadableByteStreamController,e)}respondWithNewView(e){if(!ec(this))throw eq("respondWithNewView");if(B(e,1,"respondWithNewView"),!ArrayBuffer.isView(e))throw TypeError("You can only respond with array buffer views");if(void 0===this._associatedReadableByteStreamController)throw TypeError("This BYOB request has been invalidated");e.buffer,function(e,t){let r=e._pendingPullIntos.peek();if("closed"===e._controlledReadableByteStream._state){if(0!==t.byteLength)throw TypeError("The view's length must be 0 when calling respondWithNewView() on a closed stream")}else if(0===t.byteLength)throw TypeError("The view's length must be greater than 0 when calling respondWithNewView() on a readable stream");if(r.byteOffset+r.bytesFilled!==t.byteOffset)throw RangeError("The region specified by view does not match byobRequest");if(r.bufferByteLength!==t.buffer.byteLength)throw RangeError("The buffer of view has different capacity than byobRequest");if(r.bytesFilled+t.byteLength>r.byteLength)throw RangeError("The region specified by view is larger than byobRequest");let o=t.byteLength;r.buffer=t.buffer,eT(e,o)}(this._associatedReadableByteStreamController,e)}}Object.defineProperties(es.prototype,{respond:{enumerable:!0},respondWithNewView:{enumerable:!0},view:{enumerable:!0}}),a(es.prototype.respond,"respond"),a(es.prototype.respondWithNewView,"respondWithNewView"),"symbol"==typeof o.toStringTag&&Object.defineProperty(es.prototype,o.toStringTag,{value:"ReadableStreamBYOBRequest",configurable:!0});class el{constructor(){throw TypeError("Illegal constructor")}get byobRequest(){if(!eu(this))throw eA("byobRequest");return function(e){if(null===e._byobRequest&&e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek(),r=new Uint8Array(t.buffer,t.byteOffset+t.bytesFilled,t.byteLength-t.bytesFilled),o=Object.create(es.prototype);o._associatedReadableByteStreamController=e,o._view=r,e._byobRequest=o}return e._byobRequest}(this)}get desiredSize(){if(!eu(this))throw eA("desiredSize");return eC(this)}close(){if(!eu(this))throw eA("close");if(this._closeRequested)throw TypeError("The stream has already been closed; do not close it again!");let e=this._controlledReadableByteStream._state;if("readable"!==e)throw TypeError(`The stream (in ${e} state) is not in the readable state and cannot be closed`);!function(e){let t=e._controlledReadableByteStream;if(!e._closeRequested&&"readable"===t._state){if(e._queueTotalSize>0)return void(e._closeRequested=!0);if(e._pendingPullIntos.length>0&&e._pendingPullIntos.peek().bytesFilled>0){let t=TypeError("Insufficient bytes to fill elements in the given buffer");throw eP(e,t),t}eE(e),tk(t)}}(this)}enqueue(e){if(!eu(this))throw eA("enqueue");if(B(e,1,"enqueue"),!ArrayBuffer.isView(e))throw TypeError("chunk must be an array buffer view");if(0===e.byteLength)throw TypeError("chunk must have non-zero byteLength");if(0===e.buffer.byteLength)throw TypeError("chunk's buffer must have non-zero byteLength");if(this._closeRequested)throw TypeError("stream is closed or draining");let t=this._controlledReadableByteStream._state;if("readable"!==t)throw TypeError(`The stream (in ${t} state) is not in the readable state and cannot be enqueued to`);!function(e,t){let r=e._controlledReadableByteStream;if(e._closeRequested||"readable"!==r._state)return;let o=t.buffer,n=t.byteOffset,i=t.byteLength;if(e._pendingPullIntos.length>0){let t=e._pendingPullIntos.peek();t.buffer,eS(e),t.buffer=t.buffer,"none"===t.readerType&&ey(e,t)}V(r)?(function(e){let t=e._controlledReadableByteStream._reader;for(;t._readRequests.length>0;){if(0===e._queueTotalSize)return;ek(e,t._readRequests.shift())}}(e),0===H(r))?eb(e,o,n,i):(e._pendingPullIntos.length>0&&eR(e),U(r,new Uint8Array(o,n,i),!1)):ex(r)?(eb(e,o,n,i),ew(e)):eb(e,o,n,i),ed(e)}(this,e)}error(e){if(!eu(this))throw eA("error");eP(this,e)}[S](e){ef(this),ea(this);let t=this._cancelAlgorithm(e);return eE(this),t}[w](e){let t=this._controlledReadableByteStream;if(this._queueTotalSize>0)return void ek(this,e);let r=this._autoAllocateChunkSize;if(void 0!==r){let t;try{t=new ArrayBuffer(r)}catch(t){return void e._errorSteps(t)}let o={buffer:t,bufferByteLength:r,byteOffset:0,byteLength:r,bytesFilled:0,elementSize:1,viewConstructor:Uint8Array,readerType:"default"};this._pendingPullIntos.push(o)}N(t,e),ed(this)}[T](){if(this._pendingPullIntos.length>0){let e=this._pendingPullIntos.peek();e.readerType="none",this._pendingPullIntos=new _,this._pendingPullIntos.push(e)}}}function eu(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableByteStream")&&e instanceof el}function ec(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_associatedReadableByteStreamController")&&e instanceof es}function ed(e){if(function(e){let t=e._controlledReadableByteStream;return"readable"===t._state&&!e._closeRequested&&!!e._started&&!!(V(t)&&H(t)>0||ex(t)&&ej(t)>0||eC(e)>0)}(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,h(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,ed(e)),null),t=>(eP(e,t),null))}}function ef(e){eS(e),e._pendingPullIntos=new _}function eh(e,t){let r=!1;"closed"===e._state&&(r=!0);let o=ep(t);"default"===t.readerType?U(e,o,r):function(e,t,r){let o=e._reader._readIntoRequests.shift();r?o._closeSteps(t):o._chunkSteps(t)}(e,o,r)}function ep(e){let t=e.bytesFilled,r=e.elementSize;return new e.viewConstructor(e.buffer,e.byteOffset,t/r)}function eb(e,t,r,o){e._queue.push({buffer:t,byteOffset:r,byteLength:o}),e._queueTotalSize+=o}function em(e,t,r,o){let n;try{n=t.slice(r,r+o)}catch(t){throw eP(e,t),t}eb(e,n,0,o)}function ey(e,t){t.bytesFilled>0&&em(e,t.buffer,t.byteOffset,t.bytesFilled),eR(e)}function e_(e,t){let r=t.elementSize,o=t.bytesFilled-t.bytesFilled%r,n=Math.min(e._queueTotalSize,t.byteLength-t.bytesFilled),i=t.bytesFilled+n,a=i-i%r,s=n,l=!1;a>o&&(s=a-t.bytesFilled,l=!0);let u=e._queue;for(;s>0;){let r=u.peek(),o=Math.min(s,r.byteLength),n=t.byteOffset+t.bytesFilled;er(t.buffer,n,r.buffer,r.byteOffset,o),r.byteLength===o?u.shift():(r.byteOffset+=o,r.byteLength-=o),e._queueTotalSize-=o,eg(e,o,t),s-=o}return l}function eg(e,t,r){r.bytesFilled+=t}function ev(e){0===e._queueTotalSize&&e._closeRequested?(eE(e),tk(e._controlledReadableByteStream)):ed(e)}function eS(e){null!==e._byobRequest&&(e._byobRequest._associatedReadableByteStreamController=void 0,e._byobRequest._view=null,e._byobRequest=null)}function ew(e){for(;e._pendingPullIntos.length>0;){if(0===e._queueTotalSize)return;let t=e._pendingPullIntos.peek();e_(e,t)&&(eR(e),eh(e._controlledReadableByteStream,t))}}function eT(e,t){let r=e._pendingPullIntos.peek();eS(e),"closed"===e._controlledReadableByteStream._state?function(e,t){"none"===t.readerType&&eR(e);let r=e._controlledReadableByteStream;if(ex(r))for(;ej(r)>0;)eh(r,eR(e))}(e,r):function(e,t,r){if(eg(0,t,r),"none"===r.readerType)return ey(e,r),void ew(e);if(r.bytesFilled<r.elementSize)return;eR(e);let o=r.bytesFilled%r.elementSize;if(o>0){let t=r.byteOffset+r.bytesFilled;em(e,r.buffer,t-o,o)}r.bytesFilled-=o,eh(e._controlledReadableByteStream,r),ew(e)}(e,t,r),ed(e)}function eR(e){return e._pendingPullIntos.shift()}function eE(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0}function eP(e,t){let r=e._controlledReadableByteStream;"readable"===r._state&&(ef(e),ea(e),eE(e),tC(r,t))}function ek(e,t){let r=e._queue.shift();e._queueTotalSize-=r.byteLength,ev(e);let o=new Uint8Array(r.buffer,r.byteOffset,r.byteLength);t._chunkSteps(o)}function eC(e){let t=e._controlledReadableByteStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function eq(e){return TypeError(`ReadableStreamBYOBRequest.prototype.${e} can only be used on a ReadableStreamBYOBRequest`)}function eA(e){return TypeError(`ReadableByteStreamController.prototype.${e} can only be used on a ReadableByteStreamController`)}function eO(e,t){e._reader._readIntoRequests.push(t)}function ej(e){return e._reader._readIntoRequests.length}function ex(e){let t=e._reader;return void 0!==t&&!!eW(t)}Object.defineProperties(el.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},byobRequest:{enumerable:!0},desiredSize:{enumerable:!0}}),a(el.prototype.close,"close"),a(el.prototype.enqueue,"enqueue"),a(el.prototype.error,"error"),"symbol"==typeof o.toStringTag&&Object.defineProperty(el.prototype,o.toStringTag,{value:"ReadableByteStreamController",configurable:!0});class eL{constructor(e){if(B(e,1,"ReadableStreamBYOBReader"),D(e,"First parameter"),tE(e))throw TypeError("This stream has already been locked for exclusive reading by another reader");if(!eu(e._readableStreamController))throw TypeError("Cannot construct a ReadableStreamBYOBReader for a stream not constructed with a byte source");R(this,e),this._readIntoRequests=new _}get closed(){return eW(this)?this._closedPromise:c(e$("closed"))}cancel(e){return eW(this)?void 0===this._ownerReadableStream?c(k("cancel")):E(this,e):c(e$("cancel"))}read(e){let t,r;if(!eW(this))return c(e$("read"));if(!ArrayBuffer.isView(e))return c(TypeError("view must be an array buffer view"));if(0===e.byteLength)return c(TypeError("view must have non-zero byteLength"));if(0===e.buffer.byteLength)return c(TypeError("view's buffer must have non-zero byteLength"));if(e.buffer,void 0===this._ownerReadableStream)return c(k("read from"));let o=d((e,o)=>{t=e,r=o});return function(e,t,r){let o=e._ownerReadableStream;o._disturbed=!0,"errored"===o._state?r._errorSteps(o._storedError):function(e,t,r){let o=e._controlledReadableByteStream,n=1;t.constructor!==DataView&&(n=t.constructor.BYTES_PER_ELEMENT);let i=t.constructor,a=t.buffer,s={buffer:a,bufferByteLength:a.byteLength,byteOffset:t.byteOffset,byteLength:t.byteLength,bytesFilled:0,elementSize:n,viewConstructor:i,readerType:"byob"};if(e._pendingPullIntos.length>0)return e._pendingPullIntos.push(s),void eO(o,r);if("closed"!==o._state){if(e._queueTotalSize>0){if(e_(e,s)){let t=ep(s);return ev(e),void r._chunkSteps(t)}if(e._closeRequested){let t=TypeError("Insufficient bytes to fill elements in the given buffer");return eP(e,t),void r._errorSteps(t)}}e._pendingPullIntos.push(s),eO(o,r),ed(e)}else{let e=new i(s.buffer,s.byteOffset,0);r._closeSteps(e)}}(o._readableStreamController,t,r)}(this,e,{_chunkSteps:e=>t({value:e,done:!1}),_closeSteps:e=>t({value:e,done:!0}),_errorSteps:e=>r(e)}),o}releaseLock(){if(!eW(this))throw e$("releaseLock");void 0!==this._ownerReadableStream&&(P(this),eB(this,TypeError("Reader was released")))}}function eW(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readIntoRequests")&&e instanceof eL}function eB(e,t){let r=e._readIntoRequests;e._readIntoRequests=new _,r.forEach(e=>{e._errorSteps(t)})}function e$(e){return TypeError(`ReadableStreamBYOBReader.prototype.${e} can only be used on a ReadableStreamBYOBReader`)}function eI(e,t){let{highWaterMark:r}=e;if(void 0===r)return t;if(et(r)||r<0)throw RangeError("Invalid highWaterMark");return r}function ez(e){let{size:t}=e;return t||(()=>1)}function eF(e,t){x(e,t);let r=null==e?void 0:e.highWaterMark,o=null==e?void 0:e.size;return{highWaterMark:void 0===r?void 0:I(r),size:void 0===o?void 0:(L(o,`${t} has member 'size' that`),e=>I(o(e)))}}Object.defineProperties(eL.prototype,{cancel:{enumerable:!0},read:{enumerable:!0},releaseLock:{enumerable:!0},closed:{enumerable:!0}}),a(eL.prototype.cancel,"cancel"),a(eL.prototype.read,"read"),a(eL.prototype.releaseLock,"releaseLock"),"symbol"==typeof o.toStringTag&&Object.defineProperty(eL.prototype,o.toStringTag,{value:"ReadableStreamBYOBReader",configurable:!0});let eM="function"==typeof AbortController;class eD{constructor(e={},t={}){void 0===e?e=null:W(e,"First parameter");let r=eF(t,"Second parameter"),o=function(e,t){x(e,t);let r=null==e?void 0:e.abort,o=null==e?void 0:e.close,n=null==e?void 0:e.start,i=null==e?void 0:e.type,a=null==e?void 0:e.write;return{abort:void 0===r?void 0:(L(r,`${t} has member 'abort' that`),t=>y(r,e,[t])),close:void 0===o?void 0:(L(o,`${t} has member 'close' that`),()=>y(o,e,[])),start:void 0===n?void 0:(L(n,`${t} has member 'start' that`),t=>m(n,e,[t])),write:void 0===a?void 0:(L(a,`${t} has member 'write' that`),(t,r)=>y(a,e,[t,r])),type:i}}(e,"First parameter");if(this._state="writable",this._storedError=void 0,this._writer=void 0,this._writableStreamController=void 0,this._writeRequests=new _,this._inFlightWriteRequest=void 0,this._closeRequest=void 0,this._inFlightCloseRequest=void 0,this._pendingAbortRequest=void 0,this._backpressure=!1,void 0!==o.type)throw RangeError("Invalid type is specified");let n=ez(r);!function(e,t,r,o){let n,i,a,s;let l=Object.create(e8.prototype);n=void 0!==t.start?()=>t.start(l):()=>{},i=void 0!==t.write?e=>t.write(e,l):()=>u(void 0),a=void 0!==t.close?()=>t.close():()=>u(void 0),s=void 0!==t.abort?e=>t.abort(e):()=>u(void 0),l._controlledWritableStream=e,e._writableStreamController=l,l._queue=void 0,l._queueTotalSize=void 0,ea(l),l._abortReason=void 0,l._abortController=function(){if(eM)return new AbortController}(),l._started=!1,l._strategySizeAlgorithm=o,l._strategyHWM=r,l._writeAlgorithm=i,l._closeAlgorithm=a,l._abortAlgorithm=s,eJ(e,0>=e4(l)),h(u(n()),()=>(l._started=!0,e5(l),null),t=>(l._started=!0,eQ(e,t),null))}(this,o,eI(r,1),n)}get locked(){if(!eN(this))throw te("locked");return eU(this)}abort(e){return eN(this)?eU(this)?c(TypeError("Cannot abort a stream that already has a writer")):eH(this,e):c(te("abort"))}close(){return eN(this)?eU(this)?c(TypeError("Cannot close a stream that already has a writer")):eG(this)?c(TypeError("Cannot close an already-closing stream")):eV(this):c(te("close"))}getWriter(){if(!eN(this))throw te("getWriter");return new eZ(this)}}function eN(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_writableStreamController")&&e instanceof eD}function eU(e){return void 0!==e._writer}function eH(e,t){var r;if("closed"===e._state||"errored"===e._state)return u(void 0);e._writableStreamController._abortReason=t,null===(r=e._writableStreamController._abortController)||void 0===r||r.abort(t);let o=e._state;if("closed"===o||"errored"===o)return u(void 0);if(void 0!==e._pendingAbortRequest)return e._pendingAbortRequest._promise;let n=!1;"erroring"===o&&(n=!0,t=void 0);let i=d((r,o)=>{e._pendingAbortRequest={_promise:void 0,_resolve:r,_reject:o,_reason:t,_wasAlreadyErroring:n}});return e._pendingAbortRequest._promise=i,n||eX(e,t),i}function eV(e){var t;let r=e._state;if("closed"===r||"errored"===r)return c(TypeError(`The stream (in ${r} state) is not in the writable state and cannot be closed`));let o=d((t,r)=>{e._closeRequest={_resolve:t,_reject:r}}),n=e._writer;return void 0!==n&&e._backpressure&&"writable"===r&&tc(n),ei(t=e._writableStreamController,e3,0),e5(t),o}function eQ(e,t){"writable"!==e._state?eY(e):eX(e,t)}function eX(e,t){let r=e._writableStreamController;e._state="erroring",e._storedError=t;let o=e._writer;void 0!==o&&e1(o,t),!(void 0!==e._inFlightWriteRequest||void 0!==e._inFlightCloseRequest)&&r._started&&eY(e)}function eY(e){e._state="errored",e._writableStreamController[v]();let t=e._storedError;if(e._writeRequests.forEach(e=>{e._reject(t)}),e._writeRequests=new _,void 0===e._pendingAbortRequest)return void eK(e);let r=e._pendingAbortRequest;if(e._pendingAbortRequest=void 0,r._wasAlreadyErroring)return r._reject(t),void eK(e);h(e._writableStreamController[g](r._reason),()=>(r._resolve(),eK(e),null),t=>(r._reject(t),eK(e),null))}function eG(e){return void 0!==e._closeRequest||void 0!==e._inFlightCloseRequest}function eK(e){void 0!==e._closeRequest&&(e._closeRequest._reject(e._storedError),e._closeRequest=void 0);let t=e._writer;void 0!==t&&ti(t,e._storedError)}function eJ(e,t){let r=e._writer;void 0!==r&&t!==e._backpressure&&(t?ts(r):tc(r)),e._backpressure=t}Object.defineProperties(eD.prototype,{abort:{enumerable:!0},close:{enumerable:!0},getWriter:{enumerable:!0},locked:{enumerable:!0}}),a(eD.prototype.abort,"abort"),a(eD.prototype.close,"close"),a(eD.prototype.getWriter,"getWriter"),"symbol"==typeof o.toStringTag&&Object.defineProperty(eD.prototype,o.toStringTag,{value:"WritableStream",configurable:!0});class eZ{constructor(e){if(B(e,1,"WritableStreamDefaultWriter"),function(e,t){if(!eN(e))throw TypeError(`${t} is not a WritableStream.`)}(e,"First parameter"),eU(e))throw TypeError("This stream has already been locked for exclusive writing by another writer");this._ownerWritableStream=e,e._writer=this;let t=e._state;if("writable"===t)!eG(e)&&e._backpressure?ts(this):function(e){ts(e),tc(e)}(this),tn(this);else if("erroring"===t)tl(this,e._storedError),tn(this);else if("closed"===t)(function(e){ts(e),tc(e)})(this),tn(this),ta(this);else{let t=e._storedError;tl(this,t),function(e,t){tn(e),ti(e,t)}(this,t)}}get closed(){return e0(this)?this._closedPromise:c(tr("closed"))}get desiredSize(){if(!e0(this))throw tr("desiredSize");if(void 0===this._ownerWritableStream)throw to("desiredSize");return function(e){let t=e._ownerWritableStream,r=t._state;return"errored"===r||"erroring"===r?null:"closed"===r?0:e4(t._writableStreamController)}(this)}get ready(){return e0(this)?this._readyPromise:c(tr("ready"))}abort(e){return e0(this)?void 0===this._ownerWritableStream?c(to("abort")):eH(this._ownerWritableStream,e):c(tr("abort"))}close(){if(!e0(this))return c(tr("close"));let e=this._ownerWritableStream;return void 0===e?c(to("close")):eG(e)?c(TypeError("Cannot close an already-closing stream")):eV(this._ownerWritableStream)}releaseLock(){if(!e0(this))throw tr("releaseLock");void 0!==this._ownerWritableStream&&function(e){let t=e._ownerWritableStream,r=TypeError("Writer was released and can no longer be used to monitor the stream's closedness");e1(e,r),"pending"===e._closedPromiseState||tn(e),ti(e,r),t._writer=void 0,e._ownerWritableStream=void 0}(this)}write(e){return e0(this)?void 0===this._ownerWritableStream?c(to("write to")):function(e,t){let r=e._ownerWritableStream,o=r._writableStreamController,n=function(e,t){try{return e._strategySizeAlgorithm(t)}catch(t){return e7(e,t),1}}(o,t);if(r!==e._ownerWritableStream)return c(to("write to"));let i=r._state;if("errored"===i)return c(r._storedError);if(eG(r)||"closed"===i)return c(TypeError("The stream is closing or closed and cannot be written to"));if("erroring"===i)return c(r._storedError);let a=d((e,t)=>{r._writeRequests.push({_resolve:e,_reject:t})});return function(e,t,r){try{ei(e,t,r)}catch(t){return void e7(e,t)}let o=e._controlledWritableStream;eG(o)||"writable"!==o._state||eJ(o,0>=e4(e)),e5(e)}(o,t,n),a}(this,e):c(tr("write"))}}function e0(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_ownerWritableStream")&&e instanceof eZ}function e1(e,t){"pending"===e._readyPromiseState?tu(e,t):tl(e,t)}Object.defineProperties(eZ.prototype,{abort:{enumerable:!0},close:{enumerable:!0},releaseLock:{enumerable:!0},write:{enumerable:!0},closed:{enumerable:!0},desiredSize:{enumerable:!0},ready:{enumerable:!0}}),a(eZ.prototype.abort,"abort"),a(eZ.prototype.close,"close"),a(eZ.prototype.releaseLock,"releaseLock"),a(eZ.prototype.write,"write"),"symbol"==typeof o.toStringTag&&Object.defineProperty(eZ.prototype,o.toStringTag,{value:"WritableStreamDefaultWriter",configurable:!0});let e3={};class e8{constructor(){throw TypeError("Illegal constructor")}get abortReason(){if(!e2(this))throw tt("abortReason");return this._abortReason}get signal(){if(!e2(this))throw tt("signal");if(void 0===this._abortController)throw TypeError("WritableStreamDefaultController.prototype.signal is not supported");return this._abortController.signal}error(e){if(!e2(this))throw tt("error");"writable"===this._controlledWritableStream._state&&e9(this,e)}[g](e){let t=this._abortAlgorithm(e);return e6(this),t}[v](){ea(this)}}function e2(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledWritableStream")&&e instanceof e8}function e6(e){e._writeAlgorithm=void 0,e._closeAlgorithm=void 0,e._abortAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function e4(e){return e._strategyHWM-e._queueTotalSize}function e5(e){let t=e._controlledWritableStream;if(!e._started||void 0!==t._inFlightWriteRequest)return;if("erroring"===t._state)return void eY(t);if(0===e._queue.length)return;let r=e._queue.peek().value;r===e3?function(e){let t=e._controlledWritableStream;t._inFlightCloseRequest=t._closeRequest,t._closeRequest=void 0,en(e);let r=e._closeAlgorithm();e6(e),h(r,()=>((function(e){e._inFlightCloseRequest._resolve(void 0),e._inFlightCloseRequest=void 0,"erroring"===e._state&&(e._storedError=void 0,void 0!==e._pendingAbortRequest&&(e._pendingAbortRequest._resolve(),e._pendingAbortRequest=void 0)),e._state="closed";let t=e._writer;void 0!==t&&ta(t)})(t),null),e=>(t._inFlightCloseRequest._reject(e),t._inFlightCloseRequest=void 0,void 0!==t._pendingAbortRequest&&(t._pendingAbortRequest._reject(e),t._pendingAbortRequest=void 0),eQ(t,e),null))}(e):function(e,t){let r=e._controlledWritableStream;r._inFlightWriteRequest=r._writeRequests.shift(),h(e._writeAlgorithm(t),()=>{r._inFlightWriteRequest._resolve(void 0),r._inFlightWriteRequest=void 0;let t=r._state;return en(e),eG(r)||"writable"!==t||eJ(r,0>=e4(e)),e5(e),null},t=>("writable"===r._state&&e6(e),r._inFlightWriteRequest._reject(t),r._inFlightWriteRequest=void 0,eQ(r,t),null))}(e,r)}function e7(e,t){"writable"===e._controlledWritableStream._state&&e9(e,t)}function e9(e,t){let r=e._controlledWritableStream;e6(e),eX(r,t)}function te(e){return TypeError(`WritableStream.prototype.${e} can only be used on a WritableStream`)}function tt(e){return TypeError(`WritableStreamDefaultController.prototype.${e} can only be used on a WritableStreamDefaultController`)}function tr(e){return TypeError(`WritableStreamDefaultWriter.prototype.${e} can only be used on a WritableStreamDefaultWriter`)}function to(e){return TypeError("Cannot "+e+" a stream using a released writer")}function tn(e){e._closedPromise=d((t,r)=>{e._closedPromise_resolve=t,e._closedPromise_reject=r,e._closedPromiseState="pending"})}function ti(e,t){void 0!==e._closedPromise_reject&&(p(e._closedPromise),e._closedPromise_reject(t),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="rejected")}function ta(e){void 0!==e._closedPromise_resolve&&(e._closedPromise_resolve(void 0),e._closedPromise_resolve=void 0,e._closedPromise_reject=void 0,e._closedPromiseState="resolved")}function ts(e){e._readyPromise=d((t,r)=>{e._readyPromise_resolve=t,e._readyPromise_reject=r}),e._readyPromiseState="pending"}function tl(e,t){ts(e),tu(e,t)}function tu(e,t){void 0!==e._readyPromise_reject&&(p(e._readyPromise),e._readyPromise_reject(t),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="rejected")}function tc(e){void 0!==e._readyPromise_resolve&&(e._readyPromise_resolve(void 0),e._readyPromise_resolve=void 0,e._readyPromise_reject=void 0,e._readyPromiseState="fulfilled")}Object.defineProperties(e8.prototype,{abortReason:{enumerable:!0},signal:{enumerable:!0},error:{enumerable:!0}}),"symbol"==typeof o.toStringTag&&Object.defineProperty(e8.prototype,o.toStringTag,{value:"WritableStreamDefaultController",configurable:!0});let td="undefined"!=typeof DOMException?DOMException:void 0,tf=!function(e){if("function"!=typeof e&&"object"!=typeof e)return!1;try{return new e,!0}catch(e){return!1}}(td)?function(){let e=function(e,t){this.message=e||"",this.name=t||"Error",Error.captureStackTrace&&Error.captureStackTrace(this,this.constructor)};return e.prototype=Object.create(Error.prototype),Object.defineProperty(e.prototype,"constructor",{value:e,writable:!0,configurable:!0}),e}():td;function th(e,t,r,o,n,i){let a=e.getReader(),s=t.getWriter();tR(e)&&(e._disturbed=!0);let l,m,y,_=!1,g=!1,v="readable",S="writable",w=!1,T=!1,R=d(e=>{y=e}),E=Promise.resolve(void 0);return d((P,k)=>{let C;function q(){if(_)return;let e=d((e,t)=>{!function r(o){o?e():f(_?u(!0):f(s.ready,()=>f(a.read(),e=>!!e.done||(p(E=s.write(e.value)),!1))),r,t)}(!1)});p(e)}function A(){return v="closed",r?L():x(()=>(eN(t)&&(w=eG(t),S=t._state),w||"closed"===S?u(void 0):"erroring"===S||"errored"===S?c(m):(w=!0,s.close())),!1,void 0),null}function O(e){return _||(v="errored",l=e,o?L(!0,e):x(()=>s.abort(e),!0,e)),null}function j(e){return g||(S="errored",m=e,n?L(!0,e):x(()=>a.cancel(e),!0,e)),null}if(void 0!==i&&(C=()=>{let e=void 0!==i.reason?i.reason:new tf("Aborted","AbortError"),t=[];o||t.push(()=>"writable"===S?s.abort(e):u(void 0)),n||t.push(()=>"readable"===v?a.cancel(e):u(void 0)),x(()=>Promise.all(t.map(e=>e())),!0,e)},i.aborted?C():i.addEventListener("abort",C)),tR(e)&&(v=e._state,l=e._storedError),eN(t)&&(S=t._state,m=t._storedError,w=eG(t)),tR(e)&&eN(t)&&(T=!0,y()),"errored"===v)O(l);else if("erroring"===S||"errored"===S)j(m);else if("closed"===v)A();else if(w||"closed"===S){let e=TypeError("the destination writable stream closed before all data could be piped to it");n?L(!0,e):x(()=>a.cancel(e),!0,e)}function x(e,t,r){function o(){let e;return"writable"!==S||w?n():h(u(function t(){if(e!==E)return e=E,f(E,t,t)}()),n),null}function n(){return e?h(e(),()=>W(t,r),e=>W(!0,e)):W(t,r),null}_||(_=!0,T?o():h(R,o))}function L(e,t){x(void 0,e,t)}function W(e,t){return g=!0,s.releaseLock(),a.releaseLock(),void 0!==i&&i.removeEventListener("abort",C),e?k(t):P(void 0),null}_||(h(a.closed,A,O),h(s.closed,function(){return g||(S="closed"),null},j)),T?q():b(()=>{T=!0,y(),q()})})}class tp{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!tb(this))throw tS("desiredSize");return tg(this)}close(){if(!tb(this))throw tS("close");if(!tv(this))throw TypeError("The stream is not in a state that permits close");!function(e){if(!tv(e))return;let t=e._controlledReadableStream;e._closeRequested=!0,0===e._queue.length&&(ty(e),tk(t))}(this)}enqueue(e){if(!tb(this))throw tS("enqueue");if(!tv(this))throw TypeError("The stream is not in a state that permits enqueue");return function(e,t){if(!tv(e))return;let r=e._controlledReadableStream;if(tE(r)&&H(r)>0)U(r,t,!1);else{let r;try{r=e._strategySizeAlgorithm(t)}catch(t){throw t_(e,t),t}try{ei(e,t,r)}catch(t){throw t_(e,t),t}}tm(e)}(this,e)}error(e){if(!tb(this))throw tS("error");t_(this,e)}[S](e){ea(this);let t=this._cancelAlgorithm(e);return ty(this),t}[w](e){let t=this._controlledReadableStream;if(this._queue.length>0){let r=en(this);this._closeRequested&&0===this._queue.length?(ty(this),tk(t)):tm(this),e._chunkSteps(r)}else N(t,e),tm(this)}[T](){}}function tb(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledReadableStream")&&e instanceof tp}function tm(e){if(function(e){let t=e._controlledReadableStream;return!!tv(e)&&!!e._started&&!!(tE(t)&&H(t)>0||tg(e)>0)}(e)){if(e._pulling)return void(e._pullAgain=!0);e._pulling=!0,h(e._pullAlgorithm(),()=>(e._pulling=!1,e._pullAgain&&(e._pullAgain=!1,tm(e)),null),t=>(t_(e,t),null))}}function ty(e){e._pullAlgorithm=void 0,e._cancelAlgorithm=void 0,e._strategySizeAlgorithm=void 0}function t_(e,t){let r=e._controlledReadableStream;"readable"===r._state&&(ea(e),ty(e),tC(r,t))}function tg(e){let t=e._controlledReadableStream._state;return"errored"===t?null:"closed"===t?0:e._strategyHWM-e._queueTotalSize}function tv(e){return!e._closeRequested&&"readable"===e._controlledReadableStream._state}function tS(e){return TypeError(`ReadableStreamDefaultController.prototype.${e} can only be used on a ReadableStreamDefaultController`)}function tw(e,t){x(e,t);let r=null==e?void 0:e.preventAbort,o=null==e?void 0:e.preventCancel,n=null==e?void 0:e.preventClose,i=null==e?void 0:e.signal;return void 0!==i&&function(e,t){if(!function(e){if("object"!=typeof e||null===e)return!1;try{return"boolean"==typeof e.aborted}catch(e){return!1}}(e))throw TypeError(`${t} is not an AbortSignal.`)}(i,`${t} has member 'signal' that`),{preventAbort:!!r,preventCancel:!!o,preventClose:!!n,signal:i}}Object.defineProperties(tp.prototype,{close:{enumerable:!0},enqueue:{enumerable:!0},error:{enumerable:!0},desiredSize:{enumerable:!0}}),a(tp.prototype.close,"close"),a(tp.prototype.enqueue,"enqueue"),a(tp.prototype.error,"error"),"symbol"==typeof o.toStringTag&&Object.defineProperty(tp.prototype,o.toStringTag,{value:"ReadableStreamDefaultController",configurable:!0});class tT{constructor(e={},t={}){void 0===e?e=null:W(e,"First parameter");let r=eF(t,"Second parameter"),o=function(e,t){x(e,t);let r=null==e?void 0:e.autoAllocateChunkSize,o=null==e?void 0:e.cancel,n=null==e?void 0:e.pull,i=null==e?void 0:e.start,a=null==e?void 0:e.type;return{autoAllocateChunkSize:void 0===r?void 0:z(r,`${t} has member 'autoAllocateChunkSize' that`),cancel:void 0===o?void 0:(L(o,`${t} has member 'cancel' that`),t=>y(o,e,[t])),pull:void 0===n?void 0:(L(n,`${t} has member 'pull' that`),t=>y(n,e,[t])),start:void 0===i?void 0:(L(i,`${t} has member 'start' that`),t=>m(i,e,[t])),type:void 0===a?void 0:function(e,t){if("bytes"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamType`);return e}(a,`${t} has member 'type' that`)}}(e,"First parameter");if(this._state="readable",this._reader=void 0,this._storedError=void 0,this._disturbed=!1,"bytes"===o.type){if(void 0!==r.size)throw RangeError("The strategy for a byte stream cannot have a size function");!function(e,t,r){let o,n,i;let a=Object.create(el.prototype);o=void 0!==t.start?()=>t.start(a):()=>{},n=void 0!==t.pull?()=>t.pull(a):()=>u(void 0),i=void 0!==t.cancel?e=>t.cancel(e):()=>u(void 0);let s=t.autoAllocateChunkSize;if(0===s)throw TypeError("autoAllocateChunkSize must be greater than 0");a._controlledReadableByteStream=e,a._pullAgain=!1,a._pulling=!1,a._byobRequest=null,a._queue=a._queueTotalSize=void 0,ea(a),a._closeRequested=!1,a._started=!1,a._strategyHWM=r,a._pullAlgorithm=n,a._cancelAlgorithm=i,a._autoAllocateChunkSize=s,a._pendingPullIntos=new _,e._readableStreamController=a,h(u(o()),()=>(a._started=!0,ed(a),null),e=>(eP(a,e),null))}(this,o,eI(r,0))}else{let e=ez(r);!function(e,t,r,o){let n,i,a;let s=Object.create(tp.prototype);n=void 0!==t.start?()=>t.start(s):()=>{},i=void 0!==t.pull?()=>t.pull(s):()=>u(void 0),a=void 0!==t.cancel?e=>t.cancel(e):()=>u(void 0),s._controlledReadableStream=e,s._queue=void 0,s._queueTotalSize=void 0,ea(s),s._started=!1,s._closeRequested=!1,s._pullAgain=!1,s._pulling=!1,s._strategySizeAlgorithm=o,s._strategyHWM=r,s._pullAlgorithm=i,s._cancelAlgorithm=a,e._readableStreamController=s,h(u(n()),()=>(s._started=!0,tm(s),null),e=>(t_(s,e),null))}(this,o,eI(r,1),e)}}get locked(){if(!tR(this))throw tq("locked");return tE(this)}cancel(e){return tR(this)?tE(this)?c(TypeError("Cannot cancel a stream that already has a reader")):tP(this,e):c(tq("cancel"))}getReader(e){if(!tR(this))throw tq("getReader");return void 0===function(e,t){x(e,t);let r=null==e?void 0:e.mode;return{mode:void 0===r?void 0:function(e,t){if("byob"!=(e=`${e}`))throw TypeError(`${t} '${e}' is not a valid enumeration value for ReadableStreamReaderMode`);return e}(r,`${t} has member 'mode' that`)}}(e,"First parameter").mode?new Q(this):new eL(this)}pipeThrough(e,t={}){if(!F(this))throw tq("pipeThrough");B(e,1,"pipeThrough");let r=function(e,t){x(e,t);let r=null==e?void 0:e.readable;$(r,"readable","ReadableWritablePair"),function(e,t){if(!F(e))throw TypeError(`${t} is not a ReadableStream.`)}(r,`${t} has member 'readable' that`);let o=null==e?void 0:e.writable;return $(o,"writable","ReadableWritablePair"),function(e,t){if(!M(e))throw TypeError(`${t} is not a WritableStream.`)}(o,`${t} has member 'writable' that`),{readable:r,writable:o}}(e,"First parameter"),o=tw(t,"Second parameter");if(this.locked)throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked ReadableStream");if(r.writable.locked)throw TypeError("ReadableStream.prototype.pipeThrough cannot be used on a locked WritableStream");return p(th(this,r.writable,o.preventClose,o.preventAbort,o.preventCancel,o.signal)),r.readable}pipeTo(e,t={}){let r;if(!F(this))return c(tq("pipeTo"));if(void 0===e)return c("Parameter 1 is required in 'pipeTo'.");if(!M(e))return c(TypeError("ReadableStream.prototype.pipeTo's first argument must be a WritableStream"));try{r=tw(t,"Second parameter")}catch(e){return c(e)}return this.locked?c(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked ReadableStream")):e.locked?c(TypeError("ReadableStream.prototype.pipeTo cannot be used on a locked WritableStream")):th(this,e,r.preventClose,r.preventAbort,r.preventCancel,r.signal)}tee(){if(!F(this))throw tq("tee");if(this.locked)throw TypeError("Cannot tee a stream that already has a reader");return!function(e){try{return e.getReader({mode:"byob"}).releaseLock(),!0}catch(e){return!1}}(this)?function(e,t){let r=e.getReader(),o,n,i,a,s,l=!1,c=!1,f=!1,p=!1,b=d(e=>{s=e});function m(){return l?c=!0:(l=!0,h(r.read(),e=>{if(c=!1,e.done)return f||i.close(),p||a.close(),f&&p||s(void 0),null;let t=e.value;return f||i.enqueue(t),p||a.enqueue(t),l=!1,c&&m(),null},()=>(l=!1,null))),u(void 0)}let y=new tT({start(e){i=e},pull:m,cancel:function(e){if(f=!0,o=e,p){let e=[o,n],t=r.cancel(e);s(t)}return b}}),_=new tT({start(e){a=e},pull:m,cancel:function(e){if(p=!0,n=e,f){let e=[o,n],t=r.cancel(e);s(t)}return b}});return h(r.closed,void 0,e=>(i.error(e),a.error(e),f&&p||s(void 0),null)),[y,_]}(this):function(e){let t,r,o,n,i,a=e.getReader(),s=!1,l=!1,c=!1,f=!1,p=!1,b=!1,m=d(e=>{i=e});function y(e){h(e.closed,void 0,t=>(e!==a||(o.error(t),n.error(t),p&&b||i(void 0)),null))}function _(){s&&(a.releaseLock(),y(a=e.getReader()),s=!1),h(a.read(),e=>{var t,r;if(c=!1,f=!1,e.done)return p||o.close(),b||n.close(),null===(t=o.byobRequest)||void 0===t||t.respond(0),null===(r=n.byobRequest)||void 0===r||r.respond(0),p&&b||i(void 0),null;let s=e.value,u=s;if(!p&&!b)try{u=eo(s)}catch(e){return o.error(e),n.error(e),i(a.cancel(e)),null}return p||o.enqueue(s),b||n.enqueue(u),l=!1,c?v():f&&S(),null},()=>(l=!1,null))}function g(t,r){s||(a.releaseLock(),y(a=e.getReader({mode:"byob"})),s=!0);let u=r?n:o,d=r?o:n;h(a.read(t),e=>{var t;c=!1,f=!1;let o=r?b:p,n=r?p:b;if(e.done){o||u.close(),n||d.close();let r=e.value;return void 0!==r&&(o||u.byobRequest.respondWithNewView(r),n||null===(t=d.byobRequest)||void 0===t||t.respond(0)),o&&n||i(void 0),null}let s=e.value;if(n)o||u.byobRequest.respondWithNewView(s);else{let e;try{e=eo(s)}catch(e){return u.error(e),d.error(e),i(a.cancel(e)),null}o||u.byobRequest.respondWithNewView(s),d.enqueue(e)}return l=!1,c?v():f&&S(),null},()=>(l=!1,null))}function v(){if(l)return c=!0,u(void 0);l=!0;let e=o.byobRequest;return null===e?_():g(e.view,!1),u(void 0)}function S(){if(l)return f=!0,u(void 0);l=!0;let e=n.byobRequest;return null===e?_():g(e.view,!0),u(void 0)}let w=new tT({type:"bytes",start(e){o=e},pull:v,cancel:function(e){if(p=!0,t=e,b){let e=[t,r],o=a.cancel(e);i(o)}return m}}),T=new tT({type:"bytes",start(e){n=e},pull:S,cancel:function(e){if(b=!0,r=e,p){let e=[t,r],o=a.cancel(e);i(o)}return m}});return y(a),[w,T]}(this)}values(e){if(!F(this))throw tq("values");return function(e,t){let r=new K(e.getReader(),t),o=Object.create(J);return o._asyncIteratorImpl=r,o}(this,(x(e,"First parameter"),{preventCancel:!!(null==e?void 0:e.preventCancel)}).preventCancel)}}function tR(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_readableStreamController")&&e instanceof tT}function tE(e){return void 0!==e._reader}function tP(e,t){if(e._disturbed=!0,"closed"===e._state)return u(void 0);if("errored"===e._state)return c(e._storedError);tk(e);let r=e._reader;if(void 0!==r&&eW(r)){let e=r._readIntoRequests;r._readIntoRequests=new _,e.forEach(e=>{e._closeSteps(void 0)})}return f(e._readableStreamController[S](t),n,void 0)}function tk(e){e._state="closed";let t=e._reader;if(void 0!==t&&(A(t),X(t))){let e=t._readRequests;t._readRequests=new _,e.forEach(e=>{e._closeSteps()})}}function tC(e,t){e._state="errored",e._storedError=t;let r=e._reader;void 0!==r&&(q(r,t),X(r)?Y(r,t):eB(r,t))}function tq(e){return TypeError(`ReadableStream.prototype.${e} can only be used on a ReadableStream`)}function tA(e,t){x(e,t);let r=null==e?void 0:e.highWaterMark;return $(r,"highWaterMark","QueuingStrategyInit"),{highWaterMark:I(r)}}Object.defineProperties(tT.prototype,{cancel:{enumerable:!0},getReader:{enumerable:!0},pipeThrough:{enumerable:!0},pipeTo:{enumerable:!0},tee:{enumerable:!0},values:{enumerable:!0},locked:{enumerable:!0}}),a(tT.prototype.cancel,"cancel"),a(tT.prototype.getReader,"getReader"),a(tT.prototype.pipeThrough,"pipeThrough"),a(tT.prototype.pipeTo,"pipeTo"),a(tT.prototype.tee,"tee"),a(tT.prototype.values,"values"),"symbol"==typeof o.toStringTag&&Object.defineProperty(tT.prototype,o.toStringTag,{value:"ReadableStream",configurable:!0}),"symbol"==typeof o.asyncIterator&&Object.defineProperty(tT.prototype,o.asyncIterator,{value:tT.prototype.values,writable:!0,configurable:!0});let tO=e=>e.byteLength;a(tO,"size");class tj{constructor(e){B(e,1,"ByteLengthQueuingStrategy"),e=tA(e,"First parameter"),this._byteLengthQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!tL(this))throw tx("highWaterMark");return this._byteLengthQueuingStrategyHighWaterMark}get size(){if(!tL(this))throw tx("size");return tO}}function tx(e){return TypeError(`ByteLengthQueuingStrategy.prototype.${e} can only be used on a ByteLengthQueuingStrategy`)}function tL(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_byteLengthQueuingStrategyHighWaterMark")&&e instanceof tj}Object.defineProperties(tj.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof o.toStringTag&&Object.defineProperty(tj.prototype,o.toStringTag,{value:"ByteLengthQueuingStrategy",configurable:!0});let tW=()=>1;a(tW,"size");class tB{constructor(e){B(e,1,"CountQueuingStrategy"),e=tA(e,"First parameter"),this._countQueuingStrategyHighWaterMark=e.highWaterMark}get highWaterMark(){if(!tI(this))throw t$("highWaterMark");return this._countQueuingStrategyHighWaterMark}get size(){if(!tI(this))throw t$("size");return tW}}function t$(e){return TypeError(`CountQueuingStrategy.prototype.${e} can only be used on a CountQueuingStrategy`)}function tI(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_countQueuingStrategyHighWaterMark")&&e instanceof tB}Object.defineProperties(tB.prototype,{highWaterMark:{enumerable:!0},size:{enumerable:!0}}),"symbol"==typeof o.toStringTag&&Object.defineProperty(tB.prototype,o.toStringTag,{value:"CountQueuingStrategy",configurable:!0});class tz{constructor(e={},t={},r={}){let o;void 0===e&&(e=null);let n=eF(t,"Second parameter"),i=eF(r,"Third parameter"),a=function(e,t){x(e,t);let r=null==e?void 0:e.flush,o=null==e?void 0:e.readableType,n=null==e?void 0:e.start,i=null==e?void 0:e.transform,a=null==e?void 0:e.writableType;return{flush:void 0===r?void 0:(L(r,`${t} has member 'flush' that`),t=>y(r,e,[t])),readableType:o,start:void 0===n?void 0:(L(n,`${t} has member 'start' that`),t=>m(n,e,[t])),transform:void 0===i?void 0:(L(i,`${t} has member 'transform' that`),(t,r)=>y(i,e,[t,r])),writableType:a}}(e,"First parameter");if(void 0!==a.readableType)throw RangeError("Invalid readableType specified");if(void 0!==a.writableType)throw RangeError("Invalid writableType specified");let s=eI(i,0),l=ez(i),h=eI(n,1),p=ez(n);(function(e,t,r,o,n,i){function a(){return t}e._writableState="writable",e._writableStoredError=void 0,e._writableHasInFlightOperation=!1,e._writableStarted=!1,e._writable=new eD({start(r){e._writableController=r;try{let t=r.signal;void 0!==t&&t.addEventListener("abort",()=>{"writable"===e._writableState&&(e._writableState="erroring",t.reason&&(e._writableStoredError=t.reason))})}catch(e){}return f(t,()=>(e._writableStarted=!0,t2(e),null),t=>{throw e._writableStarted=!0,t1(e,t),t})},write:t=>(e._writableHasInFlightOperation=!0,f(function(e,t){let r=e._transformStreamController;return e._backpressure?f(e._backpressureChangePromise,()=>{if("erroring"===(eN(e._writable)?e._writable._state:e._writableState))throw eN(e._writable)?e._writable._storedError:e._writableStoredError;return tX(r,t)},void 0):tX(r,t)}(e,t),()=>(e._writableHasInFlightOperation=!1,t2(e),null),t=>{throw e._writableHasInFlightOperation=!1,t1(e,t),t})),close:()=>(e._writableHasInFlightOperation=!0,f(function(e){let t=e._transformStreamController,r=t._flushAlgorithm();return tV(t),f(r,()=>{if("errored"===e._readableState)throw e._readableStoredError;tK(e)&&tJ(e)},t=>{throw tM(e,t),e._readableStoredError})}(e),()=>(e._writableHasInFlightOperation=!1,"erroring"===e._writableState&&(e._writableStoredError=void 0),e._writableState="closed",null),t=>{throw e._writableHasInFlightOperation=!1,e._writableState,t1(e,t),t})),abort:t=>(e._writableState="errored",e._writableStoredError=t,tM(e,t),u(void 0))},{highWaterMark:r,size:o}),e._readableState="readable",e._readableStoredError=void 0,e._readableCloseRequested=!1,e._readablePulling=!1,e._readable=new tT({start:r=>(e._readableController=r,t.catch(t=>{tZ(e,t)})),pull:()=>(e._readablePulling=!0,(tN(e,!1),e._backpressureChangePromise).catch(t=>{tZ(e,t)})),cancel:t=>(e._readableState="closed",tD(e,t),u(void 0))},{highWaterMark:n,size:i}),e._backpressure=void 0,e._backpressureChangePromise=void 0,e._backpressureChangePromise_resolve=void 0,tN(e,!0),e._transformStreamController=void 0})(this,d(e=>{o=e}),h,p,s,l),function(e,t){let r,o;let n=Object.create(tU.prototype);r=void 0!==t.transform?e=>t.transform(e,n):e=>{try{return tQ(n,e),u(void 0)}catch(e){return c(e)}},o=void 0!==t.flush?()=>t.flush(n):()=>u(void 0),n._controlledTransformStream=e,e._transformStreamController=n,n._transformAlgorithm=r,n._flushAlgorithm=o}(this,a),void 0!==a.start?o(a.start(this._transformStreamController)):o(void 0)}get readable(){if(!tF(this))throw tG("readable");return this._readable}get writable(){if(!tF(this))throw tG("writable");return this._writable}}function tF(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_transformStreamController")&&e instanceof tz}function tM(e,t){tZ(e,t),tD(e,t)}function tD(e,t){tV(e._transformStreamController),e._writableController.error(t),"writable"===e._writableState&&t3(e,t),e._backpressure&&tN(e,!1)}function tN(e,t){void 0!==e._backpressureChangePromise&&e._backpressureChangePromise_resolve(),e._backpressureChangePromise=d(t=>{e._backpressureChangePromise_resolve=t}),e._backpressure=t}Object.defineProperties(tz.prototype,{readable:{enumerable:!0},writable:{enumerable:!0}}),"symbol"==typeof o.toStringTag&&Object.defineProperty(tz.prototype,o.toStringTag,{value:"TransformStream",configurable:!0});class tU{constructor(){throw TypeError("Illegal constructor")}get desiredSize(){if(!tH(this))throw tY("desiredSize");return t0(this._controlledTransformStream)}enqueue(e){if(!tH(this))throw tY("enqueue");tQ(this,e)}error(e){if(!tH(this))throw tY("error");tM(this._controlledTransformStream,e)}terminate(){if(!tH(this))throw tY("terminate");!function(e){let t=e._controlledTransformStream;tK(t)&&tJ(t),tD(t,TypeError("TransformStream terminated"))}(this)}}function tH(e){return!!i(e)&&!!Object.prototype.hasOwnProperty.call(e,"_controlledTransformStream")&&e instanceof tU}function tV(e){e._transformAlgorithm=void 0,e._flushAlgorithm=void 0}function tQ(e,t){let r=e._controlledTransformStream;if(!tK(r))throw TypeError("Readable side is not in a state that permits enqueue");try{!function(e,t){e._readablePulling=!1;try{e._readableController.enqueue(t)}catch(t){throw tZ(e,t),t}}(r,t)}catch(e){throw tD(r,e),r._readableStoredError}!(tK(r)&&(r._readablePulling||t0(r)>0))!==r._backpressure&&tN(r,!0)}function tX(e,t){return f(e._transformAlgorithm(t),void 0,t=>{throw tM(e._controlledTransformStream,t),t})}function tY(e){return TypeError(`TransformStreamDefaultController.prototype.${e} can only be used on a TransformStreamDefaultController`)}function tG(e){return TypeError(`TransformStream.prototype.${e} can only be used on a TransformStream`)}function tK(e){return!e._readableCloseRequested&&"readable"===e._readableState}function tJ(e){e._readableState="closed",e._readableCloseRequested=!0,e._readableController.close()}function tZ(e,t){"readable"===e._readableState&&(e._readableState="errored",e._readableStoredError=t),e._readableController.error(t)}function t0(e){return e._readableController.desiredSize}function t1(e,t){"writable"!==e._writableState?t8(e):t3(e,t)}function t3(e,t){e._writableState="erroring",e._writableStoredError=t,!e._writableHasInFlightOperation&&e._writableStarted&&t8(e)}function t8(e){e._writableState="errored"}function t2(e){"erroring"===e._writableState&&t8(e)}Object.defineProperties(tU.prototype,{enqueue:{enumerable:!0},error:{enumerable:!0},terminate:{enumerable:!0},desiredSize:{enumerable:!0}}),a(tU.prototype.enqueue,"enqueue"),a(tU.prototype.error,"error"),a(tU.prototype.terminate,"terminate"),"symbol"==typeof o.toStringTag&&Object.defineProperty(tU.prototype,o.toStringTag,{value:"TransformStreamDefaultController",configurable:!0});var t6,t4,t5,t7=r(8692);async function*t9(e){let t=e.byteOffset+e.byteLength,r=e.byteOffset;for(;r!==t;){let o=Math.min(t-r,65536),n=e.buffer.slice(r,r+o);r+=n.byteLength,yield new Uint8Array(n)}}async function*re(e){let t=0;for(;t!==e.size;){let r=e.slice(t,Math.min(e.size,t+65536)),o=await r.arrayBuffer();t+=o.byteLength,yield new Uint8Array(o)}}async function*rt(e,t=!1){for(let r of e)ArrayBuffer.isView(r)?t?yield*t9(r):yield r:(0,t7.m)(r.stream)?yield*r.stream():yield*re(r)}/*! Based on fetch-blob. MIT License. Jimmy Wärting <https://jimmy.warting.se/opensource> & David Frank */var rr=function(e,t,r,o){if("a"===r&&!o)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?o:"a"===r?o.call(e):o?o.value:t.get(e)},ro=function(e,t,r,o,n){if("m"===o)throw TypeError("Private method is not writable");if("a"===o&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===o?n.call(e,r):n?n.value=r:t.set(e,r),r};class rn{constructor(e=[],t={}){if(t6.set(this,[]),t4.set(this,""),t5.set(this,0),null!=t||(t={}),"object"!=typeof e||null===e)throw TypeError("Failed to construct 'Blob': The provided value cannot be converted to a sequence.");if(!(0,t7.m)(e[Symbol.iterator]))throw TypeError("Failed to construct 'Blob': The object must have a callable @@iterator property.");if("object"!=typeof t&&!(0,t7.m)(t))throw TypeError("Failed to construct 'Blob': parameter 2 cannot convert to dictionary.");let r=new TextEncoder;for(let t of e){let e;e=ArrayBuffer.isView(t)?new Uint8Array(t.buffer.slice(t.byteOffset,t.byteOffset+t.byteLength)):t instanceof ArrayBuffer?new Uint8Array(t.slice(0)):t instanceof rn?t:r.encode(String(t)),ro(this,t5,rr(this,t5,"f")+(ArrayBuffer.isView(e)?e.byteLength:e.size),"f"),rr(this,t6,"f").push(e)}let o=void 0===t.type?"":String(t.type);ro(this,t4,/^[\x20-\x7E]*$/.test(o)?o:"","f")}static[(t6=new WeakMap,t4=new WeakMap,t5=new WeakMap,Symbol.hasInstance)](e){return!!(e&&"object"==typeof e&&(0,t7.m)(e.constructor)&&((0,t7.m)(e.stream)||(0,t7.m)(e.arrayBuffer))&&/^(Blob|File)$/.test(e[Symbol.toStringTag]))}get type(){return rr(this,t4,"f")}get size(){return rr(this,t5,"f")}slice(e,t,r){return new rn(function*(e,t,r=0,o){null!=o||(o=t);let n=r<0?Math.max(t+r,0):Math.min(r,t),i=o<0?Math.max(t+o,0):Math.min(o,t),a=Math.max(i-n,0),s=0;for(let t of e){if(s>=a)break;let e=ArrayBuffer.isView(t)?t.byteLength:t.size;if(n&&e<=n)n-=e,i-=e;else{let r;ArrayBuffer.isView(t)?s+=(r=t.subarray(n,Math.min(e,i))).byteLength:s+=(r=t.slice(n,Math.min(e,i))).size,i-=e,n=0,yield r}}}(rr(this,t6,"f"),this.size,e,t),{type:r})}async text(){let e=new TextDecoder,t="";for await(let r of rt(rr(this,t6,"f")))t+=e.decode(r,{stream:!0});return t+e.decode()}async arrayBuffer(){let e=new Uint8Array(this.size),t=0;for await(let r of rt(rr(this,t6,"f")))e.set(r,t),t+=r.length;return e.buffer}stream(){let e=rt(rr(this,t6,"f"),!0);return new tT({async pull(t){let{value:r,done:o}=await e.next();if(o)return queueMicrotask(()=>t.close());t.enqueue(r)},async cancel(){await e.return()}})}get[Symbol.toStringTag](){return"Blob"}}Object.defineProperties(rn.prototype,{type:{enumerable:!0},size:{enumerable:!0},slice:{enumerable:!0},stream:{enumerable:!0},text:{enumerable:!0},arrayBuffer:{enumerable:!0}})},2169:(e,t,r)=>{"use strict";r.d(t,{$:()=>l});var o,n,i=r(3687),a=function(e,t,r,o,n){if("m"===o)throw TypeError("Private method is not writable");if("a"===o&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===o?n.call(e,r):n?n.value=r:t.set(e,r),r},s=function(e,t,r,o){if("a"===r&&!o)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?o:"a"===r?o.call(e):o?o.value:t.get(e)};class l extends i.t{constructor(e,t,r={}){if(super(e,r),o.set(this,void 0),n.set(this,0),arguments.length<2)throw TypeError(`Failed to construct 'File': 2 arguments required, but only ${arguments.length} present.`);a(this,o,String(t),"f");let i=void 0===r.lastModified?Date.now():Number(r.lastModified);Number.isNaN(i)||a(this,n,i,"f")}static[(o=new WeakMap,n=new WeakMap,Symbol.hasInstance)](e){return e instanceof i.t&&"File"===e[Symbol.toStringTag]&&"string"==typeof e.name}get name(){return s(this,o,"f")}get lastModified(){return s(this,n,"f")}get webkitRelativePath(){return""}get[Symbol.toStringTag](){return"File"}}},5620:(e,t,r)=>{"use strict";r.d(t,{z:()=>n});var o=r(2169);let n=e=>e instanceof o.$},8692:(e,t,r)=>{"use strict";r.d(t,{m:()=>o});let o=e=>"function"==typeof e},7554:(e,t,r)=>{"use strict";let o,n,i,a,s,l,u,c,d,f,h;r.d(t,{ZP:()=>rt});var p,b,m,y,_,g,v,S,w,T,R,E,P,k,C={};r.r(C),r.d(C,{APIConnectionError:()=>e5,APIConnectionTimeoutError:()=>e7,APIError:()=>e6,APIUserAbortError:()=>e4,AuthenticationError:()=>te,BadRequestError:()=>e9,ConflictError:()=>to,GroqError:()=>e2,InternalServerError:()=>ta,NotFoundError:()=>tr,PermissionDeniedError:()=>tt,RateLimitError:()=>ti,UnprocessableEntityError:()=>tn});let q="0.3.3",A=!1;var O=r(2781),j=r(3685),x=r(7310),L=r(3122),W=r(5687),B=r(9796);let $=O.Readable,I=Symbol("buffer"),z=Symbol("type");class F{constructor(){this[z]="";let e=arguments[0],t=arguments[1],r=[];if(e){let t=Number(e.length);for(let o=0;o<t;o++){let t;let n=e[o];(t=n instanceof Buffer?n:ArrayBuffer.isView(n)?Buffer.from(n.buffer,n.byteOffset,n.byteLength):n instanceof ArrayBuffer?Buffer.from(n):n instanceof F?n[I]:Buffer.from("string"==typeof n?n:String(n))).length,r.push(t)}}this[I]=Buffer.concat(r);let o=t&&void 0!==t.type&&String(t.type).toLowerCase();o&&!/[^\u0020-\u007E]/.test(o)&&(this[z]=o)}get size(){return this[I].length}get type(){return this[z]}text(){return Promise.resolve(this[I].toString())}arrayBuffer(){let e=this[I];return Promise.resolve(e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength))}stream(){let e=new $;return e._read=function(){},e.push(this[I]),e.push(null),e}toString(){return"[object Blob]"}slice(){let e;let t=this.size,r=arguments[0],o=arguments[1];e=void 0===r?0:r<0?Math.max(t+r,0):Math.min(r,t);let n=Math.max((void 0===o?t:o<0?Math.max(t+o,0):Math.min(o,t))-e,0),i=this[I].slice(e,e+n),a=new F([],{type:arguments[2]});return a[I]=i,a}}function M(e,t,r){Error.call(this,e),this.message=e,this.type=t,r&&(this.code=this.errno=r.code),Error.captureStackTrace(this,this.constructor)}Object.defineProperties(F.prototype,{size:{enumerable:!0},type:{enumerable:!0},slice:{enumerable:!0}}),Object.defineProperty(F.prototype,Symbol.toStringTag,{value:"Blob",writable:!1,enumerable:!1,configurable:!0}),M.prototype=Object.create(Error.prototype),M.prototype.constructor=M,M.prototype.name="FetchError";try{o=require("encoding").convert}catch(e){}let D=Symbol("Body internals"),N=O.PassThrough;function U(e){var t=this,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=r.size,n=r.timeout;null==e?e=null:V(e)?e=Buffer.from(e.toString()):Q(e)||Buffer.isBuffer(e)||("[object ArrayBuffer]"===Object.prototype.toString.call(e)?e=Buffer.from(e):ArrayBuffer.isView(e)?e=Buffer.from(e.buffer,e.byteOffset,e.byteLength):e instanceof O||(e=Buffer.from(String(e)))),this[D]={body:e,disturbed:!1,error:null},this.size=void 0===o?0:o,this.timeout=void 0===n?0:n,e instanceof O&&e.on("error",function(e){let r="AbortError"===e.name?e:new M(`Invalid response body while trying to fetch ${t.url}: ${e.message}`,"system",e);t[D].error=r})}function H(){var e=this;if(this[D].disturbed)return U.Promise.reject(TypeError(`body used already for: ${this.url}`));if(this[D].disturbed=!0,this[D].error)return U.Promise.reject(this[D].error);let t=this.body;if(null===t)return U.Promise.resolve(Buffer.alloc(0));if(Q(t)&&(t=t.stream()),Buffer.isBuffer(t))return U.Promise.resolve(t);if(!(t instanceof O))return U.Promise.resolve(Buffer.alloc(0));let r=[],o=0,n=!1;return new U.Promise(function(i,a){let s;e.timeout&&(s=setTimeout(function(){n=!0,a(new M(`Response timeout while trying to fetch ${e.url} (over ${e.timeout}ms)`,"body-timeout"))},e.timeout)),t.on("error",function(t){"AbortError"===t.name?(n=!0,a(t)):a(new M(`Invalid response body while trying to fetch ${e.url}: ${t.message}`,"system",t))}),t.on("data",function(t){if(!n&&null!==t){if(e.size&&o+t.length>e.size){n=!0,a(new M(`content size at ${e.url} over limit: ${e.size}`,"max-size"));return}o+=t.length,r.push(t)}}),t.on("end",function(){if(!n){clearTimeout(s);try{i(Buffer.concat(r,o))}catch(t){a(new M(`Could not create Buffer from response body for ${e.url}: ${t.message}`,"system",t))}}})})}function V(e){return"object"==typeof e&&"function"==typeof e.append&&"function"==typeof e.delete&&"function"==typeof e.get&&"function"==typeof e.getAll&&"function"==typeof e.has&&"function"==typeof e.set&&("URLSearchParams"===e.constructor.name||"[object URLSearchParams]"===Object.prototype.toString.call(e)||"function"==typeof e.sort)}function Q(e){return"object"==typeof e&&"function"==typeof e.arrayBuffer&&"string"==typeof e.type&&"function"==typeof e.stream&&"function"==typeof e.constructor&&"string"==typeof e.constructor.name&&/^(Blob|File)$/.test(e.constructor.name)&&/^(Blob|File)$/.test(e[Symbol.toStringTag])}function X(e){let t,r;let o=e.body;if(e.bodyUsed)throw Error("cannot clone body after it is used");return o instanceof O&&"function"!=typeof o.getBoundary&&(t=new N,r=new N,o.pipe(t),o.pipe(r),e[D].body=t,o=r),o}function Y(e){if(null===e)return null;if("string"==typeof e)return"text/plain;charset=UTF-8";if(V(e))return"application/x-www-form-urlencoded;charset=UTF-8";if(Q(e))return e.type||null;if(Buffer.isBuffer(e))return null;if("[object ArrayBuffer]"===Object.prototype.toString.call(e))return null;if(ArrayBuffer.isView(e))return null;else if("function"==typeof e.getBoundary)return`multipart/form-data;boundary=${e.getBoundary()}`;else if(e instanceof O)return null;else return"text/plain;charset=UTF-8"}function G(e){let t=e.body;return null===t?0:Q(t)?t.size:Buffer.isBuffer(t)?t.length:t&&"function"==typeof t.getLengthSync&&(t._lengthRetrievers&&0==t._lengthRetrievers.length||t.hasKnownLength&&t.hasKnownLength())?t.getLengthSync():null}U.prototype={get body(){return this[D].body},get bodyUsed(){return this[D].disturbed},arrayBuffer(){return H.call(this).then(function(e){return e.buffer.slice(e.byteOffset,e.byteOffset+e.byteLength)})},blob(){let e=this.headers&&this.headers.get("content-type")||"";return H.call(this).then(function(t){return Object.assign(new F([],{type:e.toLowerCase()}),{[I]:t})})},json(){var e=this;return H.call(this).then(function(t){try{return JSON.parse(t.toString())}catch(t){return U.Promise.reject(new M(`invalid json response body at ${e.url} reason: ${t.message}`,"invalid-json"))}})},text(){return H.call(this).then(function(e){return e.toString()})},buffer(){return H.call(this)},textConverted(){var e=this;return H.call(this).then(function(t){return function(e,t){let r,n;if("function"!=typeof o)throw Error("The package `encoding` must be installed to use the textConverted() function");let i=t.get("content-type"),a="utf-8";return i&&(r=/charset=([^;]*)/i.exec(i)),n=e.slice(0,1024).toString(),!r&&n&&(r=/<meta.+?charset=(['"])(.+?)\1/i.exec(n)),!r&&n&&(!(r=/<meta[\s]+?http-equiv=(['"])content-type\1[\s]+?content=(['"])(.+?)\2/i.exec(n))&&(r=/<meta[\s]+?content=(['"])(.+?)\1[\s]+?http-equiv=(['"])content-type\3/i.exec(n))&&r.pop(),r&&(r=/charset=(.*)/i.exec(r.pop()))),!r&&n&&(r=/<\?xml.+?encoding=(['"])(.+?)\1/i.exec(n)),r&&("gb2312"===(a=r.pop())||"gbk"===a)&&(a="gb18030"),o(e,"UTF-8",a).toString()}(t,e.headers)})}},Object.defineProperties(U.prototype,{body:{enumerable:!0},bodyUsed:{enumerable:!0},arrayBuffer:{enumerable:!0},blob:{enumerable:!0},json:{enumerable:!0},text:{enumerable:!0}}),U.mixIn=function(e){for(let t of Object.getOwnPropertyNames(U.prototype))if(!(t in e)){let r=Object.getOwnPropertyDescriptor(U.prototype,t);Object.defineProperty(e,t,r)}},U.Promise=global.Promise;let K=/[^\^_`a-zA-Z\-0-9!#$%&'*+.|~]/,J=/[^\t\x20-\x7e\x80-\xff]/;function Z(e){if(e=`${e}`,K.test(e)||""===e)throw TypeError(`${e} is not a legal HTTP header name`)}function ee(e){if(e=`${e}`,J.test(e))throw TypeError(`${e} is not a legal HTTP header value`)}function et(e,t){for(let r in t=t.toLowerCase(),e)if(r.toLowerCase()===t)return r}let er=Symbol("map");class eo{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:void 0;if(this[er]=Object.create(null),e instanceof eo){let t=e.raw();for(let e of Object.keys(t))for(let r of t[e])this.append(e,r);return}if(null==e);else if("object"==typeof e){let t=e[Symbol.iterator];if(null!=t){if("function"!=typeof t)throw TypeError("Header pairs must be iterable");let r=[];for(let t of e){if("object"!=typeof t||"function"!=typeof t[Symbol.iterator])throw TypeError("Each header pair must be iterable");r.push(Array.from(t))}for(let e of r){if(2!==e.length)throw TypeError("Each header pair must be a name/value tuple");this.append(e[0],e[1])}}else for(let t of Object.keys(e)){let r=e[t];this.append(t,r)}}else throw TypeError("Provided initializer must be an object")}get(e){Z(e=`${e}`);let t=et(this[er],e);return void 0===t?null:this[er][t].join(", ")}forEach(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:void 0,r=en(this),o=0;for(;o<r.length;){var n=r[o];let i=n[0],a=n[1];e.call(t,a,i,this),r=en(this),o++}}set(e,t){e=`${e}`,t=`${t}`,Z(e),ee(t);let r=et(this[er],e);this[er][void 0!==r?r:e]=[t]}append(e,t){e=`${e}`,t=`${t}`,Z(e),ee(t);let r=et(this[er],e);void 0!==r?this[er][r].push(t):this[er][e]=[t]}has(e){return Z(e=`${e}`),void 0!==et(this[er],e)}delete(e){Z(e=`${e}`);let t=et(this[er],e);void 0!==t&&delete this[er][t]}raw(){return this[er]}keys(){return ea(this,"key")}values(){return ea(this,"value")}[Symbol.iterator](){return ea(this,"key+value")}}function en(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:"key+value";return Object.keys(e[er]).sort().map("key"===t?function(e){return e.toLowerCase()}:"value"===t?function(t){return e[er][t].join(", ")}:function(t){return[t.toLowerCase(),e[er][t].join(", ")]})}eo.prototype.entries=eo.prototype[Symbol.iterator],Object.defineProperty(eo.prototype,Symbol.toStringTag,{value:"Headers",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(eo.prototype,{get:{enumerable:!0},forEach:{enumerable:!0},set:{enumerable:!0},append:{enumerable:!0},has:{enumerable:!0},delete:{enumerable:!0},keys:{enumerable:!0},values:{enumerable:!0},entries:{enumerable:!0}});let ei=Symbol("internal");function ea(e,t){let r=Object.create(es);return r[ei]={target:e,kind:t,index:0},r}let es=Object.setPrototypeOf({next(){if(!this||Object.getPrototypeOf(this)!==es)throw TypeError("Value of `this` is not a HeadersIterator");var e=this[ei];let t=e.target,r=e.kind,o=e.index,n=en(t,r);return o>=n.length?{value:void 0,done:!0}:(this[ei].index=o+1,{value:n[o],done:!1})}},Object.getPrototypeOf(Object.getPrototypeOf([][Symbol.iterator]())));Object.defineProperty(es,Symbol.toStringTag,{value:"HeadersIterator",writable:!1,enumerable:!1,configurable:!0});let el=Symbol("Response internals"),eu=j.STATUS_CODES;class ec{constructor(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:null,t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};U.call(this,e,t);let r=t.status||200,o=new eo(t.headers);if(null!=e&&!o.has("Content-Type")){let t=Y(e);t&&o.append("Content-Type",t)}this[el]={url:t.url,status:r,statusText:t.statusText||eu[r],headers:o,counter:t.counter}}get url(){return this[el].url||""}get status(){return this[el].status}get ok(){return this[el].status>=200&&this[el].status<300}get redirected(){return this[el].counter>0}get statusText(){return this[el].statusText}get headers(){return this[el].headers}clone(){return new ec(X(this),{url:this.url,status:this.status,statusText:this.statusText,headers:this.headers,ok:this.ok,redirected:this.redirected})}}U.mixIn(ec.prototype),Object.defineProperties(ec.prototype,{url:{enumerable:!0},status:{enumerable:!0},ok:{enumerable:!0},redirected:{enumerable:!0},statusText:{enumerable:!0},headers:{enumerable:!0},clone:{enumerable:!0}}),Object.defineProperty(ec.prototype,Symbol.toStringTag,{value:"Response",writable:!1,enumerable:!1,configurable:!0});let ed=Symbol("Request internals"),ef=x.URL||L.URL,eh=x.parse,ep=x.format;function eb(e){return/^[a-zA-Z][a-zA-Z\d+\-.]*:/.exec(e)&&(e=new ef(e).toString()),eh(e)}let em="destroy"in O.Readable.prototype;function ey(e){return"object"==typeof e&&"object"==typeof e[ed]}class e_{constructor(e){let t,r=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};ey(e)?t=eb(e.url):(t=e&&e.href?eb(e.href):eb(`${e}`),e={});let o=r.method||e.method||"GET";if(o=o.toUpperCase(),(null!=r.body||ey(e)&&null!==e.body)&&("GET"===o||"HEAD"===o))throw TypeError("Request with GET/HEAD method cannot have body");let n=null!=r.body?r.body:ey(e)&&null!==e.body?X(e):null;U.call(this,n,{timeout:r.timeout||e.timeout||0,size:r.size||e.size||0});let i=new eo(r.headers||e.headers||{});if(null!=n&&!i.has("Content-Type")){let e=Y(n);e&&i.append("Content-Type",e)}let a=ey(e)?e.signal:null;if("signal"in r&&(a=r.signal),null!=a&&!function(e){let t=e&&"object"==typeof e&&Object.getPrototypeOf(e);return!!(t&&"AbortSignal"===t.constructor.name)}(a))throw TypeError("Expected signal to be an instanceof AbortSignal");this[ed]={method:o,redirect:r.redirect||e.redirect||"follow",headers:i,parsedURL:t,signal:a},this.follow=void 0!==r.follow?r.follow:void 0!==e.follow?e.follow:20,this.compress=void 0!==r.compress?r.compress:void 0===e.compress||e.compress,this.counter=r.counter||e.counter||0,this.agent=r.agent||e.agent}get method(){return this[ed].method}get url(){return ep(this[ed].parsedURL)}get headers(){return this[ed].headers}get redirect(){return this[ed].redirect}get signal(){return this[ed].signal}clone(){return new e_(this)}}function eg(e){Error.call(this,e),this.type="aborted",this.message=e,Error.captureStackTrace(this,this.constructor)}U.mixIn(e_.prototype),Object.defineProperty(e_.prototype,Symbol.toStringTag,{value:"Request",writable:!1,enumerable:!1,configurable:!0}),Object.defineProperties(e_.prototype,{method:{enumerable:!0},url:{enumerable:!0},headers:{enumerable:!0},redirect:{enumerable:!0},clone:{enumerable:!0},signal:{enumerable:!0}}),eg.prototype=Object.create(Error.prototype),eg.prototype.constructor=eg,eg.prototype.name="AbortError";let ev=x.URL||L.URL,eS=O.PassThrough,ew=function(e,t){let r=new ev(t).hostname,o=new ev(e).hostname;return r===o||"."===r[r.length-o.length-1]&&r.endsWith(o)};function eT(e,t){if(!eT.Promise)throw Error("native promise missing, set fetch.Promise to your favorite alternative");return U.Promise=eT.Promise,new eT.Promise(function(r,o){var n;let i,a;let s=new e_(e,t),l=function(e){let t=e[ed].parsedURL,r=new eo(e[ed].headers);if(r.has("Accept")||r.set("Accept","*/*"),!t.protocol||!t.hostname)throw TypeError("Only absolute URLs are supported");if(!/^https?:$/.test(t.protocol))throw TypeError("Only HTTP(S) protocols are supported");if(e.signal&&e.body instanceof O.Readable&&!em)throw Error("Cancellation of streamed requests with AbortSignal is not supported in node < 8");let o=null;if(null==e.body&&/^(POST|PUT)$/i.test(e.method)&&(o="0"),null!=e.body){let t=G(e);"number"==typeof t&&(o=String(t))}o&&r.set("Content-Length",o),r.has("User-Agent")||r.set("User-Agent","node-fetch/1.0 (+https://github.com/bitinn/node-fetch)"),e.compress&&!r.has("Accept-Encoding")&&r.set("Accept-Encoding","gzip,deflate");let n=e.agent;return"function"==typeof n&&(n=n(t)),Object.assign({},t,{method:e.method,headers:function(e){let t=Object.assign({__proto__:null},e[er]),r=et(e[er],"Host");return void 0!==r&&(t[r]=t[r][0]),t}(r),agent:n})}(s),u=("https:"===l.protocol?W:j).request,c=s.signal,d=null,f=function(){let e=new eg("The user aborted a request.");o(e),s.body&&s.body instanceof O.Readable&&eR(s.body,e),d&&d.body&&d.body.emit("error",e)};if(c&&c.aborted){f();return}let h=function(){f(),b()},p=u(l);function b(){p.abort(),c&&c.removeEventListener("abort",h),clearTimeout(i)}c&&c.addEventListener("abort",h),s.timeout&&p.once("socket",function(e){i=setTimeout(function(){o(new M(`network timeout at: ${s.url}`,"request-timeout")),b()},s.timeout)}),p.on("error",function(e){o(new M(`request to ${s.url} failed, reason: ${e.message}`,"system",e)),d&&d.body&&eR(d.body,e),b()}),n=function(e){(!c||!c.aborted)&&d&&d.body&&eR(d.body,e)},p.on("socket",function(e){a=e}),p.on("response",function(e){let t=e.headers;"chunked"!==t["transfer-encoding"]||t["content-length"]||e.once("close",function(e){if(a&&a.listenerCount("data")>0&&!e){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",n(e)}})}),14>parseInt(process.version.substring(1))&&p.on("socket",function(e){e.addListener("close",function(t){let r=e.listenerCount("data")>0;if(d&&r&&!t&&!(c&&c.aborted)){let e=Error("Premature close");e.code="ERR_STREAM_PREMATURE_CLOSE",d.body.emit("error",e)}})}),p.on("response",function(e){clearTimeout(i);let t=function(e){let t=new eo;for(let r of Object.keys(e))if(!K.test(r)){if(Array.isArray(e[r]))for(let o of e[r])J.test(o)||(void 0===t[er][r]?t[er][r]=[o]:t[er][r].push(o));else J.test(e[r])||(t[er][r]=[e[r]])}return t}(e.headers);if(eT.isRedirect(e.statusCode)){let i=t.get("Location"),a=null;try{a=null===i?null:new ev(i,s.url).toString()}catch(e){if("manual"!==s.redirect){o(new M(`uri requested responds with an invalid redirect URL: ${i}`,"invalid-redirect")),b();return}}switch(s.redirect){case"error":o(new M(`uri requested responds with a redirect, redirect mode is set to error: ${s.url}`,"no-redirect")),b();return;case"manual":if(null!==a)try{t.set("Location",a)}catch(e){o(e)}break;case"follow":var n;if(null===a)break;if(s.counter>=s.follow){o(new M(`maximum redirect reached at: ${s.url}`,"max-redirect")),b();return}let l={headers:new eo(s.headers),follow:s.follow,counter:s.counter+1,agent:s.agent,compress:s.compress,method:s.method,body:s.body,signal:s.signal,timeout:s.timeout,size:s.size};if(!ew(s.url,a)||(n=s.url,new ev(a).protocol!==new ev(n).protocol))for(let e of["authorization","www-authenticate","cookie","cookie2"])l.headers.delete(e);if(303!==e.statusCode&&s.body&&null===G(s)){o(new M("Cannot follow redirect with body being a readable stream","unsupported-redirect")),b();return}(303===e.statusCode||(301===e.statusCode||302===e.statusCode)&&"POST"===s.method)&&(l.method="GET",l.body=void 0,l.headers.delete("content-length")),r(eT(new e_(a,l))),b();return}}e.once("end",function(){c&&c.removeEventListener("abort",h)});let a=e.pipe(new eS),l={url:s.url,status:e.statusCode,statusText:e.statusMessage,headers:t,size:s.size,timeout:s.timeout,counter:s.counter},u=t.get("Content-Encoding");if(!s.compress||"HEAD"===s.method||null===u||204===e.statusCode||304===e.statusCode){r(d=new ec(a,l));return}let f={flush:B.Z_SYNC_FLUSH,finishFlush:B.Z_SYNC_FLUSH};if("gzip"==u||"x-gzip"==u){r(d=new ec(a=a.pipe(B.createGunzip(f)),l));return}if("deflate"==u||"x-deflate"==u){let t=e.pipe(new eS);t.once("data",function(e){r(d=new ec(a=(15&e[0])==8?a.pipe(B.createInflate()):a.pipe(B.createInflateRaw()),l))}),t.on("end",function(){d||r(d=new ec(a,l))});return}if("br"==u&&"function"==typeof B.createBrotliDecompress){r(d=new ec(a=a.pipe(B.createBrotliDecompress()),l));return}r(d=new ec(a,l))}),function(e,t){let r=t.body;null===r?e.end():Q(r)?r.stream().pipe(e):Buffer.isBuffer(r)?(e.write(r),e.end()):r.pipe(e)}(p,s)})}function eR(e,t){e.destroy?e.destroy(t):(e.emit("error",t),e.end())}eT.isRedirect=function(e){return 301===e||302===e||303===e||307===e||308===e},eT.Promise=global.Promise;var eE=r(3837),eP=r(2169),ek=r(5620),eC=r(3687);let eq=e=>e instanceof eC.t;var eA=r(8692);let eO=(0,eE.deprecate)(()=>{},'Constructor "entries" argument is not spec-compliant and will be removed in next major release.');var ej=function(e,t,r,o){if("a"===r&&!o)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?o:"a"===r?o.call(e):o?o.value:t.get(e)};class ex{constructor(e){p.add(this),b.set(this,new Map),e&&(eO(),e.forEach(({name:e,value:t,fileName:r})=>this.append(e,t,r)))}static[(b=new WeakMap,p=new WeakSet,Symbol.hasInstance)](e){return!!(e&&(0,eA.m)(e.constructor)&&"FormData"===e[Symbol.toStringTag]&&(0,eA.m)(e.append)&&(0,eA.m)(e.set)&&(0,eA.m)(e.get)&&(0,eA.m)(e.getAll)&&(0,eA.m)(e.has)&&(0,eA.m)(e.delete)&&(0,eA.m)(e.entries)&&(0,eA.m)(e.values)&&(0,eA.m)(e.keys)&&(0,eA.m)(e[Symbol.iterator])&&(0,eA.m)(e.forEach))}append(e,t,r){ej(this,p,"m",m).call(this,{name:e,fileName:r,append:!0,rawValue:t,argsLength:arguments.length})}set(e,t,r){ej(this,p,"m",m).call(this,{name:e,fileName:r,append:!1,rawValue:t,argsLength:arguments.length})}get(e){let t=ej(this,b,"f").get(String(e));return t?t[0]:null}getAll(e){let t=ej(this,b,"f").get(String(e));return t?t.slice():[]}has(e){return ej(this,b,"f").has(String(e))}delete(e){ej(this,b,"f").delete(String(e))}*keys(){for(let e of ej(this,b,"f").keys())yield e}*entries(){for(let e of this.keys())for(let t of this.getAll(e))yield[e,t]}*values(){for(let[,e]of this)yield e}[(m=function({name:e,rawValue:t,append:r,fileName:o,argsLength:n}){let i;let a=r?"append":"set";if(n<2)throw TypeError(`Failed to execute '${a}' on 'FormData': 2 arguments required, but only ${n} present.`);if(e=String(e),(0,ek.z)(t))i=void 0===o?t:new eP.$([t],o,{type:t.type,lastModified:t.lastModified});else if(eq(t))i=new eP.$([t],void 0===o?"blob":o,{type:t.type});else if(o)throw TypeError(`Failed to execute '${a}' on 'FormData': parameter 2 is not of type 'Blob'.`);else i=String(t);let s=ej(this,b,"f").get(e);if(!s||!r)return void ej(this,b,"f").set(e,[i]);s.push(i)},Symbol.iterator)](){return this.entries()}forEach(e,t){for(let[r,o]of this)e.call(t,o,r,this)}get[Symbol.toStringTag](){return"FormData"}[eE.inspect.custom](){return this[Symbol.toStringTag]}}var eL=r(5685),eW=r(3843),eB=r(7561);let e$="abcdefghijklmnopqrstuvwxyz0123456789",eI=function(){let e=16,t="";for(;e--;)t+=e$[Math.random()*e$.length<<0];return t},ez=e=>Object.prototype.toString.call(e).slice(8,-1).toLowerCase(),eF=function(e){if("object"!==ez(e))return!1;let t=Object.getPrototypeOf(e);return null==t||(t.constructor&&t.constructor.toString())===Object.toString()},eM=e=>String(e).replace(/\r|\n/g,(e,t,r)=>"\r"===e&&"\n"!==r[t+1]||"\n"===e&&"\r"!==r[t-1]?"\r\n":e),eD=e=>String(e).replace(/\r/g,"%0D").replace(/\n/g,"%0A").replace(/"/g,"%22"),eN=e=>"function"==typeof e,eU=e=>!!(e&&"object"==typeof e&&eN(e.constructor)&&"File"===e[Symbol.toStringTag]&&eN(e.stream)&&null!=e.name&&null!=e.size&&null!=e.lastModified),eH=e=>!!(e&&eN(e.constructor)&&"FormData"===e[Symbol.toStringTag]&&eN(e.append)&&eN(e.getAll)&&eN(e.entries)&&eN(e[Symbol.iterator]));var eV=function(e,t,r,o,n){if("m"===o)throw TypeError("Private method is not writable");if("a"===o&&!n)throw TypeError("Private accessor was defined without a setter");if("function"==typeof t?e!==t||!n:!t.has(e))throw TypeError("Cannot write private member to an object whose class did not declare it");return"a"===o?n.call(e,r):n?n.value=r:t.set(e,r),r},eQ=function(e,t,r,o){if("a"===r&&!o)throw TypeError("Private accessor was defined without a getter");if("function"==typeof t?e!==t||!o:!t.has(e))throw TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?o:"a"===r?o.call(e):o?o.value:t.get(e)};let eX={enableAdditionalHeaders:!1};class eY{constructor(e,t,r){let o;if(y.add(this),_.set(this,"\r\n"),g.set(this,void 0),v.set(this,void 0),S.set(this,"-".repeat(2)),w.set(this,new TextEncoder),T.set(this,void 0),R.set(this,void 0),E.set(this,void 0),!eH(e))throw TypeError("Expected first argument to be a FormData instance.");if(eF(t)?r=t:o=t,o||(o=eI()),"string"!=typeof o)throw TypeError("Expected boundary argument to be a string.");if(r&&!eF(r))throw TypeError("Expected options argument to be an object.");eV(this,R,e,"f"),eV(this,E,{...eX,...r},"f"),eV(this,g,eQ(this,w,"f").encode(eQ(this,_,"f")),"f"),eV(this,v,eQ(this,g,"f").byteLength,"f"),this.boundary=`form-data-boundary-${o}`,this.contentType=`multipart/form-data; boundary=${this.boundary}`,eV(this,T,eQ(this,w,"f").encode(`${eQ(this,S,"f")}${this.boundary}${eQ(this,S,"f")}${eQ(this,_,"f").repeat(2)}`),"f"),this.contentLength=String(this.getContentLength()),this.headers=Object.freeze({"Content-Type":this.contentType,"Content-Length":this.contentLength}),Object.defineProperties(this,{boundary:{writable:!1,configurable:!1},contentType:{writable:!1,configurable:!1},contentLength:{writable:!1,configurable:!1},headers:{writable:!1,configurable:!1}})}getContentLength(){let e=0;for(let[t,r]of eQ(this,R,"f")){let o=eU(r)?r:eQ(this,w,"f").encode(eM(r));e+=eQ(this,y,"m",P).call(this,t,o).byteLength+(eU(o)?o.size:o.byteLength)+eQ(this,v,"f")}return e+eQ(this,T,"f").byteLength}*values(){for(let[e,t]of eQ(this,R,"f").entries()){let r=eU(t)?t:eQ(this,w,"f").encode(eM(t));yield eQ(this,y,"m",P).call(this,e,r),yield r,yield eQ(this,g,"f")}yield eQ(this,T,"f")}async *encode(){for(let e of this.values())eU(e)?yield*e.stream():yield e}[(_=new WeakMap,g=new WeakMap,v=new WeakMap,S=new WeakMap,w=new WeakMap,T=new WeakMap,R=new WeakMap,E=new WeakMap,y=new WeakSet,P=function(e,t){let r="";return r+=`${eQ(this,S,"f")}${this.boundary}${eQ(this,_,"f")}Content-Disposition: form-data; name="${eD(e)}"`,eU(t)&&(r+=`; filename="${eD(t.name)}"${eQ(this,_,"f")}Content-Type: ${t.type||"application/octet-stream"}`),!0===eQ(this,E,"f").enableAdditionalHeaders&&(r+=`${eQ(this,_,"f")}Content-Length: ${eU(t)?t.size:t.byteLength}`),eQ(this,w,"f").encode(`${r}${eQ(this,_,"f").repeat(2)}`)},Symbol.iterator)](){return this.values()}[Symbol.asyncIterator](){return this.encode()}}var eG=r(4492);class eK{constructor(e){this.body=e}get[Symbol.toStringTag](){return"MultipartBody"}}var eJ=r(6789);let eZ=!1;async function e0(e,...t){let{fileFromPath:o}=await r.e(958).then(r.bind(r,1958));return eZ||(console.warn(`fileFromPath is deprecated; use fs.createReadStream(${JSON.stringify(e)}) instead`),eZ=!0),await o(e,...t)}let e1=new eL({keepAlive:!0,timeout:3e5}),e3=new eL.HttpsAgent({keepAlive:!0,timeout:3e5});async function e8(e,t){let r=new eY(e),o=new eK(eG.Readable.from(r)),n={...t.headers,...r.headers,"Content-Length":r.contentLength};return{...t,body:o,headers:n}}i||function(e,t={auto:!1}){if(A)throw Error(`you must \`import 'groq-sdk/shims/${e.kind}'\` before importing anything else from groq-sdk`);if(i)throw Error(`can't \`import 'groq-sdk/shims/${e.kind}'\` after \`import 'groq-sdk/shims/${i}'\``);A=t.auto,i=e.kind,a=e.fetch,e.Request,e.Response,e.Headers,s=e.FormData,e.Blob,l=e.File,u=e.ReadableStream,c=e.getMultipartRequestOptions,d=e.getDefaultAgent,f=e.fileFromPath,h=e.isFsReadStream}(("undefined"==typeof AbortController&&(globalThis.AbortController=eW.AbortController),{kind:"node",fetch:eT,Request:e_,Response:ec,Headers:eo,FormData:ex,Blob:eC.t,File:eP.$,ReadableStream:eJ.ReadableStream,getMultipartRequestOptions:e8,getDefaultAgent:e=>e.startsWith("https")?e3:e1,fileFromPath:e0,isFsReadStream:e=>e instanceof eB.ReadStream}),{auto:!0});class e2 extends Error{}class e6 extends e2{constructor(e,t,r,o){super(`${e6.makeMessage(e,t,r)}`),this.status=e,this.headers=o,this.error=t}static makeMessage(e,t,r){let o=t?.message?"string"==typeof t.message?t.message:JSON.stringify(t.message):t?JSON.stringify(t):r;return e&&o?`${e} ${o}`:e?`${e} status code (no body)`:o||"(no status code or body)"}static generate(e,t,r,o){return e?400===e?new e9(e,t,r,o):401===e?new te(e,t,r,o):403===e?new tt(e,t,r,o):404===e?new tr(e,t,r,o):409===e?new to(e,t,r,o):422===e?new tn(e,t,r,o):429===e?new ti(e,t,r,o):e>=500?new ta(e,t,r,o):new e6(e,t,r,o):new e5({cause:tI(t)})}}class e4 extends e6{constructor({message:e}={}){super(void 0,void 0,e||"Request was aborted.",void 0),this.status=void 0}}class e5 extends e6{constructor({message:e,cause:t}){super(void 0,void 0,e||"Connection error.",void 0),this.status=void 0,t&&(this.cause=t)}}class e7 extends e5{constructor({message:e}={}){super({message:e??"Request timed out."})}}class e9 extends e6{constructor(){super(...arguments),this.status=400}}class te extends e6{constructor(){super(...arguments),this.status=401}}class tt extends e6{constructor(){super(...arguments),this.status=403}}class tr extends e6{constructor(){super(...arguments),this.status=404}}class to extends e6{constructor(){super(...arguments),this.status=409}}class tn extends e6{constructor(){super(...arguments),this.status=422}}class ti extends e6{constructor(){super(...arguments),this.status=429}}class ta extends e6{}class ts{constructor(e,t){this.iterator=e,this.controller=t}static fromSSEResponse(e,t){let r=!1,o=new tl;async function*n(){if(!e.body)throw t.abort(),new e2("Attempted to iterate over a response with no body");let r=new tu;for await(let t of tc(e.body))for(let e of r.decode(t)){let t=o.decode(e);t&&(yield t)}for(let e of r.flush()){let t=o.decode(e);t&&(yield t)}}return new ts(async function*(){if(r)throw Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let e=!1;try{for await(let t of n())if(!e){if(t.data.startsWith("[DONE]")){e=!0;continue}if(null===t.event){let e;try{e=JSON.parse(t.data)}catch(e){throw console.error("Could not parse message into JSON:",t.data),console.error("From chunk:",t.raw),e}if(e&&e.error)throw new e6(void 0,e.error,void 0,void 0);yield e}}e=!0}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw e}finally{e||t.abort()}},t)}static fromReadableStream(e,t){let r=!1;async function*o(){let t=new tu;for await(let r of tc(e))for(let e of t.decode(r))yield e;for(let e of t.flush())yield e}return new ts(async function*(){if(r)throw Error("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");r=!0;let e=!1;try{for await(let t of o())!e&&t&&(yield JSON.parse(t));e=!0}catch(e){if(e instanceof Error&&"AbortError"===e.name)return;throw e}finally{e||t.abort()}},t)}[Symbol.asyncIterator](){return this.iterator()}tee(){let e=[],t=[],r=this.iterator(),o=o=>({next:()=>{if(0===o.length){let o=r.next();e.push(o),t.push(o)}return o.shift()}});return[new ts(()=>o(e),this.controller),new ts(()=>o(t),this.controller)]}toReadableStream(){let e;let t=this,r=new TextEncoder;return new u({async start(){e=t[Symbol.asyncIterator]()},async pull(t){try{let{value:o,done:n}=await e.next();if(n)return t.close();let i=r.encode(JSON.stringify(o)+"\n");t.enqueue(i)}catch(e){t.error(e)}},async cancel(){await e.return?.()}})}}class tl{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(e){if(e.endsWith("\r")&&(e=e.substring(0,e.length-1)),!e){if(!this.event&&!this.data.length)return null;let e={event:this.event,data:this.data.join("\n"),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],e}if(this.chunks.push(e),e.startsWith(":"))return null;let[t,r,o]=function(e,t){let r=e.indexOf(t);return -1!==r?[e.substring(0,r),t,e.substring(r+t.length)]:[e,"",""]}(e,":");return o.startsWith(" ")&&(o=o.substring(1)),"event"===t?this.event=o:"data"===t&&this.data.push(o),null}}class tu{constructor(){this.buffer=[],this.trailingCR=!1}decode(e){let t=this.decodeText(e);if(this.trailingCR&&(t="\r"+t,this.trailingCR=!1),t.endsWith("\r")&&(this.trailingCR=!0,t=t.slice(0,-1)),!t)return[];let r=tu.NEWLINE_CHARS.has(t[t.length-1]||""),o=t.split(tu.NEWLINE_REGEXP);return 1!==o.length||r?(this.buffer.length>0&&(o=[this.buffer.join("")+o[0],...o.slice(1)],this.buffer=[]),r||(this.buffer=[o.pop()||""]),o):(this.buffer.push(o[0]),[])}decodeText(e){if(null==e)return"";if("string"==typeof e)return e;if("undefined"!=typeof Buffer){if(e instanceof Buffer)return e.toString();if(e instanceof Uint8Array)return Buffer.from(e).toString();throw new e2(`Unexpected: received non-Uint8Array (${e.constructor.name}) stream chunk in an environment with a global "Buffer" defined, which this library assumes to be Node. Please report this error.`)}if("undefined"!=typeof TextDecoder){if(e instanceof Uint8Array||e instanceof ArrayBuffer)return this.textDecoder??(this.textDecoder=new TextDecoder("utf8")),this.textDecoder.decode(e);throw new e2(`Unexpected: received non-Uint8Array/ArrayBuffer (${e.constructor.name}) in a web platform. Please report this error.`)}throw new e2("Unexpected: neither Buffer nor TextDecoder are available as globals. Please report this error.")}flush(){if(!this.buffer.length&&!this.trailingCR)return[];let e=[this.buffer.join("")];return this.buffer=[],this.trailingCR=!1,e}}function tc(e){if(e[Symbol.asyncIterator])return e;let t=e.getReader();return{async next(){try{let e=await t.read();return e?.done&&t.releaseLock(),e}catch(e){throw t.releaseLock(),e}},async return(){let e=t.cancel();return t.releaseLock(),await e,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}tu.NEWLINE_CHARS=new Set(["\n","\r","\v","\f","\x1c","\x1d","\x1e","\x85","\u2028","\u2029"]),tu.NEWLINE_REGEXP=/\r\n|[\n\r\x0b\x0c\x1c\x1d\x1e\x85\u2028\u2029]/g;let td=e=>null!=e&&"object"==typeof e&&"string"==typeof e.url&&"function"==typeof e.blob,tf=e=>null!=e&&"object"==typeof e&&"string"==typeof e.name&&"number"==typeof e.lastModified&&th(e),th=e=>null!=e&&"object"==typeof e&&"number"==typeof e.size&&"string"==typeof e.type&&"function"==typeof e.text&&"function"==typeof e.slice&&"function"==typeof e.arrayBuffer,tp=e=>tf(e)||td(e)||h(e);async function tb(e,t,r={}){var o;if(td(e=await e)){let o=await e.blob();return t||(t=new URL(e.url).pathname.split(/[\\/]/).pop()??"unknown_file"),new l([o],t,r)}let n=await tm(e);if(t||(t=(ty((o=e).name)||ty(o.filename)||ty(o.path)?.split(/[\\/]/).pop())??"unknown_file"),!r.type){let e=n[0]?.type;"string"==typeof e&&(r={...r,type:e})}return new l(n,t,r)}async function tm(e){let t=[];if("string"==typeof e||ArrayBuffer.isView(e)||e instanceof ArrayBuffer)t.push(e);else if(th(e))t.push(await e.arrayBuffer());else if(t_(e))for await(let r of e)t.push(r);else throw Error(`Unexpected data type: ${typeof e}; constructor: ${e?.constructor?.name}; props: ${function(e){let t=Object.getOwnPropertyNames(e);return`[${t.map(e=>`"${e}"`).join(", ")}]`}(e)}`);return t}let ty=e=>"string"==typeof e?e:"undefined"!=typeof Buffer&&e instanceof Buffer?String(e):void 0,t_=e=>null!=e&&"object"==typeof e&&"function"==typeof e[Symbol.asyncIterator],tg=e=>e&&"object"==typeof e&&e.body&&"MultipartBody"===e[Symbol.toStringTag],tv=async e=>{let t=await tS(e.body);return c(t,e)},tS=async e=>{let t=new s;return await Promise.all(Object.entries(e||{}).map(([e,r])=>tT(t,e,r))),t},tw=e=>{if(tp(e))return!0;if(Array.isArray(e))return e.some(tw);if(e&&"object"==typeof e){for(let t in e)if(tw(e[t]))return!0}return!1},tT=async(e,t,r)=>{if(void 0!==r){if(null==r)throw TypeError(`Received null for "${t}"; to pass null in FormData, you must use the string 'null'`);if("string"==typeof r||"number"==typeof r||"boolean"==typeof r)e.append(t,String(r));else if(tp(r)){let o=await tb(r);e.append(t,o)}else if(Array.isArray(r))await Promise.all(r.map(r=>tT(e,t+"[]",r)));else if("object"==typeof r)await Promise.all(Object.entries(r).map(([r,o])=>tT(e,`${t}[${r}]`,o)));else throw TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${r} instead`)}};async function tR(e){let{response:t}=e;if(e.options.stream)return(tM("response",t.status,t.url,t.headers,t.body),e.options.__streamClass)?e.options.__streamClass.fromSSEResponse(t,e.controller):ts.fromSSEResponse(t,e.controller);if(204===t.status)return null;if(e.options.__binaryResponse)return t;let r=t.headers.get("content-type");if(r?.includes("application/json")||r?.includes("application/vnd.api+json")){let e=await t.json();return tM("response",t.status,t.url,t.headers,e),e}let o=await t.text();return tM("response",t.status,t.url,t.headers,o),o}class tE extends Promise{constructor(e,t=tR){super(e=>{e(null)}),this.responsePromise=e,this.parseResponse=t}_thenUnwrap(e){return new tE(this.responsePromise,async t=>e(await this.parseResponse(t)))}asResponse(){return this.responsePromise.then(e=>e.response)}async withResponse(){let[e,t]=await Promise.all([this.parse(),this.asResponse()]);return{data:e,response:t}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(this.parseResponse)),this.parsedPromise}then(e,t){return this.parse().then(e,t)}catch(e){return this.parse().catch(e)}finally(e){return this.parse().finally(e)}}class tP{constructor({baseURL:e,maxRetries:t=2,timeout:r=6e4,httpAgent:o,fetch:n}){this.baseURL=e,this.maxRetries=t$("maxRetries",t),this.timeout=t$("timeout",r),this.httpAgent=o,this.fetch=n??a}authHeaders(e){return{}}defaultHeaders(e){return{Accept:"application/json","Content-Type":"application/json","User-Agent":this.getUserAgent(),...tj(),...this.authHeaders(e)}}validateHeaders(e,t){}defaultIdempotencyKey(){return`stainless-node-retry-${tD()}`}get(e,t){return this.methodRequest("get",e,t)}post(e,t){return this.methodRequest("post",e,t)}patch(e,t){return this.methodRequest("patch",e,t)}put(e,t){return this.methodRequest("put",e,t)}delete(e,t){return this.methodRequest("delete",e,t)}methodRequest(e,t,r){return this.request(Promise.resolve(r).then(r=>({method:e,path:t,...r})))}getAPIList(e,t,r){return this.requestAPIList(t,{method:"get",path:e,...r})}calculateContentLength(e){if("string"==typeof e){if("undefined"!=typeof Buffer)return Buffer.byteLength(e,"utf8").toString();if("undefined"!=typeof TextEncoder)return new TextEncoder().encode(e).length.toString()}return null}buildRequest(e){let{method:t,path:r,query:o,headers:n={}}=e,i=tg(e.body)?e.body.body:e.body?JSON.stringify(e.body,null,2):null,a=this.calculateContentLength(i),s=this.buildURL(r,o);"timeout"in e&&t$("timeout",e.timeout);let l=e.timeout??this.timeout,u=e.httpAgent??this.httpAgent??d(s),c=l+1e3;"number"==typeof u?.options?.timeout&&c>(u.options.timeout??0)&&(u.options.timeout=c),this.idempotencyHeader&&"get"!==t&&(e.idempotencyKey||(e.idempotencyKey=this.defaultIdempotencyKey()),n[this.idempotencyHeader]=e.idempotencyKey);let f=this.buildHeaders({options:e,headers:n,contentLength:a});return{req:{method:t,...i&&{body:i},headers:f,...u&&{agent:u},signal:e.signal??null},url:s,timeout:l}}buildHeaders({options:e,headers:t,contentLength:r}){let o={};return r&&(o["content-length"]=r),tF(o,this.defaultHeaders(e)),tF(o,t),tg(e.body)&&"node"!==i&&delete o["content-type"],this.validateHeaders(o,t),o}async prepareOptions(e){}async prepareRequest(e,{url:t,options:r}){}parseHeaders(e){return e?Symbol.iterator in e?Object.fromEntries(Array.from(e).map(e=>[...e])):{...e}:{}}makeStatusError(e,t,r,o){return e6.generate(e,t,r,o)}request(e,t=null){return new tE(this.makeRequest(e,t))}async makeRequest(e,t){let r=await e;null==t&&(t=r.maxRetries??this.maxRetries),await this.prepareOptions(r);let{req:o,url:n,timeout:i}=this.buildRequest(r);if(await this.prepareRequest(o,{url:n,options:r}),tM("request",n,r,o.headers),r.signal?.aborted)throw new e4;let a=new AbortController,s=await this.fetchWithTimeout(n,o,i,a).catch(tI);if(s instanceof Error){if(r.signal?.aborted)throw new e4;if(t)return this.retryRequest(r,t);if("AbortError"===s.name)throw new e7;throw new e5({cause:s})}let l=tC(s.headers);if(!s.ok){if(t&&this.shouldRetry(s)){let e=`retrying, ${t} attempts remaining`;return tM(`response (error; ${e})`,s.status,n,l),this.retryRequest(r,t,l)}let e=await s.text().catch(e=>tI(e).message),o=tx(e),i=o?void 0:e,a=t?"(error; no more retries left)":"(error; not retryable)";throw tM(`response (error; ${a})`,s.status,n,l,i),this.makeStatusError(s.status,o,i,l)}return{response:s,options:r,controller:a}}requestAPIList(e,t){return new tk(this,this.makeRequest(t,null),e)}buildURL(e,t){let r=new URL(tW(e)?e:this.baseURL+(this.baseURL.endsWith("/")&&e.startsWith("/")?e.slice(1):e)),o=this.defaultQuery();return!function(e){if(!e)return!0;for(let t in e)return!1;return!0}(o)&&(t={...o,...t}),"object"==typeof t&&t&&!Array.isArray(t)&&(r.search=this.stringifyQuery(t)),r.toString()}stringifyQuery(e){return Object.entries(e).filter(([e,t])=>void 0!==t).map(([e,t])=>{if("string"==typeof t||"number"==typeof t||"boolean"==typeof t)return`${encodeURIComponent(e)}=${encodeURIComponent(t)}`;if(null===t)return`${encodeURIComponent(e)}=`;throw new e2(`Cannot stringify type ${typeof t}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}async fetchWithTimeout(e,t,r,o){let{signal:n,...i}=t||{};n&&n.addEventListener("abort",()=>o.abort());let a=setTimeout(()=>o.abort(),r);return this.getRequestClient().fetch.call(void 0,e,{signal:o.signal,...i}).finally(()=>{clearTimeout(a)})}getRequestClient(){return{fetch:this.fetch}}shouldRetry(e){let t=e.headers.get("x-should-retry");return"true"===t||"false"!==t&&(408===e.status||409===e.status||429===e.status||e.status>=500)}async retryRequest(e,t,r){let o;let n=r?.["retry-after-ms"];if(n){let e=parseFloat(n);Number.isNaN(e)||(o=e)}let i=r?.["retry-after"];if(i&&!o){let e=parseFloat(i);o=Number.isNaN(e)?Date.parse(i)-Date.now():1e3*e}if(!(o&&0<=o&&o<6e4)){let r=e.maxRetries??this.maxRetries;o=this.calculateDefaultRetryTimeoutMillis(t,r)}return await tB(o),this.makeRequest(e,t-1)}calculateDefaultRetryTimeoutMillis(e,t){return Math.min(.5*Math.pow(2,t-e),8)*(1-.25*Math.random())*1e3}getUserAgent(){return`${this.constructor.name}/JS ${q}`}}new WeakMap,Symbol.asyncIterator;class tk extends tE{constructor(e,t,r){super(t,async t=>new r(e,t.response,await tR(t),t.options))}async *[Symbol.asyncIterator](){for await(let e of(await this))yield e}}let tC=e=>new Proxy(Object.fromEntries(e.entries()),{get(e,t){let r=t.toString();return e[r.toLowerCase()]||e[r]}}),tq=()=>{if("undefined"!=typeof Deno&&null!=Deno.build)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":q,"X-Stainless-OS":tO(Deno.build.os),"X-Stainless-Arch":tA(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":Deno.version};if("undefined"!=typeof EdgeRuntime)return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":q,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":process.version};if("[object process]"===Object.prototype.toString.call("undefined"!=typeof process?process:0))return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":q,"X-Stainless-OS":tO(process.platform),"X-Stainless-Arch":tA(process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":process.version};let e=function(){if("undefined"==typeof navigator||!navigator)return null;for(let{key:e,pattern:t}of[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}]){let r=t.exec(navigator.userAgent);if(r){let t=r[1]||0,o=r[2]||0,n=r[3]||0;return{browser:e,version:`${t}.${o}.${n}`}}}return null}();return e?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":q,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${e.browser}`,"X-Stainless-Runtime-Version":e.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":q,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}},tA=e=>"x32"===e?"x32":"x86_64"===e||"x64"===e?"x64":"arm"===e?"arm":"aarch64"===e||"arm64"===e?"arm64":e?`other:${e}`:"unknown",tO=e=>(e=e.toLowerCase()).includes("ios")?"iOS":"android"===e?"Android":"darwin"===e?"MacOS":"win32"===e?"Windows":"freebsd"===e?"FreeBSD":"openbsd"===e?"OpenBSD":"linux"===e?"Linux":e?`Other:${e}`:"Unknown",tj=()=>n??(n=tq()),tx=e=>{try{return JSON.parse(e)}catch(e){return}},tL=RegExp("^(?:[a-z]+:)?//","i"),tW=e=>tL.test(e),tB=e=>new Promise(t=>setTimeout(t,e)),t$=(e,t)=>{if("number"!=typeof t||!Number.isInteger(t))throw new e2(`${e} must be an integer`);if(t<0)throw new e2(`${e} must be a positive integer`);return t},tI=e=>e instanceof Error?e:Error(e),tz=e=>"undefined"!=typeof process?process.env?.[e]?.trim()??void 0:"undefined"!=typeof Deno?Deno.env?.get?.(e)?.trim():void 0;function tF(e,t){for(let r in t){if(!Object.prototype.hasOwnProperty.call(t,r))continue;let o=r.toLowerCase();if(!o)continue;let n=t[r];null===n?delete e[o]:void 0!==n&&(e[o]=n)}}function tM(e,...t){"undefined"!=typeof process&&"true"===process.env.DEBUG&&console.log(`Groq:DEBUG:${e}`,...t)}let tD=()=>"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,e=>{let t=16*Math.random()|0;return("x"===e?t:3&t|8).toString(16)}),tN=()=>!1;class tU{constructor(e){this._client=e}}class tH extends tU{create(e,t){return this._client.post("/openai/v1/chat/completions",{body:e,...t,stream:e.stream??!1})}}tH||(tH={});class tV extends tU{constructor(){super(...arguments),this.completions=new tH(this._client)}}(tV||(tV={})).Completions=tH;class tQ extends tU{create(e,t){return this._client.post("/openai/v1/audio/transcriptions",tv({body:e,...t}))}}tQ||(tQ={});class tX extends tU{create(e,t){return this._client.post("/openai/v1/audio/translations",tv({body:e,...t}))}}tX||(tX={});class tY extends tU{constructor(){super(...arguments),this.transcriptions=new tQ(this._client),this.translations=new tX(this._client)}}!function(e){e.Transcriptions=tQ,e.Translations=tX}(tY||(tY={}));class tG extends tU{retrieve(e,t){return this._client.get(`/openai/v1/models/${e}`,t)}list(e){return this._client.get("/openai/v1/models",e)}delete(e,t){return this._client.delete(`/openai/v1/models/${e}`,{...t,headers:{Accept:"*/*",...t?.headers}})}}tG||(tG={});class tK extends tP{constructor({baseURL:e=tz("GROQ_BASE_URL"),apiKey:t=tz("GROQ_API_KEY"),...r}={}){if(void 0===t)throw new e2("The GROQ_API_KEY environment variable is missing or empty; either provide it, or instantiate the Groq client with an apiKey option, like new Groq({ apiKey: 'My API Key' }).");let o={apiKey:t,...r,baseURL:e||"https://api.groq.com"};if(!o.dangerouslyAllowBrowser&&tN())throw new e2("This is disabled by default, as it risks exposing your secret API credentials to attackers.\nIf you understand the risks and have appropriate mitigations in place,\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\n\nnew Groq({ dangerouslyAllowBrowser: true })");super({baseURL:o.baseURL,timeout:o.timeout??6e4,httpAgent:o.httpAgent,maxRetries:o.maxRetries,fetch:o.fetch}),this.chat=new tV(this),this.audio=new tY(this),this.models=new tG(this),this._options=o,this.apiKey=t}defaultQuery(){return this._options.defaultQuery}defaultHeaders(e){return{...super.defaultHeaders(e),...this._options.defaultHeaders}}authHeaders(e){return{Authorization:`Bearer ${this.apiKey}`}}}k=tK,tK.Groq=k,tK.GroqError=e2,tK.APIError=e6,tK.APIConnectionError=e5,tK.APIConnectionTimeoutError=e7,tK.APIUserAbortError=e4,tK.NotFoundError=tr,tK.ConflictError=to,tK.RateLimitError=ti,tK.BadRequestError=e9,tK.AuthenticationError=te,tK.InternalServerError=ta,tK.PermissionDeniedError=tt,tK.UnprocessableEntityError=tn;let{GroqError:tJ,APIError:tZ,APIConnectionError:t0,APIConnectionTimeoutError:t1,APIUserAbortError:t3,NotFoundError:t8,ConflictError:t2,RateLimitError:t6,BadRequestError:t4,AuthenticationError:t5,InternalServerError:t7,PermissionDeniedError:t9,UnprocessableEntityError:re}=C;!function(e){e.toFile=tb,e.fileFromPath=f,e.Chat=tV,e.Audio=tY,e.Models=tG}(tK||(tK={}));let rt=tK}};
// Core Types for SEO SAAS Application

export interface User {
  id: string;
  email: string;
  name: string;
  subscription_tier: 'free' | 'pro' | 'enterprise';
  created_at: string;
  updated_at: string;
}

export interface Project {
  id: string;
  user_id: string;
  name: string;
  description?: string;
  target_keywords: string[];
  industry: string;
  country: string;
  language: string;
  created_at: string;
  updated_at: string;
}

export interface ContentGeneration {
  id: string;
  project_id: string;
  title: string;
  content: string;
  keywords: string[];
  word_count: number;
  seo_score: number;
  competitor_analysis: CompetitorAnalysis[];
  created_at: string;
  updated_at: string;
}

export interface CompetitorAnalysis {
  id: string;
  url: string;
  title: string;
  word_count: number;
  keyword_density: KeywordDensity[];
  headings: HeadingStructure[];
  meta_description: string;
  seo_score: number;
}

export interface KeywordDensity {
  keyword: string;
  count: number;
  density: number;
}

export interface HeadingStructure {
  level: number;
  text: string;
  count: number;
}

export interface SerpResult {
  position: number;
  title: string;
  link: string;
  snippet: string;
  displayed_link: string;
}

export interface ContentGenerationRequest {
  keyword: string;
  content_type: 'article' | 'product' | 'service' | 'landing_page';
  target_audience: string;
  tone: 'professional' | 'casual' | 'friendly' | 'authoritative';
  word_count: number;
  include_faq: boolean;
  include_cta: boolean;
  industry: string;
  country: string;
  language: string;
}

export interface SeoOptimization {
  keyword_density: number;
  heading_optimization: boolean;
  meta_tags: {
    title: string;
    description: string;
    keywords: string[];
  };
  internal_links: string[];
  external_links: string[];
  schema_markup: string;
  readability_score: number;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface GroqResponse {
  choices: Array<{
    message: {
      content: string;
    };
  }>;
}

export interface SerperResponse {
  organic: SerpResult[];
  peopleAlsoAsk?: Array<{
    question: string;
    answer: string;
  }>;
  relatedSearches?: Array<{
    query: string;
  }>;
}

export interface FormState {
  loading: boolean;
  error: string | null;
  success: boolean;
}

export interface DashboardStats {
  total_projects: number;
  content_generated: number;
  seo_score_average: number;
  api_usage: {
    groq: number;
    serper: number;
  };
}
// Serper API Integration for SERP Analysis
// Real-time competitor data extraction and search result analysis

import { config } from '../config';
import { withErrorHandling } from '../error-handler';
import { logApiUsage } from '../supabase';

interface SerperSearchRequest {
  query: string;
  location?: string;
  country?: string;
  language?: string;
  device?: 'desktop' | 'mobile';
  num?: number; // Number of results (max 100)
  page?: number;
  type?: 'search' | 'images' | 'videos' | 'news' | 'shopping';
  tbs?: string; // Time-based search filters
  safe?: 'active' | 'off';
  autocorrect?: boolean;
}

interface SerperSearchResult {
  searchParameters: SearchParameters;
  organic: OrganicResult[];
  peopleAlsoAsk?: PeopleAlsoAskResult[];
  relatedSearches?: RelatedSearchResult[];
  answerBox?: AnswerBox;
  knowledgeGraph?: KnowledgeGraph;
  credits: number;
}

interface SearchParameters {
  q: string;
  type: string;
  engine: string;
  location?: string;
  country?: string;
  language?: string;
  device?: string;
  num?: number;
}

interface OrganicResult {
  position: number;
  title: string;
  link: string;
  snippet: string;
  domain: string;
  date?: string;
  sitelinks?: Sitelink[];
  attributes?: { [key: string]: string };
}

interface Sitelink {
  title: string;
  link: string;
}

interface PeopleAlsoAskResult {
  question: string;
  snippet: string;
  title: string;
  link: string;
}

interface RelatedSearchResult {
  query: string;
}

interface AnswerBox {
  type: string;
  title?: string;
  snippet?: string;
  source?: string;
  link?: string;
}

interface KnowledgeGraph {
  title: string;
  type: string;
  description?: string;
  descriptionSource?: string;
  attributes?: { [key: string]: string };
  images?: string[];
}

// Competitor analysis interfaces
interface CompetitorAnalysis {
  query: string;
  location: string;
  totalResults: number;
  averageWordCount: number;
  averageHeadings: { [key: string]: number };
  commonKeywords: string[];
  averageKeywordDensity: number;
  contentGaps: string[];
  topCompetitors: CompetitorData[];
  searchFeatures: SearchFeature[];
  difficulty: 'low' | 'medium' | 'high' | 'very-high';
}

interface CompetitorData {
  position: number;
  domain: string;
  title: string;
  url: string;
  snippet: string;
  estimatedTraffic?: number;
  domainAuthority?: number;
  backlinks?: number;
  wordCount?: number;
  headingStructure?: { [key: string]: number };
  keywordDensity?: number;
  contentScore?: number;
  technicalScore?: number;
}

interface SearchFeature {
  type: 'featured_snippet' | 'people_also_ask' | 'knowledge_graph' | 'local_pack' | 'image_pack' | 'video';
  present: boolean;
  position?: number;
  content?: any;
}

// Cache interface
interface CacheEntry {
  data: any;
  timestamp: number;
  ttl: number;
}

export class SerperService {
  private apiKey: string;
  private baseUrl: string;
  private cache = new Map<string, CacheEntry>();
  
  // Cache TTL settings (in milliseconds)
  private readonly CACHE_TTL = {
    search: 15 * 60 * 1000,    // 15 minutes for search results
    analysis: 30 * 60 * 1000,  // 30 minutes for competitor analysis
    keywords: 60 * 60 * 1000,  // 1 hour for keyword data
  };

  constructor() {
    this.apiKey = config.apis.serper.apiKey;
    this.baseUrl = config.apis.serper.baseUrl;
    
    // Start cache cleanup interval
    setInterval(() => this.cleanupCache(), 5 * 60 * 1000); // Every 5 minutes
  }

  // Main search method
  async search(
    request: SerperSearchRequest,
    userId?: string
  ): Promise<SerperSearchResult> {
    return withErrorHandling(
      'serper',
      async () => {
        const startTime = Date.now();
        
        // Check cache first
        const cacheKey = this.generateCacheKey('search', request);
        const cached = this.getFromCache(cacheKey);
        if (cached) {
          console.log('Returning cached Serper search results');
          return cached;
        }

        console.log('Fetching fresh SERP data from Serper...', {
          query: request.query,
          location: request.location,
          device: request.device,
        });

        // Prepare request body
        const requestBody = {
          q: request.query,
          ...request.location && { location: request.location },
          ...request.country && { gl: request.country },
          ...request.language && { hl: request.language },
          ...request.device && { device: request.device },
          num: request.num || 10,
          ...request.page && { start: (request.page - 1) * (request.num || 10) },
          ...request.type && request.type !== 'search' && { type: request.type },
          ...request.tbs && { tbs: request.tbs },
          ...request.safe && { safe: request.safe },
          autocorrect: request.autocorrect !== false,
        };

        // Make API call
        const response = await fetch(`${this.baseUrl}/search`, {
          method: 'POST',
          headers: {
            'X-API-KEY': this.apiKey,
            'Content-Type': 'application/json',
          },
          body: JSON.stringify(requestBody),
        });

        if (!response.ok) {
          throw new Error(`Serper API error: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        const responseTime = Date.now() - startTime;

        // Log API usage
        if (userId) {
          await logApiUsage({
            user_id: userId,
            api_name: 'serper',
            endpoint: 'search',
            method: 'POST',
            tokens_used: 0, // Serper doesn't use tokens
            cost: this.calculateCost(data.credits || 1),
            response_time_ms: responseTime,
            status_code: response.status,
          });
        }

        // Cache the result
        this.setCache(cacheKey, data, this.CACHE_TTL.search);

        console.log('Serper search completed', {
          responseTime,
          resultsCount: data.organic?.length || 0,
          credits: data.credits,
        });

        return data;
      },
      {
        method: 'POST',
        endpoint: 'search',
        query: request.query,
        ...request,
      },
      userId
    );
  }

  // Comprehensive competitor analysis
  async analyzeCompetitors(
    query: string,
    location: string = '',
    options: {
      analyzeTop?: number;
      includeContent?: boolean;
      includeMetrics?: boolean;
    } = {},
    userId?: string
  ): Promise<CompetitorAnalysis> {
    return withErrorHandling(
      'serper',
      async () => {
        const startTime = Date.now();
        const { analyzeTop = 5, includeContent = true, includeMetrics = true } = options;

        // Check cache first
        const cacheKey = this.generateCacheKey('analysis', { query, location, options });
        const cached = this.getFromCache(cacheKey);
        if (cached) {
          console.log('Returning cached competitor analysis');
          return cached;
        }

        console.log('Starting comprehensive competitor analysis...', {
          query,
          location,
          analyzeTop,
        });

        // Get search results
        const searchRequest: SerperSearchRequest = {
          query,
          location,
          num: Math.max(analyzeTop, 10),
          device: 'desktop',
        };

        const searchResults = await this.search(searchRequest, userId);
        
        if (!searchResults.organic || searchResults.organic.length === 0) {
          throw new Error('No organic search results found for analysis');
        }

        // Analyze top competitors
        const topCompetitors: CompetitorData[] = [];
        const wordCounts: number[] = [];
        const headingCounts: { [key: string]: number[] } = { h1: [], h2: [], h3: [], h4: [] };
        const keywordDensities: number[] = [];
        const allKeywords = new Set<string>();

        for (let i = 0; i < Math.min(analyzeTop, searchResults.organic.length); i++) {
          const result = searchResults.organic[i];
          
          try {
            console.log(`Analyzing competitor ${i + 1}: ${result.domain}`);
            
            const competitorData: CompetitorData = {
              position: result.position,
              domain: result.domain,
              title: result.title,
              url: result.link,
              snippet: result.snippet,
            };

            if (includeContent) {
              // In a real implementation, you would scrape the content here
              // For now, we'll estimate based on snippet and available data
              const estimatedWordCount = this.estimateWordCount(result);
              const estimatedHeadings = this.estimateHeadings(result);
              const estimatedKeywordDensity = this.estimateKeywordDensity(result, query);
              
              competitorData.wordCount = estimatedWordCount;
              competitorData.headingStructure = estimatedHeadings;
              competitorData.keywordDensity = estimatedKeywordDensity;
              
              wordCounts.push(estimatedWordCount);
              keywordDensities.push(estimatedKeywordDensity);
              
              // Collect heading counts
              Object.entries(estimatedHeadings).forEach(([tag, count]) => {
                if (headingCounts[tag]) {
                  headingCounts[tag].push(count);
                }
              });

              // Extract keywords from title and snippet
              this.extractKeywords(result.title + ' ' + result.snippet)
                .forEach(keyword => allKeywords.add(keyword));
            }

            if (includeMetrics) {
              // In a real implementation, you would get these from SEO tools APIs
              competitorData.domainAuthority = this.estimateDomainAuthority(result.domain);
              competitorData.estimatedTraffic = this.estimateTraffic(result.position);
              competitorData.contentScore = this.calculateContentScore(competitorData);
              competitorData.technicalScore = this.calculateTechnicalScore(result);
            }

            topCompetitors.push(competitorData);
          } catch (error) {
            console.warn(`Failed to analyze competitor ${result.domain}:`, error);
          }
        }

        // Calculate averages
        const averageWordCount = wordCounts.length > 0 
          ? Math.round(wordCounts.reduce((a, b) => a + b, 0) / wordCounts.length)
          : 800;

        const averageHeadings: { [key: string]: number } = {};
        Object.entries(headingCounts).forEach(([tag, counts]) => {
          averageHeadings[tag] = counts.length > 0 
            ? Math.round(counts.reduce((a, b) => a + b, 0) / counts.length)
            : 0;
        });

        const averageKeywordDensity = keywordDensities.length > 0
          ? Math.round((keywordDensities.reduce((a, b) => a + b, 0) / keywordDensities.length) * 100) / 100
          : 2.0;

        // Identify search features
        const searchFeatures = this.identifySearchFeatures(searchResults);

        // Calculate difficulty
        const difficulty = this.calculateDifficulty(topCompetitors, searchFeatures);

        // Identify content gaps
        const contentGaps = this.identifyContentGaps(searchResults, query);

        const analysis: CompetitorAnalysis = {
          query,
          location,
          totalResults: searchResults.organic.length,
          averageWordCount,
          averageHeadings,
          commonKeywords: Array.from(allKeywords).slice(0, 20),
          averageKeywordDensity,
          contentGaps,
          topCompetitors,
          searchFeatures,
          difficulty,
        };

        // Cache the analysis
        this.setCache(cacheKey, analysis, this.CACHE_TTL.analysis);

        const responseTime = Date.now() - startTime;
        console.log('Competitor analysis completed', {
          responseTime,
          competitorsAnalyzed: topCompetitors.length,
          averageWordCount,
          difficulty,
        });

        return analysis;
      },
      {
        method: 'POST',
        endpoint: 'analyze',
        query,
        location,
        ...options,
      },
      userId
    );
  }

  // Get People Also Ask questions
  async getPeopleAlsoAsk(
    query: string,
    location?: string,
    userId?: string
  ): Promise<PeopleAlsoAskResult[]> {
    const searchRequest: SerperSearchRequest = {
      query,
      location,
      num: 10,
    };

    const results = await this.search(searchRequest, userId);
    return results.peopleAlsoAsk || [];
  }

  // Get related searches
  async getRelatedSearches(
    query: string,
    location?: string,
    userId?: string
  ): Promise<RelatedSearchResult[]> {
    const searchRequest: SerperSearchRequest = {
      query,
      location,
      num: 10,
    };

    const results = await this.search(searchRequest, userId);
    return results.relatedSearches || [];
  }

  // Get keyword suggestions based on autocomplete
  async getKeywordSuggestions(
    query: string,
    location?: string,
    userId?: string
  ): Promise<string[]> {
    return withErrorHandling(
      'serper',
      async () => {
        const cacheKey = this.generateCacheKey('keywords', { query, location });
        const cached = this.getFromCache(cacheKey);
        if (cached) {
          return cached;
        }

        // Get related searches and PAA questions for keyword ideas
        const [relatedSearches, paaQuestions] = await Promise.all([
          this.getRelatedSearches(query, location, userId),
          this.getPeopleAlsoAsk(query, location, userId),
        ]);

        const suggestions = new Set<string>();
        
        // Add related searches
        relatedSearches.forEach(related => {
          suggestions.add(related.query);
        });

        // Extract keywords from PAA questions
        paaQuestions.forEach(paa => {
          this.extractKeywords(paa.question).forEach(keyword => {
            suggestions.add(keyword);
          });
        });

        const result = Array.from(suggestions).slice(0, 50);
        this.setCache(cacheKey, result, this.CACHE_TTL.keywords);

        return result;
      },
      {
        method: 'POST',
        endpoint: 'keywords',
        query,
        location,
      },
      userId
    );
  }

  // Private helper methods

  private estimateWordCount(result: OrganicResult): number {
    // Estimate based on snippet length and typical ratios
    const snippetWords = result.snippet.split(/\s+/).length;
    const baseEstimate = snippetWords * 25; // Typical snippet is 1/25th of content
    
    // Adjust based on domain type
    if (result.domain.includes('wikipedia')) return Math.max(baseEstimate * 3, 2000);
    if (result.domain.includes('blog') || result.title.toLowerCase().includes('guide')) {
      return Math.max(baseEstimate * 2, 1500);
    }
    
    return Math.max(baseEstimate, 500);
  }

  private estimateHeadings(result: OrganicResult): { [key: string]: number } {
    const wordCount = this.estimateWordCount(result);
    
    // Estimate heading structure based on word count and type
    const h1 = 1; // Always one H1
    const h2 = Math.max(2, Math.floor(wordCount / 300));
    const h3 = Math.max(0, Math.floor(wordCount / 500));
    const h4 = Math.max(0, Math.floor(wordCount / 800));
    
    return { h1, h2, h3, h4 };
  }

  private estimateKeywordDensity(result: OrganicResult, query: string): number {
    const content = `${result.title} ${result.snippet}`.toLowerCase();
    const queryWords = query.toLowerCase().split(/\s+/);
    const contentWords = content.split(/\s+/);
    
    let matches = 0;
    queryWords.forEach(queryWord => {
      matches += (content.match(new RegExp(queryWord, 'g')) || []).length;
    });
    
    return Math.min((matches / contentWords.length) * 100, 5); // Cap at 5%
  }

  private estimateDomainAuthority(domain: string): number {
    // Simplified domain authority estimation
    const commonHighAuthDomains = [
      'wikipedia.org', 'gov', 'edu', 'forbes.com', 'nytimes.com',
      'bbc.com', 'cnn.com', 'techcrunch.com', 'medium.com'
    ];
    
    if (commonHighAuthDomains.some(d => domain.includes(d))) {
      return Math.floor(Math.random() * 20) + 80; // 80-100
    }
    
    return Math.floor(Math.random() * 50) + 30; // 30-80
  }

  private estimateTraffic(position: number): number {
    // Simplified traffic estimation based on CTR curves
    const ctrByPosition = [0.31, 0.24, 0.18, 0.13, 0.09, 0.06, 0.04, 0.03, 0.02, 0.02];
    const baseCtr = ctrByPosition[position - 1] || 0.01;
    const estimatedSearchVolume = Math.floor(Math.random() * 10000) + 1000;
    
    return Math.floor(estimatedSearchVolume * baseCtr);
  }

  private calculateContentScore(competitor: CompetitorData): number {
    let score = 0;
    
    // Word count scoring (0-30 points)
    if (competitor.wordCount) {
      if (competitor.wordCount >= 1000) score += 30;
      else if (competitor.wordCount >= 500) score += 20;
      else score += 10;
    }
    
    // Heading structure scoring (0-25 points)
    if (competitor.headingStructure) {
      const { h1, h2, h3 } = competitor.headingStructure;
      if (h1 === 1) score += 5;
      if (h2 >= 3) score += 10;
      if (h3 >= 2) score += 10;
    }
    
    // Keyword density scoring (0-20 points)
    if (competitor.keywordDensity) {
      if (competitor.keywordDensity >= 1 && competitor.keywordDensity <= 3) score += 20;
      else if (competitor.keywordDensity >= 0.5 && competitor.keywordDensity <= 5) score += 10;
    }
    
    // Position bonus (0-25 points)
    score += Math.max(0, 25 - (competitor.position - 1) * 5);
    
    return Math.min(score, 100);
  }

  private calculateTechnicalScore(result: OrganicResult): number {
    let score = 50; // Base score
    
    // HTTPS bonus
    if (result.link.startsWith('https://')) score += 10;
    
    // Sitelinks bonus
    if (result.sitelinks && result.sitelinks.length > 0) score += 15;
    
    // Domain factors
    if (result.domain.length < 15) score += 5; // Short domain bonus
    if (!result.domain.includes('-')) score += 5; // No hyphens bonus
    
    // Title optimization
    if (result.title.length >= 30 && result.title.length <= 60) score += 10;
    
    // Meta description
    if (result.snippet.length >= 120 && result.snippet.length <= 160) score += 5;
    
    return Math.min(score, 100);
  }

  private identifySearchFeatures(results: SerperSearchResult): SearchFeature[] {
    const features: SearchFeature[] = [];
    
    features.push({
      type: 'featured_snippet',
      present: !!results.answerBox,
      position: results.answerBox ? 0 : undefined,
      content: results.answerBox,
    });

    features.push({
      type: 'people_also_ask',
      present: !!(results.peopleAlsoAsk && results.peopleAlsoAsk.length > 0),
      content: results.peopleAlsoAsk,
    });

    features.push({
      type: 'knowledge_graph',
      present: !!results.knowledgeGraph,
      content: results.knowledgeGraph,
    });

    return features;
  }

  private calculateDifficulty(competitors: CompetitorData[], features: SearchFeature[]): 'low' | 'medium' | 'high' | 'very-high' {
    let difficultyScore = 0;
    
    // Average domain authority factor
    const avgDomainAuthority = competitors.reduce((sum, comp) => 
      sum + (comp.domainAuthority || 50), 0) / competitors.length;
    
    if (avgDomainAuthority > 80) difficultyScore += 3;
    else if (avgDomainAuthority > 60) difficultyScore += 2;
    else if (avgDomainAuthority > 40) difficultyScore += 1;
    
    // Content quality factor
    const avgContentScore = competitors.reduce((sum, comp) => 
      sum + (comp.contentScore || 50), 0) / competitors.length;
    
    if (avgContentScore > 80) difficultyScore += 3;
    else if (avgContentScore > 60) difficultyScore += 2;
    else if (avgContentScore > 40) difficultyScore += 1;
    
    // SERP features factor
    const activeFeatures = features.filter(f => f.present).length;
    if (activeFeatures > 3) difficultyScore += 2;
    else if (activeFeatures > 1) difficultyScore += 1;
    
    // Return difficulty level
    if (difficultyScore >= 7) return 'very-high';
    if (difficultyScore >= 5) return 'high';
    if (difficultyScore >= 3) return 'medium';
    return 'low';
  }

  private identifyContentGaps(results: SerperSearchResult, query: string): string[] {
    const gaps: string[] = [];
    
    // Analyze PAA questions for gaps
    if (results.peopleAlsoAsk) {
      const commonQuestionTypes = ['how', 'what', 'why', 'when', 'where', 'which'];
      const existingQuestions = results.peopleAlsoAsk.map(q => q.question.toLowerCase());
      
      commonQuestionTypes.forEach(type => {
        const hasTypeQuestion = existingQuestions.some(q => q.startsWith(type));
        if (!hasTypeQuestion) {
          gaps.push(`${type.charAt(0).toUpperCase() + type.slice(1)} questions about ${query}`);
        }
      });
    }
    
    // Common content gaps
    const commonGaps = [
      'Step-by-step guides',
      'Comparison tables',
      'Pro and cons lists',
      'Case studies',
      'Expert quotes',
      'Statistics and data',
      'Visual content suggestions',
      'Local information',
    ];
    
    // Add some random gaps (in real implementation, this would be more sophisticated)
    gaps.push(...commonGaps.slice(0, 3));
    
    return gaps;
  }

  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase()
      .replace(/[^\w\s]/g, '')
      .split(/\s+/)
      .filter(word => word.length > 3);
    
    const stopWords = new Set([
      'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 
      'with', 'by', 'from', 'as', 'is', 'was', 'are', 'were', 'been',
      'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could',
      'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these',
      'those', 'what', 'when', 'where', 'why', 'how', 'which', 'who'
    ]);
    
    return words.filter(word => !stopWords.has(word));
  }

  // Cache management methods
  private generateCacheKey(type: string, data: any): string {
    const dataStr = JSON.stringify(data);
    return `${type}:${Buffer.from(dataStr).toString('base64')}`;
  }

  private getFromCache(key: string): any {
    const entry = this.cache.get(key);
    if (!entry) return null;
    
    if (Date.now() > entry.timestamp + entry.ttl) {
      this.cache.delete(key);
      return null;
    }
    
    return entry.data;
  }

  private setCache(key: string, data: any, ttl: number): void {
    this.cache.set(key, {
      data,
      timestamp: Date.now(),
      ttl,
    });
  }

  private cleanupCache(): void {
    const now = Date.now();
    for (const [key, entry] of this.cache.entries()) {
      if (now > entry.timestamp + entry.ttl) {
        this.cache.delete(key);
      }
    }
  }

  // Cost calculation
  private calculateCost(credits: number): number {
    // Serper charges per credit, typical cost is $0.001 per credit
    return credits * 0.001;
  }

  // Health check
  async healthCheck(): Promise<{ status: string; latency: number }> {
    const startTime = Date.now();
    
    try {
      const response = await fetch(`${this.baseUrl}/search`, {
        method: 'POST',
        headers: {
          'X-API-KEY': this.apiKey,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: 'test',
          num: 1,
        }),
      });
      
      const latency = Date.now() - startTime;
      
      return {
        status: response.ok ? 'healthy' : 'unhealthy',
        latency,
      };
    } catch (error) {
      return {
        status: 'unhealthy',
        latency: Date.now() - startTime,
      };
    }
  }
}

// Export singleton instance
export const serperService = new SerperService();
export default SerperService;
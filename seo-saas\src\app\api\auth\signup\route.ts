import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient, supabaseAdmin } from '@/lib/supabase';
import { z } from 'zod';
import { logUserActivity } from '@/lib/supabase';

const signUpSchema = z.object({
  email: z.string().email('Invalid email address'),
  password: z.string().min(6, 'Password must be at least 6 characters'),
  fullName: z.string().min(2, 'Full name must be at least 2 characters').optional(),
});

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validate input
    const { email, password, fullName } = signUpSchema.parse(body);
    
    const supabase = createSupabaseServerClient();
    
    // Check if user already exists
    const { data: existingUser } = await supabaseAdmin.auth.admin.getUserByEmail(email);
    
    if (existingUser.user) {
      return NextResponse.json(
        { error: 'An account with this email already exists' },
        { status: 409 }
      );
    }
    
    // Attempt sign up
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          full_name: fullName,
        },
      },
    });
    
    if (error) {
      // Log failed sign-up attempt
      await logUserActivity({
        action_type: 'signup_failed',
        action_details: {
          email,
          error: error.message,
        },
        ip_address: request.ip,
        user_agent: request.headers.get('user-agent'),
      });
      
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    // Create initial subscription record for new user
    if (data.user) {
      try {
        await supabaseAdmin
          .from('user_subscriptions')
          .insert({
            user_id: data.user.id,
            plan_type: 'free',
            status: 'active',
            current_period_start: new Date().toISOString(),
            current_period_end: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(), // 1 year from now for free plan
          });
      } catch (subscriptionError) {
        console.error('Error creating initial subscription:', subscriptionError);
        // Don't fail the signup if subscription creation fails
      }
    }
    
    // Log successful sign-up
    await logUserActivity({
      user_id: data.user?.id,
      action_type: 'signup_success',
      action_details: {
        email,
        full_name: fullName,
        method: 'email_password',
      },
      ip_address: request.ip,
      user_agent: request.headers.get('user-agent'),
    });
    
    return NextResponse.json({
      success: true,
      user: data.user,
      session: data.session,
      message: data.user?.email_confirmed_at 
        ? 'Account created successfully' 
        : 'Please check your email to confirm your account',
    });
    
  } catch (error) {
    console.error('Sign-up error:', error);
    
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid input', details: error.errors },
        { status: 400 }
      );
    }
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
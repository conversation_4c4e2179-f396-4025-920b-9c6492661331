(()=>{var e={};e.id=599,e.ids=[599],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},1283:(e,t,r)=>{"use strict";r.r(t),r.d(t,{headerHooks:()=>m,originalPathname:()=>v,patchFetch:()=>h,requestAsyncStorage:()=>d,routeModule:()=>p,serverHooks:()=>S,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>x});var s={};r.r(s),r.d(s,{POST:()=>l});var a=r(5419),i=r(9108),o=r(9678),n=r(8070),u=r(2045),c=r(9393);async function l(e){try{let t=(0,u.jq)(),{data:{user:r}}=await t.auth.getUser(),{error:s}=await t.auth.signOut();if(s)return n.Z.json({error:s.message},{status:400});return r&&await (0,c.Tj)({user_id:r.id,action_type:"signout",action_details:{email:r.email},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),n.Z.json({success:!0,message:"Signed out successfully"})}catch(e){return console.error("Sign-out error:",e),n.Z.json({error:"Internal server error"},{status:500})}}let p=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/signout/route",pathname:"/api/auth/signout",filename:"route",bundlePath:"app/api/auth/signout/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/auth/signout/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:d,staticGenerationAsyncStorage:g,serverHooks:S,headerHooks:m,staticGenerationBailout:x}=p,v="/api/auth/signout/route";function h(){return(0,o.patchFetch)({serverHooks:S,staticGenerationAsyncStorage:g})}},6843:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return s}});let s=r(8195).createClientModuleProxy},482:(e,t,r)=>{"use strict";e.exports=r(399)},8195:(e,t,r)=>{"use strict";e.exports=r(482).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},2045:(e,t,r)=>{"use strict";r.d(t,{jq:()=>i});var s=r(7699),a=r(2455);let i=()=>(0,s.createServerComponentClient)({cookies:a.cookies})},9393:(e,t,r)=>{"use strict";r.d(t,{xc:()=>v,Tj:()=>h,pR:()=>m});var s=r(1971),a=r(7699),i=r(6843);let o=(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts`),{__esModule:n,$$typeof:u}=o;o.default;let c=(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#validateEnvironment`),l=(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#getEnvironment`);(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#logEnvironmentStatus`);let p=l(),d=c(),g=(e,t)=>{};d.isValid||(["Supabase configuration error:",...d.missingVars.map(e=>`- Missing: ${e}`),...d.errors.map(e=>`- Error: ${e}`)].join("\n"),g("Environment validation failed",{validation:d}));let S=null;try{p.supabaseUrl&&p.supabaseKey?(0,s.eI)(p.supabaseUrl,p.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):g("Cannot create Supabase client: missing URL or key")}catch(e){g("Failed to create Supabase client",e)}try{p.supabaseUrl&&p.supabaseServiceKey&&(S=(0,s.eI)(p.supabaseUrl,p.supabaseServiceKey,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}}))}catch(e){g("Failed to create Supabase admin client",e)}let m=S,x=()=>{try{return(0,a.createClientComponentClient)()}catch(e){throw g("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function v(e){let t=x(),{error:r}=await t.from("api_usage_logs").insert(e);r&&console.error("Error logging API usage:",r)}async function h(e){let t=x(),{error:r}=await t.from("user_activity_logs").insert(e);r&&console.error("Error logging user activity:",r)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,280],()=>r(1283));module.exports=s})();
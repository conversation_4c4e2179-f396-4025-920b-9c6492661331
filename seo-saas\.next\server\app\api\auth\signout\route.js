(()=>{var e={};e.id=599,e.ids=[599],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},1283:(e,t,s)=>{"use strict";s.r(t),s.d(t,{headerHooks:()=>x,originalPathname:()=>q,patchFetch:()=>I,requestAsyncStorage:()=>d,routeModule:()=>l,serverHooks:()=>h,staticGenerationAsyncStorage:()=>g,staticGenerationBailout:()=>m});var r={};s.r(r),s.d(r,{POST:()=>p});var i=s(5419),o=s(9108),a=s(9678),n=s(8070),u=s(2045),c=s(6517);async function p(e){try{let t=(0,u.jq)(),{data:{user:s}}=await t.auth.getUser(),{error:r}=await t.auth.signOut();if(r)return n.Z.json({error:r.message},{status:400});return s&&await (0,c.Tj)({user_id:s.id,action_type:"signout",action_details:{email:s.email},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),n.Z.json({success:!0,message:"Signed out successfully"})}catch(e){return console.error("Sign-out error:",e),n.Z.json({error:"Internal server error"},{status:500})}}let l=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/signout/route",pathname:"/api/auth/signout",filename:"route",bundlePath:"app/api/auth/signout/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/auth/signout/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:d,staticGenerationAsyncStorage:g,serverHooks:h,headerHooks:x,staticGenerationBailout:m}=l,q="/api/auth/signout/route";function I(){return(0,a.patchFetch)({serverHooks:h,staticGenerationAsyncStorage:g})}},2045:(e,t,s)=>{"use strict";s.d(t,{jq:()=>o});var r=s(7699),i=s(2455);let o=()=>(0,r.createServerComponentClient)({cookies:i.cookies})},6517:(e,t,s)=>{"use strict";s.d(t,{Tj:()=>u,pR:()=>a,xc:()=>n});var r=s(1971),i=s(7699);(0,r.eI)("https://zqrmpanonghggoxdjirq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}});let o=()=>(0,i.createClientComponentClient)(),a=(0,r.eI)("https://zqrmpanonghggoxdjirq.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}});async function n(e){let t=o(),{error:s}=await t.from("api_usage_logs").insert(e);s&&console.error("Error logging API usage:",s)}async function u(e){let t=o(),{error:s}=await t.from("user_activity_logs").insert(e);s&&console.error("Error logging user activity:",s)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,280],()=>s(1283));module.exports=r})();
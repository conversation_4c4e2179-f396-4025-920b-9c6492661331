(()=>{var e={};e.id=702,e.ids=[702],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},1017:e=>{"use strict";e.exports=require("path")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},6460:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var r=s(482),a=s(9108),n=s(2563),i=s.n(n),l=s(8300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);s.d(t,o);let c=["",{children:["dashboard",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,9468)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/dashboard/page.tsx"]}]},{layout:[()=>Promise.resolve().then(s.bind(s,7639)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,6343)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/dashboard/page.tsx"],m="/dashboard/page",x={require:s,loadChunk:()=>Promise.resolve()},u=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/dashboard/page",pathname:"/dashboard",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},9948:(e,t,s)=>{Promise.resolve().then(s.bind(s,7955))},7955:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>j});var r=s(2295),a=s(3729),n=s(3673),i=s(5094),l=s(9591),o=s(7210),c=s(1453),d=s(454),m=s(6233);let x=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 4.5v15m7.5-7.5h-15"}))});var u=s(6215);let h=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.25 18 9 11.25l4.306 4.306a11.95 11.95 0 0 1 5.814-5.518l2.74-1.22m0 0-5.94-2.281m5.94 2.28-2.28 5.941"}))});var p=s(7993);let g=a.forwardRef(function({title:e,titleId:t,...s},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":t},s),e?a.createElement("title",{id:t},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M12 6v6h4.5m4.5 0a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});function j(){let[e,t]=a.useState({totalProjects:12,contentGenerated:48,avgSeoScore:78,totalAnalyses:156,monthlyUsage:{current:23,limit:100}}),[s,j]=a.useState([{id:"1",type:"content",title:"SEO Services Landing Page",score:85,createdAt:"2024-01-15T10:30:00Z",status:"completed"},{id:"2",type:"analysis",title:"Competitor Analysis - Digital Marketing",score:72,createdAt:"2024-01-15T09:15:00Z",status:"completed"},{id:"3",type:"project",title:"Healthcare Website Project",createdAt:"2024-01-14T16:45:00Z",status:"processing"},{id:"4",type:"content",title:"Blog Post - AI in Marketing",score:91,createdAt:"2024-01-14T14:20:00Z",status:"completed"},{id:"5",type:"analysis",title:"Technical SEO Audit",score:67,createdAt:"2024-01-14T11:30:00Z",status:"completed"}]),f=[{title:"Generate Content",description:"Create SEO-optimized content with AI",icon:d.Z,href:"/dashboard/content",color:"bg-blue-500"},{title:"SEO Analysis",description:"Analyze existing content performance",icon:m.Z,href:"/dashboard/analysis",color:"bg-green-500"},{title:"New Project",description:"Start a new SEO project",icon:x,href:"/dashboard/projects/new",color:"bg-purple-500"},{title:"Competitor Research",description:"Research competitor strategies",icon:u.Z,href:"/dashboard/competitors",color:"bg-orange-500"}],y=e=>{switch(e){case"content":default:return d.Z;case"analysis":return m.Z;case"project":return x}},b=e=>{switch(e){case"completed":return"text-green-600 bg-green-100";case"processing":return"text-yellow-600 bg-yellow-100";case"failed":return"text-red-600 bg-red-100";default:return"text-gray-600 bg-gray-100"}};return(0,r.jsxs)("div",{className:"space-y-8",children:[(0,r.jsxs)("div",{children:[r.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"Dashboard"}),r.jsx("p",{className:"text-gray-600 mt-2",children:"Welcome back! Here's an overview of your SEO performance."})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(n.ll,{className:"text-sm font-medium",children:"Total Projects"}),r.jsx(x,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:e.totalProjects}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+2 from last month"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(n.ll,{className:"text-sm font-medium",children:"Content Generated"}),r.jsx(d.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:e.contentGenerated}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+12 from last month"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(n.ll,{className:"text-sm font-medium",children:"Avg SEO Score"}),r.jsx(h,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.aY,{children:[(0,r.jsxs)("div",{className:"text-2xl font-bold",children:[e.avgSeoScore,"/100"]}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+5 points improvement"})]})]}),(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[r.jsx(n.ll,{className:"text-sm font-medium",children:"Total Analyses"}),r.jsx(m.Z,{className:"h-4 w-4 text-muted-foreground"})]}),(0,r.jsxs)(n.aY,{children:[r.jsx("div",{className:"text-2xl font-bold",children:(0,c.uf)(e.totalAnalyses)}),r.jsx("p",{className:"text-xs text-muted-foreground",children:"+23 this month"})]})]})]}),(0,r.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-3 gap-6",children:[(0,r.jsxs)(n.Zb,{children:[(0,r.jsxs)(n.Ol,{children:[r.jsx(n.ll,{children:"Monthly Usage"}),r.jsx(n.SZ,{children:"Content generations this month"})]}),(0,r.jsxs)(n.aY,{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("span",{className:"text-sm font-medium",children:[e.monthlyUsage.current," / ",e.monthlyUsage.limit]}),(0,r.jsxs)(l.Ct,{variant:"secondary",children:[Math.round(e.monthlyUsage.current/e.monthlyUsage.limit*100),"%"]})]}),r.jsx(o.Ex,{value:e.monthlyUsage.current,max:e.monthlyUsage.limit,variant:e.monthlyUsage.current>.8*e.monthlyUsage.limit?"warning":"default"}),(0,r.jsxs)("p",{className:"text-xs text-muted-foreground",children:[e.monthlyUsage.limit-e.monthlyUsage.current," generations remaining"]})]})]}),(0,r.jsxs)(n.Zb,{className:"lg:col-span-2",children:[(0,r.jsxs)(n.Ol,{children:[r.jsx(n.ll,{children:"Quick Actions"}),r.jsx(n.SZ,{children:"Get started with common tasks"})]}),r.jsx(n.aY,{children:r.jsx("div",{className:"grid grid-cols-1 sm:grid-cols-2 gap-4",children:f.map((e,t)=>r.jsx(i.z,{variant:"outline",className:"h-auto p-4 justify-start",asChild:!0,children:r.jsx("a",{href:e.href,children:(0,r.jsxs)("div",{className:"flex items-start space-x-3",children:[r.jsx("div",{className:(0,c.cn)("p-2 rounded-md",e.color),children:r.jsx(e.icon,{className:"h-5 w-5 text-white"})}),(0,r.jsxs)("div",{className:"text-left",children:[r.jsx("div",{className:"font-medium",children:e.title}),r.jsx("div",{className:"text-sm text-muted-foreground",children:e.description})]})]})})},t))})})]})]}),(0,r.jsxs)(n.Zb,{children:[r.jsx(n.Ol,{children:(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsxs)("div",{children:[r.jsx(n.ll,{children:"Recent Activity"}),r.jsx(n.SZ,{children:"Your latest content generations and analyses"})]}),(0,r.jsxs)(i.z,{variant:"outline",size:"sm",children:[r.jsx(p.Z,{className:"h-4 w-4 mr-2"}),"View All"]})]})}),r.jsx(n.aY,{children:r.jsx("div",{className:"space-y-4",children:s.map(e=>{let t=y(e.type);return(0,r.jsxs)("div",{className:"flex items-center space-x-4 p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors",children:[r.jsx("div",{className:"flex-shrink-0",children:r.jsx("div",{className:"p-2 bg-gray-100 rounded-md",children:r.jsx(t,{className:"h-5 w-5 text-gray-600"})})}),(0,r.jsxs)("div",{className:"flex-1 min-w-0",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[r.jsx("p",{className:"text-sm font-medium text-gray-900 truncate",children:e.title}),(0,r.jsxs)("div",{className:"flex items-center space-x-2",children:[e.score&&r.jsx(l.Dk,{score:e.score}),r.jsx(l.Ct,{variant:"outline",className:b(e.status),children:e.status})]})]}),(0,r.jsxs)("div",{className:"flex items-center mt-1 space-x-2",children:[r.jsx(g,{className:"h-3 w-3 text-gray-400"}),r.jsx("p",{className:"text-xs text-gray-500",children:(0,c.p6)(e.createdAt)})]})]})]},e.id)})})})]})]})}},9468:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let r=(0,s(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/dashboard/page.tsx`),{__esModule:a,$$typeof:n}=r,i=r.default},7993:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var r=s(3729);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},6215:(e,t,s)=>{"use strict";s.d(t,{Z:()=>a});var r=s(3729);let a=r.forwardRef(function({title:e,titleId:t,...s},a){return r.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},s),e?r.createElement("title",{id:t},e):null,r.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))})}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,972,337,35,783,129,206],()=>s(6460));module.exports=r})();
exports.id=493,exports.ids=[493],exports.modules={8359:()=>{},3739:()=>{},917:(e,r,t)=>{"use strict";t.d(r,{vc:()=>s});let a=["NEXT_PUBLIC_SUPABASE_URL","NEXT_PUBLIC_SUPABASE_ANON_KEY","SUPABASE_SERVICE_ROLE_KEY","GROQ_API_KEY","SERPER_API_KEY","NEXTAUTH_SECRET"];try{(function(){let e=a.filter(e=>!process.env[e]);if(e.length>0)throw Error(`Missing required environment variables: ${e.join(", ")}`)})()}catch(e){console.warn("Environment validation warning:",e)}let s={supabase:{url:"https://xpcbyzcaidfukddqniny.supabase.co",anon<PERSON>ey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A",serviceRoleKey:process.env.SUPABASE_SERVICE_ROLE_KEY},apis:{groq:{apiKey:process.env.GROQ_API_KEY,baseUrl:"https://api.groq.com/openai/v1",model:"llama3-8b-8192",rateLimitPerMinute:parseInt(process.env.GROQ_RATE_LIMIT_PER_MINUTE||"60")},serper:{apiKey:process.env.SERPER_API_KEY,baseUrl:"https://google.serper.dev",rateLimitPerMinute:parseInt(process.env.SERPER_RATE_LIMIT_PER_MINUTE||"100")}},app:{url:"http://localhost:3000",name:"SEO Content Generator",version:"1.0.0",environment:"production"},auth:{secret:process.env.NEXTAUTH_SECRET,url:process.env.NEXTAUTH_URL||"http://localhost:3000"},features:{analytics:!0,testing:!1}};s.supabase,s.apis.groq,s.apis.serper,s.app,s.auth,s.features,s.app.environment,s.app.environment,s.app.environment},8280:(e,r,t)=>{"use strict";t.d(r,{WH:()=>n,bi:()=>i});var a=t(9393);class s{static getInstance(){return s.instance||(s.instance=new s),s.instance}async handleApiError(e,r,t,a){if(console.error(`${e} API Error:`,r),await this.logError(e,r,t,a),this.isCircuitBreakerOpen(e))throw Error(`${e} service is temporarily unavailable. Please try again later.`);let s=this.classifyError(r);switch(console.log(`Error classified as: ${s} for service: ${e}`),e.toLowerCase()){case"groq":return this.handleGroqError(s,r,t,a);case"serper":return this.handleSerperError(s,r,t,a);case"openai":return this.handleOpenAiError(s,r,t,a);default:return this.handleGenericError(s,r,t,a)}}async handleGroqError(e,r,t,a){switch(e){case"RATE_LIMIT":return console.log("Handling Groq rate limit..."),this.handleRateLimit("groq",t,a);case"QUOTA_EXCEEDED":return console.log("Groq quota exceeded, checking cache..."),this.handleQuotaExceeded("groq",t);case"INVALID_REQUEST":throw console.log("Invalid Groq request, returning error..."),Error(`Invalid request: ${r.message||"Please check your input parameters"}`);case"API_DOWN":return console.log("Groq API is down, using fallback..."),this.handleServiceDown("groq",t);case"NETWORK_ERROR":return console.log("Network error with Groq, retrying..."),this.handleNetworkError("groq",t,a);default:return console.log("Unknown Groq error, using generic handler..."),this.handleUnknownError("groq",r,t)}}async handleSerperError(e,r,t,a){switch(e){case"RATE_LIMIT":return console.log("Handling Serper rate limit..."),this.handleRateLimit("serper",t,a);case"BLOCKED":return console.log("Serper request blocked, using cached data..."),this.useCachedData("serper",t);case"QUOTA_EXCEEDED":return console.log("Serper quota exceeded, using cached data..."),this.useCachedData("serper",t);case"INVALID_REQUEST":throw Error(`Invalid search request: ${r.message||"Please check your search parameters"}`);case"API_DOWN":return console.log("Serper API is down, using cached data..."),this.useCachedData("serper",t);default:return this.handleUnknownError("serper",r,t)}}async handleOpenAiError(e,r,t,a){return"RATE_LIMIT"===e?this.handleRateLimit("openai",t,a):this.handleUnknownError("openai",r,t)}async handleGenericError(e,r,t,a){throw console.log("Handling generic error..."),Error(`Service temporarily unavailable: ${r.message}`)}classifyError(e){let r=e.message?.toLowerCase()||"",t=e.status||e.response?.status||0;return 429===t||r.includes("rate limit")||r.includes("too many requests")?"RATE_LIMIT":402===t||r.includes("quota")||r.includes("billing")||r.includes("insufficient funds")?"QUOTA_EXCEEDED":t>=400&&t<500&&429!==t?"INVALID_REQUEST":t>=500||r.includes("internal server error")||r.includes("service unavailable")?"API_DOWN":r.includes("network")||r.includes("timeout")||r.includes("connection")?"NETWORK_ERROR":r.includes("blocked")||r.includes("forbidden")||403===t?"BLOCKED":"UNKNOWN"}async handleRateLimit(e,r,t){let a=this.retryConfigs[e],s=this.retryAttempts.get(e)||0;if(s>=a.maxRetries)throw this.retryAttempts.delete(e),Error(`${e} rate limit exceeded. Please try again later.`);let n=this.calculateBackoffDelay(e,s);throw console.log(`Rate limited. Retrying ${e} in ${n}ms (attempt ${s+1}/${a.maxRetries})`),this.retryAttempts.set(e,s+1),await this.sleep(n),Error(`${e} rate limit exceeded after ${s+1} attempts`)}async handleQuotaExceeded(e,r){console.log(`${e} quota exceeded, checking for cached data...`);let t=this.getCachedResult(e,r);if(t)return console.log(`Returning cached ${e} data due to quota exceeded`),t;throw Error(`${e} quota exceeded and no cached data available. Please upgrade your plan or try again later.`)}async handleServiceDown(e,r){console.log(`${e} service is down, checking alternatives...`),this.recordFailure(e);let t=this.getCachedResult(e,r);if(t)return console.log(`Returning cached ${e} data due to service down`),t;throw Error(`${e} service is currently unavailable. Please try again later.`)}async handleNetworkError(e,r,t){let a=this.retryConfigs[e],s=this.retryAttempts.get(e)||0;if(s>=a.maxRetries)throw this.retryAttempts.delete(e),Error(`Network error communicating with ${e}. Please check your connection.`);let n=this.calculateBackoffDelay(e,s);throw console.log(`Network error. Retrying ${e} in ${n}ms (attempt ${s+1}/${a.maxRetries})`),this.retryAttempts.set(e,s+1),await this.sleep(n),Error(`Network error after ${s+1} attempts`)}async handleUnknownError(e,r,t){console.error(`Unknown ${e} error:`,r),this.recordFailure(e);let a=this.getCachedResult(e,t);if(a)return console.log(`Returning cached ${e} data due to unknown error`),a;throw Error(`${e} service error: ${r.message||"Unknown error occurred"}`)}useCachedData(e,r){let t=this.getCachedResult(e,r);if(t)return console.log(`Returning cached ${e} data`),t;throw Error(`${e} service unavailable and no cached data found. Please try again later.`)}calculateBackoffDelay(e,r){let t=this.retryConfigs[e];return Math.min(t.baseDelay*Math.pow(t.backoffMultiplier,r)+1e3*Math.random(),t.maxDelay)}async sleep(e){return new Promise(r=>setTimeout(r,e))}isCircuitBreakerOpen(e){let r=this.circuitBreakers.get(e);if(!r)return!1;let t=Date.now();return r.isOpen&&t>r.nextAttempt&&(r.isOpen=!1,r.failures=0,console.log(`Circuit breaker for ${e} moved to half-open state`)),r.isOpen}recordFailure(e){let r=Date.now(),t=this.circuitBreakers.get(e);t||(t={isOpen:!1,failures:0,lastFailure:r,nextAttempt:r},this.circuitBreakers.set(e,t)),t.failures++,t.lastFailure=r,t.failures>=this.CIRCUIT_BREAKER_THRESHOLD&&(t.isOpen=!0,t.nextAttempt=r+this.CIRCUIT_BREAKER_RESET_TIMEOUT,console.warn(`Circuit breaker opened for ${e} after ${t.failures} failures`))}recordSuccess(e){let r=this.circuitBreakers.get(e);r&&(r.failures=0,r.isOpen=!1,console.log(`Circuit breaker reset for ${e} after successful request`))}getCachedResult(e,r){let t=this.generateCacheKey(e,r),a=this.cache.get(t);return a&&a.expires>Date.now()?a.data:(a&&this.cache.delete(t),null)}setCachedResult(e,r,t,a=36e5){let s=this.generateCacheKey(e,r);this.cache.set(s,{data:t,expires:Date.now()+a})}generateCacheKey(e,r){let t=JSON.stringify(r);return`${e}:${Buffer.from(t).toString("base64")}`}async logError(e,r,t,s){try{await (0,a.xc)({user_id:s,api_name:e,endpoint:t.endpoint||"unknown",method:t.method||"POST",tokens_used:0,cost:0,status_code:r.status||500,error_message:r.message||"Unknown error"})}catch(e){console.error("Failed to log API error:",e)}}updateMetrics(e,r,t,a){let s=this.apiMetrics.get(e);s||(s={totalRequests:0,successCount:0,errorCount:0,averageResponseTime:0},this.apiMetrics.set(e,s)),s.totalRequests++,r?(s.successCount++,this.recordSuccess(e)):(s.errorCount++,this.recordFailure(e),a&&(s.lastError=a)),s.averageResponseTime=(s.averageResponseTime*(s.totalRequests-1)+t)/s.totalRequests}getMetrics(e){return this.apiMetrics.get(e)||null}getServiceHealth(){let e={};for(let[r,t]of this.apiMetrics.entries()){let a=this.circuitBreakers.get(r);e[r]={...t,circuitBreakerOpen:a?.isOpen||!1,uptime:t.totalRequests>0?t.successCount/t.totalRequests*100:100,status:a?.isOpen?"down":"up"}}return e}cleanupCache(){let e=Date.now();for(let[r,t]of this.cache.entries())t.expires<=e&&this.cache.delete(r)}constructor(){this.retryAttempts=new Map,this.circuitBreakers=new Map,this.apiMetrics=new Map,this.cache=new Map,this.CIRCUIT_BREAKER_THRESHOLD=5,this.CIRCUIT_BREAKER_TIMEOUT=6e4,this.CIRCUIT_BREAKER_RESET_TIMEOUT=3e5,this.retryConfigs={groq:{maxRetries:3,baseDelay:1e3,maxDelay:3e4,backoffMultiplier:2},serper:{maxRetries:2,baseDelay:500,maxDelay:15e3,backoffMultiplier:1.5},openai:{maxRetries:3,baseDelay:2e3,maxDelay:6e4,backoffMultiplier:2}}}}let n=s.getInstance(),i=async(e,r,t,a)=>{let s=Date.now();try{let t=await r(),a=Date.now()-s;return n.updateMetrics(e,!0,a),t}catch(i){let r=Date.now()-s;return n.updateMetrics(e,!1,r,i instanceof Error?i.message:String(i)),n.handleApiError(e,i,t,a)}}},2045:(e,r,t)=>{"use strict";t.d(r,{jq:()=>n});var a=t(7699),s=t(2455);let n=()=>(0,a.createServerComponentClient)({cookies:s.cookies})},9393:(e,r,t)=>{"use strict";t.d(r,{xc:()=>R,Tj:()=>f,pR:()=>g});var a=t(1971),s=t(7699),n=t(6843);let i=(0,n.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts`),{__esModule:o,$$typeof:c}=i;i.default;let l=(0,n.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#validateEnvironment`),u=(0,n.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#getEnvironment`);(0,n.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#logEnvironmentStatus`);let h=u(),d=l(),p=(e,r)=>{};d.isValid||(["Supabase configuration error:",...d.missingVars.map(e=>`- Missing: ${e}`),...d.errors.map(e=>`- Error: ${e}`)].join("\n"),p("Environment validation failed",{validation:d}));let E=null;try{h.supabaseUrl&&h.supabaseKey?(0,a.eI)(h.supabaseUrl,h.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):p("Cannot create Supabase client: missing URL or key")}catch(e){p("Failed to create Supabase client",e)}try{h.supabaseUrl&&h.supabaseServiceKey&&(E=(0,a.eI)(h.supabaseUrl,h.supabaseServiceKey,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}}))}catch(e){p("Failed to create Supabase admin client",e)}let g=E,m=()=>{try{return(0,s.createClientComponentClient)()}catch(e){throw p("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function R(e){let r=m(),{error:t}=await r.from("api_usage_logs").insert(e);t&&console.error("Error logging API usage:",t)}async function f(e){let r=m(),{error:t}=await r.from("user_activity_logs").insert(e);t&&console.error("Error logging user activity:",t)}}};
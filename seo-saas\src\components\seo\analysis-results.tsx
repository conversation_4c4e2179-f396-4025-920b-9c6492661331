// Professional SEO Analysis Results Display
// Enterprise-grade component for displaying comprehensive SEO analysis

'use client'

import * as React from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge, ScoreBadge, PriorityBadge } from "@/components/ui/badge"
import { Progress, CircularProgress } from "@/components/ui/progress"
import { Button } from "@/components/ui/button"
import { cn, formatPercentage, copyToClipboard, downloadAsFile } from "@/lib/utils"
import {
  ChartBarIcon,
  DocumentTextIcon,
  LightBulbIcon,
  ClipboardDocumentIcon,
  ArrowDownTrayIcon,
  ExclamationTriangleIcon,
  CheckCircleIcon
} from "@heroicons/react/24/outline"

interface SEOAnalysisResults {
  overallScore: number
  keywordAnalysis: {
    score: number
    primaryKeywordDensity: number
    secondaryKeywordDensity: number
    recommendations: Array<{
      type: string
      priority: 'high' | 'medium' | 'low'
      description: string
      impact: number
    }>
  }
  headingAnalysis: {
    score: number
    totalHeadings: number
    optimizedHeadings: number
    hierarchy: {
      isValid: boolean
      issues: string[]
    }
    recommendations: Array<{
      type: string
      priority: 'high' | 'medium' | 'low'
      description: string
      impact: number
    }>
  }
  contentQuality: {
    score: number
    wordCount: number
    readabilityScore: number
    uniqueWords: number
    recommendations: Array<{
      type: string
      priority: 'high' | 'medium' | 'low'
      description: string
      impact: number
    }>
  }
  competitorAnalysis?: {
    averageWordCount: number
    averageScore: number
    topPerformerScore: number
    gaps: string[]
    opportunities: Array<{
      type: string
      description: string
      impact: 'high' | 'medium' | 'low'
    }>
  }
  lsiKeywords: Array<{
    keyword: string
    relevanceScore: number
    frequency: number
    category: string
  }>
  recommendations: Array<{
    type: string
    priority: 'high' | 'medium' | 'low'
    title: string
    description: string
    expectedImpact: number
  }>
}

interface AnalysisResultsProps {
  results: SEOAnalysisResults
  loading?: boolean
  className?: string
}

export function AnalysisResults({ results, loading = false, className }: AnalysisResultsProps) {
  const [activeTab, setActiveTab] = React.useState<'overview' | 'keywords' | 'content' | 'competitors' | 'recommendations'>('overview')

  const handleCopyResults = async () => {
    const text = `SEO Analysis Results
Overall Score: ${results.overallScore}/100
Keyword Analysis: ${results.keywordAnalysis.score}/100
Heading Analysis: ${results.headingAnalysis.score}/100
Content Quality: ${results.contentQuality.score}/100

Top Recommendations:
${results.recommendations.slice(0, 5).map((rec, i) => `${i + 1}. ${rec.title} (${rec.priority} priority)`).join('\n')}
`
    await copyToClipboard(text)
  }

  const handleDownloadReport = () => {
    const report = {
      timestamp: new Date().toISOString(),
      overallScore: results.overallScore,
      analysis: {
        keywords: results.keywordAnalysis,
        headings: results.headingAnalysis,
        content: results.contentQuality,
        competitors: results.competitorAnalysis
      },
      lsiKeywords: results.lsiKeywords,
      recommendations: results.recommendations
    }
    
    downloadAsFile(
      JSON.stringify(report, null, 2),
      `seo-analysis-${new Date().toISOString().split('T')[0]}.json`,
      'application/json'
    )
  }

  if (loading) {
    return (
      <Card className={cn("w-full", className)}>
        <CardContent className="p-8">
          <div className="flex items-center justify-center space-x-4">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
            <span className="text-lg">Analyzing your content...</span>
          </div>
        </CardContent>
      </Card>
    )
  }

  const tabs = [
    { id: 'overview', label: 'Overview', icon: ChartBarIcon },
    { id: 'keywords', label: 'Keywords', icon: DocumentTextIcon },
    { id: 'content', label: 'Content', icon: DocumentTextIcon },
    { id: 'competitors', label: 'Competitors', icon: ChartBarIcon },
    { id: 'recommendations', label: 'Recommendations', icon: LightBulbIcon },
  ]

  return (
    <div className={cn("w-full space-y-6", className)}>
      {/* Header with Overall Score */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle className="text-2xl">SEO Analysis Results</CardTitle>
              <CardDescription>Comprehensive analysis of your content's SEO performance</CardDescription>
            </div>
            <div className="flex items-center space-x-4">
              <CircularProgress 
                value={results.overallScore} 
                size={80} 
                variant={results.overallScore >= 80 ? 'success' : results.overallScore >= 60 ? 'warning' : 'error'}
              />
              <div className="space-x-2">
                <Button variant="outline" size="sm" onClick={handleCopyResults}>
                  <ClipboardDocumentIcon className="h-4 w-4 mr-2" />
                  Copy
                </Button>
                <Button variant="outline" size="sm" onClick={handleDownloadReport}>
                  <ArrowDownTrayIcon className="h-4 w-4 mr-2" />
                  Download
                </Button>
              </div>
            </div>
          </div>
        </CardHeader>
      </Card>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          {tabs.map((tab) => (
            <button
              key={tab.id}
              onClick={() => setActiveTab(tab.id as any)}
              className={cn(
                "flex items-center py-2 px-1 border-b-2 font-medium text-sm",
                activeTab === tab.id
                  ? "border-primary text-primary"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              )}
            >
              <tab.icon className="h-5 w-5 mr-2" />
              {tab.label}
            </button>
          ))}
        </nav>
      </div>

      {/* Tab Content */}
      <div className="space-y-6">
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Keyword Analysis</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">{results.keywordAnalysis.score}</span>
                  <ScoreBadge score={results.keywordAnalysis.score} />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Primary: {formatPercentage(results.keywordAnalysis.primaryKeywordDensity)}
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Heading Structure</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">{results.headingAnalysis.score}</span>
                  <ScoreBadge score={results.headingAnalysis.score} />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {results.headingAnalysis.optimizedHeadings}/{results.headingAnalysis.totalHeadings} optimized
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">Content Quality</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">{results.contentQuality.score}</span>
                  <ScoreBadge score={results.contentQuality.score} />
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  {results.contentQuality.wordCount} words
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <CardTitle className="text-sm font-medium">LSI Keywords</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex items-center justify-between">
                  <span className="text-2xl font-bold">{results.lsiKeywords.length}</span>
                  <Badge variant="secondary">Found</Badge>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Semantic keywords identified
                </p>
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'keywords' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Keyword Analysis</CardTitle>
                <CardDescription>Analysis of keyword usage and optimization</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium">Primary Keyword Density</label>
                    <Progress 
                      value={results.keywordAnalysis.primaryKeywordDensity} 
                      max={3} 
                      showLabel 
                      variant={results.keywordAnalysis.primaryKeywordDensity > 3 ? 'error' : 'success'}
                    />
                  </div>
                  <div>
                    <label className="text-sm font-medium">Secondary Keyword Density</label>
                    <Progress 
                      value={results.keywordAnalysis.secondaryKeywordDensity} 
                      max={2} 
                      showLabel 
                      variant="default"
                    />
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">LSI Keywords Found</h4>
                  <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2">
                    {results.lsiKeywords.slice(0, 12).map((keyword, index) => (
                      <Badge key={index} variant="outline" className="justify-center">
                        {keyword.keyword}
                      </Badge>
                    ))}
                  </div>
                </div>

                {results.keywordAnalysis.recommendations.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3">Keyword Recommendations</h4>
                    <div className="space-y-2">
                      {results.keywordAnalysis.recommendations.slice(0, 3).map((rec, index) => (
                        <div key={index} className="flex items-start space-x-3 p-3 bg-gray-50 rounded-lg">
                          <PriorityBadge priority={rec.priority} />
                          <div className="flex-1">
                            <p className="text-sm">{rec.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'content' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Content Quality Analysis</CardTitle>
                <CardDescription>Comprehensive analysis of content quality and structure</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{results.contentQuality.wordCount}</div>
                    <div className="text-sm text-muted-foreground">Total Words</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{results.contentQuality.readabilityScore}</div>
                    <div className="text-sm text-muted-foreground">Readability Score</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{results.contentQuality.uniqueWords}</div>
                    <div className="text-sm text-muted-foreground">Unique Words</div>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-3">Heading Structure</h4>
                  <div className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Total Headings</span>
                      <span className="font-medium">{results.headingAnalysis.totalHeadings}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Optimized Headings</span>
                      <span className="font-medium">{results.headingAnalysis.optimizedHeadings}</span>
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-sm">Hierarchy Valid</span>
                      <div className="flex items-center">
                        {results.headingAnalysis.hierarchy.isValid ? (
                          <CheckCircleIcon className="h-5 w-5 text-green-500" />
                        ) : (
                          <ExclamationTriangleIcon className="h-5 w-5 text-red-500" />
                        )}
                      </div>
                    </div>
                  </div>
                </div>

                {!results.headingAnalysis.hierarchy.isValid && (
                  <div>
                    <h4 className="font-medium mb-3 text-red-600">Heading Issues</h4>
                    <div className="space-y-1">
                      {results.headingAnalysis.hierarchy.issues.map((issue, index) => (
                        <div key={index} className="flex items-start space-x-2">
                          <ExclamationTriangleIcon className="h-4 w-4 text-red-500 mt-0.5" />
                          <span className="text-sm text-red-600">{issue}</span>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'competitors' && results.competitorAnalysis && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Competitor Analysis</CardTitle>
                <CardDescription>How your content compares to top competitors</CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{results.competitorAnalysis.averageWordCount}</div>
                    <div className="text-sm text-muted-foreground">Avg. Word Count</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{results.competitorAnalysis.averageScore}</div>
                    <div className="text-sm text-muted-foreground">Avg. SEO Score</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{results.competitorAnalysis.topPerformerScore}</div>
                    <div className="text-sm text-muted-foreground">Top Performer</div>
                  </div>
                </div>

                {results.competitorAnalysis.gaps.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3">Content Gaps</h4>
                    <div className="space-y-2">
                      {results.competitorAnalysis.gaps.map((gap, index) => (
                        <div key={index} className="p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                          <p className="text-sm text-yellow-800">{gap}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {results.competitorAnalysis.opportunities.length > 0 && (
                  <div>
                    <h4 className="font-medium mb-3">Opportunities</h4>
                    <div className="space-y-2">
                      {results.competitorAnalysis.opportunities.map((opp, index) => (
                        <div key={index} className="flex items-start space-x-3 p-3 bg-green-50 border border-green-200 rounded-lg">
                          <Badge variant={opp.impact === 'high' ? 'destructive' : opp.impact === 'medium' ? 'warning' : 'success'}>
                            {opp.impact}
                          </Badge>
                          <div className="flex-1">
                            <p className="text-sm text-green-800">{opp.description}</p>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}

        {activeTab === 'recommendations' && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>SEO Recommendations</CardTitle>
                <CardDescription>Prioritized recommendations to improve your SEO performance</CardDescription>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {results.recommendations.map((rec, index) => (
                    <div key={index} className="flex items-start space-x-4 p-4 border border-gray-200 rounded-lg">
                      <div className="flex-shrink-0">
                        <PriorityBadge priority={rec.priority} />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-medium text-gray-900">{rec.title}</h4>
                        <p className="text-sm text-gray-600 mt-1">{rec.description}</p>
                        <div className="flex items-center mt-2 space-x-4">
                          <span className="text-xs text-gray-500">Expected Impact: {rec.expectedImpact}/10</span>
                          <Progress value={rec.expectedImpact * 10} max={100} size="sm" className="w-20" />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </div>
  )
}

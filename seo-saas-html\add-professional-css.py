#!/usr/bin/env python3
"""
Script to add professional-fixes.css to all HTML files
"""

import os
import re

def add_professional_css_to_file(filepath):
    """Add professional-fixes.css to an HTML file if not already present"""
    try:
        with open(filepath, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if professional-fixes.css is already included
        if 'professional-fixes.css' in content:
            print(f"✓ {filepath} already has professional-fixes.css")
            return
        
        # Find the last CSS link and add professional-fixes.css after it
        css_pattern = r'(\s*<link href="css/animations\.css" rel="stylesheet">)'
        replacement = r'\1\n    <link href="css/professional-fixes.css" rel="stylesheet">'
        
        new_content = re.sub(css_pattern, replacement, content)
        
        if new_content != content:
            with open(filepath, 'w', encoding='utf-8') as f:
                f.write(new_content)
            print(f"✓ Added professional-fixes.css to {filepath}")
        else:
            print(f"⚠ Could not find CSS links in {filepath}")
            
    except Exception as e:
        print(f"✗ Error processing {filepath}: {e}")

def main():
    """Main function to process all HTML files"""
    html_files = [
        'index.html',
        'dashboard.html',
        'content-generator.html',
        'seo-analysis.html',
        'competitor-analysis.html',
        'projects.html',
        'analytics.html',
        'profile.html',
        'settings.html',
        'billing.html',
        'login.html',
        'register.html',
        'forgot-password.html',
        'pricing.html',
        'features.html',
        'about.html',
        'contact.html',
        'test.html'
    ]
    
    print("Adding professional-fixes.css to all HTML files...")
    print("-" * 50)
    
    for html_file in html_files:
        if os.path.exists(html_file):
            add_professional_css_to_file(html_file)
        else:
            print(f"⚠ File not found: {html_file}")
    
    print("-" * 50)
    print("✓ Professional CSS fixes applied to all HTML files!")

if __name__ == "__main__":
    main()

// Groq Content Generation API Route
// Direct access to Groq content generation service

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { groqService } from '@/lib/services/groq';
import { withRateLimit, withSubscriptionLimit, withCostLimit } from '@/lib/rate-limiter';
import { createSupabaseServerClient } from '@/lib/supabase';

// Request validation schema
const groqGenerationSchema = z.object({
  keyword: z.string().min(1, 'Keyword is required').max(100, 'Keyword too long'),
  location: z.string().max(100, 'Location too long').optional().default(''),
  industry: z.string().min(1, 'Industry is required').max(50, 'Industry too long'),
  contentType: z.enum(['service', 'blog', 'product', 'landing', 'category', 'faq']),
  tone: z.enum(['professional', 'conversational', 'authoritative', 'friendly', 'technical', 'casual']),
  intent: z.enum(['informational', 'commercial', 'transactional', 'navigational']),
  targetWordCount: z.number().min(200).max(5000).default(800),
  includeImages: z.boolean().default(false),
  includeSchema: z.boolean().default(false),
  includeFaq: z.boolean().default(false),
  competitorData: z.any().optional(),
  templateStructure: z.any().optional(),
  brandVoice: z.string().max(500).optional(),
  targetAudience: z.string().max(200).optional(),
});

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    // Parse and validate request body
    const body = await request.json();
    const validatedData = groqGenerationSchema.parse(body);

    // Get user session
    const supabase = createSupabaseServerClient();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user subscription tier
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier, credits_remaining')
      .eq('id', user.id)
      .single();

    const subscriptionTier = profile?.subscription_tier || 'free';

    // Estimate costs
    const tokens = Math.floor(validatedData.targetWordCount * 1.3);
    const estimatedCost = (tokens / 1000) * 0.05; // Groq pricing estimate

    // Check subscription limits
    try {
      await withSubscriptionLimit(
        user.id,
        subscriptionTier,
        'apiCalls',
        async () => ({ success: true })
      );
    } catch (error: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          code: 'SUBSCRIPTION_LIMIT_EXCEEDED'
        },
        { status: 429 }
      );
    }

    // Check cost limits
    try {
      await withCostLimit(
        user.id,
        subscriptionTier,
        estimatedCost,
        async () => ({ success: true })
      );
    } catch (error: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          code: 'COST_LIMIT_EXCEEDED'
        },
        { status: 402 }
      );
    }

    // Check API rate limits
    try {
      await withRateLimit(
        user.id,
        subscriptionTier,
        'groq',
        async () => ({ success: true })
      );
    } catch (error: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          code: 'RATE_LIMIT_EXCEEDED'
        },
        { status: 429 }
      );
    }

    // Generate content
    console.log('Generating content with Groq for user:', user.id, {
      keyword: validatedData.keyword,
      contentType: validatedData.contentType,
      wordCount: validatedData.targetWordCount,
    });

    const result = await groqService.generateContent(validatedData, user.id);

    // Update user credits
    await updateUserCredits(supabase, user.id, 2, estimatedCost);

    const responseTime = Date.now() - startTime;

    console.log('Content generation completed', {
      userId: user.id,
      keyword: validatedData.keyword,
      wordCount: result.seoMetrics.wordCount,
      responseTime,
    });

    return NextResponse.json({
      success: true,
      data: {
        title: result.title,
        metaDescription: result.metaDescription,
        content: result.content,
        outline: result.outline,
        seoMetrics: {
          keywordDensity: result.seoMetrics.keywordDensity,
          wordCount: result.seoMetrics.wordCount,
          readabilityScore: result.seoMetrics.readabilityScore,
          seoScore: result.seoMetrics.seoScore,
          headingCount: result.seoMetrics.headingCount,
        },
        suggestions: result.suggestions,
        schemaMarkup: result.schemaMarkup,
        imagePrompts: result.imagePrompts,
        faqSection: result.faqSection,
        internalLinks: result.internalLinks,
        externalLinks: result.externalLinks,
      },
      usage: {
        creditsUsed: 2,
        costUSD: estimatedCost,
        tokensUsed: tokens,
        responseTime,
      },
    }, {
      status: 200,
      headers: {
        'X-Response-Time': responseTime.toString(),
        'X-Word-Count': result.seoMetrics.wordCount.toString(),
        'X-Credits-Used': '2',
      },
    });

  } catch (error) {
    console.error('Groq generation error:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    // Handle other errors
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    const isGroqError = errorMessage.includes('groq') || errorMessage.includes('rate limit');
    
    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        code: isGroqError ? 'GROQ_API_ERROR' : 'INTERNAL_ERROR',
      },
      { status: isGroqError ? 502 : 500 }
    );
  }
}

// GET endpoint for available models and health check
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'health') {
      // Health check
      const healthCheck = await groqService.healthCheck();
      
      return NextResponse.json({
        success: true,
        data: {
          status: healthCheck.status,
          latency: healthCheck.latency,
          model: healthCheck.model,
          timestamp: new Date().toISOString(),
        },
      });
    }

    if (action === 'models') {
      // Get available models
      const models = groqService.getAvailableModels();
      
      return NextResponse.json({
        success: true,
        data: {
          models: Object.entries(models).map(([id, info]) => ({
            id,
            name: info.name,
            maxTokens: info.maxTokens,
            bestFor: info.bestFor,
            costPer1kTokens: info.costPer1kTokens,
          })),
        },
      });
    }

    // Default: return service info
    return NextResponse.json({
      success: true,
      data: {
        service: 'Groq Content Generation',
        version: '1.0.0',
        endpoints: {
          generate: 'POST /api/groq/generate',
          health: 'GET /api/groq/generate?action=health',
          models: 'GET /api/groq/generate?action=models',
        },
      },
    });

  } catch (error) {
    console.error('GET /api/groq/generate error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}

// Helper function to update user credits
async function updateUserCredits(
  supabase: any,
  userId: string,
  creditsUsed: number,
  costUSD: number
) {
  const { error } = await supabase.rpc('update_user_usage', {
    user_id: userId,
    credits_used: creditsUsed,
    cost_usd: costUSD,
  });

  if (error) {
    console.error('Failed to update user credits:', error);
  }
}
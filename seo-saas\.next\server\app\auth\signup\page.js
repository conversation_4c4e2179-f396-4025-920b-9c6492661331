(()=>{var e={};e.id=271,e.ids=[271],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},5038:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=t(482),r=t(9108),l=t(2563),i=t.n(l),n=t(8300),o={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>n[e]);t.d(s,o);let c=["",{children:["auth",{children:["signup",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,3035)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/signup/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,1342)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/signup/page.tsx"],m="/auth/signup/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/auth/signup/page",pathname:"/auth/signup",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6223:(e,s,t)=>{Promise.resolve().then(t.bind(t,9744))},9744:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>h});var a=t(2295),r=t(3729),l=t(783),i=t.n(l),n=t(5094),o=t(6540),c=t(3673),d=t(3952),m=t(2416),x=t(3969),u=t(7623),p=t(7993);function h(){let[e,s]=r.useState({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1}),[t,l]=r.useState(!1),[h,g]=r.useState(!1),[f,v]=r.useState(!1),[j,N]=r.useState({}),w=(e,t)=>{s(s=>({...s,[e]:t})),j[e]&&N(s=>({...s,[e]:""}))},y=()=>{let s={};return e.firstName.trim()||(s.firstName="First name is required"),e.lastName.trim()||(s.lastName="Last name is required"),e.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||(s.email="Please enter a valid email address"):s.email="Email is required",e.password?e.password.length<8?s.password="Password must be at least 8 characters":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(e.password)||(s.password="Password must contain at least one uppercase letter, one lowercase letter, and one number"):s.password="Password is required",e.confirmPassword?e.password!==e.confirmPassword&&(s.confirmPassword="Passwords do not match"):s.confirmPassword="Please confirm your password",e.agreeToTerms||(s.agreeToTerms="You must agree to the Terms of Service"),N(s),0===Object.keys(s).length},b=async e=>{if(e.preventDefault(),y()){v(!0);try{await new Promise(e=>setTimeout(e,2e3)),window.location.href="/dashboard"}catch(e){N({general:"Failed to create account. Please try again."})}finally{v(!1)}}};return a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx(i(),{href:"/",className:"inline-block",children:a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"SEO Pro"})}),a.jsx("p",{className:"text-gray-600",children:"Create your account and start optimizing"})]}),(0,a.jsxs)(c.Zb,{className:"shadow-lg",children:[(0,a.jsxs)(c.Ol,{className:"space-y-1",children:[a.jsx(c.ll,{className:"text-2xl font-bold text-center",children:"Create Account"}),a.jsx(c.SZ,{className:"text-center",children:"Join thousands of users creating SEO-optimized content"})]}),(0,a.jsxs)(c.aY,{children:[(0,a.jsxs)("form",{onSubmit:b,className:"space-y-4",children:[j.general&&a.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md",children:a.jsx("p",{className:"text-sm text-red-600",children:j.general})}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"firstName",className:"text-sm font-medium text-gray-700",children:"First Name"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx(o.I,{id:"firstName",type:"text",placeholder:"John",value:e.firstName,onChange:e=>w("firstName",e.target.value),error:j.firstName,className:"pl-10",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"lastName",className:"text-sm font-medium text-gray-700",children:"Last Name"}),a.jsx(o.I,{id:"lastName",type:"text",placeholder:"Doe",value:e.lastName,onChange:e=>w("lastName",e.target.value),error:j.lastName,required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(m.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx(o.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:e.email,onChange:e=>w("email",e.target.value),error:j.email,className:"pl-10",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(x.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx(o.I,{id:"password",type:t?"text":"password",placeholder:"Create a strong password",value:e.password,onChange:e=>w("password",e.target.value),error:j.password,className:"pl-10 pr-10",required:!0}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>l(!t),children:t?a.jsx(u.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):a.jsx(p.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"confirmPassword",className:"text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(x.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx(o.I,{id:"confirmPassword",type:h?"text":"password",placeholder:"Confirm your password",value:e.confirmPassword,onChange:e=>w("confirmPassword",e.target.value),error:j.confirmPassword,className:"pl-10 pr-10",required:!0}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>g(!h),children:h?a.jsx(u.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):a.jsx(p.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsxs)("label",{className:"flex items-start space-x-2",children:[a.jsx("input",{type:"checkbox",checked:e.agreeToTerms,onChange:e=>w("agreeToTerms",e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500 mt-1"}),(0,a.jsxs)("span",{className:"text-sm text-gray-600",children:["I agree to the"," ",a.jsx(i(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",a.jsx(i(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})]}),j.agreeToTerms&&a.jsx("p",{className:"text-sm text-red-600",children:j.agreeToTerms})]}),a.jsx(n.z,{type:"submit",className:"w-full",loading:f,disabled:f,children:f?"Creating Account...":"Create Account"})]}),a.jsx("div",{className:"mt-6",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("div",{className:"w-full border-t border-gray-300"})}),a.jsx("div",{className:"relative flex justify-center text-sm",children:a.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or sign up with"})})]})}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,a.jsxs)(n.z,{variant:"outline",className:"w-full",children:[(0,a.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[a.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),a.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),a.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),a.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]}),(0,a.jsxs)(n.z,{variant:"outline",className:"w-full",children:[a.jsx("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{d:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"})}),"Twitter"]})]}),a.jsx("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",a.jsx(i(),{href:"/auth/signin",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in"})]})})]})]})]})})}},3035:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>i});let a=(0,t(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/signup/page.tsx`),{__esModule:r,$$typeof:l}=a,i=a.default},3952:(e,s,t)=>{"use strict";t.d(s,{Z:()=>r});var a=t(3729);let r=a.forwardRef(function({title:e,titleId:s,...t},r){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:r,"aria-labelledby":s},t),e?a.createElement("title",{id:s},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[638,312,337,439,783,72],()=>t(5038));module.exports=a})();
/* SEO Pro - Component Styles */

/* ==================== Buttons ==================== */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: var(--space-3) var(--space-6);
  font-size: var(--text-base);
  font-weight: var(--font-medium);
  line-height: 1;
  text-decoration: none;
  border-radius: var(--radius-md);
  border: none;
  cursor: pointer;
  transition: all var(--transition-fast);
  white-space: nowrap;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.btn:focus {
  outline: 2px solid var(--primary-blue);
  outline-offset: 2px;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Button Variants */
.btn-primary {
  background: var(--primary-gradient);
  color: var(--white);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2);
}

.btn-primary:hover:not(:disabled) {
  background: var(--primary-gradient-hover);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

.btn-primary:active:not(:disabled) {
  transform: translateY(0);
  box-shadow: 0 4px 6px rgba(59, 130, 246, 0.2);
}

.btn-secondary {
  background: var(--gray-100);
  color: var(--gray-700);
  border: 1px solid var(--gray-200);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--gray-200);
  border-color: var(--gray-300);
}

.btn-outline {
  background: transparent;
  color: var(--primary-blue);
  border: 2px solid var(--primary-blue);
}

.btn-outline:hover:not(:disabled) {
  background: var(--primary-blue);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.2);
}

.btn-ghost {
  background: transparent;
  color: var(--gray-700);
  border: none;
}

.btn-ghost:hover:not(:disabled) {
  background: var(--gray-100);
}

/* Button Sizes */
.btn-sm {
  padding: var(--space-2) var(--space-4);
  font-size: var(--text-sm);
}

.btn-large {
  padding: var(--space-4) var(--space-8);
  font-size: var(--text-lg);
}

/* Button Icons */
.btn-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-left: var(--space-2);
}

.btn-icon-only {
  padding: var(--space-3);
}

/* ==================== Cards ==================== */
.card {
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  overflow: hidden;
  transition: all var(--transition-normal);
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: var(--shadow-xl);
}

.card-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-100);
}

.card-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--gray-900);
  margin: 0;
}

.card-description {
  font-size: var(--text-sm);
  color: var(--gray-600);
  margin-top: var(--space-2);
}

.card-content {
  padding: var(--space-6);
}

.card-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-100);
  background: var(--gray-50);
}

/* Feature Cards */
.feature-card {
  padding: var(--space-8);
  text-align: center;
  background: var(--white);
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-md);
  transition: all var(--transition-normal);
  border: 1px solid var(--gray-100);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: var(--shadow-xl);
  border-color: var(--primary-blue);
}

.feature-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto var(--space-6);
  padding: var(--space-4);
  border-radius: var(--radius-xl);
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon svg {
  width: 2rem;
  height: 2rem;
}

.ai-icon {
  background: linear-gradient(135deg, #3b82f6 0%, #60a5fa 100%);
  color: var(--white);
}

.analysis-icon {
  background: linear-gradient(135deg, #10b981 0%, #34d399 100%);
  color: var(--white);
}

.competitor-icon {
  background: linear-gradient(135deg, #7c3aed 0%, #a78bfa 100%);
  color: var(--white);
}

.industry-icon {
  background: linear-gradient(135deg, #f59e0b 0%, #fbbf24 100%);
  color: var(--white);
}

.serp-icon {
  background: linear-gradient(135deg, #ef4444 0%, #f87171 100%);
  color: var(--white);
}

.keyword-icon {
  background: linear-gradient(135deg, #8b5cf6 0%, #a78bfa 100%);
  color: var(--white);
}

.feature-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--gray-900);
  margin-bottom: var(--space-3);
}

.feature-description {
  color: var(--gray-600);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-4);
}

.feature-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.feature-list li {
  position: relative;
  padding-left: var(--space-6);
  margin-bottom: var(--space-2);
  color: var(--gray-700);
  font-size: var(--text-sm);
}

.feature-list li::before {
  content: "✓";
  position: absolute;
  left: 0;
  color: var(--success-green);
  font-weight: var(--font-bold);
}

/* Stat Cards */
.stat-card {
  background: var(--white);
  padding: var(--space-6);
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--gray-100);
  text-align: center;
}

.stat-number {
  font-size: var(--text-4xl);
  font-weight: var(--font-bold);
  color: var(--primary-blue);
  display: block;
  margin-bottom: var(--space-2);
}

.stat-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* ==================== Forms ==================== */
.form-group {
  margin-bottom: var(--space-6);
}

.form-label {
  display: block;
  font-size: var(--text-sm);
  font-weight: var(--font-medium);
  color: var(--gray-700);
  margin-bottom: var(--space-2);
}

.form-label-required::after {
  content: " *";
  color: var(--error-red);
}

.form-input,
.form-textarea,
.form-select {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  font-size: var(--text-base);
  color: var(--gray-900);
  background: var(--white);
  border: 2px solid var(--gray-200);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
  appearance: none;
}

.form-input:focus,
.form-textarea:focus,
.form-select:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input::placeholder,
.form-textarea::placeholder {
  color: var(--gray-400);
}

.form-textarea {
  min-height: 120px;
  resize: vertical;
}

.form-select {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%236b7280' d='M6 9L1 4h10z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right var(--space-3) center;
  padding-right: var(--space-10);
}

.form-checkbox,
.form-radio {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: var(--space-2);
  cursor: pointer;
}

.form-help {
  font-size: var(--text-sm);
  color: var(--gray-600);
  margin-top: var(--space-2);
}

.form-error {
  font-size: var(--text-sm);
  color: var(--error-red);
  margin-top: var(--space-2);
}

/* Input Groups */
.input-group {
  position: relative;
  display: flex;
  align-items: center;
}

.input-group-prepend,
.input-group-append {
  display: flex;
  align-items: center;
  padding: var(--space-3) var(--space-4);
  background: var(--gray-100);
  border: 2px solid var(--gray-200);
  color: var(--gray-600);
  font-size: var(--text-sm);
  white-space: nowrap;
}

.input-group-prepend {
  border-right: 0;
  border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.input-group-append {
  border-left: 0;
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

.input-group .form-input {
  border-radius: 0;
}

.input-group .form-input:first-child {
  border-radius: var(--radius-md) 0 0 var(--radius-md);
}

.input-group .form-input:last-child {
  border-radius: 0 var(--radius-md) var(--radius-md) 0;
}

/* Form Steps */
.form-steps {
  display: flex;
  justify-content: space-between;
  margin-bottom: var(--space-8);
  position: relative;
}

.form-steps::before {
  content: "";
  position: absolute;
  top: 50%;
  left: 0;
  right: 0;
  height: 2px;
  background: var(--gray-200);
  z-index: -1;
}

.form-step {
  display: flex;
  flex-direction: column;
  align-items: center;
  background: var(--white);
  padding: 0 var(--space-2);
}

.form-step-number {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: var(--radius-full);
  background: var(--gray-200);
  color: var(--gray-600);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-2);
  transition: all var(--transition-fast);
}

.form-step.active .form-step-number {
  background: var(--primary-gradient);
  color: var(--white);
}

.form-step.completed .form-step-number {
  background: var(--success-green);
  color: var(--white);
}

.form-step-label {
  font-size: var(--text-sm);
  color: var(--gray-600);
  text-align: center;
}

/* ==================== Navigation ==================== */
.header {
  background: var(--white);
  box-shadow: var(--shadow-sm);
  position: sticky;
  top: 0;
  z-index: var(--z-dropdown);
}

.nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 4rem;
}

.nav-brand {
  display: flex;
  align-items: center;
}

.logo {
  display: flex;
  align-items: center;
  text-decoration: none;
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--gray-900);
}

.logo-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.nav-menu {
  display: none;
  list-style: none;
  margin: 0;
  padding: 0;
  gap: var(--space-8);
}

@media (min-width: 1024px) {
  .nav-menu {
    display: flex;
  }
}

.nav-link {
  color: var(--gray-700);
  font-weight: var(--font-medium);
  text-decoration: none;
  transition: color var(--transition-fast);
  position: relative;
}

.nav-link:hover {
  color: var(--primary-blue);
}

.nav-link::after {
  content: "";
  position: absolute;
  bottom: -4px;
  left: 0;
  width: 0;
  height: 2px;
  background: var(--primary-gradient);
  transition: width var(--transition-fast);
}

.nav-link:hover::after {
  width: 100%;
}

.nav-auth {
  display: none;
  align-items: center;
  gap: var(--space-4);
}

@media (min-width: 1024px) {
  .nav-auth {
    display: flex;
  }
}

/* Mobile Menu Button */
.mobile-menu-btn {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  width: 2.5rem;
  height: 2.5rem;
  background: none;
  border: none;
  cursor: pointer;
  padding: var(--space-2);
}

@media (min-width: 1024px) {
  .mobile-menu-btn {
    display: none;
  }
}

.hamburger,
.hamburger::before,
.hamburger::after {
  width: 1.5rem;
  height: 2px;
  background: var(--gray-700);
  transition: all var(--transition-fast);
}

.hamburger {
  position: relative;
}

.hamburger::before,
.hamburger::after {
  content: "";
  position: absolute;
  left: 0;
}

.hamburger::before {
  top: -6px;
}

.hamburger::after {
  bottom: -6px;
}

.mobile-menu-btn.active .hamburger {
  background: transparent;
}

.mobile-menu-btn.active .hamburger::before {
  transform: rotate(45deg);
  top: 0;
}

.mobile-menu-btn.active .hamburger::after {
  transform: rotate(-45deg);
  bottom: 0;
}

/* Mobile Menu */
.mobile-menu {
  display: none;
  position: fixed;
  top: 4rem;
  left: 0;
  right: 0;
  background: var(--white);
  box-shadow: var(--shadow-lg);
  max-height: calc(100vh - 4rem);
  overflow-y: auto;
  z-index: var(--z-dropdown);
}

.mobile-menu.active {
  display: block;
}

.mobile-nav {
  list-style: none;
  margin: 0;
  padding: var(--space-4);
}

.mobile-nav-link {
  display: block;
  padding: var(--space-3) var(--space-4);
  color: var(--gray-700);
  text-decoration: none;
  font-weight: var(--font-medium);
  border-radius: var(--radius-md);
  transition: all var(--transition-fast);
}

.mobile-nav-link:hover {
  background: var(--gray-100);
  color: var(--primary-blue);
}

.mobile-cta {
  width: 100%;
  margin-top: var(--space-4);
}

/* ==================== Hero Section ==================== */
.hero {
  padding: var(--space-20) 0;
  background: linear-gradient(135deg, var(--bg-accent) 0%, var(--bg-secondary) 100%);
  position: relative;
  overflow: hidden;
}

.hero::before {
  content: "";
  position: absolute;
  top: -50%;
  right: -50%;
  width: 200%;
  height: 200%;
  background: radial-gradient(circle, rgba(59, 130, 246, 0.1) 0%, transparent 70%);
  animation: float 20s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translate(0, 0) rotate(0deg); }
  33% { transform: translate(30px, -30px) rotate(120deg); }
  66% { transform: translate(-20px, 20px) rotate(240deg); }
}

.hero-content {
  position: relative;
  z-index: 1;
  display: grid;
  gap: var(--space-12);
  align-items: center;
}

@media (min-width: 1024px) {
  .hero-content {
    grid-template-columns: 1fr 1fr;
  }
}

.hero-text {
  text-align: center;
}

@media (min-width: 1024px) {
  .hero-text {
    text-align: left;
  }
}

.hero-title {
  font-size: var(--text-4xl);
  font-weight: var(--font-extrabold);
  line-height: 1.1;
  color: var(--gray-900);
  margin-bottom: var(--space-6);
}

@media (min-width: 768px) {
  .hero-title {
    font-size: var(--text-5xl);
  }
}

@media (min-width: 1024px) {
  .hero-title {
    font-size: var(--text-6xl);
  }
}

.hero-description {
  font-size: var(--text-lg);
  color: var(--gray-600);
  line-height: var(--leading-relaxed);
  margin-bottom: var(--space-8);
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

@media (min-width: 1024px) {
  .hero-description {
    margin-left: 0;
    font-size: var(--text-xl);
  }
}

.hero-cta {
  display: flex;
  flex-wrap: wrap;
  gap: var(--space-4);
  justify-content: center;
}

@media (min-width: 1024px) {
  .hero-cta {
    justify-content: flex-start;
  }
}

.hero-stats {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: var(--space-4);
  margin-top: var(--space-12);
}

.stat {
  text-align: center;
}

.hero-image {
  position: relative;
}

.hero-img {
  width: 100%;
  height: auto;
  border-radius: var(--radius-xl);
  box-shadow: var(--shadow-2xl);
  animation: float-image 6s ease-in-out infinite;
}

@keyframes float-image {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

/* ==================== Sections ==================== */
.section {
  padding: var(--space-20) 0;
}

.section-header {
  text-align: center;
  margin-bottom: var(--space-16);
}

.section-title {
  font-size: var(--text-3xl);
  font-weight: var(--font-bold);
  color: var(--gray-900);
  margin-bottom: var(--space-4);
}

@media (min-width: 768px) {
  .section-title {
    font-size: var(--text-4xl);
  }
}

.section-description {
  font-size: var(--text-lg);
  color: var(--gray-600);
  max-width: 600px;
  margin: 0 auto;
  line-height: var(--leading-relaxed);
}

/* ==================== Features Grid ==================== */
.features-grid {
  display: grid;
  gap: var(--space-8);
}

@media (min-width: 768px) {
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
}

@media (min-width: 1024px) {
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* ==================== Badges ==================== */
.badge {
  display: inline-flex;
  align-items: center;
  padding: var(--space-1) var(--space-3);
  font-size: var(--text-xs);
  font-weight: var(--font-medium);
  border-radius: var(--radius-full);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.badge-primary {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-blue);
}

.badge-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-green);
}

.badge-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-orange);
}

.badge-error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-red);
}

/* ==================== Progress Indicators ==================== */
.progress {
  width: 100%;
  height: 0.5rem;
  background: var(--gray-200);
  border-radius: var(--radius-full);
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: var(--primary-gradient);
  border-radius: var(--radius-full);
  transition: width var(--transition-slow);
  position: relative;
  overflow: hidden;
}

.progress-bar::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
  animation: shimmer 2s infinite;
}

@keyframes shimmer {
  0% { transform: translateX(-100%); }
  100% { transform: translateX(100%); }
}

/* Circular Progress */
.progress-circle {
  width: 120px;
  height: 120px;
  position: relative;
  display: inline-block;
}

.progress-circle svg {
  width: 100%;
  height: 100%;
  transform: rotate(-90deg);
}

.progress-circle-bg {
  fill: none;
  stroke: var(--gray-200);
  stroke-width: 8;
}

.progress-circle-fill {
  fill: none;
  stroke: url(#gradient);
  stroke-width: 8;
  stroke-linecap: round;
  stroke-dasharray: 314;
  stroke-dashoffset: 314;
  transition: stroke-dashoffset var(--transition-slow);
}

.progress-circle-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--gray-900);
}

/* ==================== Modals ==================== */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: none;
  align-items: center;
  justify-content: center;
  z-index: var(--z-modal);
  padding: var(--space-4);
}

.modal-overlay.active {
  display: flex;
}

.modal {
  background: var(--white);
  border-radius: var(--radius-xl);
  max-width: 500px;
  width: 100%;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: var(--shadow-2xl);
  animation: modal-enter 0.3s ease-out;
}

@keyframes modal-enter {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.modal-header {
  padding: var(--space-6);
  border-bottom: 1px solid var(--gray-100);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title {
  font-size: var(--text-xl);
  font-weight: var(--font-semibold);
  color: var(--gray-900);
  margin: 0;
}

.modal-close {
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  border: none;
  cursor: pointer;
  color: var(--gray-500);
  transition: color var(--transition-fast);
}

.modal-close:hover {
  color: var(--gray-700);
}

.modal-body {
  padding: var(--space-6);
  overflow-y: auto;
  max-height: calc(90vh - 140px);
}

.modal-footer {
  padding: var(--space-6);
  border-top: 1px solid var(--gray-100);
  display: flex;
  gap: var(--space-4);
  justify-content: flex-end;
}

/* ==================== Tables ==================== */
.table-container {
  overflow-x: auto;
  border-radius: var(--radius-lg);
  box-shadow: var(--shadow-md);
}

.table {
  width: 100%;
  background: var(--white);
  border-collapse: collapse;
}

.table th,
.table td {
  padding: var(--space-4);
  text-align: left;
  border-bottom: 1px solid var(--gray-100);
}

.table th {
  background: var(--gray-50);
  font-weight: var(--font-semibold);
  color: var(--gray-700);
  font-size: var(--text-sm);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table tbody tr {
  transition: background var(--transition-fast);
}

.table tbody tr:hover {
  background: var(--gray-50);
}

.table tbody tr:last-child td {
  border-bottom: none;
}

/* ==================== Loading States ==================== */
.skeleton {
  background: linear-gradient(90deg, var(--gray-200) 25%, var(--gray-100) 50%, var(--gray-200) 75%);
  background-size: 200% 100%;
  animation: skeleton-loading 1.5s infinite;
}

@keyframes skeleton-loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.skeleton-text {
  height: 1rem;
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-2);
}

.skeleton-title {
  height: 2rem;
  border-radius: var(--radius-sm);
  margin-bottom: var(--space-4);
  width: 60%;
}

.skeleton-card {
  height: 200px;
  border-radius: var(--radius-lg);
}

.spinner {
  width: 2rem;
  height: 2rem;
  border: 3px solid var(--gray-200);
  border-top: 3px solid var(--primary-blue);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* ==================== Alerts ==================== */
.alert {
  padding: var(--space-4);
  border-radius: var(--radius-md);
  display: flex;
  align-items: start;
  gap: var(--space-3);
  margin-bottom: var(--space-4);
}

.alert-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.alert-content {
  flex: 1;
}

.alert-title {
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-1);
}

.alert-message {
  font-size: var(--text-sm);
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  color: var(--primary-blue);
}

.alert-success {
  background: rgba(16, 185, 129, 0.1);
  color: var(--success-green);
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  color: var(--warning-orange);
}

.alert-error {
  background: rgba(239, 68, 68, 0.1);
  color: var(--error-red);
}

/* ==================== Footer ==================== */
.footer {
  background: var(--gray-900);
  color: var(--gray-300);
  padding: var(--space-16) 0 var(--space-8);
  margin-top: var(--space-20);
}

.footer-content {
  display: grid;
  gap: var(--space-12);
  margin-bottom: var(--space-12);
}

@media (min-width: 768px) {
  .footer-content {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }
}

.footer-brand {
  max-width: 300px;
}

.footer-logo {
  font-size: var(--text-2xl);
  font-weight: var(--font-bold);
  color: var(--white);
  text-decoration: none;
  display: inline-block;
  margin-bottom: var(--space-4);
}

.footer-description {
  color: var(--gray-400);
  line-height: var(--leading-relaxed);
}

.footer-section h4 {
  color: var(--white);
  font-size: var(--text-lg);
  font-weight: var(--font-semibold);
  margin-bottom: var(--space-4);
}

.footer-links {
  list-style: none;
  padding: 0;
  margin: 0;
}

.footer-links li {
  margin-bottom: var(--space-2);
}

.footer-links a {
  color: var(--gray-400);
  text-decoration: none;
  transition: color var(--transition-fast);
}

.footer-links a:hover {
  color: var(--white);
}

.footer-bottom {
  padding-top: var(--space-8);
  border-top: 1px solid var(--gray-800);
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: var(--space-4);
}

.footer-copyright {
  color: var(--gray-500);
  font-size: var(--text-sm);
}

.footer-social {
  display: flex;
  gap: var(--space-4);
}

.social-link {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--gray-800);
  border-radius: var(--radius-full);
  color: var(--gray-400);
  transition: all var(--transition-fast);
}

.social-link:hover {
  background: var(--primary-gradient);
  color: var(--white);
  transform: translateY(-2px);
}
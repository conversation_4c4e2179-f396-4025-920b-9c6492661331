/* SEO Pro - Responsive Styles */

/* ==================== Mobile First Base (320px - 767px) ==================== */
/* Base styles are defined in main.css and components.css */

/* ==================== Tablet Styles (768px - 1023px) ==================== */
@media (min-width: 768px) {
  /* Container adjustments */
  .container {
    padding: 0 var(--space-6);
  }
  
  /* Typography adjustments */
  h1 {
    font-size: var(--text-5xl);
  }
  
  h2 {
    font-size: var(--text-4xl);
  }
  
  h3 {
    font-size: var(--text-3xl);
  }
  
  /* Hero adjustments */
  .hero {
    padding: var(--space-24) 0;
  }
  
  .hero-title {
    font-size: var(--text-5xl);
  }
  
  .hero-description {
    font-size: var(--text-xl);
  }
  
  /* Grid adjustments */
  .features-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .pricing-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .testimonials-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  /* Form adjustments */
  .form-row {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: var(--space-6);
  }
  
  /* Dashboard adjustments */
  .dashboard-stats {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .dashboard-main {
    display: grid;
    grid-template-columns: 1fr 300px;
    gap: var(--space-8);
  }
  
  /* Modal adjustments */
  .modal {
    max-width: 600px;
  }
  
  /* Footer adjustments */
  .footer-content {
    grid-template-columns: 2fr 1fr 1fr 1fr;
  }
}

/* ==================== Desktop Styles (1024px - 1279px) ==================== */
@media (min-width: 1024px) {
  /* Container adjustments */
  .container {
    padding: 0 var(--space-8);
  }
  
  /* Typography adjustments */
  h1 {
    font-size: var(--text-6xl);
  }
  
  h2 {
    font-size: var(--text-5xl);
  }
  
  /* Navigation - Show desktop menu */
  .nav-menu {
    display: flex;
  }
  
  .nav-auth {
    display: flex;
  }
  
  .mobile-menu-btn {
    display: none;
  }
  
  .mobile-menu {
    display: none !important;
  }
  
  /* Hero adjustments */
  .hero-content {
    grid-template-columns: 1fr 1fr;
    align-items: center;
  }
  
  .hero-text {
    text-align: left;
  }
  
  .hero-description {
    margin-left: 0;
    margin-right: 0;
  }
  
  .hero-cta {
    justify-content: flex-start;
  }
  
  /* Grid adjustments */
  .features-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .pricing-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  .testimonials-grid {
    grid-template-columns: repeat(3, 1fr);
  }
  
  /* Dashboard Layout */
  .dashboard-layout {
    display: grid;
    grid-template-columns: 250px 1fr;
    min-height: calc(100vh - 4rem);
  }
  
  .dashboard-sidebar {
    background: var(--white);
    border-right: 1px solid var(--gray-200);
    padding: var(--space-6);
    position: sticky;
    top: 4rem;
    height: calc(100vh - 4rem);
    overflow-y: auto;
  }
  
  .dashboard-content {
    padding: var(--space-8);
    background: var(--bg-secondary);
  }
  
  .dashboard-stats {
    grid-template-columns: repeat(4, 1fr);
  }
  
  /* Form adjustments */
  .form-row-3 {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: var(--space-6);
  }
  
  /* Table adjustments */
  .table-responsive {
    overflow-x: visible;
  }
  
  /* Modal adjustments */
  .modal {
    max-width: 800px;
  }
  
  .modal-large {
    max-width: 1200px;
  }
  
  /* Section spacing */
  .section {
    padding: var(--space-24) 0;
  }
}

/* ==================== Large Desktop Styles (1280px - 1535px) ==================== */
@media (min-width: 1280px) {
  /* Container max-width */
  .container {
    max-width: var(--container-xl);
  }
  
  /* Enhanced spacing */
  .hero {
    padding: var(--space-32) 0;
  }
  
  .section {
    padding: var(--space-32) 0;
  }
  
  /* Dashboard enhancements */
  .dashboard-sidebar {
    width: 280px;
  }
  
  .dashboard-layout {
    grid-template-columns: 280px 1fr;
  }
  
  /* Grid enhancements */
  .features-grid-4 {
    grid-template-columns: repeat(4, 1fr);
  }
  
  /* Form enhancements */
  .form-row-4 {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: var(--space-6);
  }
}

/* ==================== Extra Large Desktop Styles (1536px+) ==================== */
@media (min-width: 1536px) {
  /* Container max-width */
  .container {
    max-width: var(--container-2xl);
  }
  
  /* Enhanced typography */
  .hero-title {
    font-size: 4.5rem;
  }
  
  .section-title {
    font-size: 3.5rem;
  }
  
  /* Grid enhancements */
  .features-grid-6 {
    grid-template-columns: repeat(6, 1fr);
  }
}

/* ==================== Mobile Landscape Specific ==================== */
@media (min-width: 568px) and (max-width: 767px) and (orientation: landscape) {
  .hero {
    padding: var(--space-12) 0;
  }
  
  .hero-title {
    font-size: var(--text-3xl);
  }
  
  .mobile-menu {
    max-height: calc(100vh - 3rem);
  }
}

/* ==================== Print Styles ==================== */
@media print {
  /* Hide navigation and non-essential elements */
  .header,
  .footer,
  .mobile-menu,
  .nav-auth,
  .hero-cta,
  .no-print {
    display: none !important;
  }
  
  /* Reset backgrounds */
  body {
    background: white;
    color: black;
  }
  
  .hero {
    background: none;
    padding: var(--space-8) 0;
  }
  
  /* Improve readability */
  .container {
    max-width: 100%;
    padding: 0;
  }
  
  h1, h2, h3, h4, h5, h6 {
    color: black;
    page-break-after: avoid;
  }
  
  img {
    page-break-inside: avoid;
  }
  
  /* Show URLs for links */
  a[href]:after {
    content: " (" attr(href) ")";
  }
  
  /* Page breaks */
  .section {
    page-break-inside: avoid;
  }
}

/* ==================== High DPI / Retina Displays ==================== */
@media (-webkit-min-device-pixel-ratio: 2),
       (min-resolution: 192dpi),
       (min-resolution: 2dppx) {
  /* Use higher resolution images */
  .hero-img,
  .feature-img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
  
  /* Thinner borders for sharper appearance */
  .btn,
  .card,
  .form-input {
    border-width: 0.5px;
  }
}

/* ==================== Reduced Motion ==================== */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
  
  .hero-img {
    animation: none;
  }
  
  .progress-bar::after {
    animation: none;
  }
}

/* ==================== Dark Mode Support ==================== */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #0f0f0f;
    --gray-900: #f9fafb;
    --gray-800: #f3f4f6;
    --gray-700: #e5e7eb;
    --gray-600: #d1d5db;
    --gray-500: #9ca3af;
    --gray-400: #6b7280;
    --gray-300: #4b5563;
    --gray-200: #374151;
    --gray-100: #1f2937;
    --gray-50: #111827;
  }
  
  body {
    background-color: var(--bg-secondary);
    color: var(--gray-900);
  }
  
  .card,
  .modal,
  .header {
    background: var(--bg-primary);
  }
  
  .hero {
    background: linear-gradient(135deg, #1a1a1a 0%, #0f0f0f 100%);
  }
  
  .footer {
    background: #000000;
  }
  
  .form-input,
  .form-textarea,
  .form-select {
    background: var(--gray-100);
    border-color: var(--gray-200);
    color: var(--gray-900);
  }
  
  .table {
    background: var(--bg-primary);
  }
  
  .table th {
    background: var(--gray-100);
  }
  
  /* Adjust shadows for dark mode */
  .shadow-sm,
  .shadow,
  .shadow-lg,
  .shadow-xl,
  .shadow-2xl {
    box-shadow: none;
    border: 1px solid var(--gray-200);
  }
}

/* ==================== Custom Breakpoint Utilities ==================== */
/* Hide on mobile only */
@media (max-width: 767px) {
  .hide-mobile {
    display: none !important;
  }
}

/* Hide on tablet only */
@media (min-width: 768px) and (max-width: 1023px) {
  .hide-tablet {
    display: none !important;
  }
}

/* Hide on desktop only */
@media (min-width: 1024px) {
  .hide-desktop {
    display: none !important;
  }
}

/* Show on mobile only */
@media (min-width: 768px) {
  .show-mobile {
    display: none !important;
  }
}

/* Show on tablet only */
.show-tablet {
  display: none !important;
}

@media (min-width: 768px) and (max-width: 1023px) {
  .show-tablet {
    display: block !important;
  }
}

/* Show on desktop only */
.show-desktop {
  display: none !important;
}

@media (min-width: 1024px) {
  .show-desktop {
    display: block !important;
  }
}
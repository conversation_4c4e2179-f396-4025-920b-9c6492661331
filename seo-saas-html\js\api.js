// API integration utilities for SEO Pro
// This file handles all external API calls (<PERSON><PERSON><PERSON>, <PERSON><PERSON>, etc.)

class APIClient {
    constructor() {
        this.groqApiKey = CONFIG.APIS.GROQ.API_KEY;
        this.serperApiKey = CONFIG.APIS.SERPER.API_KEY;
        this.requestQueue = [];
        this.isProcessing = false;
    }

    // Generic API request method with error handling and retries
    async makeRequest(url, options = {}, retries = 3) {
        const defaultOptions = {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
            },
            ...options
        };

        for (let attempt = 1; attempt <= retries; attempt++) {
            try {
                const response = await fetch(url, defaultOptions);
                
                if (!response.ok) {
                    const errorData = await response.json().catch(() => ({}));
                    throw new Error(`HTTP ${response.status}: ${errorData.message || response.statusText}`);
                }

                return await response.json();
            } catch (error) {
                console.error(`API request attempt ${attempt} failed:`, error);
                
                if (attempt === retries) {
                    throw error;
                }
                
                // Wait before retrying (exponential backoff)
                await new Promise(resolve => setTimeout(resolve, Math.pow(2, attempt) * 1000));
            }
        }
    }

    // Groq API methods for content generation
    async generateContent(prompt, options = {}) {
        try {
            const {
                model = CONFIG.APIS.GROQ.DEFAULT_MODEL,
                maxTokens = CONFIG.APIS.GROQ.MAX_TOKENS,
                temperature = CONFIG.APIS.GROQ.TEMPERATURE,
                systemPrompt = "You are an expert SEO content writer. Create high-quality, engaging, and SEO-optimized content."
            } = options;

            const requestBody = {
                model,
                messages: [
                    {
                        role: "system",
                        content: systemPrompt
                    },
                    {
                        role: "user",
                        content: prompt
                    }
                ],
                max_tokens: maxTokens,
                temperature,
                stream: false
            };

            const response = await this.makeRequest(
                `${CONFIG.APIS.GROQ.BASE_URL}/chat/completions`,
                {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${this.groqApiKey}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                }
            );

            if (response.choices && response.choices.length > 0) {
                return {
                    success: true,
                    content: response.choices[0].message.content,
                    usage: response.usage,
                    model: response.model
                };
            } else {
                throw new Error('No content generated');
            }
        } catch (error) {
            console.error('Content generation error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Generate SEO-optimized content with specific parameters
    async generateSEOContent(params) {
        const {
            keywords,
            industry,
            contentType,
            tone,
            wordCount,
            targetAudience,
            additionalInstructions = ''
        } = params;

        const prompt = this.buildContentPrompt({
            keywords,
            industry,
            contentType,
            tone,
            wordCount,
            targetAudience,
            additionalInstructions
        });

        const systemPrompt = `You are an expert SEO content writer with deep knowledge of search engine optimization, content marketing, and ${industry} industry. 

Your task is to create high-quality, engaging, and SEO-optimized content that:
1. Naturally incorporates the target keywords
2. Follows SEO best practices
3. Provides genuine value to readers
4. Matches the specified tone and style
5. Is appropriate for the target audience
6. Includes proper heading structure (H1, H2, H3)
7. Has optimal keyword density (1-3%)
8. Includes relevant LSI keywords
9. Is well-structured and readable

Always prioritize quality and user value over keyword stuffing.`;

        return await this.generateContent(prompt, {
            systemPrompt,
            maxTokens: Math.min(CONFIG.APIS.GROQ.MAX_TOKENS, Math.ceil(wordCount * 1.5))
        });
    }

    // Build content generation prompt
    buildContentPrompt(params) {
        const {
            keywords,
            industry,
            contentType,
            tone,
            wordCount,
            targetAudience,
            additionalInstructions
        } = params;

        const primaryKeyword = Array.isArray(keywords) ? keywords[0] : keywords;
        const secondaryKeywords = Array.isArray(keywords) ? keywords.slice(1) : [];

        return `Create a ${contentType} about "${primaryKeyword}" for the ${industry} industry.

Target Specifications:
- Primary Keyword: ${primaryKeyword}
- Secondary Keywords: ${secondaryKeywords.join(', ')}
- Industry: ${industry}
- Content Type: ${contentType}
- Tone: ${tone}
- Target Word Count: ${wordCount} words
- Target Audience: ${targetAudience}

Requirements:
1. Create an engaging title that includes the primary keyword
2. Write a compelling introduction that hooks the reader
3. Use proper heading structure (H1, H2, H3) with keywords
4. Naturally incorporate all keywords throughout the content
5. Include relevant statistics, examples, or case studies
6. Add a strong conclusion with a call-to-action
7. Ensure the content is valuable and informative
8. Optimize for search engines while maintaining readability

${additionalInstructions ? `Additional Instructions: ${additionalInstructions}` : ''}

Please provide the complete content with proper formatting and structure.`;
    }

    // Serper API methods for search and competitor analysis
    async searchGoogle(query, options = {}) {
        try {
            const {
                location = 'us',
                language = 'en',
                num = 10,
                type = 'search'
            } = options;

            const requestBody = {
                q: query,
                gl: location,
                hl: language,
                num
            };

            const endpoint = CONFIG.APIS.SERPER.ENDPOINTS[type.toUpperCase()] || CONFIG.APIS.SERPER.ENDPOINTS.SEARCH;
            
            const response = await this.makeRequest(
                `${CONFIG.APIS.SERPER.BASE_URL}${endpoint}`,
                {
                    method: 'POST',
                    headers: {
                        'X-API-KEY': this.serperApiKey,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                }
            );

            return {
                success: true,
                data: response
            };
        } catch (error) {
            console.error('Google search error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Analyze competitors for a specific keyword
    async analyzeCompetitors(keyword, options = {}) {
        try {
            const searchResult = await this.searchGoogle(keyword, options);
            
            if (!searchResult.success) {
                throw new Error(searchResult.error);
            }

            const competitors = [];
            const organicResults = searchResult.data.organic || [];

            for (const result of organicResults.slice(0, 10)) {
                const competitor = {
                    position: result.position,
                    title: result.title,
                    link: result.link,
                    snippet: result.snippet,
                    domain: new URL(result.link).hostname,
                    // Additional analysis would be done here
                    estimatedTraffic: this.estimateTraffic(result.position),
                    titleLength: result.title.length,
                    snippetLength: result.snippet.length,
                    hasKeyword: result.title.toLowerCase().includes(keyword.toLowerCase())
                };

                competitors.push(competitor);
            }

            return {
                success: true,
                keyword,
                competitors,
                totalResults: searchResult.data.searchInformation?.totalResults,
                searchTime: searchResult.data.searchInformation?.searchTime
            };
        } catch (error) {
            console.error('Competitor analysis error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Estimate traffic based on SERP position
    estimateTraffic(position) {
        const ctrRates = {
            1: 0.284,
            2: 0.147,
            3: 0.103,
            4: 0.073,
            5: 0.053,
            6: 0.040,
            7: 0.031,
            8: 0.025,
            9: 0.020,
            10: 0.016
        };

        return ctrRates[position] || 0.01;
    }

    // Get keyword suggestions and search volume data
    async getKeywordData(keyword, options = {}) {
        try {
            // Use Google search to get related queries and suggestions
            const searchResult = await this.searchGoogle(keyword, options);
            
            if (!searchResult.success) {
                throw new Error(searchResult.error);
            }

            const keywordData = {
                keyword,
                searchVolume: this.estimateSearchVolume(searchResult.data.searchInformation?.totalResults),
                competition: this.analyzeCompetition(searchResult.data.organic || []),
                relatedQueries: searchResult.data.relatedSearches || [],
                peopleAlsoAsk: searchResult.data.peopleAlsoAsk || [],
                suggestions: this.extractKeywordSuggestions(searchResult.data)
            };

            return {
                success: true,
                data: keywordData
            };
        } catch (error) {
            console.error('Keyword data error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Estimate search volume based on total results
    estimateSearchVolume(totalResults) {
        if (!totalResults) return 'Unknown';
        
        const results = parseInt(totalResults.replace(/,/g, ''));
        
        if (results > 100000000) return 'Very High (100K+)';
        if (results > 10000000) return 'High (10K-100K)';
        if (results > 1000000) return 'Medium (1K-10K)';
        if (results > 100000) return 'Low (100-1K)';
        return 'Very Low (<100)';
    }

    // Analyze competition level
    analyzeCompetition(organicResults) {
        const domainAuthorities = organicResults.map(result => {
            const domain = new URL(result.link).hostname;
            // This would typically use a domain authority API
            return this.estimateDomainAuthority(domain);
        });

        const avgAuthority = domainAuthorities.reduce((a, b) => a + b, 0) / domainAuthorities.length;
        
        if (avgAuthority > 80) return 'Very High';
        if (avgAuthority > 60) return 'High';
        if (avgAuthority > 40) return 'Medium';
        if (avgAuthority > 20) return 'Low';
        return 'Very Low';
    }

    // Estimate domain authority (simplified)
    estimateDomainAuthority(domain) {
        const highAuthDomains = ['wikipedia.org', 'youtube.com', 'facebook.com', 'twitter.com', 'linkedin.com'];
        const mediumAuthDomains = ['medium.com', 'reddit.com', 'quora.com'];
        
        if (highAuthDomains.some(d => domain.includes(d))) return 90;
        if (mediumAuthDomains.some(d => domain.includes(d))) return 70;
        if (domain.includes('.edu') || domain.includes('.gov')) return 85;
        
        return Math.floor(Math.random() * 40) + 30; // Random between 30-70
    }

    // Extract keyword suggestions from search data
    extractKeywordSuggestions(searchData) {
        const suggestions = [];
        
        // From related searches
        if (searchData.relatedSearches) {
            suggestions.push(...searchData.relatedSearches.map(item => item.query));
        }
        
        // From people also ask
        if (searchData.peopleAlsoAsk) {
            suggestions.push(...searchData.peopleAlsoAsk.map(item => item.question));
        }
        
        return [...new Set(suggestions)]; // Remove duplicates
    }

    // Rate limiting and queue management
    async queueRequest(requestFn, priority = 'normal') {
        return new Promise((resolve, reject) => {
            this.requestQueue.push({
                fn: requestFn,
                priority,
                resolve,
                reject
            });
            
            this.processQueue();
        });
    }

    async processQueue() {
        if (this.isProcessing || this.requestQueue.length === 0) return;
        
        this.isProcessing = true;
        
        // Sort by priority
        this.requestQueue.sort((a, b) => {
            const priorities = { high: 3, normal: 2, low: 1 };
            return priorities[b.priority] - priorities[a.priority];
        });
        
        const request = this.requestQueue.shift();
        
        try {
            const result = await request.fn();
            request.resolve(result);
        } catch (error) {
            request.reject(error);
        }
        
        this.isProcessing = false;
        
        // Process next request after a delay
        if (this.requestQueue.length > 0) {
            setTimeout(() => this.processQueue(), 1000);
        }
    }
}

// Create global API client instance
let apiClient;

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    apiClient = new APIClient();
    window.apiClient = apiClient;
    console.log('API client initialized');
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = APIClient;
}

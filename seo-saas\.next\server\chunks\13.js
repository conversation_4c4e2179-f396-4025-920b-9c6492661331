exports.id=13,exports.ids=[13],exports.modules={9713:(e,r,t)=>{Promise.resolve().then(t.bind(t,4643))},783:(e,r,t)=>{e.exports=t(1476)},2254:(e,r,t)=>{e.exports=t(4767)},4643:(e,r,t)=>{"use strict";t.r(r),t.d(r,{DashboardLayout:()=>y});var s=t(2295),a=t(3729),n=t(783),l=t.n(n),i=t(2254),o=t(1453),c=t(5094);let d=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m2.25 12 8.954-8.955c.44-.439 1.152-.439 1.591 0L21.75 12M4.5 9.75v10.125c0 .621.504 1.125 1.125 1.125H9.75v-4.875c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125V21h4.125c.621 0 1.125-.504 1.125-1.125V9.75M8.25 21h8.25"}))});var m=t(454),x=t(6233);let u=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M4.5 12a7.5 7.5 0 0 0 15 0m-15 0a7.5 7.5 0 1 1 15 0m-15 0H3m16.5 0H21m-1.5 0H12m-8.457 3.077 1.41-.513m14.095-5.13 1.41-.513M5.106 17.785l1.15-.964m11.49-9.642 1.149-.964M7.501 19.795l.75-1.3m7.5-12.99.75-1.3m-6.063 16.658.26-1.477m2.605-14.772.26-1.477m0 17.726-.26-1.477M10.698 4.614l-.26-1.477M16.5 19.794l-.75-1.299M7.5 4.205 12 12m6.894 5.785-1.149-.964M6.256 7.178l-1.15-.964m15.352 8.864-1.41-.513M4.954 9.435l-1.41-.514M12.002 12l-3.75 6.495"}))}),h=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M6 18 18 6M6 6l12 12"}))}),f=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"}))});var g=t(5965);let b=a.forwardRef(function({title:e,titleId:r,...t},s){return a.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:s,"aria-labelledby":r},t),e?a.createElement("title",{id:r},e):null,a.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M14.857 17.082a23.848 23.848 0 0 0 5.454-1.31A8.967 8.967 0 0 1 18 9.75V9A6 6 0 0 0 6 9v.75a8.967 8.967 0 0 1-2.312 6.022c1.733.64 3.56 1.085 5.455 1.31m5.714 0a24.255 24.255 0 0 1-5.714 0m5.714 0a3 3 0 1 1-5.714 0"}))});var v=t(3952);let p=[{name:"Dashboard",href:"/dashboard",icon:d},{name:"Content Generator",href:"/dashboard/content",icon:m.Z},{name:"SEO Analysis",href:"/dashboard/analysis",icon:x.Z},{name:"Projects",href:"/dashboard/projects",icon:m.Z},{name:"Settings",href:"/dashboard/settings",icon:u}];function y({children:e}){let[r,t]=a.useState(!1),n=(0,i.usePathname)();return(0,s.jsxs)("div",{className:"min-h-screen bg-gray-50",children:[(0,s.jsxs)("div",{className:(0,o.cn)("fixed inset-0 z-50 lg:hidden",r?"block":"hidden"),children:[s.jsx("div",{className:"fixed inset-0 bg-gray-600 bg-opacity-75",onClick:()=>t(!1)}),(0,s.jsxs)("div",{className:"fixed inset-y-0 left-0 flex w-64 flex-col bg-white",children:[(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between px-4",children:[s.jsx("div",{className:"flex items-center",children:s.jsx("div",{className:"flex-shrink-0",children:s.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"SEO Pro"})})}),s.jsx(c.z,{variant:"ghost",size:"icon",onClick:()=>t(!1),children:s.jsx(h,{className:"h-6 w-6"})})]}),s.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:p.map(e=>{let r=n===e.href;return(0,s.jsxs)(l(),{href:e.href,className:(0,o.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",r?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),onClick:()=>t(!1),children:[s.jsx(e.icon,{className:(0,o.cn)("mr-3 h-6 w-6 flex-shrink-0",r?"text-gray-500":"text-gray-400 group-hover:text-gray-500")}),e.name,e.badge&&s.jsx("span",{className:"ml-auto inline-block py-0.5 px-3 text-xs rounded-full bg-gray-100 text-gray-600",children:e.badge})]},e.name)})})]})]}),s.jsx("div",{className:"hidden lg:fixed lg:inset-y-0 lg:flex lg:w-64 lg:flex-col",children:(0,s.jsxs)("div",{className:"flex flex-col flex-grow bg-white border-r border-gray-200",children:[s.jsx("div",{className:"flex items-center h-16 px-4 border-b border-gray-200",children:s.jsx("div",{className:"flex items-center",children:s.jsx("div",{className:"flex-shrink-0",children:s.jsx("h1",{className:"text-xl font-bold text-gray-900",children:"SEO Pro"})})})}),s.jsx("nav",{className:"flex-1 space-y-1 px-2 py-4",children:p.map(e=>{let r=n===e.href;return(0,s.jsxs)(l(),{href:e.href,className:(0,o.cn)("group flex items-center px-2 py-2 text-sm font-medium rounded-md",r?"bg-gray-100 text-gray-900":"text-gray-600 hover:bg-gray-50 hover:text-gray-900"),children:[s.jsx(e.icon,{className:(0,o.cn)("mr-3 h-6 w-6 flex-shrink-0",r?"text-gray-500":"text-gray-400 group-hover:text-gray-500")}),e.name,e.badge&&s.jsx("span",{className:"ml-auto inline-block py-0.5 px-3 text-xs rounded-full bg-gray-100 text-gray-600",children:e.badge})]},e.name)})})]})}),(0,s.jsxs)("div",{className:"lg:pl-64",children:[s.jsx("div",{className:"sticky top-0 z-40 bg-white border-b border-gray-200",children:(0,s.jsxs)("div",{className:"flex h-16 items-center justify-between px-4 sm:px-6 lg:px-8",children:[(0,s.jsxs)("div",{className:"flex items-center",children:[s.jsx(c.z,{variant:"ghost",size:"icon",className:"lg:hidden",onClick:()=>t(!0),children:s.jsx(f,{className:"h-6 w-6"})}),s.jsx("div",{className:"hidden sm:block sm:ml-6",children:(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(g.Z,{className:"h-5 w-5 text-gray-400"})}),s.jsx("input",{type:"text",placeholder:"Search...",className:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"})]})})]}),(0,s.jsxs)("div",{className:"flex items-center space-x-4",children:[s.jsx(c.z,{variant:"ghost",size:"icon",children:s.jsx(b,{className:"h-6 w-6"})}),s.jsx("div",{className:"relative",children:s.jsx(c.z,{variant:"ghost",size:"icon",children:s.jsx(v.Z,{className:"h-6 w-6"})})})]})]})}),s.jsx("main",{className:"flex-1",children:s.jsx("div",{className:"py-6",children:s.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:e})})})]})]})}},9591:(e,r,t)=>{"use strict";t.d(r,{Ct:()=>i,Dk:()=>o,U2:()=>c});var s=t(2295);t(3729);var a=t(9247),n=t(1453);let l=(0,a.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",error:"border-transparent bg-red-100 text-red-800 hover:bg-red-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function i({className:e,variant:r,...t}){return s.jsx("div",{className:(0,n.cn)(l({variant:r}),e),...t})}function o({score:e,className:r}){return(0,s.jsxs)(i,{variant:e>=80?"success":e>=60?"warning":e>=40?"error":"destructive",className:r,children:[e,"/100"]})}function c({priority:e,className:r}){return s.jsx(i,{variant:(e=>{switch(e){case"high":return"error";case"medium":return"warning";case"low":return"success";default:return"secondary"}})(e),className:r,children:e.toUpperCase()})}},7210:(e,r,t)=>{"use strict";t.d(r,{D8:()=>i,Ex:()=>l,Kl:()=>o});var s=t(2295),a=t(3729),n=t(1453);let l=a.forwardRef(({className:e,value:r=0,max:t=100,showLabel:a=!1,size:l="md",variant:i="default",...o},c)=>{let d=Math.min(Math.max(r/t*100,0),100);return(0,s.jsxs)("div",{className:"space-y-2",children:[a&&(0,s.jsxs)("div",{className:"flex justify-between text-sm",children:[s.jsx("span",{children:"Progress"}),(0,s.jsxs)("span",{children:[Math.round(d),"%"]})]}),s.jsx("div",{ref:c,className:(0,n.cn)("relative w-full overflow-hidden rounded-full bg-secondary",{sm:"h-2",md:"h-3",lg:"h-4"}[l],e),...o,children:s.jsx("div",{className:(0,n.cn)("h-full w-full flex-1 transition-all duration-300 ease-in-out",{default:"bg-primary",success:"bg-green-500",warning:"bg-yellow-500",error:"bg-red-500"}[i]),style:{transform:`translateX(-${100-d}%)`}})})]})});l.displayName="Progress";let i=({value:e,max:r=100,size:t=120,strokeWidth:a=8,className:l,showLabel:i=!0,variant:o="default"})=>{let c=Math.min(Math.max(e/r*100,0),100),d=(t-a)/2,m=2*d*Math.PI;return(0,s.jsxs)("div",{className:(0,n.cn)("relative inline-flex items-center justify-center",l),children:[(0,s.jsxs)("svg",{width:t,height:t,className:"transform -rotate-90",children:[s.jsx("circle",{cx:t/2,cy:t/2,r:d,stroke:"currentColor",strokeWidth:a,fill:"transparent",className:"text-gray-200"}),s.jsx("circle",{cx:t/2,cy:t/2,r:d,stroke:{default:"#3b82f6",success:"#10b981",warning:"#f59e0b",error:"#ef4444"}[o],strokeWidth:a,fill:"transparent",strokeDasharray:m,strokeDashoffset:m-c/100*m,strokeLinecap:"round",className:"transition-all duration-300 ease-in-out"})]}),i&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:(0,s.jsxs)("span",{className:"text-2xl font-bold text-gray-700",children:[Math.round(c),"%"]})})]})},o=({steps:e,currentStep:r,className:t})=>(0,s.jsxs)("div",{className:(0,n.cn)("w-full",t),children:[s.jsx("div",{className:"flex items-center justify-between mb-4",children:e.map((e,t)=>(0,s.jsxs)("div",{className:"flex flex-col items-center flex-1",children:[s.jsx("div",{className:(0,n.cn)("w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium border-2 transition-colors",t<r?"bg-primary text-primary-foreground border-primary":t===r?"bg-primary text-primary-foreground border-primary":"bg-background text-muted-foreground border-muted"),children:t<r?s.jsx("svg",{className:"w-4 h-4",fill:"currentColor",viewBox:"0 0 20 20",children:s.jsx("path",{fillRule:"evenodd",d:"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z",clipRule:"evenodd"})}):t+1}),s.jsx("span",{className:(0,n.cn)("text-xs mt-2 text-center",t<=r?"text-foreground":"text-muted-foreground"),children:e})]},t))}),s.jsx("div",{className:"flex items-center",children:e.map((e,t)=>s.jsx(a.Fragment,{children:t>0&&s.jsx("div",{className:(0,n.cn)("flex-1 h-1 mx-2 rounded",t<=r?"bg-primary":"bg-muted")})},t))})]})},7639:(e,r,t)=>{"use strict";t.r(r),t.d(r,{default:()=>c});var s=t(5036),a=t(6843);let n=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/components/layout/dashboard-layout.tsx`),{__esModule:l,$$typeof:i}=n;n.default;let o=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/components/layout/dashboard-layout.tsx#DashboardLayout`);function c({children:e}){return s.jsx(o,{children:e})}},6233:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});var s=t(3729);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))})},454:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});var s=t(3729);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))})},5965:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});var s=t(3729);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"m21 21-5.197-5.197m0 0A7.5 7.5 0 1 0 5.196 5.196a7.5 7.5 0 0 0 10.607 10.607Z"}))})},3952:(e,r,t)=>{"use strict";t.d(r,{Z:()=>a});var s=t(3729);let a=s.forwardRef(function({title:e,titleId:r,...t},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":r},t),e?s.createElement("title",{id:r},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))})}};
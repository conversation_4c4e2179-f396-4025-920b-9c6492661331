(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[642],{8357:function(e,t,r){Promise.resolve().then(r.bind(r,6953))},6953:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return l}});var n=r(7437),s=r(2265),a=r(4958),c=r(5671),i=r(575),o=r(3277);function l(){let[e,t]=s.useState("testing"),[r,l]=s.useState(null),[d,u]=s.useState(null),f=async()=>{t("testing"),l(null);try{let{data:e,error:r}=await a.OQ.from("profiles").select("count",{count:"exact",head:!0});if(r)throw r;u((null==e?void 0:e.length)||0),t("connected")}catch(e){l(e.message||"Unknown error"),t("error")}};return s.useEffect(()=>{f()},[]),(0,n.jsx)("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,n.jsxs)(c.Zb,{className:"w-full max-w-md",children:[(0,n.jsxs)(c.Ol,{className:"text-center",children:[(0,n.jsx)(c.ll,{children:"Supabase Connection Test"}),(0,n.jsx)(c.SZ,{children:"Testing the connection to your Supabase database"})]}),(0,n.jsxs)(c.aY,{className:"space-y-4",children:[(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)(o.Ct,{variant:(()=>{switch(e){case"connected":return"success";case"error":return"destructive";default:return"secondary"}})(),className:"text-lg px-4 py-2",children:(()=>{switch(e){case"connected":return"Connected";case"error":return"Error";default:return"Testing..."}})()})}),"connected"===e&&(0,n.jsxs)("div",{className:"text-center space-y-2",children:[(0,n.jsx)("p",{className:"text-green-600 font-medium",children:"✅ Successfully connected to Supabase!"}),(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"Database is accessible and ready for use."})]}),"error"===e&&(0,n.jsxs)("div",{className:"text-center space-y-2",children:[(0,n.jsx)("p",{className:"text-red-600 font-medium",children:"❌ Connection failed"}),(0,n.jsx)("p",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:r})]}),"testing"===e&&(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),(0,n.jsx)("p",{className:"text-sm text-gray-600 mt-2",children:"Testing connection..."})]}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsx)("h4",{className:"font-medium",children:"Environment Variables:"}),(0,n.jsxs)("div",{className:"text-sm space-y-1",children:[(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Supabase URL:"}),(0,n.jsx)(o.Ct,{variant:"outline",children:"✅ Set"})]}),(0,n.jsxs)("div",{className:"flex justify-between",children:[(0,n.jsx)("span",{children:"Anon Key:"}),(0,n.jsx)(o.Ct,{variant:"outline",children:"✅ Set"})]})]})]}),(0,n.jsx)(i.z,{onClick:f,className:"w-full",disabled:"testing"===e,children:"testing"===e?"Testing...":"Test Again"}),(0,n.jsx)("div",{className:"text-center",children:(0,n.jsx)("a",{href:"/",className:"text-blue-600 hover:text-blue-500 text-sm",children:"← Back to Home"})})]})]})})}},3277:function(e,t,r){"use strict";r.d(t,{Ct:function(){return i},Dk:function(){return o},U2:function(){return l}});var n=r(7437);r(2265);var s=r(6061),a=r(2169);let c=(0,s.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",error:"border-transparent bg-red-100 text-red-800 hover:bg-red-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function i(e){let{className:t,variant:r,...s}=e;return(0,n.jsx)("div",{className:(0,a.cn)(c({variant:r}),t),...s})}function o(e){let{score:t,className:r}=e;return(0,n.jsxs)(i,{variant:t>=80?"success":t>=60?"warning":t>=40?"error":"destructive",className:r,children:[t,"/100"]})}function l(e){let{priority:t,className:r}=e;return(0,n.jsx)(i,{variant:(e=>{switch(e){case"high":return"error";case"medium":return"warning";case"low":return"success";default:return"secondary"}})(t),className:r,children:t.toUpperCase()})}},575:function(e,t,r){"use strict";r.d(t,{z:function(){return o}});var n=r(7437),s=r(2265),a=r(6061),c=r(2169);let i=(0,a.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=s.forwardRef((e,t)=>{let{className:r,variant:s,size:a,loading:o,children:l,disabled:d,...u}=e;return(0,n.jsxs)("button",{className:(0,c.cn)(i({variant:s,size:a,className:r})),ref:t,disabled:d||o,...u,children:[o&&(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),l]})});o.displayName="Button"},5671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return i},SZ:function(){return l},Zb:function(){return c},aY:function(){return d},ll:function(){return o}});var n=r(7437),s=r(2265),a=r(2169);let c=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...s})});c.displayName="Card";let i=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",r),...s})});i.displayName="CardHeader";let o=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("h3",{ref:t,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",r),...s})});o.displayName="CardTitle";let l=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("p",{ref:t,className:(0,a.cn)("text-sm text-muted-foreground",r),...s})});l.displayName="CardDescription";let d=s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("p-6 pt-0",r),...s})});d.displayName="CardContent",s.forwardRef((e,t)=>{let{className:r,...s}=e;return(0,n.jsx)("div",{ref:t,className:(0,a.cn)("flex items-center p-6 pt-0",r),...s})}).displayName="CardFooter"},4958:function(e,t,r){"use strict";r.d(t,{OQ:function(){return c},R9:function(){return i}});var n=r(1492),s=r(3082),a=r(2601);let c=(0,n.eI)("https://zqrmpanonghggoxdjirq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}),i=()=>(0,s.createClientComponentClient)();(0,n.eI)("https://zqrmpanonghggoxdjirq.supabase.co",a.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}})},2169:function(e,t,r){"use strict";r.d(t,{KG:function(){return d},cn:function(){return a},p6:function(){return i},rl:function(){return o},uf:function(){return c},vQ:function(){return l}});var n=r(7042),s=r(4769);function a(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,s.m6)((0,n.W)(t))}function c(e){return e.toLocaleString()}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function o(e){return"".concat(e.toFixed(1),"%")}function l(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");return t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),Promise.resolve()}function d(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"text/plain",n=new Blob([e],{type:r}),s=URL.createObjectURL(n),a=document.createElement("a");a.href=s,a.download=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(s)}}},function(e){e.O(0,[696,496,971,938,744],function(){return e(e.s=8357)}),_N_E=e.O()}]);
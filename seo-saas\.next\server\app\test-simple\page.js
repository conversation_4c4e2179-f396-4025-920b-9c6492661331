(()=>{var e={};e.id=437,e.ids=[437],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},1017:e=>{"use strict";e.exports=require("path")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},5080:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>n.a,__next_app__:()=>u,originalPathname:()=>d,pages:()=>p,routeModule:()=>x,tree:()=>c});var r=s(482),i=s(9108),a=s(2563),n=s.n(a),o=s(8300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["test-simple",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,3732)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-simple/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,6343)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],p=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-simple/page.tsx"],d="/test-simple/page",u={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:i.x.APP_PAGE,page:"/test-simple/page",pathname:"/test-simple",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},5303:()=>{},3732:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(5036);function i(){return r.jsx("div",{className:"min-h-screen bg-white flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[r.jsx("h1",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Simple Test Page"}),r.jsx("p",{className:"text-gray-600 mb-8",children:"If you can see this, the basic Next.js setup is working."}),(0,r.jsxs)("div",{className:"space-y-4",children:[(0,r.jsxs)("div",{className:"p-4 bg-green-50 border border-green-200 rounded-lg",children:[r.jsx("h3",{className:"font-semibold text-green-800",children:"✅ Next.js Working"}),r.jsx("p",{className:"text-green-700 text-sm",children:"Page rendering successfully"})]}),(0,r.jsxs)("div",{className:"p-4 bg-blue-50 border border-blue-200 rounded-lg",children:[r.jsx("h3",{className:"font-semibold text-blue-800",children:"✅ Tailwind CSS Working"}),r.jsx("p",{className:"text-blue-700 text-sm",children:"Styles are being applied"})]}),(0,r.jsxs)("div",{className:"p-4 bg-purple-50 border border-purple-200 rounded-lg",children:[r.jsx("h3",{className:"font-semibold text-purple-800",children:"✅ TypeScript Working"}),r.jsx("p",{className:"text-purple-700 text-sm",children:"No compilation errors"})]})]}),r.jsx("div",{className:"mt-8",children:r.jsx("a",{href:"/",className:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors",children:"← Back to Home"})})]})})}},3881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>i});var r=s(337);let i=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,972,337,129],()=>s(5080));module.exports=r})();
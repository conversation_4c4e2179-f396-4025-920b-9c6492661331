-- Seed data for content templates
-- Insert industry-specific content templates

-- Technology Industry Templates
INSERT INTO content_templates (name, description, industry, content_type, template_structure, prompt_template, example_output, tags, is_premium) VALUES
(
  'SaaS Product Page',
  'Optimized product page template for SaaS companies',
  'technology',
  'product',
  '{
    "sections": [
      {"type": "hero", "required": true, "wordCount": 100},
      {"type": "features", "required": true, "wordCount": 300},
      {"type": "benefits", "required": true, "wordCount": 200},
      {"type": "testimonials", "required": false, "wordCount": 150},
      {"type": "pricing", "required": true, "wordCount": 100},
      {"type": "faq", "required": false, "wordCount": 200},
      {"type": "cta", "required": true, "wordCount": 50}
    ],
    "headingStructure": {"h1": 1, "h2": 4, "h3": 6},
    "keywordDensity": 2.5,
    "includeSchema": true
  }',
  'Create a comprehensive SaaS product page for {keyword} targeting {location}. Focus on {tone} tone and emphasize value proposition, key features, and social proof. Include technical benefits and ROI metrics.',
  'Complete SaaS product page with hero section, feature highlights, customer testimonials, and clear pricing structure.',
  ARRAY['saas', 'product', 'technology', 'conversion'],
  false
),
(
  'Technical Blog Post',
  'In-depth technical content for software companies',
  'technology',
  'blog',
  '{
    "sections": [
      {"type": "introduction", "required": true, "wordCount": 150},
      {"type": "problem_definition", "required": true, "wordCount": 200},
      {"type": "solution_overview", "required": true, "wordCount": 250},
      {"type": "implementation", "required": true, "wordCount": 400},
      {"type": "best_practices", "required": true, "wordCount": 200},
      {"type": "conclusion", "required": true, "wordCount": 100}
    ],
    "headingStructure": {"h1": 1, "h2": 5, "h3": 8},
    "keywordDensity": 1.8,
    "includeCodeExamples": true
  }',
  'Write a technical blog post about {keyword} for developers and IT professionals in {location}. Use {tone} tone and include practical examples, code snippets, and implementation guidance.',
  'Comprehensive technical article with code examples, best practices, and actionable insights.',
  ARRAY['technical', 'blog', 'developers', 'tutorial'],
  false
);

-- Healthcare Industry Templates
INSERT INTO content_templates (name, description, industry, content_type, template_structure, prompt_template, example_output, tags, is_premium) VALUES
(
  'Medical Service Page',
  'Professional medical service description',
  'healthcare',
  'service',
  '{
    "sections": [
      {"type": "overview", "required": true, "wordCount": 150},
      {"type": "procedure_details", "required": true, "wordCount": 300},
      {"type": "benefits", "required": true, "wordCount": 200},
      {"type": "qualifications", "required": true, "wordCount": 150},
      {"type": "patient_testimonials", "required": false, "wordCount": 150},
      {"type": "insurance_coverage", "required": true, "wordCount": 100},
      {"type": "contact_cta", "required": true, "wordCount": 50}
    ],
    "headingStructure": {"h1": 1, "h2": 4, "h3": 5},
    "keywordDensity": 2.0,
    "includeSchema": true,
    "medicalCompliance": true
  }',
  'Create a professional medical service page for {keyword} in {location}. Use {tone} tone while maintaining medical accuracy and compliance. Include procedure details, benefits, and qualification information.',
  'Professional medical service page with detailed procedure information, benefits, and practitioner qualifications.',
  ARRAY['medical', 'healthcare', 'service', 'professional'],
  false
),
(
  'Health Information Article',
  'Educational health content for patients',
  'healthcare',
  'blog',
  '{
    "sections": [
      {"type": "introduction", "required": true, "wordCount": 100},
      {"type": "what_is", "required": true, "wordCount": 200},
      {"type": "symptoms", "required": true, "wordCount": 150},
      {"type": "causes", "required": true, "wordCount": 150},
      {"type": "treatment_options", "required": true, "wordCount": 250},
      {"type": "prevention", "required": true, "wordCount": 150},
      {"type": "when_to_see_doctor", "required": true, "wordCount": 100}
    ],
    "headingStructure": {"h1": 1, "h2": 6, "h3": 4},
    "keywordDensity": 1.5,
    "medicalDisclaimer": true
  }',
  'Write an educational health article about {keyword} for patients in {location}. Use {tone} tone and include accurate medical information, symptoms, treatments, and prevention tips. Include appropriate medical disclaimers.',
  'Educational health article with symptoms, causes, treatments, and prevention information.',
  ARRAY['health', 'education', 'patients', 'informational'],
  false
);

-- Legal Industry Templates
INSERT INTO content_templates (name, description, industry, content_type, template_structure, prompt_template, example_output, tags, is_premium) VALUES
(
  'Legal Service Page',
  'Professional legal service description',
  'legal',
  'service',
  '{
    "sections": [
      {"type": "service_overview", "required": true, "wordCount": 150},
      {"type": "legal_process", "required": true, "wordCount": 250},
      {"type": "attorney_expertise", "required": true, "wordCount": 150},
      {"type": "case_results", "required": false, "wordCount": 100},
      {"type": "client_testimonials", "required": false, "wordCount": 150},
      {"type": "consultation_cta", "required": true, "wordCount": 75}
    ],
    "headingStructure": {"h1": 1, "h2": 4, "h3": 3},
    "keywordDensity": 2.2,
    "includeSchema": true,
    "legalDisclaimer": true
  }',
  'Create a professional legal service page for {keyword} law in {location}. Use {tone} tone and include service details, legal process, attorney qualifications, and consultation information.',
  'Professional legal service page with process details, attorney credentials, and consultation call-to-action.',
  ARRAY['legal', 'attorney', 'law', 'professional'],
  true
);

-- Real Estate Industry Templates
INSERT INTO content_templates (name, description, industry, content_type, template_structure, prompt_template, example_output, tags, is_premium) VALUES
(
  'Property Listing Page',
  'Optimized real estate property listing',
  'real-estate',
  'product',
  '{
    "sections": [
      {"type": "property_highlights", "required": true, "wordCount": 100},
      {"type": "detailed_description", "required": true, "wordCount": 300},
      {"type": "neighborhood_info", "required": true, "wordCount": 200},
      {"type": "amenities", "required": true, "wordCount": 150},
      {"type": "schools_nearby", "required": true, "wordCount": 100},
      {"type": "investment_potential", "required": false, "wordCount": 150},
      {"type": "contact_agent", "required": true, "wordCount": 50}
    ],
    "headingStructure": {"h1": 1, "h2": 5, "h3": 6},
    "keywordDensity": 2.0,
    "includeSchema": true,
    "localSEO": true
  }',
  'Create an engaging property listing for {keyword} in {location}. Use {tone} tone and highlight property features, neighborhood benefits, and investment potential.',
  'Compelling property listing with detailed descriptions, neighborhood information, and amenities.',
  ARRAY['real-estate', 'property', 'listing', 'local'],
  false
),
(
  'Market Analysis Article',
  'Real estate market insights and trends',
  'real-estate',
  'blog',
  '{
    "sections": [
      {"type": "market_overview", "required": true, "wordCount": 150},
      {"type": "current_trends", "required": true, "wordCount": 250},
      {"type": "price_analysis", "required": true, "wordCount": 200},
      {"type": "buyer_seller_tips", "required": true, "wordCount": 200},
      {"type": "forecast", "required": true, "wordCount": 150},
      {"type": "expert_advice", "required": true, "wordCount": 100}
    ],
    "headingStructure": {"h1": 1, "h2": 5, "h3": 4},
    "keywordDensity": 1.8,
    "includeStats": true
  }',
  'Write a comprehensive real estate market analysis for {keyword} in {location}. Use {tone} tone and include market trends, pricing data, and expert insights.',
  'Detailed market analysis with trends, pricing data, and expert predictions.',
  ARRAY['market-analysis', 'real-estate', 'trends', 'investment'],
  true
);

-- E-commerce Industry Templates
INSERT INTO content_templates (name, description, industry, content_type, template_structure, prompt_template, example_output, tags, is_premium) VALUES
(
  'Product Description',
  'Converting e-commerce product descriptions',
  'retail',
  'product',
  '{
    "sections": [
      {"type": "product_overview", "required": true, "wordCount": 100},
      {"type": "key_features", "required": true, "wordCount": 150},
      {"type": "benefits", "required": true, "wordCount": 150},
      {"type": "specifications", "required": true, "wordCount": 100},
      {"type": "usage_instructions", "required": false, "wordCount": 100},
      {"type": "customer_reviews", "required": false, "wordCount": 100},
      {"type": "purchase_cta", "required": true, "wordCount": 50}
    ],
    "headingStructure": {"h1": 1, "h2": 4, "h3": 3},
    "keywordDensity": 2.5,
    "includeSchema": true,
    "conversionOptimized": true
  }',
  'Create a compelling product description for {keyword} targeting customers in {location}. Use {tone} tone and focus on benefits, features, and conversion optimization.',
  'Conversion-optimized product description with features, benefits, and clear call-to-action.',
  ARRAY['ecommerce', 'product', 'retail', 'conversion'],
  false
);

-- Finance Industry Templates
INSERT INTO content_templates (name, description, industry, content_type, template_structure, prompt_template, example_output, tags, is_premium) VALUES
(
  'Financial Service Page',
  'Professional financial services description',
  'finance',
  'service',
  '{
    "sections": [
      {"type": "service_overview", "required": true, "wordCount": 150},
      {"type": "benefits", "required": true, "wordCount": 200},
      {"type": "process", "required": true, "wordCount": 150},
      {"type": "qualifications", "required": true, "wordCount": 100},
      {"type": "client_results", "required": false, "wordCount": 150},
      {"type": "consultation_cta", "required": true, "wordCount": 75}
    ],
    "headingStructure": {"h1": 1, "h2": 4, "h3": 2},
    "keywordDensity": 2.0,
    "includeSchema": true,
    "complianceRequired": true
  }',
  'Create a professional financial service page for {keyword} in {location}. Use {tone} tone and include service benefits, process, qualifications, and compliance information.',
  'Professional financial service page with benefits, process details, and qualifications.',
  ARRAY['finance', 'financial-services', 'professional', 'compliance'],
  true
);

-- Insert usage statistics (for demonstration)
UPDATE content_templates SET usage_count = 0;

-- Create template categories
INSERT INTO content_templates (name, description, industry, content_type, template_structure, prompt_template, example_output, tags, is_premium) VALUES
(
  'Generic Service Page',
  'Flexible service page template for any industry',
  'general',
  'service',
  '{
    "sections": [
      {"type": "service_intro", "required": true, "wordCount": 100},
      {"type": "service_details", "required": true, "wordCount": 250},
      {"type": "benefits", "required": true, "wordCount": 150},
      {"type": "process", "required": true, "wordCount": 150},
      {"type": "testimonials", "required": false, "wordCount": 100},
      {"type": "contact_cta", "required": true, "wordCount": 50}
    ],
    "headingStructure": {"h1": 1, "h2": 4, "h3": 3},
    "keywordDensity": 2.0,
    "includeSchema": true
  }',
  'Create a professional service page for {keyword} in {location}. Use {tone} tone and include service details, benefits, process, and clear call-to-action.',
  'Professional service page with comprehensive details and conversion elements.',
  ARRAY['service', 'general', 'professional', 'flexible'],
  false
),
(
  'Blog Article Template',
  'Versatile blog article template',
  'general',
  'blog',
  '{
    "sections": [
      {"type": "introduction", "required": true, "wordCount": 100},
      {"type": "main_content", "required": true, "wordCount": 600},
      {"type": "key_takeaways", "required": true, "wordCount": 150},
      {"type": "conclusion", "required": true, "wordCount": 100},
      {"type": "cta", "required": true, "wordCount": 50}
    ],
    "headingStructure": {"h1": 1, "h2": 3, "h3": 5},
    "keywordDensity": 1.5,
    "includeImages": true
  }',
  'Write an engaging blog article about {keyword} for readers in {location}. Use {tone} tone and provide valuable insights, practical tips, and actionable advice.',
  'Engaging blog article with valuable insights and practical information.',
  ARRAY['blog', 'article', 'content', 'engaging'],
  false
);
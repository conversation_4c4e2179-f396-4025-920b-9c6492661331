(()=>{var e={};e.id=642,e.ids=[642],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},1017:e=>{"use strict";e.exports=require("path")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},9677:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>x,tree:()=>l});var s=r(482),n=r(9108),a=r(2563),i=r.n(a),o=r(8300),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let l=["",{children:["test-supabase",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9483)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-supabase/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1623)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-supabase/page.tsx"],d="/test-supabase/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/test-supabase/page",pathname:"/test-supabase",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:l}})},7667:(e,t,r)=>{Promise.resolve().then(r.bind(r,6490))},6490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>l});var s=r(2295),n=r(3729),a=r(8704),i=r(3673),o=r(5094),c=r(9591);function l(){let[e,t]=n.useState("testing"),[r,l]=n.useState(null),[u,d]=n.useState(null),p=async()=>{t("testing"),l(null);try{let{data:e,error:r}=await a.OQ.from("profiles").select("count",{count:"exact",head:!0});if(r)throw r;d(e?.length||0),t("connected")}catch(e){l(e.message||"Unknown error"),t("error")}};return n.useEffect(()=>{p()},[]),s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,s.jsxs)(i.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(i.Ol,{className:"text-center",children:[s.jsx(i.ll,{children:"Supabase Connection Test"}),s.jsx(i.SZ,{children:"Testing the connection to your Supabase database"})]}),(0,s.jsxs)(i.aY,{className:"space-y-4",children:[s.jsx("div",{className:"text-center",children:s.jsx(c.Ct,{variant:(()=>{switch(e){case"connected":return"success";case"error":return"destructive";default:return"secondary"}})(),className:"text-lg px-4 py-2",children:(()=>{switch(e){case"connected":return"Connected";case"error":return"Error";default:return"Testing..."}})()})}),"connected"===e&&(0,s.jsxs)("div",{className:"text-center space-y-2",children:[s.jsx("p",{className:"text-green-600 font-medium",children:"✅ Successfully connected to Supabase!"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Database is accessible and ready for use."})]}),"error"===e&&(0,s.jsxs)("div",{className:"text-center space-y-2",children:[s.jsx("p",{className:"text-red-600 font-medium",children:"❌ Connection failed"}),s.jsx("p",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:r})]}),"testing"===e&&(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Testing connection..."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("h4",{className:"font-medium",children:"Environment Variables:"}),(0,s.jsxs)("div",{className:"text-sm space-y-1",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Supabase URL:"}),s.jsx(c.Ct,{variant:"outline",children:"✅ Set"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Anon Key:"}),s.jsx(c.Ct,{variant:"outline",children:"✅ Set"})]})]})]}),s.jsx(o.z,{onClick:p,className:"w-full",disabled:"testing"===e,children:"testing"===e?"Testing...":"Test Again"}),s.jsx("div",{className:"text-center",children:s.jsx("a",{href:"/",className:"text-blue-600 hover:text-blue-500 text-sm",children:"← Back to Home"})})]})]})})}},9591:(e,t,r)=>{"use strict";r.d(t,{Ct:()=>o,Dk:()=>c,U2:()=>l});var s=r(2295);r(3729);var n=r(9247),a=r(1453);let i=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",error:"border-transparent bg-red-100 text-red-800 hover:bg-red-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return s.jsx("div",{className:(0,a.cn)(i({variant:t}),e),...r})}function c({score:e,className:t}){return(0,s.jsxs)(o,{variant:e>=80?"success":e>=60?"warning":e>=40?"error":"destructive",className:t,children:[e,"/100"]})}function l({priority:e,className:t}){return s.jsx(o,{variant:(e=>{switch(e){case"high":return"error";case"medium":return"warning";case"low":return"success";default:return"secondary"}})(e),className:t,children:e.toUpperCase()})}},9483:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let s=(0,r(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-supabase/page.tsx`),{__esModule:n,$$typeof:a}=s,i=s.default}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,575,337,636,948],()=>r(9677));module.exports=s})();
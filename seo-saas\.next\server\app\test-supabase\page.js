(()=>{var e={};e.id=642,e.ids=[642],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},1017:e=>{"use strict";e.exports=require("path")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},9677:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>l,routeModule:()=>x,tree:()=>d});var s=r(482),n=r(9108),a=r(2563),i=r.n(a),o=r(8300),c={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>o[e]);r.d(t,c);let d=["",{children:["test-supabase",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,9483)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-supabase/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,6343)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],l=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-supabase/page.tsx"],u="/test-supabase/page",p={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:n.x.APP_PAGE,page:"/test-supabase/page",pathname:"/test-supabase",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:d}})},7667:(e,t,r)=>{Promise.resolve().then(r.bind(r,6490))},6490:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>d});var s=r(2295),n=r(3729),a=r(9299),i=r(3673),o=r(5094),c=r(9591);function d(){let[e,t]=n.useState("testing"),[r,d]=n.useState(null),[l,u]=n.useState(null),p=async()=>{t("testing"),d(null);try{if(!a.OQ)throw Error("Supabase client not initialized");let{data:e,error:r}=await a.OQ.from("profiles").select("count",{count:"exact",head:!0});if(r)throw r;u(e?.length||0),t("connected")}catch(e){d(e.message||"Unknown error"),t("error")}};return n.useEffect(()=>{p()},[]),s.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,s.jsxs)(i.Zb,{className:"w-full max-w-md",children:[(0,s.jsxs)(i.Ol,{className:"text-center",children:[s.jsx(i.ll,{children:"Supabase Connection Test"}),s.jsx(i.SZ,{children:"Testing the connection to your Supabase database"})]}),(0,s.jsxs)(i.aY,{className:"space-y-4",children:[s.jsx("div",{className:"text-center",children:s.jsx(c.Ct,{variant:(()=>{switch(e){case"connected":return"success";case"error":return"destructive";default:return"secondary"}})(),className:"text-lg px-4 py-2",children:(()=>{switch(e){case"connected":return"Connected";case"error":return"Error";default:return"Testing..."}})()})}),"connected"===e&&(0,s.jsxs)("div",{className:"text-center space-y-2",children:[s.jsx("p",{className:"text-green-600 font-medium",children:"✅ Successfully connected to Supabase!"}),s.jsx("p",{className:"text-sm text-gray-600",children:"Database is accessible and ready for use."})]}),"error"===e&&(0,s.jsxs)("div",{className:"text-center space-y-2",children:[s.jsx("p",{className:"text-red-600 font-medium",children:"❌ Connection failed"}),s.jsx("p",{className:"text-sm text-red-600 bg-red-50 p-2 rounded",children:r})]}),"testing"===e&&(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"}),s.jsx("p",{className:"text-sm text-gray-600 mt-2",children:"Testing connection..."})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("h4",{className:"font-medium",children:"Environment Variables:"}),(0,s.jsxs)("div",{className:"text-sm space-y-1",children:[(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Supabase URL:"}),s.jsx(c.Ct,{variant:"outline",children:"✅ Set"})]}),(0,s.jsxs)("div",{className:"flex justify-between",children:[s.jsx("span",{children:"Anon Key:"}),s.jsx(c.Ct,{variant:"outline",children:"✅ Set"})]})]})]}),s.jsx(o.z,{onClick:p,className:"w-full",disabled:"testing"===e,children:"testing"===e?"Testing...":"Test Again"}),s.jsx("div",{className:"text-center",children:s.jsx("a",{href:"/",className:"text-blue-600 hover:text-blue-500 text-sm",children:"← Back to Home"})})]})]})})}},9591:(e,t,r)=>{"use strict";r.d(t,{Ct:()=>o,Dk:()=>c,U2:()=>d});var s=r(2295);r(3729);var n=r(9247),a=r(1453);let i=(0,n.j)("inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground hover:bg-primary/80",secondary:"border-transparent bg-secondary text-secondary-foreground hover:bg-secondary/80",destructive:"border-transparent bg-destructive text-destructive-foreground hover:bg-destructive/80",outline:"text-foreground",success:"border-transparent bg-green-100 text-green-800 hover:bg-green-200",warning:"border-transparent bg-yellow-100 text-yellow-800 hover:bg-yellow-200",error:"border-transparent bg-red-100 text-red-800 hover:bg-red-200",info:"border-transparent bg-blue-100 text-blue-800 hover:bg-blue-200"}},defaultVariants:{variant:"default"}});function o({className:e,variant:t,...r}){return s.jsx("div",{className:(0,a.cn)(i({variant:t}),e),...r})}function c({score:e,className:t}){return(0,s.jsxs)(o,{variant:e>=80?"success":e>=60?"warning":e>=40?"error":"destructive",className:t,children:[e,"/100"]})}function d({priority:e,className:t}){return s.jsx(o,{variant:(e=>{switch(e){case"high":return"error";case"medium":return"warning";case"low":return"success";default:return"secondary"}})(e),className:t,children:e.toUpperCase()})}},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>d});var s=r(2295),n=r(3729),a=r(5877),i=r(9247),o=r(1453);let c=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),d=n.forwardRef(({className:e,variant:t,size:r,loading:n,children:i,disabled:d,asChild:l=!1,...u},p)=>{let x=l?a.g7:"button";return(0,s.jsxs)(x,{className:(0,o.cn)(c({variant:t,size:r,className:e})),ref:p,disabled:d||n,...u,children:[n&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),i]})});d.displayName="Button"},3673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>d,Zb:()=>i,aY:()=>l,ll:()=>c});var s=r(2295),n=r(3729),a=r(1453);let i=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));i.displayName="Card";let o=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let c=n.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,a.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));c.displayName="CardTitle";let d=n.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,a.cn)("text-sm text-muted-foreground",e),...t}));d.displayName="CardDescription";let l=n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("p-6 pt-0",e),...t}));l.displayName="CardContent",n.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,a.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},1453:(e,t,r)=>{"use strict";r.d(t,{KG:()=>l,cn:()=>a,p6:()=>o,rl:()=>c,uf:()=>i,vQ:()=>d});var s=r(6815),n=r(9377);function a(...e){return(0,n.m6)((0,s.W)(e))}function i(e){return e.toLocaleString()}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function c(e){return`${e.toFixed(1)}%`}function d(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");return t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),Promise.resolve()}function l(e,t,r="text/plain"){let s=new Blob([e],{type:r}),n=URL.createObjectURL(s),a=document.createElement("a");a.href=n,a.download=t,document.body.appendChild(a),a.click(),document.body.removeChild(a),URL.revokeObjectURL(n)}},9483:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>a,__esModule:()=>n,default:()=>i});let s=(0,r(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-supabase/page.tsx`),{__esModule:n,$$typeof:a}=s,i=s.default},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>n});var s=r(337);let n=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,972,337,35,129],()=>r(9677));module.exports=s})();
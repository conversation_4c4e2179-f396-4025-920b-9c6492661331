(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[185,183],{2189:function(e,t,r){Promise.resolve().then(r.t.bind(r,3385,23)),Promise.resolve().then(r.t.bind(r,5726,23)),Promise.resolve().then(r.bind(r,976)),Promise.resolve().then(r.bind(r,6493)),Promise.resolve().then(r.bind(r,6210))},976:function(e,t,r){"use strict";r.r(t),r.d(t,{ClientOnly:function(){return o},useClientOnly:function(){return a}});var n=r(7437),i=r(2265);function o(e){let{children:t,fallback:r=null}=e,[o,a]=(0,i.useState)(!1);return((0,i.useEffect)(()=>{a(!0)},[]),o)?(0,n.jsx)(n.Fragment,{children:t}):(0,n.jsx)(n.Fragment,{children:r})}function a(){let[e,t]=(0,i.useState)(!1);return(0,i.useEffect)(()=>{t(!0)},[]),e}},6493:function(e,t,r){"use strict";r.r(t),r.d(t,{ErrorBoundary:function(){return o}});var n=r(7437),i=r(2265);class o extends i.Component{static getDerivedStateFromError(e){return{hasError:!0,error:e}}componentDidCatch(e,t){console.error("Error Boundary caught an error:",e,t)}render(){if(this.state.hasError){let e=this.props.fallback||a;return(0,n.jsx)(e,{error:this.state.error,reset:()=>this.setState({hasError:!1,error:void 0})})}return this.props.children}constructor(e){super(e),this.state={hasError:!1}}}function a(e){let{error:t,reset:r}=e;return(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100",children:(0,n.jsxs)("div",{className:"max-w-md w-full glass rounded-2xl shadow-xl p-8",children:[(0,n.jsxs)("div",{className:"text-center mb-6",children:[(0,n.jsx)("div",{className:"w-16 h-16 bg-red-100 rounded-xl flex items-center justify-center mx-auto mb-4",children:(0,n.jsx)("svg",{className:"h-8 w-8 text-red-600",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:(0,n.jsx)("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 19.5c-.77.833.192 2.5 1.732 2.5z"})})}),(0,n.jsx)("h3",{className:"text-2xl font-bold text-gray-900 mb-2",children:"Something went wrong"}),(0,n.jsx)("p",{className:"text-gray-600",children:"We're sorry for the inconvenience. Please try again."})]}),!1,(0,n.jsxs)("div",{className:"space-y-3",children:[(0,n.jsx)("button",{onClick:r,className:"w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-6 py-3 rounded-lg font-medium transition-all shadow-lg hover:shadow-xl",children:"Try Again"}),(0,n.jsx)("button",{onClick:()=>window.location.reload(),className:"w-full bg-white border border-gray-300 hover:border-blue-500 text-gray-700 hover:text-blue-600 px-6 py-3 rounded-lg font-medium transition-all",children:"Reload Page"})]}),(0,n.jsx)("div",{className:"mt-6 text-center",children:(0,n.jsx)("a",{href:"/",className:"inline-flex items-center text-sm font-medium text-blue-600 hover:text-blue-500 transition-colors",children:"← Return to Home"})})]})})}},6210:function(e,t,r){"use strict";r.r(t),r.d(t,{AuthProvider:function(){return s},useAuth:function(){return l},useAuthGuard:function(){return c},useSubscription:function(){return u}});var n=r(7437),i=r(2265),o=r(5183);let a=(0,i.createContext)(void 0);function s(e){let{children:t}=e,[r,s]=(0,i.useState)(null),[l,u]=(0,i.useState)(null),[c,d]=(0,i.useState)(null),[h,f]=(0,i.useState)(null),[p,m]=(0,i.useState)(!0),[g,b]=(0,i.useState)(null),[w,y]=(0,i.useState)(!1),[v,E]=(0,i.useState)(null);(0,i.useEffect)(()=>{try{let e=(0,o.createSupabaseComponentClient)();E(e),y(!0)}catch(e){console.error("Failed to initialize Supabase client:",e),b("Failed to initialize authentication. Please check your configuration."),m(!1),y(!0)}},[]);let _=async e=>{if(!v)return console.error("Supabase client not initialized"),null;try{let{data:t,error:r}=await v.from("profiles").select("*").eq("id",e).single();if(r)return console.error("Error fetching profile:",r),null;return t}catch(e){return console.error("Error fetching profile:",e),null}},I=async e=>{if(!v)return console.error("Supabase client not initialized"),null;try{let{data:t,error:r}=await v.from("user_subscriptions").select("*").eq("user_id",e).single();if(r&&"PGRST116"!==r.code)return console.error("Error fetching subscription:",r),null;return t}catch(e){return console.error("Error fetching subscription:",e),null}};(0,i.useEffect)(()=>{if(!v)return;(async()=>{try{let{data:{session:e},error:t}=await v.auth.getSession();if(t){console.error("Error getting session:",t),m(!1);return}if(null==e?void 0:e.user){u(e),s(e.user);let[t,r]=await Promise.all([_(e.user.id),I(e.user.id)]);d(t),f(r)}}catch(e){console.error("Error initializing auth:",e)}finally{m(!1)}})();let{data:{subscription:e}}=v.auth.onAuthStateChange(async(e,t)=>{var r,n;if(console.log("Auth state changed:",e,null==t?void 0:null===(r=t.user)||void 0===r?void 0:r.id),u(t),s(null!==(n=null==t?void 0:t.user)&&void 0!==n?n:null),null==t?void 0:t.user){let[e,r]=await Promise.all([_(t.user.id),I(t.user.id)]);d(e),f(r)}else d(null),f(null);m(!1)});return()=>{e.unsubscribe()}},[v]);let S=async e=>{if(!v)throw Error("Authentication not initialized");if(!r)throw Error("No user logged in");let{data:t,error:n}=await v.from("profiles").update({...e,updated_at:new Date().toISOString()}).eq("id",r.id).select().single();if(n)throw Error(n.message);d(t)},x=async()=>{r&&d(await _(r.id))},N=async()=>{r&&f(await I(r.id))},A={user:r,session:l,profile:c,subscription:h,loading:p,signIn:async(e,t)=>{if(!v)throw Error("Authentication not initialized");let{error:r}=await v.auth.signInWithPassword({email:e,password:t});if(r)throw Error(r.message)},signUp:async(e,t,r)=>{if(!v)throw Error("Authentication not initialized");let{error:n}=await v.auth.signUp({email:e,password:t,options:{data:{full_name:r}}});if(n)throw Error(n.message)},signInWithGoogle:async()=>{if(!v)throw Error("Authentication not initialized");let{error:e}=await v.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat(window.location.origin,"/auth/callback"),queryParams:{access_type:"offline",prompt:"consent"}}});if(e)throw Error(e.message)},signInWithGitHub:async()=>{if(!v)throw Error("Authentication not initialized");let{error:e}=await v.auth.signInWithOAuth({provider:"github",options:{redirectTo:"".concat(window.location.origin,"/auth/callback")}});if(e)throw Error(e.message)},signOut:async()=>{if(!v)throw Error("Authentication not initialized");let{error:e}=await v.auth.signOut();if(e)throw Error(e.message)},resetPassword:async e=>{if(!v)throw Error("Authentication not initialized");let{error:t}=await v.auth.resetPasswordForEmail(e,{redirectTo:"".concat(window.location.origin,"/auth/reset-password")});if(t)throw Error(t.message)},updateProfile:S,refreshProfile:x,refreshSubscription:N};return w?(0,n.jsx)(a.Provider,{value:A,children:t}):(0,n.jsx)(a.Provider,{value:A,children:(0,n.jsx)("div",{className:"min-h-screen flex items-center justify-center",children:(0,n.jsx)("div",{className:"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"})})})}function l(){let e=(0,i.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function u(){let{subscription:e}=l(),t=(null==e?void 0:e.status)==="active",r=(null==e?void 0:e.status)==="trialing",n=(null==e?void 0:e.status)==="past_due",i=(null==e?void 0:e.status)==="cancelled";return{subscription:e,hasActiveSubscription:t,isOnTrial:r,isPastDue:n,isCancelled:i,canAccessFeature:r=>!!e&&("pro"===r?["pro","enterprise"].includes(e.plan_type)&&t:"enterprise"===r&&"enterprise"===e.plan_type&&t)}}function c(){let{user:e,loading:t}=l();return{isAuthenticated:!!e,isLoading:t,user:e}}},5183:function(e,t,r){"use strict";r.d(t,{createSupabaseComponentClient:function(){return c},signIn:function(){return d},signInWithGitHub:function(){return p},signInWithGoogle:function(){return f},signUp:function(){return h},OQ:function(){return u}});var n=r(1492),i=r(3082);let o={supabaseUrl:"https://xpcbyzcaidfukddqniny.supabase.co",supabaseKey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A",supabaseServiceKey:""},a=function(){let e={NEXT_PUBLIC_SUPABASE_URL:"https://xpcbyzcaidfukddqniny.supabase.co",NEXT_PUBLIC_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A"},t=[],r=[];if(Object.entries(e).forEach(e=>{let[r,n]=e;n&&""!==n.trim()||t.push(r)}),e.NEXT_PUBLIC_SUPABASE_URL)try{new URL(e.NEXT_PUBLIC_SUPABASE_URL)}catch(e){r.push("NEXT_PUBLIC_SUPABASE_URL is not a valid URL")}if(e.NEXT_PUBLIC_SUPABASE_ANON_KEY){let t=e.NEXT_PUBLIC_SUPABASE_ANON_KEY;t.startsWith("eyJ")&&3===t.split(".").length||r.push("NEXT_PUBLIC_SUPABASE_ANON_KEY appears to be invalid (not a JWT token)")}return{isValid:0===t.length&&0===r.length,missingVars:t,errors:r,vars:e}}(),s=(e,t)=>{};a.isValid||(["Supabase configuration error:",...a.missingVars.map(e=>"- Missing: ".concat(e)),...a.errors.map(e=>"- Error: ".concat(e))].join("\n"),s("Environment validation failed",{validation:a}));let l=null;try{o.supabaseUrl&&o.supabaseKey?l=(0,n.eI)(o.supabaseUrl,o.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):s("Cannot create Supabase client: missing URL or key")}catch(e){s("Failed to create Supabase client",e)}try{o.supabaseUrl&&o.supabaseServiceKey}catch(e){s("Failed to create Supabase admin client",e)}let u=l,c=()=>{try{return(0,i.createClientComponentClient)()}catch(e){throw s("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function d(e,t){let r=c(),{data:n,error:i}=await r.auth.signInWithPassword({email:e,password:t});if(i)throw Error(i.message);return n}async function h(e,t,r){let n=c(),{data:i,error:o}=await n.auth.signUp({email:e,password:t,options:{data:{full_name:r},emailRedirectTo:"".concat("http://localhost:3000","/auth/callback")}});if(o)throw Error(o.message);return i}async function f(){let e=c(),{data:t,error:r}=await e.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat("http://localhost:3000","/auth/callback"),queryParams:{access_type:"offline",prompt:"consent"}}});if(r)throw Error(r.message);return t}async function p(){let e=c(),{data:t,error:r}=await e.auth.signInWithOAuth({provider:"github",options:{redirectTo:"".concat("http://localhost:3000","/auth/callback")}});if(r)throw Error(r.message);return t}},3385:function(){},5726:function(e){e.exports={style:{fontFamily:"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'",fontStyle:"normal"},className:"__className_e8ce0c",variable:"__variable_e8ce0c"}},622:function(e,t,r){"use strict";var n=r(2265),i=Symbol.for("react.element"),o=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,s=n.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,l={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,r){var n,o={},u=null,c=null;for(n in void 0!==r&&(u=""+r),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(c=t.ref),t)a.call(t,n)&&!l.hasOwnProperty(n)&&(o[n]=t[n]);if(e&&e.defaultProps)for(n in t=e.defaultProps)void 0===o[n]&&(o[n]=t[n]);return{$$typeof:i,type:e,key:u,ref:c,props:o,_owner:s.current}}t.Fragment=o,t.jsx=u,t.jsxs=u},7437:function(e,t,r){"use strict";e.exports=r(622)}},function(e){e.O(0,[82,971,938,744],function(){return e(e.s=2189)}),_N_E=e.O()}]);
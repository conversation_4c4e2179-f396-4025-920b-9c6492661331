(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[496],{3082:function(e,t,r){"use strict";var i,s=Object.defineProperty,n=Object.getOwnPropertyDescriptor,o=Object.getOwnPropertyNames,a=Object.prototype.hasOwnProperty,l={};((e,t)=>{for(var r in t)s(e,r,{get:t[r],enumerable:!0})})(l,{createBrowserSupabaseClient:()=>I,createClientComponentClient:()=>u,createMiddlewareClient:()=>w,createMiddlewareSupabaseClient:()=>C,createPagesBrowserClient:()=>c,createPagesServerClient:()=>g,createRouteHandlerClient:()=>T,createServerActionClient:()=>j,createServerComponentClient:()=>k,createServerSupabaseClient:()=>A}),e.exports=((e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let r of o(t))a.call(e,r)||void 0===r||s(e,r,{get:()=>t[r],enumerable:!(i=n(t,r))||i.enumerable});return e})(s({},"__esModule",{value:!0}),l);var h=r(626);function u({supabaseUrl:e="https://zqrmpanonghggoxdjirq.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",options:r,cookieOptions:s,isSingleton:n=!0}={}){if(!e||!t)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");let o=()=>{var i;return(0,h.createSupabaseClient)(e,t,{...r,global:{...null==r?void 0:r.global,headers:{...null==(i=null==r?void 0:r.global)?void 0:i.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new h.BrowserCookieAuthStorageAdapter(s)}})};if(n){let e=i??o();return"undefined"==typeof window?e:(i||(i=e),i)}return o()}var c=u,d=r(626),f=r(8027),p=class extends d.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t,r,i;return(0,f.splitCookiesString)((null==(r=null==(t=this.context.res)?void 0:t.getHeader("set-cookie"))?void 0:r.toString())??"").map(t=>(0,d.parseCookies)(t)[e]).find(e=>!!e)??(null==(i=this.context.req)?void 0:i.cookies[e])}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){var i;let s=(0,f.splitCookiesString)((null==(i=this.context.res.getHeader("set-cookie"))?void 0:i.toString())??"").filter(t=>!(e in(0,d.parseCookies)(t))),n=(0,d.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.setHeader("set-cookie",[...s,n])}};function g(e,{supabaseUrl:t="https://zqrmpanonghggoxdjirq.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",options:i,cookieOptions:s}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,d.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(n=null==i?void 0:i.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new p(e,s)}})}var y=r(626),v=r(8027),m=class extends y.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return(0,v.splitCookiesString)((null==(t=this.context.res.headers.get("set-cookie"))?void 0:t.toString())??"").map(t=>(0,y.parseCookies)(t)[e]).find(e=>!!e)||(0,y.parseCookies)(this.context.req.headers.get("cookie")??"")[e]}setCookie(e,t){this._setCookie(e,t)}deleteCookie(e){this._setCookie(e,"",{maxAge:0})}_setCookie(e,t,r){let i=(0,y.serializeCookie)(e,t,{...this.cookieOptions,...r,httpOnly:!1});this.context.res.headers&&this.context.res.headers.append("set-cookie",i)}};function w(e,{supabaseUrl:t="https://zqrmpanonghggoxdjirq.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",options:i,cookieOptions:s}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,y.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(n=null==i?void 0:i.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new m(e,s)}})}var b=r(626),_=class extends b.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){}deleteCookie(e){}};function k(e,{supabaseUrl:t="https://zqrmpanonghggoxdjirq.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",options:i,cookieOptions:s}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,b.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(n=null==i?void 0:i.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new _(e,s)}})}var S=r(626),E=class extends S.CookieAuthStorageAdapter{constructor(e,t){super(t),this.context=e}getCookie(e){var t;return null==(t=this.context.cookies().get(e))?void 0:t.value}setCookie(e,t){this.context.cookies().set(e,t,this.cookieOptions)}deleteCookie(e){this.context.cookies().set(e,"",{...this.cookieOptions,maxAge:0})}};function T(e,{supabaseUrl:t="https://zqrmpanonghggoxdjirq.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",options:i,cookieOptions:s}={}){var n;if(!t||!r)throw Error("either NEXT_PUBLIC_SUPABASE_URL and NEXT_PUBLIC_SUPABASE_ANON_KEY env variables or supabaseUrl and supabaseKey are required!");return(0,S.createSupabaseClient)(t,r,{...i,global:{...null==i?void 0:i.global,headers:{...null==(n=null==i?void 0:i.global)?void 0:n.headers,"X-Client-Info":"@supabase/auth-helpers-nextjs@0.8.7"}},auth:{storage:new E(e,s)}})}var j=T;function I({supabaseUrl:e="https://zqrmpanonghggoxdjirq.supabase.co",supabaseKey:t="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",options:r,cookieOptions:i}={}){return console.warn("Please utilize the `createPagesBrowserClient` function instead of the deprecated `createBrowserSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),c({supabaseUrl:e,supabaseKey:t,options:r,cookieOptions:i})}function A(e,{supabaseUrl:t="https://zqrmpanonghggoxdjirq.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",options:i,cookieOptions:s}={}){return console.warn("Please utilize the `createPagesServerClient` function instead of the deprecated `createServerSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs-pages"),g(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:s})}function C(e,{supabaseUrl:t="https://zqrmpanonghggoxdjirq.supabase.co",supabaseKey:r="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",options:i,cookieOptions:s}={}){return console.warn("Please utilize the `createMiddlewareClient` function instead of the deprecated `createMiddlewareSupabaseClient` function. Learn more: https://supabase.com/docs/guides/auth/auth-helpers/nextjs#middleware"),w(e,{supabaseUrl:t,supabaseKey:r,options:i,cookieOptions:s})}},9494:function(e,t,r){"use strict";r.r(t),r.d(t,{Headers:function(){return n},Request:function(){return o},Response:function(){return a},fetch:function(){return s}});var i=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if(void 0!==r.g)return r.g;throw Error("unable to locate global object")}();let s=i.fetch;t.default=i.fetch.bind(i);let n=i.Headers,o=i.Request,a=i.Response},9430:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(9494)),n=i(r(6711));class o{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=s.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,r,i;let s=null,o=null,a=null,l=e.status,h=e.statusText;if(e.ok){if("HEAD"!==this.method){let t=await e.text();""===t||(o="text/csv"===this.headers.Accept?t:this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}let i=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),n=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");i&&n&&n.length>1&&(a=parseInt(n[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(o)&&(o.length>1?(s={code:"PGRST116",details:`Results contain ${o.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},o=null,a=null,l=406,h="Not Acceptable"):o=1===o.length?o[0]:null)}else{let t=await e.text();try{s=JSON.parse(t),Array.isArray(s)&&404===e.status&&(o=[],s=null,l=200,h="OK")}catch(r){404===e.status&&""===t?(l=204,h="No Content"):s={message:t}}if(s&&this.isMaybeSingle&&(null===(i=null==s?void 0:s.details)||void 0===i?void 0:i.includes("0 rows"))&&(s=null,l=200,h="OK"),s&&this.shouldThrowOnError)throw new n.default(s)}return{error:s,data:o,count:a,status:l,statusText:h}});return this.shouldThrowOnError||(r=r.catch(e=>{var t,r,i;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(r=null==e?void 0:e.stack)&&void 0!==r?r:""}`,hint:"",code:`${null!==(i=null==e?void 0:e.code)&&void 0!==i?i:""}`},data:null,count:null,status:0,statusText:""}})),r.then(e,t)}returns(){return this}overrideTypes(){return this}}t.default=o},8551:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(807)),n=i(r(4384)),o=r(9085);class a{constructor(e,{headers:t={},schema:r,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},o.DEFAULT_HEADERS),t),this.schemaName=r,this.fetch=i}from(e){let t=new URL(`${this.url}/${e}`);return new s.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new a(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:i=!1,count:s}={}){let o,a;let l=new URL(`${this.url}/rpc/${e}`);r||i?(o=r?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{l.searchParams.append(e,t)})):(o="POST",a=t);let h=Object.assign({},this.headers);return s&&(h.Prefer=`count=${s}`),new n.default({method:o,url:l,headers:h,schema:this.schemaName,body:a,fetch:this.fetch,allowEmpty:!1})}}t.default=a},6711:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0});class r extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}t.default=r},4384:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(1483));class n extends s.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){let r=Array.from(new Set(t)).map(e=>"string"==typeof e&&RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${r})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:r,type:i}={}){let s="";"plain"===i?s="pl":"phrase"===i?s="ph":"websearch"===i&&(s="w");let n=void 0===r?"":`(${r})`;return this.url.searchParams.append(e,`${s}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,r){return this.url.searchParams.append(e,`not.${t}.${r}`),this}or(e,{foreignTable:t,referencedTable:r=t}={}){let i=r?`${r}.or`:"or";return this.url.searchParams.append(i,`(${e})`),this}filter(e,t,r){return this.url.searchParams.append(e,`${t}.${r}`),this}}t.default=n},807:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(4384));class n{constructor(e,{headers:t={},schema:r,fetch:i}){this.url=e,this.headers=t,this.schema=r,this.fetch=i}select(e,{head:t=!1,count:r}={}){let i=!1,n=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!i?"":('"'===e&&(i=!i),e)).join("");return this.url.searchParams.set("select",n),r&&(this.headers.Prefer=`count=${r}`),new s.default({method:t?"HEAD":"GET",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:r=!0}={}){let i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),t&&i.push(`count=${t}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:r=!1,count:i,defaultToNull:n=!0}={}){let o=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&o.push(this.headers.Prefer),i&&o.push(`count=${i}`),n||o.push("missing=default"),this.headers.Prefer=o.join(","),Array.isArray(e)){let t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){let e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new s.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){let r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),this.headers.Prefer=r.join(","),new s.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){let t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new s.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}}t.default=n},1483:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0});let s=i(r(9430));class n extends s.default{select(e){let t=!1,r=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",r),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:r,foreignTable:i,referencedTable:s=i}={}){let n=s?`${s}.order`:"order",o=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${o?`${o},`:""}${e}.${t?"asc":"desc"}${void 0===r?"":r?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:r=t}={}){let i=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this}range(e,t,{foreignTable:r,referencedTable:i=r}={}){let s=void 0===i?"offset":`${i}.offset`,n=void 0===i?"limit":`${i}.limit`;return this.url.searchParams.set(s,`${e}`),this.url.searchParams.set(n,`${t-e+1}`),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:r=!1,buffers:i=!1,wal:s=!1,format:n="text"}={}){var o;let a=[e?"analyze":null,t?"verbose":null,r?"settings":null,i?"buffers":null,s?"wal":null].filter(Boolean).join("|"),l=null!==(o=this.headers.Accept)&&void 0!==o?o:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${a};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}t.default=n},9085:function(e,t,r){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.DEFAULT_HEADERS=void 0;let i=r(7687);t.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${i.version}`}},4830:function(e,t,r){"use strict";var i=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(t,"__esModule",{value:!0}),t.PostgrestError=t.PostgrestBuilder=t.PostgrestTransformBuilder=t.PostgrestFilterBuilder=t.PostgrestQueryBuilder=t.PostgrestClient=void 0;let s=i(r(8551));t.PostgrestClient=s.default;let n=i(r(807));t.PostgrestQueryBuilder=n.default;let o=i(r(4384));t.PostgrestFilterBuilder=o.default;let a=i(r(1483));t.PostgrestTransformBuilder=a.default;let l=i(r(9430));t.PostgrestBuilder=l.default;let h=i(r(6711));t.PostgrestError=h.default,t.default={PostgrestClient:s.default,PostgrestQueryBuilder:n.default,PostgrestFilterBuilder:o.default,PostgrestTransformBuilder:a.default,PostgrestBuilder:l.default,PostgrestError:h.default}},7687:function(e,t){"use strict";Object.defineProperty(t,"__esModule",{value:!0}),t.version=void 0,t.version="0.0.0-automated"},1492:function(e,t,r){"use strict";r.d(t,{eI:function(){return tq}});let i=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,9494)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)};class s extends Error{constructor(e,t="FunctionsError",r){super(e),this.name=t,this.context=r}}class n extends s{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class o extends s{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class a extends s{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}(q=J||(J={})).Any="any",q.ApNortheast1="ap-northeast-1",q.ApNortheast2="ap-northeast-2",q.ApSouth1="ap-south-1",q.ApSoutheast1="ap-southeast-1",q.ApSoutheast2="ap-southeast-2",q.CaCentral1="ca-central-1",q.EuCentral1="eu-central-1",q.EuWest1="eu-west-1",q.EuWest2="eu-west-2",q.EuWest3="eu-west-3",q.SaEast1="sa-east-1",q.UsEast1="us-east-1",q.UsWest1="us-west-1",q.UsWest2="us-west-2";class l{constructor(e,{headers:t={},customFetch:r,region:s=J.Any}={}){this.url=e,this.headers=t,this.region=s,this.fetch=i(r)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var r,i,s,l,h;return i=this,s=void 0,l=void 0,h=function*(){try{let i;let{headers:s,method:l,body:h}=t,u={},{region:c}=t;c||(c=this.region);let d=new URL(`${this.url}/${e}`);c&&"any"!==c&&(u["x-region"]=c,d.searchParams.set("forceFunctionRegion",c)),h&&(s&&!Object.prototype.hasOwnProperty.call(s,"Content-Type")||!s)&&("undefined"!=typeof Blob&&h instanceof Blob||h instanceof ArrayBuffer?(u["Content-Type"]="application/octet-stream",i=h):"string"==typeof h?(u["Content-Type"]="text/plain",i=h):"undefined"!=typeof FormData&&h instanceof FormData?i=h:(u["Content-Type"]="application/json",i=JSON.stringify(h)));let f=yield this.fetch(d.toString(),{method:l||"POST",headers:Object.assign(Object.assign(Object.assign({},u),this.headers),s),body:i}).catch(e=>{throw new n(e)}),p=f.headers.get("x-relay-error");if(p&&"true"===p)throw new o(f);if(!f.ok)throw new a(f);let g=(null!==(r=f.headers.get("Content-Type"))&&void 0!==r?r:"text/plain").split(";")[0].trim();return{data:"application/json"===g?yield f.json():"application/octet-stream"===g?yield f.blob():"text/event-stream"===g?f:"multipart/form-data"===g?yield f.formData():yield f.text(),error:null,response:f}}catch(e){return{data:null,error:e,response:e instanceof a||e instanceof o?e.context:void 0}}},new(l||(l=Promise))(function(e,t){function r(e){try{o(h.next(e))}catch(e){t(e)}}function n(e){try{o(h.throw(e))}catch(e){t(e)}}function o(t){var i;t.done?e(t.value):((i=t.value)instanceof l?i:new l(function(e){e(i)})).then(r,n)}o((h=h.apply(i,s||[])).next())})}}let{PostgrestClient:h,PostgrestQueryBuilder:u,PostgrestFilterBuilder:c,PostgrestTransformBuilder:d,PostgrestBuilder:f,PostgrestError:p}=r(4830),g=function(){if("undefined"!=typeof WebSocket)return WebSocket;if(void 0!==global.WebSocket)return global.WebSocket;if(void 0!==window.WebSocket)return window.WebSocket;if(void 0!==self.WebSocket)return self.WebSocket;throw Error("`WebSocket` is not supported in this environment")}();(Q=z||(z={}))[Q.connecting=0]="connecting",Q[Q.open=1]="open",Q[Q.closing=2]="closing",Q[Q.closed=3]="closed",(ee=F||(F={})).closed="closed",ee.errored="errored",ee.joined="joined",ee.joining="joining",ee.leaving="leaving",(et=K||(K={})).close="phx_close",et.error="phx_error",et.join="phx_join",et.reply="phx_reply",et.leave="phx_leave",et.access_token="access_token",(W||(W={})).websocket="websocket",(er=H||(H={})).Connecting="connecting",er.Open="open",er.Closing="closing",er.Closed="closed";class y{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):"string"==typeof e?t(JSON.parse(e)):t({})}_binaryDecode(e){let t=new DataView(e),r=new TextDecoder;return this._decodeBroadcast(e,t,r)}_decodeBroadcast(e,t,r){let i=t.getUint8(1),s=t.getUint8(2),n=this.HEADER_LENGTH+2,o=r.decode(e.slice(n,n+i));n+=i;let a=r.decode(e.slice(n,n+s));return n+=s,{ref:null,topic:o,event:a,payload:JSON.parse(r.decode(e.slice(n,e.byteLength)))}}}class v{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}(ei=X||(X={})).abstime="abstime",ei.bool="bool",ei.date="date",ei.daterange="daterange",ei.float4="float4",ei.float8="float8",ei.int2="int2",ei.int4="int4",ei.int4range="int4range",ei.int8="int8",ei.int8range="int8range",ei.json="json",ei.jsonb="jsonb",ei.money="money",ei.numeric="numeric",ei.oid="oid",ei.reltime="reltime",ei.text="text",ei.time="time",ei.timestamp="timestamp",ei.timestamptz="timestamptz",ei.timetz="timetz",ei.tsrange="tsrange",ei.tstzrange="tstzrange";let m=(e,t,r={})=>{var i;let s=null!==(i=r.skipTypes)&&void 0!==i?i:[];return Object.keys(t).reduce((r,i)=>(r[i]=w(i,e,t,s),r),{})},w=(e,t,r,i)=>{let s=t.find(t=>t.name===e),n=null==s?void 0:s.type,o=r[e];return n&&!i.includes(n)?b(n,o):_(o)},b=(e,t)=>{if("_"===e.charAt(0))return T(t,e.slice(1,e.length));switch(e){case X.bool:return k(t);case X.float4:case X.float8:case X.int2:case X.int4:case X.int8:case X.numeric:case X.oid:return S(t);case X.json:case X.jsonb:return E(t);case X.timestamp:return j(t);case X.abstime:case X.date:case X.daterange:case X.int4range:case X.int8range:case X.money:case X.reltime:case X.text:case X.time:case X.timestamptz:case X.timetz:case X.tsrange:case X.tstzrange:default:return _(t)}},_=e=>e,k=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},S=e=>{if("string"==typeof e){let t=parseFloat(e);if(!Number.isNaN(t))return t}return e},E=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(e){console.log(`JSON parse error: ${e}`)}return e},T=(e,t)=>{if("string"!=typeof e)return e;let r=e.length-1,i=e[r];if("{"===e[0]&&"}"===i){let i;let s=e.slice(1,r);try{i=JSON.parse("["+s+"]")}catch(e){i=s?s.split(","):[]}return i.map(e=>b(t,e))}return e},j=e=>"string"==typeof e?e.replace(" ","T"):e,I=e=>{let t=e;return(t=(t=t.replace(/^ws/i,"http")).replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,"")).replace(/\/+$/,"")};class A{constructor(e,t,r={},i=1e4){this.channel=e,this.event=t,this.payload=r,this.timeout=i,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var r;return this._hasReceived(e)&&t(null===(r=this.receivedResp)||void 0===r?void 0:r.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){this.timeoutTimer||(this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref),this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout))}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}(es=G||(G={})).SYNC="sync",es.JOIN="join",es.LEAVE="leave";class C{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};let r=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(r.state,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.joinRef=this.channel._joinRef(),this.state=C.syncState(this.state,e,t,r),this.pendingDiffs.forEach(e=>{this.state=C.syncDiff(this.state,e,t,r)}),this.pendingDiffs=[],i()}),this.channel._on(r.diff,{},e=>{let{onJoin:t,onLeave:r,onSync:i}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=C.syncDiff(this.state,e,t,r),i())}),this.onJoin((e,t,r)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:r})}),this.onLeave((e,t,r)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:r})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,r,i){let s=this.cloneDeep(e),n=this.transformState(t),o={},a={};return this.map(s,(e,t)=>{n[e]||(a[e]=t)}),this.map(n,(e,t)=>{let r=s[e];if(r){let i=t.map(e=>e.presence_ref),s=r.map(e=>e.presence_ref),n=t.filter(e=>0>s.indexOf(e.presence_ref)),l=r.filter(e=>0>i.indexOf(e.presence_ref));n.length>0&&(o[e]=n),l.length>0&&(a[e]=l)}else o[e]=t}),this.syncDiff(s,{joins:o,leaves:a},r,i)}static syncDiff(e,t,r,i){let{joins:s,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return r||(r=()=>{}),i||(i=()=>{}),this.map(s,(t,i)=>{var s;let n=null!==(s=e[t])&&void 0!==s?s:[];if(e[t]=this.cloneDeep(i),n.length>0){let r=e[t].map(e=>e.presence_ref),i=n.filter(e=>0>r.indexOf(e.presence_ref));e[t].unshift(...i)}r(t,n,i)}),this.map(n,(t,r)=>{let s=e[t];if(!s)return;let n=r.map(e=>e.presence_ref);s=s.filter(e=>0>n.indexOf(e.presence_ref)),e[t]=s,i(t,s,r),0===s.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(r=>t(r,e[r]))}static transformState(e){return Object.getOwnPropertyNames(e=this.cloneDeep(e)).reduce((t,r)=>{let i=e[r];return"metas"in i?t[r]=i.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):t[r]=i,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(en=V||(V={})).ALL="*",en.INSERT="INSERT",en.UPDATE="UPDATE",en.DELETE="DELETE",(eo=Y||(Y={})).BROADCAST="broadcast",eo.PRESENCE="presence",eo.POSTGRES_CHANGES="postgres_changes",eo.SYSTEM="system",(ea=Z||(Z={})).SUBSCRIBED="SUBSCRIBED",ea.TIMED_OUT="TIMED_OUT",ea.CLOSED="CLOSED",ea.CHANNEL_ERROR="CHANNEL_ERROR";class O{constructor(e,t={config:{}},r){this.topic=e,this.params=t,this.socket=r,this.bindings={},this.state=F.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new A(this,K.join,this.params,this.timeout),this.rejoinTimer=new v(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=F.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=F.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=F.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=F.errored,this.rejoinTimer.scheduleTimeout())}),this._on(K.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new C(this),this.broadcastEndpointURL=I(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var r,i;if(this.socket.isConnected()||this.socket.connect(),this.state==F.closed){let{config:{broadcast:s,presence:n,private:o}}=this.params;this._onError(t=>null==e?void 0:e(Z.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(Z.CLOSED));let a={},l={broadcast:s,presence:n,postgres_changes:null!==(i=null===(r=this.bindings.postgres_changes)||void 0===r?void 0:r.map(e=>e.filter))&&void 0!==i?i:[],private:o};this.socket.accessTokenValue&&(a.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},a)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var r;if(this.socket.setAuth(),void 0===t){null==e||e(Z.SUBSCRIBED);return}{let i=this.bindings.postgres_changes,s=null!==(r=null==i?void 0:i.length)&&void 0!==r?r:0,n=[];for(let r=0;r<s;r++){let s=i[r],{filter:{event:o,schema:a,table:l,filter:h}}=s,u=t&&t[r];if(u&&u.event===o&&u.schema===a&&u.table===l&&u.filter===h)n.push(Object.assign(Object.assign({},s),{id:u.id}));else{this.unsubscribe(),this.state=F.errored,null==e||e(Z.CHANNEL_ERROR,Error("mismatch between server and client bindings for postgres changes"));return}}this.bindings.postgres_changes=n,e&&e(Z.SUBSCRIBED);return}}).receive("error",t=>{this.state=F.errored,null==e||e(Z.CHANNEL_ERROR,Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(Z.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,r){return this._on(e,t,r)}async send(e,t={}){var r,i;if(this._canPush()||"broadcast"!==e.type)return new Promise(r=>{var i,s,n;let o=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(n=null===(s=null===(i=this.params)||void 0===i?void 0:i.config)||void 0===s?void 0:s.broadcast)||void 0===n?void 0:n.ack)||r("ok"),o.receive("ok",()=>r("ok")),o.receive("error",()=>r("error")),o.receive("timeout",()=>r("timed out"))});{let{event:s,payload:n}=e,o={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:s,payload:n,private:this.private}]})};try{let e=await this._fetchWithTimeout(this.broadcastEndpointURL,o,null!==(r=t.timeout)&&void 0!==r?r:this.timeout);return await (null===(i=e.body)||void 0===i?void 0:i.cancel()),e.ok?"ok":"error"}catch(e){if("AbortError"===e.name)return"timed out";return"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=F.leaving;let t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(K.close,"leave",this._joinRef())};this.joinPush.destroy();let r=null;return new Promise(i=>{(r=new A(this,K.leave,{},e)).receive("ok",()=>{t(),i("ok")}).receive("timeout",()=>{t(),i("timed out")}).receive("error",()=>{i("error")}),r.send(),this._canPush()||r.trigger("ok",{})}).finally(()=>{null==r||r.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,r){let i=new AbortController,s=setTimeout(()=>i.abort(),r),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:i.signal}));return clearTimeout(s),n}_push(e,t,r=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let i=new A(this,e,t,r);return this._canPush()?i.send():(i.startTimeout(),this.pushBuffer.push(i)),i}_onMessage(e,t,r){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,r){var i,s;let n=e.toLocaleLowerCase(),{close:o,error:a,leave:l,join:h}=K;if(r&&[o,a,l,h].indexOf(n)>=0&&r!==this._joinRef())return;let u=this._onMessage(n,t,r);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null===(i=this.bindings.postgres_changes)||void 0===i||i.filter(e=>{var t,r,i;return(null===(t=e.filter)||void 0===t?void 0:t.event)==="*"||(null===(i=null===(r=e.filter)||void 0===r?void 0:r.event)||void 0===i?void 0:i.toLocaleLowerCase())===n}).map(e=>e.callback(u,r)):null===(s=this.bindings[n])||void 0===s||s.filter(e=>{var r,i,s,o,a,l;if(!["broadcast","presence","postgres_changes"].includes(n))return e.type.toLocaleLowerCase()===n;if("id"in e){let n=e.id,o=null===(r=e.filter)||void 0===r?void 0:r.event;return n&&(null===(i=t.ids)||void 0===i?void 0:i.includes(n))&&("*"===o||(null==o?void 0:o.toLocaleLowerCase())===(null===(s=t.data)||void 0===s?void 0:s.type.toLocaleLowerCase()))}{let r=null===(a=null===(o=null==e?void 0:e.filter)||void 0===o?void 0:o.event)||void 0===a?void 0:a.toLocaleLowerCase();return"*"===r||r===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}).map(e=>{if("object"==typeof u&&"ids"in u){let e=u.data,{schema:t,table:r,commit_timestamp:i,type:s,errors:n}=e;u=Object.assign(Object.assign({},{schema:t,table:r,commit_timestamp:i,eventType:s,new:{},old:{},errors:n}),this._getPayloadRecords(e))}e.callback(u,r)})}_isClosed(){return this.state===F.closed}_isJoined(){return this.state===F.joined}_isJoining(){return this.state===F.joining}_isLeaving(){return this.state===F.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,r){let i=e.toLocaleLowerCase(),s={type:i,filter:t,callback:r};return this.bindings[i]?this.bindings[i].push(s):this.bindings[i]=[s],this}_off(e,t){let r=e.toLocaleLowerCase();return this.bindings[r]=this.bindings[r].filter(e=>{var i;return!((null===(i=e.type)||void 0===i?void 0:i.toLocaleLowerCase())===r&&O.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(let r in e)if(e[r]!==t[r])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(K.close,{},e)}_onError(e){this._on(K.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=F.joining,this.joinPush.resend(e))}_getPayloadRecords(e){let t={new:{},old:{}};return("INSERT"===e.type||"UPDATE"===e.type)&&(t.new=m(e.columns,e.record)),("UPDATE"===e.type||"DELETE"===e.type)&&(t.old=m(e.columns,e.old_record)),t}}let x=()=>{},P=`
  addEventListener("message", (e) => {
    if (e.data.event === "start") {
      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);
    }
  });`;class R{constructor(e,t){var i;this.accessTokenValue=null,this.apiKey=null,this.channels=[],this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=x,this.ref=0,this.logger=x,this.conn=null,this.sendBuffer=[],this.serializer=new y,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,9494)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${W.websocket}`,this.httpEndpoint=I(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);let s=null===(i=null==t?void 0:t.params)||void 0===i?void 0:i.apikey;if(s&&(this.accessTokenValue=s,this.apiKey=s),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new v(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=g),!this.transport)throw Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){let t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){let e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,r){this.logger(e,t,r)}connectionState(){switch(this.conn&&this.conn.readyState){case z.connecting:return H.Connecting;case z.open:return H.Open;case z.closing:return H.Closing;default:return H.Closed}}isConnected(){return this.connectionState()===H.Open}channel(e,t={config:{}}){let r=`realtime:${e}`,i=this.getChannels().find(e=>e.topic===r);if(i)return i;{let r=new O(`realtime:${e}`,t,this);return this.channels.push(r),r}}push(e){let{topic:t,event:r,payload:i,ref:s}=e,n=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${r} (${s})`,i),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{t&&e.updateJoinPayload({access_token:t,version:"realtime-js/2.11.15"}),e.joinedOnce&&e._isJoined()&&e._push(K.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(!this.isConnected()){this.heartbeatCallback("disconnected");return}if(this.pendingHeartbeatRef){this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout");return}this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:r,payload:i,ref:s}=e;"phoenix"===t&&"phx_reply"===r&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),s&&s===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${i.status||""} ${t} ${r} ${s&&"("+s+")"||""}`,i),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(r,i,s)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");let e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(K.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;let r=e.match(/\?/)?"&":"?",i=new URLSearchParams(t);return`${e}${r}${i}`}_workerObjectUrl(e){let t;if(e)t=e;else{let e=new Blob([P],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class $ extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function U(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class L extends ${constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class B extends ${constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}let N=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,9494)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},D=()=>{var e,t,i,s;return e=void 0,t=void 0,i=void 0,s=function*(){return"undefined"==typeof Response?(yield Promise.resolve().then(r.bind(r,9494))).Response:Response},new(i||(i=Promise))(function(r,n){function o(e){try{l(s.next(e))}catch(e){n(e)}}function a(e){try{l(s.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?r(e.value):((t=e.value)instanceof i?t:new i(function(e){e(t)})).then(o,a)}l((s=s.apply(e,t||[])).next())})},M=e=>{if(Array.isArray(e))return e.map(e=>M(e));if("function"==typeof e||e!==Object(e))return e;let t={};return Object.entries(e).forEach(([e,r])=>{t[e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""))]=M(r)}),t};var q,J,z,F,K,W,H,X,G,V,Y,Z,Q,ee,et,er,ei,es,en,eo,ea,el=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function o(e){try{l(i.next(e))}catch(e){n(e)}}function a(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((i=i.apply(e,t||[])).next())})};let eh=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),eu=(e,t,r)=>el(void 0,void 0,void 0,function*(){e instanceof(yield D())&&!(null==r?void 0:r.noResolveJson)?e.json().then(r=>{t(new L(eh(r),e.status||500))}).catch(e=>{t(new B(eh(e),e))}):t(new B(eh(e),e))}),ec=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),i&&(s.body=JSON.stringify(i)),Object.assign(Object.assign({},s),r))};function ed(e,t,r,i,s,n){return el(this,void 0,void 0,function*(){return new Promise((o,a)=>{e(r,ec(t,i,s,n)).then(e=>{if(!e.ok)throw e;return(null==i?void 0:i.noResolveJson)?e:e.json()}).then(e=>o(e)).catch(e=>eu(e,a,i))})})}function ef(e,t,r,i){return el(this,void 0,void 0,function*(){return ed(e,"GET",t,r,i)})}function ep(e,t,r,i,s){return el(this,void 0,void 0,function*(){return ed(e,"POST",t,i,s,r)})}function eg(e,t,r,i,s){return el(this,void 0,void 0,function*(){return ed(e,"DELETE",t,i,s,r)})}var ey=r(263).Buffer,ev=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function o(e){try{l(i.next(e))}catch(e){n(e)}}function a(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((i=i.apply(e,t||[])).next())})};let em={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},ew={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class eb{constructor(e,t={},r,i){this.url=e,this.headers=t,this.bucketId=r,this.fetch=N(i)}uploadOrUpdate(e,t,r,i){return ev(this,void 0,void 0,function*(){try{let s;let n=Object.assign(Object.assign({},ew),i),o=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)}),a=n.metadata;"undefined"!=typeof Blob&&r instanceof Blob?((s=new FormData).append("cacheControl",n.cacheControl),a&&s.append("metadata",this.encodeMetadata(a)),s.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?((s=r).append("cacheControl",n.cacheControl),a&&s.append("metadata",this.encodeMetadata(a))):(s=r,o["cache-control"]=`max-age=${n.cacheControl}`,o["content-type"]=n.contentType,a&&(o["x-metadata"]=this.toBase64(this.encodeMetadata(a)))),(null==i?void 0:i.headers)&&(o=Object.assign(Object.assign({},o),i.headers));let l=this._removeEmptyFolders(t),h=this._getFinalPath(l),u=yield this.fetch(`${this.url}/object/${h}`,Object.assign({method:e,body:s,headers:o},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),c=yield u.json();if(u.ok)return{data:{path:l,id:c.Id,fullPath:c.Key},error:null};return{data:null,error:c}}catch(e){if(U(e))return{data:null,error:e};throw e}})}upload(e,t,r){return ev(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,r)})}uploadToSignedUrl(e,t,r,i){return ev(this,void 0,void 0,function*(){let s=this._removeEmptyFolders(e),n=this._getFinalPath(s),o=new URL(this.url+`/object/upload/sign/${n}`);o.searchParams.set("token",t);try{let e;let t=Object.assign({upsert:ew.upsert},i),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&r instanceof Blob?((e=new FormData).append("cacheControl",t.cacheControl),e.append("",r)):"undefined"!=typeof FormData&&r instanceof FormData?(e=r).append("cacheControl",t.cacheControl):(e=r,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);let a=yield this.fetch(o.toString(),{method:"PUT",body:e,headers:n}),l=yield a.json();if(a.ok)return{data:{path:s,fullPath:l.Key},error:null};return{data:null,error:l}}catch(e){if(U(e))return{data:null,error:e};throw e}})}createSignedUploadUrl(e,t){return ev(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),i=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(i["x-upsert"]="true");let s=yield ep(this.fetch,`${this.url}/object/upload/sign/${r}`,{},{headers:i}),n=new URL(this.url+s.url),o=n.searchParams.get("token");if(!o)throw new $("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:o},error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}update(e,t,r){return ev(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,r)})}move(e,t,r){return ev(this,void 0,void 0,function*(){try{return{data:yield ep(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers}),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}copy(e,t,r){return ev(this,void 0,void 0,function*(){try{return{data:{path:(yield ep(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==r?void 0:r.destinationBucket},{headers:this.headers})).Key},error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}createSignedUrl(e,t,r){return ev(this,void 0,void 0,function*(){try{let i=this._getFinalPath(e),s=yield ep(this.fetch,`${this.url}/object/sign/${i}`,Object.assign({expiresIn:t},(null==r?void 0:r.transform)?{transform:r.transform}:{}),{headers:this.headers}),n=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:s={signedUrl:encodeURI(`${this.url}${s.signedURL}${n}`)},error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}createSignedUrls(e,t,r){return ev(this,void 0,void 0,function*(){try{let i=yield ep(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),s=(null==r?void 0:r.download)?`&download=${!0===r.download?"":r.download}`:"";return{data:i.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${s}`):null})),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}download(e,t){return ev(this,void 0,void 0,function*(){let r=void 0!==(null==t?void 0:t.transform),i=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),s=i?`?${i}`:"";try{let t=this._getFinalPath(e),i=yield ef(this.fetch,`${this.url}/${r?"render/image/authenticated":"object"}/${t}${s}`,{headers:this.headers,noResolveJson:!0});return{data:yield i.blob(),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}info(e){return ev(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{let e=yield ef(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:M(e),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}exists(e){return ev(this,void 0,void 0,function*(){let t=this._getFinalPath(e);try{return yield function(e,t,r,i){return el(this,void 0,void 0,function*(){return ed(e,"HEAD",t,Object.assign(Object.assign({},r),{noResolveJson:!0}),void 0)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(e){if(U(e)&&e instanceof B){let t=e.originalError;if([400,404].includes(null==t?void 0:t.status))return{data:!1,error:e}}throw e}})}getPublicUrl(e,t){let r=this._getFinalPath(e),i=[],s=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==s&&i.push(s);let n=void 0!==(null==t?void 0:t.transform),o=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==o&&i.push(o);let a=i.join("&");return""!==a&&(a=`?${a}`),{data:{publicUrl:encodeURI(`${this.url}/${n?"render/image":"object"}/public/${r}${a}`)}}}remove(e){return ev(this,void 0,void 0,function*(){try{return{data:yield eg(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}list(e,t,r){return ev(this,void 0,void 0,function*(){try{let i=Object.assign(Object.assign(Object.assign({},em),t),{prefix:e||""});return{data:yield ep(this.fetch,`${this.url}/object/list/${this.bucketId}`,i,{headers:this.headers},r),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return void 0!==ey?ey.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){let t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}let e_={"X-Client-Info":"storage-js/2.7.1"};var ek=function(e,t,r,i){return new(r||(r=Promise))(function(s,n){function o(e){try{l(i.next(e))}catch(e){n(e)}}function a(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((i=i.apply(e,t||[])).next())})};class eS{constructor(e,t={},r){this.url=e,this.headers=Object.assign(Object.assign({},e_),t),this.fetch=N(r)}listBuckets(){return ek(this,void 0,void 0,function*(){try{return{data:yield ef(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}getBucket(e){return ek(this,void 0,void 0,function*(){try{return{data:yield ef(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}createBucket(e,t={public:!1}){return ek(this,void 0,void 0,function*(){try{return{data:yield ep(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}updateBucket(e,t){return ek(this,void 0,void 0,function*(){try{return{data:yield function(e,t,r,i,s){return el(this,void 0,void 0,function*(){return ed(e,"PUT",t,i,void 0,r)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}emptyBucket(e){return ek(this,void 0,void 0,function*(){try{return{data:yield ep(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}deleteBucket(e){return ek(this,void 0,void 0,function*(){try{return{data:yield eg(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(e){if(U(e))return{data:null,error:e};throw e}})}}class eE extends eS{constructor(e,t={},r){super(e,t,r)}from(e){return new eb(this.url,this.headers,e,this.fetch)}}let eT="";eT="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";let ej={headers:{"X-Client-Info":`supabase-js-${eT}/2.50.3`}},eI={schema:"public"},eA={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},eC={};var eO=r(9494);let ex=e=>{let t;return t=e||("undefined"==typeof fetch?eO.default:fetch),(...e)=>t(...e)},eP=()=>"undefined"==typeof Headers?eO.Headers:Headers,eR=(e,t,r)=>{let i=ex(r),s=eP();return(r,n)=>{var o,a,l,h;return o=void 0,a=void 0,l=void 0,h=function*(){var o;let a=null!==(o=yield t())&&void 0!==o?o:e,l=new s(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${a}`),i(r,Object.assign(Object.assign({},n),{headers:l}))},new(l||(l=Promise))(function(e,t){function r(e){try{s(h.next(e))}catch(e){t(e)}}function i(e){try{s(h.throw(e))}catch(e){t(e)}}function s(t){var s;t.done?e(t.value):((s=t.value)instanceof l?s:new l(function(e){e(s)})).then(r,i)}s((h=h.apply(o,a||[])).next())})}},e$="2.70.0",eU={"X-Client-Info":`gotrue-js/${e$}`},eL="X-Supabase-Api-Version",eB={"2024-01-01":{timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"}},eN=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class eD extends Error{constructor(e,t,r){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=r}}function eM(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class eq extends eD{constructor(e,t,r){super(e,t,r),this.name="AuthApiError",this.status=t,this.code=r}}class eJ extends eD{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class ez extends eD{constructor(e,t,r,i){super(e,r,i),this.name=t,this.status=r}}class eF extends ez{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class eK extends ez{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class eW extends ez{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class eH extends ez{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eX extends ez{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class eG extends ez{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function eV(e){return eM(e)&&"AuthRetryableFetchError"===e.name}class eY extends ez{constructor(e,t,r){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=r}}class eZ extends ez{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}let eQ="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),e0=" 	\n\r=".split(""),e1=(()=>{let e=Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<e0.length;t+=1)e[e0[t].charCodeAt(0)]=-2;for(let t=0;t<eQ.length;t+=1)e[eQ[t].charCodeAt(0)]=t;return e})();function e2(e,t,r){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;)r(eQ[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6;else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;)r(eQ[t.queue>>t.queuedBits-6&63]),t.queuedBits-=6}function e6(e,t,r){let i=e1[e];if(i>-1)for(t.queue=t.queue<<6|i,t.queuedBits+=6;t.queuedBits>=8;)r(t.queue>>t.queuedBits-8&255),t.queuedBits-=8;else if(-2===i)return;else throw Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}function e8(e){let t=[],r=e=>{t.push(String.fromCodePoint(e))},i={utf8seq:0,codepoint:0},s={queue:0,queuedBits:0},n=e=>{!function(e,t,r){if(0===t.utf8seq){if(e<=127){r(e);return}for(let r=1;r<6;r+=1)if((e>>7-r&1)==0){t.utf8seq=r;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else if(4===t.utf8seq)t.codepoint=7&e;else throw Error("Invalid UTF-8 sequence");t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&r(t.codepoint)}}(e,i,r)};for(let t=0;t<e.length;t+=1)e6(e.charCodeAt(t),s,n);return t.join("")}let e4=()=>"undefined"!=typeof window&&"undefined"!=typeof document,e3={tested:!1,writable:!1},e5=()=>{if(!e4())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(e){return!1}if(e3.tested)return e3.writable;let e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),e3.tested=!0,e3.writable=!0}catch(e){e3.tested=!0,e3.writable=!1}return e3.writable},e9=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>Promise.resolve().then(r.bind(r,9494)).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},e7=e=>"object"==typeof e&&null!==e&&"status"in e&&"ok"in e&&"json"in e&&"function"==typeof e.json,te=async(e,t,r)=>{await e.setItem(t,JSON.stringify(r))},tt=async(e,t)=>{let r=await e.getItem(t);if(!r)return null;try{return JSON.parse(r)}catch(e){return r}},tr=async(e,t)=>{await e.removeItem(t)};class ti{constructor(){this.promise=new ti.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function ts(e){let t=e.split(".");if(3!==t.length)throw new eZ("Invalid JWT structure");for(let e=0;e<t.length;e++)if(!eN.test(t[e]))throw new eZ("JWT not in base64url format");return{header:JSON.parse(e8(t[0])),payload:JSON.parse(e8(t[1])),signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};for(let t=0;t<e.length;t+=1)e6(e.charCodeAt(t),r,i);return new Uint8Array(t)}(t[2]),raw:{header:t[0],payload:t[1]}}}async function tn(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}function to(e){return("0"+e.toString(16)).substr(-2)}async function ta(e){let t=new TextEncoder().encode(e),r=await crypto.subtle.digest("SHA-256",t);return Array.from(new Uint8Array(r)).map(e=>String.fromCharCode(e)).join("")}async function tl(e){return"undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder?btoa(await ta(e)).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):(console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e)}async function th(e,t,r=!1){let i=function(){let e=new Uint32Array(56);if("undefined"==typeof crypto){let e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length,r="";for(let i=0;i<56;i++)r+=e.charAt(Math.floor(Math.random()*t));return r}return crypto.getRandomValues(e),Array.from(e,to).join("")}(),s=i;r&&(s+="/PASSWORD_RECOVERY"),await te(e,`${t}-code-verifier`,s);let n=await tl(i),o=i===n?"plain":"s256";return[n,o]}ti.promiseConstructor=Promise;let tu=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i,tc=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function td(e){if(!tc.test(e))throw Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var tf=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};let tp=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),tg=[502,503,504];async function ty(e){var t;let r,i;if(!e7(e))throw new eG(tp(e),0);if(tg.includes(e.status))throw new eG(tp(e),e.status);try{r=await e.json()}catch(e){throw new eJ(tp(e),e)}let s=function(e){let t=e.headers.get(eL);if(!t||!t.match(tu))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(e){return null}}(e);if(s&&s.getTime()>=eB["2024-01-01"].timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?i=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(i=r.error_code),i){if("weak_password"===i)throw new eY(tp(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===i)throw new eF}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new eY(tp(r),e.status,r.weak_password.reasons);throw new eq(tp(r),e.status||500,i)}let tv=(e,t,r,i)=>{let s={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?s:(s.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),s.body=JSON.stringify(i),Object.assign(Object.assign({},s),r))};async function tm(e,t,r,i){var s;let n=Object.assign({},null==i?void 0:i.headers);n[eL]||(n[eL]=eB["2024-01-01"].name),(null==i?void 0:i.jwt)&&(n.Authorization=`Bearer ${i.jwt}`);let o=null!==(s=null==i?void 0:i.query)&&void 0!==s?s:{};(null==i?void 0:i.redirectTo)&&(o.redirect_to=i.redirectTo);let a=Object.keys(o).length?"?"+new URLSearchParams(o).toString():"",l=await tw(e,t,r+a,{headers:n,noResolveJson:null==i?void 0:i.noResolveJson},{},null==i?void 0:i.body);return(null==i?void 0:i.xform)?null==i?void 0:i.xform(l):{data:Object.assign({},l),error:null}}async function tw(e,t,r,i,s,n){let o;let a=tv(t,i,s,n);try{o=await e(r,Object.assign({},a))}catch(e){throw console.error(e),new eG(tp(e),0)}if(o.ok||await ty(o),null==i?void 0:i.noResolveJson)return o;try{return await o.json()}catch(e){await ty(e)}}function tb(e){var t,r;let i=null;return e.access_token&&e.refresh_token&&e.expires_in&&(i=Object.assign({},e),!e.expires_at)&&(i.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)),{data:{session:i,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function t_(e){let t=tb(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function tk(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function tS(e){return{data:e,error:null}}function tE(e){let{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n}=e;return{data:{properties:{action_link:t,email_otp:r,hashed_token:i,redirect_to:s,verification_type:n},user:Object.assign({},tf(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]))},error:null}}function tT(e){return e}let tj=["global","local","others"];var tI=function(e,t){var r={};for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&0>t.indexOf(i)&&(r[i]=e[i]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var s=0,i=Object.getOwnPropertySymbols(e);s<i.length;s++)0>t.indexOf(i[s])&&Object.prototype.propertyIsEnumerable.call(e,i[s])&&(r[i[s]]=e[i[s]]);return r};class tA{constructor({url:e="",headers:t={},fetch:r}){this.url=e,this.headers=t,this.fetch=e9(r),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=tj[0]){if(0>tj.indexOf(t))throw Error(`@supabase/auth-js: Parameter scope must be one of ${tj.join(", ")}`);try{return await tm(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(e){if(eM(e))return{data:null,error:e};throw e}}async inviteUserByEmail(e,t={}){try{return await tm(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:tk})}catch(e){if(eM(e))return{data:{user:null},error:e};throw e}}async generateLink(e){try{let{options:t}=e,r=tI(e,["options"]),i=Object.assign(Object.assign({},r),t);return"newEmail"in r&&(i.new_email=null==r?void 0:r.newEmail,delete i.newEmail),await tm(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:i,headers:this.headers,xform:tE,redirectTo:null==t?void 0:t.redirectTo})}catch(e){if(eM(e))return{data:{properties:null,user:null},error:e};throw e}}async createUser(e){try{return await tm(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:tk})}catch(e){if(eM(e))return{data:{user:null},error:e};throw e}}async listUsers(e){var t,r,i,s,n,o,a;try{let l={nextPage:null,lastPage:0,total:0},h=await tm(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(r=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==r?r:"",per_page:null!==(s=null===(i=null==e?void 0:e.perPage)||void 0===i?void 0:i.toString())&&void 0!==s?s:""},xform:tT});if(h.error)throw h.error;let u=await h.json(),c=null!==(n=h.headers.get("x-total-count"))&&void 0!==n?n:0,d=null!==(a=null===(o=h.headers.get("link"))||void 0===o?void 0:o.split(","))&&void 0!==a?a:[];return d.length>0&&(d.forEach(e=>{let t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),r=JSON.parse(e.split(";")[1].split("=")[1]);l[`${r}Page`]=t}),l.total=parseInt(c)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(e){if(eM(e))return{data:{users:[]},error:e};throw e}}async getUserById(e){td(e);try{return await tm(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:tk})}catch(e){if(eM(e))return{data:{user:null},error:e};throw e}}async updateUserById(e,t){td(e);try{return await tm(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:tk})}catch(e){if(eM(e))return{data:{user:null},error:e};throw e}}async deleteUser(e,t=!1){td(e);try{return await tm(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:tk})}catch(e){if(eM(e))return{data:{user:null},error:e};throw e}}async _listFactors(e){td(e.userId);try{let{data:t,error:r}=await tm(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:r}}catch(e){if(eM(e))return{data:null,error:e};throw e}}async _deleteFactor(e){td(e.userId),td(e.id);try{return{data:await tm(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(e){if(eM(e))return{data:null,error:e};throw e}}}let tC={getItem:e=>e5()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{e5()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{e5()&&globalThis.localStorage.removeItem(e)}};function tO(e={}){return{getItem:t=>e[t]||null,setItem:(t,r)=>{e[t]=r},removeItem:t=>{delete e[t]}}}let tx={debug:!!(globalThis&&e5()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"))};class tP extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class tR extends tP{}async function t$(e,t,r){tx.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);let i=new globalThis.AbortController;return t>0&&setTimeout(()=>{i.abort(),tx.debug&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:i.signal},async i=>{if(i){tx.debug&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,i.name);try{return await r()}finally{tx.debug&&console.log("@supabase/gotrue-js: navigatorLock: released",e,i.name)}}else{if(0===t)throw tx.debug&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new tR(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(tx.debug)try{let e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(e){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",e)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await r()}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();let tU={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:eU,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function tL(e,t,r){return await r()}class tB{constructor(e){var t,r;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=tB.nextInstanceID,tB.nextInstanceID+=1,this.instanceID>0&&e4()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");let i=Object.assign(Object.assign({},tU),e);if(this.logDebugMessages=!!i.debug,"function"==typeof i.debug&&(this.logger=i.debug),this.persistSession=i.persistSession,this.storageKey=i.storageKey,this.autoRefreshToken=i.autoRefreshToken,this.admin=new tA({url:i.url,headers:i.headers,fetch:i.fetch}),this.url=i.url,this.headers=i.headers,this.fetch=e9(i.fetch),this.lock=i.lock||tL,this.detectSessionInUrl=i.detectSessionInUrl,this.flowType=i.flowType,this.hasCustomAuthorizationHeader=i.hasCustomAuthorizationHeader,i.lock?this.lock=i.lock:e4()&&(null===(t=null==globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=t$:this.lock=tL,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?i.storage?this.storage=i.storage:e5()?this.storage=tC:(this.memoryStorage={},this.storage=tO(this.memoryStorage)):(this.memoryStorage={},this.storage=tO(this.memoryStorage)),e4()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(e){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",e)}null===(r=this.broadcastChannel)||void 0===r||r.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${e$}) ${new Date().toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{let t=function(e){let t={},r=new URL(e);if(r.hash&&"#"===r.hash[0])try{new URLSearchParams(r.hash.substring(1)).forEach((e,r)=>{t[r]=e})}catch(e){}return r.searchParams.forEach((e,r)=>{t[r]=e}),t}(window.location.href),r="none";if(this._isImplicitGrantCallback(t)?r="implicit":await this._isPKCECallback(t)&&(r="pkce"),e4()&&this.detectSessionInUrl&&"none"!==r){let{data:i,error:s}=await this._getSessionFromURL(t,r);if(s){if(this._debug("#_initialize()","error detecting session from URL",s),eM(s)&&"AuthImplicitGrantRedirectError"===s.name){let t=null===(e=s.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:s}}return await this._removeSession(),{error:s}}let{session:n,redirectType:o}=i;return this._debug("#_initialize()","detected session in URL",n,"redirect type",o),await this._saveSession(n),setTimeout(async()=>{"recovery"===o?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(e){if(eM(e))return{error:e};return{error:new eJ("Unexpected error during initialization",e)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,r,i;try{let{data:s,error:n}=await tm(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(r=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==r?r:{},gotrue_meta_security:{captcha_token:null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken}},xform:tb});if(n||!s)return{data:{user:null,session:null},error:n};let o=s.session,a=s.user;return s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:a,session:o},error:null}}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async signUp(e){var t,r,i;try{let s;if("email"in e){let{email:r,password:i,options:n}=e,o=null,a=null;"pkce"===this.flowType&&([o,a]=await th(this.storage,this.storageKey)),s=await tm(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:r,password:i,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:o,code_challenge_method:a},xform:tb})}else if("phone"in e){let{phone:t,password:n,options:o}=e;s=await tm(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!==(r=null==o?void 0:o.data)&&void 0!==r?r:{},channel:null!==(i=null==o?void 0:o.channel)&&void 0!==i?i:"sms",gotrue_meta_security:{captcha_token:null==o?void 0:o.captchaToken}},xform:tb})}else throw new eW("You must provide either an email or phone number and a password");let{data:n,error:o}=s;if(o||!n)return{data:{user:null,session:null},error:o};let a=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithPassword(e){try{let t;if("email"in e){let{email:r,password:i,options:s}=e;t=await tm(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:t_})}else if("phone"in e){let{phone:r,password:i,options:s}=e;t=await tm(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:r,password:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},xform:t_})}else throw new eW("You must provide either an email or phone number and a password");let{data:r,error:i}=t;if(i)return{data:{user:null,session:null},error:i};if(!r||!r.session||!r.user)return{data:{user:null,session:null},error:new eK};return r.session&&(await this._saveSession(r.session),await this._notifyAllSubscribers("SIGNED_IN",r.session)),{data:Object.assign({user:r.user,session:r.session},r.weak_password?{weakPassword:r.weak_password}:null),error:i}}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOAuth(e){var t,r,i,s;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(i=e.options)||void 0===i?void 0:i.queryParams,skipBrowserRedirect:null===(s=e.options)||void 0===s?void 0:s.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){let{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,r,i,s,n,o,a,l,h,u,c,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{let c;let{chain:d,wallet:g,statement:y,options:v}=e;if(e4()){if("object"==typeof g)c=g;else{let e=window;if("solana"in e&&"object"==typeof e.solana&&("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))c=e.solana;else throw Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.")}}else{if("object"!=typeof g||!(null==v?void 0:v.url))throw Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");c=g}let m=new URL(null!==(t=null==v?void 0:v.url)&&void 0!==t?t:window.location.href);if("signIn"in c&&c.signIn){let e;let t=await c.signIn(Object.assign(Object.assign(Object.assign({issuedAt:new Date().toISOString()},null==v?void 0:v.signInWithSolana),{version:"1",domain:m.host,uri:m.href}),y?{statement:y}:null));if(Array.isArray(t)&&t[0]&&"object"==typeof t[0])e=t[0];else if(t&&"object"==typeof t&&"signedMessage"in t&&"signature"in t)e=t;else throw Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");if("signedMessage"in e&&"signature"in e&&("string"==typeof e.signedMessage||e.signedMessage instanceof Uint8Array)&&e.signature instanceof Uint8Array)f="string"==typeof e.signedMessage?e.signedMessage:new TextDecoder().decode(e.signedMessage),p=e.signature;else throw Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields")}else{if(!("signMessage"in c)||"function"!=typeof c.signMessage||!("publicKey"in c)||"object"!=typeof c||!c.publicKey||!("toBase58"in c.publicKey)||"function"!=typeof c.publicKey.toBase58)throw Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${m.host} wants you to sign in with your Solana account:`,c.publicKey.toBase58(),...y?["",y,""]:[""],"Version: 1",`URI: ${m.href}`,`Issued At: ${null!==(i=null===(r=null==v?void 0:v.signInWithSolana)||void 0===r?void 0:r.issuedAt)&&void 0!==i?i:new Date().toISOString()}`,...(null===(s=null==v?void 0:v.signInWithSolana)||void 0===s?void 0:s.notBefore)?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...(null===(n=null==v?void 0:v.signInWithSolana)||void 0===n?void 0:n.expirationTime)?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...(null===(o=null==v?void 0:v.signInWithSolana)||void 0===o?void 0:o.chainId)?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...(null===(a=null==v?void 0:v.signInWithSolana)||void 0===a?void 0:a.nonce)?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...(null===(l=null==v?void 0:v.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...(null===(u=null===(h=null==v?void 0:v.signInWithSolana)||void 0===h?void 0:h.resources)||void 0===u?void 0:u.length)?["Resources",...v.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");let e=await c.signMessage(new TextEncoder().encode(f),"utf8");if(!e||!(e instanceof Uint8Array))throw Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{let{data:t,error:r}=await tm(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:function(e){let t=[],r={queue:0,queuedBits:0},i=e=>{t.push(e)};return e.forEach(e=>e2(e,r,i)),e2(null,r,i),t.join("")}(p)},(null===(c=e.options)||void 0===c?void 0:c.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:tb});if(r)throw r;if(!t||!t.session||!t.user)return{data:{user:null,session:null},error:new eK};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:r}}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async _exchangeCodeForSession(e){let t=await tt(this.storage,`${this.storageKey}-code-verifier`),[r,i]=(null!=t?t:"").split("/");try{let{data:t,error:s}=await tm(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:r},xform:tb});if(await tr(this.storage,`${this.storageKey}-code-verifier`),s)throw s;if(!t||!t.session||!t.user)return{data:{user:null,session:null,redirectType:null},error:new eK};return t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=i?i:null}),error:s}}catch(e){if(eM(e))return{data:{user:null,session:null,redirectType:null},error:e};throw e}}async signInWithIdToken(e){try{let{options:t,provider:r,token:i,access_token:s,nonce:n}=e,{data:o,error:a}=await tm(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:r,id_token:i,access_token:s,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:tb});if(a)return{data:{user:null,session:null},error:a};if(!o||!o.session||!o.user)return{data:{user:null,session:null},error:new eK};return o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:o,error:a}}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithOtp(e){var t,r,i,s,n;try{if("email"in e){let{email:i,options:s}=e,n=null,o=null;"pkce"===this.flowType&&([n,o]=await th(this.storage,this.storageKey));let{error:a}=await tm(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:i,data:null!==(t=null==s?void 0:s.data)&&void 0!==t?t:{},create_user:null===(r=null==s?void 0:s.shouldCreateUser)||void 0===r||r,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},code_challenge:n,code_challenge_method:o},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:a}}if("phone"in e){let{phone:t,options:r}=e,{data:o,error:a}=await tm(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(i=null==r?void 0:r.data)&&void 0!==i?i:{},create_user:null===(s=null==r?void 0:r.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null==r?void 0:r.captchaToken},channel:null!==(n=null==r?void 0:r.channel)&&void 0!==n?n:"sms"}});return{data:{user:null,session:null,messageId:null==o?void 0:o.message_id},error:a}}throw new eW("You must provide either an email or phone number.")}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async verifyOtp(e){var t,r;try{let i,s;"options"in e&&(i=null===(t=e.options)||void 0===t?void 0:t.redirectTo,s=null===(r=e.options)||void 0===r?void 0:r.captchaToken);let{data:n,error:o}=await tm(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:s}}),redirectTo:i,xform:tb});if(o)throw o;if(!n)throw Error("An error occurred on token verification.");let a=n.session,l=n.user;return(null==a?void 0:a.access_token)&&(await this._saveSession(a),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",a)),{data:{user:l,session:a},error:null}}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async signInWithSSO(e){var t,r,i;try{let s=null,n=null;return"pkce"===this.flowType&&([s,n]=await th(this.storage,this.storageKey)),await tm(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==r?r:void 0}),(null===(i=null==e?void 0:e.options)||void 0===i?void 0:i.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:s,code_challenge_method:n}),headers:this.headers,xform:tS})}catch(e){if(eM(e))return{data:null,error:e};throw e}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{let{data:{session:t},error:r}=e;if(r)throw r;if(!t)throw new eF;let{error:i}=await tm(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:i}})}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{let t=`${this.url}/resend`;if("email"in e){let{email:r,type:i,options:s}=e,{error:n}=await tm(this.fetch,"POST",t,{headers:this.headers,body:{email:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}},redirectTo:null==s?void 0:s.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){let{phone:r,type:i,options:s}=e,{data:n,error:o}=await tm(this.fetch,"POST",t,{headers:this.headers,body:{phone:r,type:i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:o}}throw new eW("You must provide either an email or phone number and a type")}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async getSession(){return await this.initializePromise,await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){let e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),r=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await r}catch(e){}})()),r}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;let e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(e){}})()),await e;this.pendingInLock.length;){let e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{let t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",Error().stack);try{let e=null,t=await tt(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};let r=!!e.expires_at&&1e3*e.expires_at-Date.now()<9e4;if(this._debug("#__loadSession()",`session has${r?"":" not"} expired`,"expires_at",e.expires_at),!r){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,r,i)=>(t||"user"!==r||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,r,i))})}return{data:{session:e},error:null}}let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{session:null},error:s};return{data:{session:i},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){return e?await this._getUser(e):(await this.initializePromise,await this._acquireLock(-1,async()=>await this._getUser()))}async _getUser(e){try{if(e)return await tm(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:tk});return await this._useSession(async e=>{var t,r,i;let{data:s,error:n}=e;if(n)throw n;return(null===(t=s.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await tm(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(i=null===(r=s.session)||void 0===r?void 0:r.access_token)&&void 0!==i?i:void 0,xform:tk}):{data:{user:null},error:new eF}})}catch(e){if(eM(e))return eM(e)&&"AuthSessionMissingError"===e.name&&(await this._removeSession(),await tr(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:e};throw e}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async r=>{let{data:i,error:s}=r;if(s)throw s;if(!i.session)throw new eF;let n=i.session,o=null,a=null;"pkce"===this.flowType&&null!=e.email&&([o,a]=await th(this.storage,this.storageKey));let{data:l,error:h}=await tm(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:o,code_challenge_method:a}),jwt:n.access_token,xform:tk});if(h)throw h;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}})}catch(e){if(eM(e))return{data:{user:null},error:e};throw e}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new eF;let t=Date.now()/1e3,r=t,i=!0,s=null,{payload:n}=ts(e.access_token);if(n.exp&&(i=(r=n.exp)<=t),i){let{session:t,error:r}=await this._callRefreshToken(e.refresh_token);if(r)return{data:{user:null,session:null},error:r};if(!t)return{data:{user:null,session:null},error:null};s=t}else{let{data:i,error:n}=await this._getUser(e.access_token);if(n)throw n;s={access_token:e.access_token,refresh_token:e.refresh_token,user:i.user,token_type:"bearer",expires_in:r-t,expires_at:r},await this._saveSession(s),await this._notifyAllSubscribers("SIGNED_IN",s)}return{data:{user:s.user,session:s},error:null}}catch(e){if(eM(e))return{data:{session:null,user:null},error:e};throw e}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var r;if(!e){let{data:i,error:s}=t;if(s)throw s;e=null!==(r=i.session)&&void 0!==r?r:void 0}if(!(null==e?void 0:e.refresh_token))throw new eF;let{session:i,error:s}=await this._callRefreshToken(e.refresh_token);return s?{data:{user:null,session:null},error:s}:i?{data:{user:i.user,session:i},error:null}:{data:{user:null,session:null},error:null}})}catch(e){if(eM(e))return{data:{user:null,session:null},error:e};throw e}}async _getSessionFromURL(e,t){try{if(!e4())throw new eH("No browser detected.");if(e.error||e.error_description||e.error_code)throw new eH(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new eX("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new eH("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new eX("No code detected.");let{data:t,error:r}=await this._exchangeCodeForSession(e.code);if(r)throw r;let i=new URL(window.location.href);return i.searchParams.delete("code"),window.history.replaceState(window.history.state,"",i.toString()),{data:{session:t.session,redirectType:null},error:null}}let{provider_token:r,provider_refresh_token:i,access_token:s,refresh_token:n,expires_in:o,expires_at:a,token_type:l}=e;if(!s||!o||!n||!l)throw new eH("No session defined in URL");let h=Math.round(Date.now()/1e3),u=parseInt(o),c=h+u;a&&(c=parseInt(a));let d=c-h;1e3*d<=3e4&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${d}s, should have been closer to ${u}s`);let f=c-u;h-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,c,h):h-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,c,h);let{data:p,error:g}=await this._getUser(s);if(g)throw g;let y={provider_token:r,provider_refresh_token:i,access_token:s,expires_in:u,expires_at:c,refresh_token:n,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:y,redirectType:e.type},error:null}}catch(e){if(eM(e))return{data:{session:null,redirectType:null},error:e};throw e}}_isImplicitGrantCallback(e){return!!(e.access_token||e.error_description)}async _isPKCECallback(e){let t=await tt(this.storage,`${this.storageKey}-code-verifier`);return!!(e.code&&t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{error:s};let n=null===(r=i.session)||void 0===r?void 0:r.access_token;if(n){let{error:t}=await this.admin.signOut(n,e);if(t&&!(eM(t)&&"AuthApiError"===t.name&&(404===t.status||401===t.status||403===t.status)))return{error:t}}return"others"!==e&&(await this._removeSession(),await tr(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){let t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){let t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),r={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,r),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:r}}}async _emitInitialSession(e){return await this._useSession(async t=>{var r,i;try{let{data:{session:i},error:s}=t;if(s)throw s;await (null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",i)),this._debug("INITIAL_SESSION","callback id",e,"session",i)}catch(t){await (null===(i=this.stateChangeEmitters.get(e))||void 0===i?void 0:i.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",t),console.error(t)}})}async resetPasswordForEmail(e,t={}){let r=null,i=null;"pkce"===this.flowType&&([r,i]=await th(this.storage,this.storageKey,!0));try{return await tm(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:r,code_challenge_method:i,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(e){if(eM(e))return{data:null,error:e};throw e}}async getUserIdentities(){var e;try{let{data:t,error:r}=await this.getUser();if(r)throw r;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(e){if(eM(e))return{data:null,error:e};throw e}}async linkIdentity(e){var t;try{let{data:r,error:i}=await this._useSession(async t=>{var r,i,s,n,o;let{data:a,error:l}=t;if(l)throw l;let h=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(r=e.options)||void 0===r?void 0:r.redirectTo,scopes:null===(i=e.options)||void 0===i?void 0:i.scopes,queryParams:null===(s=e.options)||void 0===s?void 0:s.queryParams,skipBrowserRedirect:!0});return await tm(this.fetch,"GET",h,{headers:this.headers,jwt:null!==(o=null===(n=a.session)||void 0===n?void 0:n.access_token)&&void 0!==o?o:void 0})});if(i)throw i;return!e4()||(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)||window.location.assign(null==r?void 0:r.url),{data:{provider:e.provider,url:null==r?void 0:r.url},error:null}}catch(t){if(eM(t))return{data:{provider:e.provider,url:null},error:t};throw t}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)throw n;return await tm(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(i=null===(r=s.session)||void 0===r?void 0:r.access_token)&&void 0!==i?i:void 0})})}catch(e){if(eM(e))return{data:null,error:e};throw e}}async _refreshAccessToken(e){let t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{var r,i;let s=Date.now();return await (r=async r=>(r>0&&await tn(200*Math.pow(2,r-1)),this._debug(t,"refreshing attempt",r),await tm(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:tb})),i=(e,t)=>t&&eV(t)&&Date.now()+200*Math.pow(2,e)-s<3e4,new Promise((e,t)=>{(async()=>{for(let s=0;s<1/0;s++)try{let t=await r(s);if(!i(s,null,t)){e(t);return}}catch(e){if(!i(s,e)){t(e);return}}})()}))}catch(e){if(this._debug(t,"error",e),eM(e))return{data:{session:null,user:null},error:e};throw e}finally{this._debug(t,"end")}}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){let r=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",r),e4()&&!t.skipBrowserRedirect&&window.location.assign(r),{data:{provider:e,url:r},error:null}}async _recoverAndRefresh(){var e;let t="#_recoverAndRefresh()";this._debug(t,"begin");try{let r=await tt(this.storage,this.storageKey);if(this._debug(t,"session from storage",r),!this._isValidSession(r)){this._debug(t,"session is not valid"),null!==r&&await this._removeSession();return}let i=(null!==(e=r.expires_at)&&void 0!==e?e:1/0)*1e3-Date.now()<9e4;if(this._debug(t,`session has${i?"":" not"} expired with margin of 90000s`),i){if(this.autoRefreshToken&&r.refresh_token){let{error:e}=await this._callRefreshToken(r.refresh_token);e&&(console.error(e),eV(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",r)}catch(e){this._debug(t,"error",e),console.error(e);return}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,r;if(!e)throw new eF;if(this.refreshingDeferred)return this.refreshingDeferred.promise;let i=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(i,"begin");try{this.refreshingDeferred=new ti;let{data:t,error:r}=await this._refreshAccessToken(e);if(r)throw r;if(!t.session)throw new eF;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);let i={session:t.session,error:null};return this.refreshingDeferred.resolve(i),i}catch(e){if(this._debug(i,"error",e),eM(e)){let r={session:null,error:e};return eV(e)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(r),r}throw null===(r=this.refreshingDeferred)||void 0===r||r.reject(e),e}finally{this.refreshingDeferred=null,this._debug(i,"end")}}async _notifyAllSubscribers(e,t,r=!0){let i=`#_notifyAllSubscribers(${e})`;this._debug(i,"begin",t,`broadcast = ${r}`);try{this.broadcastChannel&&r&&this.broadcastChannel.postMessage({event:e,session:t});let i=[],s=Array.from(this.stateChangeEmitters.values()).map(async r=>{try{await r.callback(e,t)}catch(e){i.push(e)}});if(await Promise.all(s),i.length>0){for(let e=0;e<i.length;e+=1)console.error(i[e]);throw i[0]}}finally{this._debug(i,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await te(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await tr(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");let e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&e4()&&(null==window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(e){console.error("removing visibilitychange callback failed",e)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");let e=setInterval(()=>this._autoRefreshTokenTick(),3e4);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");let e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{let e=Date.now();try{return await this._useSession(async t=>{let{data:{session:r}}=t;if(!r||!r.refresh_token||!r.expires_at){this._debug("#_autoRefreshTokenTick()","no session");return}let i=Math.floor((1e3*r.expires_at-e)/3e4);this._debug("#_autoRefreshTokenTick()",`access token expires in ${i} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),i<=3&&await this._callRefreshToken(r.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(e.isAcquireTimeout||e instanceof tP)this._debug("auto refresh token tick lock not available");else throw e}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!e4()||!(null==window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null==window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){let t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{if("visible"!==document.visibilityState){this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting");return}await this._recoverAndRefresh()}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,r){let i=[`provider=${encodeURIComponent(t)}`];if((null==r?void 0:r.redirectTo)&&i.push(`redirect_to=${encodeURIComponent(r.redirectTo)}`),(null==r?void 0:r.scopes)&&i.push(`scopes=${encodeURIComponent(r.scopes)}`),"pkce"===this.flowType){let[e,t]=await th(this.storage,this.storageKey),r=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});i.push(r.toString())}if(null==r?void 0:r.queryParams){let e=new URLSearchParams(r.queryParams);i.push(e.toString())}return(null==r?void 0:r.skipBrowserRedirect)&&i.push(`skip_http_redirect=${r.skipBrowserRedirect}`),`${e}?${i.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await tm(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token})})}catch(e){if(eM(e))return{data:null,error:e};throw e}}async _enroll(e){try{return await this._useSession(async t=>{var r,i;let{data:s,error:n}=t;if(n)return{data:null,error:n};let o=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:a,error:l}=await tm(this.fetch,"POST",`${this.url}/factors`,{body:o,headers:this.headers,jwt:null===(r=null==s?void 0:s.session)||void 0===r?void 0:r.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(i=null==a?void 0:a.totp)||void 0===i?void 0:i.qr_code)&&(a.totp.qr_code=`data:image/svg+xml;utf-8,${a.totp.qr_code}`),{data:a,error:null})})}catch(e){if(eM(e))return{data:null,error:e};throw e}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;if(s)return{data:null,error:s};let{data:n,error:o}=await tm(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token});return o?{data:null,error:o}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:o})})}catch(e){if(eM(e))return{data:null,error:e};throw e}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var r;let{data:i,error:s}=t;return s?{data:null,error:s}:await tm(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(r=null==i?void 0:i.session)||void 0===r?void 0:r.access_token})})}catch(e){if(eM(e))return{data:null,error:e};throw e}})}async _challengeAndVerify(e){let{data:t,error:r}=await this._challenge({factorId:e.factorId});return r?{data:null,error:r}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){let{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};let r=(null==e?void 0:e.factors)||[],i=r.filter(e=>"totp"===e.factor_type&&"verified"===e.status),s=r.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:r,totp:i,phone:s},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,r;let{data:{session:i},error:s}=e;if(s)return{data:null,error:s};if(!i)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};let{payload:n}=ts(i.access_token),o=null;n.aal&&(o=n.aal);let a=o;return(null!==(r=null===(t=i.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==r?r:[]).length>0&&(a="aal2"),{data:{currentLevel:o,nextLevel:a,currentAuthenticationMethods:n.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let r=t.keys.find(t=>t.kid===e);if(r||(r=this.jwks.keys.find(t=>t.kid===e))&&this.jwks_cached_at+6e5>Date.now())return r;let{data:i,error:s}=await tm(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(s)throw s;if(!i.keys||0===i.keys.length)throw new eZ("JWKS is empty");if(this.jwks=i,this.jwks_cached_at=Date.now(),!(r=i.keys.find(t=>t.kid===e)))throw new eZ("No matching signing key found in JWKS");return r}async getClaims(e,t={keys:[]}){try{let r=e;if(!r){let{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};r=e.session.access_token}let{header:i,payload:s,signature:n,raw:{header:o,payload:a}}=ts(r);if(!function(e){if(!e)throw Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw Error("JWT has expired")}(s.exp),!i.kid||"HS256"===i.alg||!("crypto"in globalThis&&"subtle"in globalThis.crypto)){let{error:e}=await this.getUser(r);if(e)throw e;return{data:{claims:s,header:i,signature:n},error:null}}let l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw Error("Invalid alg claim")}}(i.alg),h=await this.fetchJwk(i.kid,t),u=await crypto.subtle.importKey("jwk",h,l,!0,["verify"]);if(!await crypto.subtle.verify(l,u,n,function(e){let t=[];return!function(e,t){for(let r=0;r<e.length;r+=1){let i=e.charCodeAt(r);if(i>55295&&i<=56319){let t=(i-55296)*1024&65535;i=(e.charCodeAt(r+1)-56320&65535|t)+65536,r+=1}!function(e,t){if(e<=127){t(e);return}if(e<=2047){t(192|e>>6),t(128|63&e);return}if(e<=65535){t(224|e>>12),t(128|e>>6&63),t(128|63&e);return}if(e<=1114111){t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),t(128|63&e);return}throw Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}(i,t)}}(e,e=>t.push(e)),new Uint8Array(t)}(`${o}.${a}`)))throw new eZ("Invalid JWT signature");return{data:{claims:s,header:i,signature:n},error:null}}catch(e){if(eM(e))return{data:null,error:e};throw e}}}tB.nextInstanceID=0;var tN=tB;class tD extends tN{constructor(e){super(e)}}class tM{constructor(e,t,r){var i,s,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw Error("supabaseUrl is required.");if(!t)throw Error("supabaseKey is required.");let o=e.endsWith("/")?e:e+"/",a=new URL(o);this.realtimeUrl=new URL("realtime/v1",a),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",a),this.storageUrl=new URL("storage/v1",a),this.functionsUrl=new URL("functions/v1",a);let l=`sb-${a.hostname.split(".")[0]}-auth-token`,u=function(e,t){var r,i;let{db:s,auth:n,realtime:o,global:a}=e,{db:l,auth:h,realtime:u,global:c}=t,d={db:Object.assign(Object.assign({},l),s),auth:Object.assign(Object.assign({},h),n),realtime:Object.assign(Object.assign({},u),o),global:Object.assign(Object.assign(Object.assign({},c),a),{headers:Object.assign(Object.assign({},null!==(r=null==c?void 0:c.headers)&&void 0!==r?r:{}),null!==(i=null==a?void 0:a.headers)&&void 0!==i?i:{})}),accessToken:()=>{var e,t,r,i;return e=this,t=void 0,i=function*(){return""},new(r=void 0,r=Promise)(function(s,n){function o(e){try{l(i.next(e))}catch(e){n(e)}}function a(e){try{l(i.throw(e))}catch(e){n(e)}}function l(e){var t;e.done?s(e.value):((t=e.value)instanceof r?t:new r(function(e){e(t)})).then(o,a)}l((i=i.apply(e,t||[])).next())})}};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=r?r:{},{db:eI,realtime:eC,auth:Object.assign(Object.assign({},eA),{storageKey:l}),global:ej});this.storageKey=null!==(i=u.auth.storageKey)&&void 0!==i?i:"",this.headers=null!==(s=u.global.headers)&&void 0!==s?s:{},u.accessToken?(this.accessToken=u.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(n=u.auth)&&void 0!==n?n:{},this.headers,u.global.fetch),this.fetch=eR(t,this._getAccessToken.bind(this),u.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},u.realtime)),this.rest=new h(new URL("rest/v1",a).href,{headers:this.headers,schema:u.db.schema,fetch:this.fetch}),u.accessToken||this._listenForAuthEvents()}get functions(){return new l(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new eE(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},r={}){return this.rest.rpc(e,t,r)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t,r,i,s,n;return r=this,i=void 0,s=void 0,n=function*(){if(this.accessToken)return yield this.accessToken();let{data:r}=yield this.auth.getSession();return null!==(t=null===(e=r.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null},new(s||(s=Promise))(function(e,t){function o(e){try{l(n.next(e))}catch(e){t(e)}}function a(e){try{l(n.throw(e))}catch(e){t(e)}}function l(t){var r;t.done?e(t.value):((r=t.value)instanceof s?r:new s(function(e){e(r)})).then(o,a)}l((n=n.apply(r,i||[])).next())})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,storageKey:s,flowType:n,lock:o,debug:a},l,h){let u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new tD({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),l),storageKey:s,autoRefreshToken:e,persistSession:t,detectSessionInUrl:r,storage:i,flowType:n,lock:o,debug:a,fetch:h,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new R(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,r){("TOKEN_REFRESHED"===e||"SIGNED_IN"===e)&&this.changedAccessToken!==r?this.changedAccessToken=r:"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0)}}let tq=(e,t,r)=>new tM(e,t,r)},2601:function(e,t,r){"use strict";var i,s;e.exports=(null==(i=r.g.process)?void 0:i.env)&&"object"==typeof(null==(s=r.g.process)?void 0:s.env)?r.g.process:r(8960)},263:function(e){!function(){var t={675:function(e,t){"use strict";t.byteLength=function(e){var t=l(e),r=t[0],i=t[1];return(r+i)*3/4-i},t.toByteArray=function(e){var t,r,n=l(e),o=n[0],a=n[1],h=new s((o+a)*3/4-a),u=0,c=a>0?o-4:o;for(r=0;r<c;r+=4)t=i[e.charCodeAt(r)]<<18|i[e.charCodeAt(r+1)]<<12|i[e.charCodeAt(r+2)]<<6|i[e.charCodeAt(r+3)],h[u++]=t>>16&255,h[u++]=t>>8&255,h[u++]=255&t;return 2===a&&(t=i[e.charCodeAt(r)]<<2|i[e.charCodeAt(r+1)]>>4,h[u++]=255&t),1===a&&(t=i[e.charCodeAt(r)]<<10|i[e.charCodeAt(r+1)]<<4|i[e.charCodeAt(r+2)]>>2,h[u++]=t>>8&255,h[u++]=255&t),h},t.fromByteArray=function(e){for(var t,i=e.length,s=i%3,n=[],o=0,a=i-s;o<a;o+=16383)n.push(function(e,t,i){for(var s,n=[],o=t;o<i;o+=3)n.push(r[(s=(e[o]<<16&16711680)+(e[o+1]<<8&65280)+(255&e[o+2]))>>18&63]+r[s>>12&63]+r[s>>6&63]+r[63&s]);return n.join("")}(e,o,o+16383>a?a:o+16383));return 1===s?n.push(r[(t=e[i-1])>>2]+r[t<<4&63]+"=="):2===s&&n.push(r[(t=(e[i-2]<<8)+e[i-1])>>10]+r[t>>4&63]+r[t<<2&63]+"="),n.join("")};for(var r=[],i=[],s="undefined"!=typeof Uint8Array?Uint8Array:Array,n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",o=0,a=n.length;o<a;++o)r[o]=n[o],i[n.charCodeAt(o)]=o;function l(e){var t=e.length;if(t%4>0)throw Error("Invalid string. Length must be a multiple of 4");var r=e.indexOf("=");-1===r&&(r=t);var i=r===t?0:4-r%4;return[r,i]}i["-".charCodeAt(0)]=62,i["_".charCodeAt(0)]=63},72:function(e,t,r){"use strict";/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var i=r(675),s=r(783),n="function"==typeof Symbol&&"function"==typeof Symbol.for?Symbol.for("nodejs.util.inspect.custom"):null;function o(e){if(e>**********)throw RangeError('The value "'+e+'" is invalid for option "size"');var t=new Uint8Array(e);return Object.setPrototypeOf(t,a.prototype),t}function a(e,t,r){if("number"==typeof e){if("string"==typeof t)throw TypeError('The "string" argument must be of type string. Received type number');return u(e)}return l(e,t,r)}function l(e,t,r){if("string"==typeof e)return function(e,t){if(("string"!=typeof t||""===t)&&(t="utf8"),!a.isEncoding(t))throw TypeError("Unknown encoding: "+t);var r=0|f(e,t),i=o(r),s=i.write(e,t);return s!==r&&(i=i.slice(0,s)),i}(e,t);if(ArrayBuffer.isView(e))return c(e);if(null==e)throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e);if(C(e,ArrayBuffer)||e&&C(e.buffer,ArrayBuffer)||"undefined"!=typeof SharedArrayBuffer&&(C(e,SharedArrayBuffer)||e&&C(e.buffer,SharedArrayBuffer)))return function(e,t,r){var i;if(t<0||e.byteLength<t)throw RangeError('"offset" is outside of buffer bounds');if(e.byteLength<t+(r||0))throw RangeError('"length" is outside of buffer bounds');return Object.setPrototypeOf(i=void 0===t&&void 0===r?new Uint8Array(e):void 0===r?new Uint8Array(e,t):new Uint8Array(e,t,r),a.prototype),i}(e,t,r);if("number"==typeof e)throw TypeError('The "value" argument must not be of type number. Received type number');var i=e.valueOf&&e.valueOf();if(null!=i&&i!==e)return a.from(i,t,r);var s=function(e){if(a.isBuffer(e)){var t,r=0|d(e.length),i=o(r);return 0===i.length||e.copy(i,0,0,r),i}return void 0!==e.length?"number"!=typeof e.length||(t=e.length)!=t?o(0):c(e):"Buffer"===e.type&&Array.isArray(e.data)?c(e.data):void 0}(e);if(s)return s;if("undefined"!=typeof Symbol&&null!=Symbol.toPrimitive&&"function"==typeof e[Symbol.toPrimitive])return a.from(e[Symbol.toPrimitive]("string"),t,r);throw TypeError("The first argument must be one of type string, Buffer, ArrayBuffer, Array, or Array-like Object. Received type "+typeof e)}function h(e){if("number"!=typeof e)throw TypeError('"size" argument must be of type number');if(e<0)throw RangeError('The value "'+e+'" is invalid for option "size"')}function u(e){return h(e),o(e<0?0:0|d(e))}function c(e){for(var t=e.length<0?0:0|d(e.length),r=o(t),i=0;i<t;i+=1)r[i]=255&e[i];return r}function d(e){if(e>=**********)throw RangeError("Attempt to allocate Buffer larger than maximum size: 0x7fffffff bytes");return 0|e}function f(e,t){if(a.isBuffer(e))return e.length;if(ArrayBuffer.isView(e)||C(e,ArrayBuffer))return e.byteLength;if("string"!=typeof e)throw TypeError('The "string" argument must be one of type string, Buffer, or ArrayBuffer. Received type '+typeof e);var r=e.length,i=arguments.length>2&&!0===arguments[2];if(!i&&0===r)return 0;for(var s=!1;;)switch(t){case"ascii":case"latin1":case"binary":return r;case"utf8":case"utf-8":return T(e).length;case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return 2*r;case"hex":return r>>>1;case"base64":return I(e).length;default:if(s)return i?-1:T(e).length;t=(""+t).toLowerCase(),s=!0}}function p(e,t,r){var s,n,o=!1;if((void 0===t||t<0)&&(t=0),t>this.length||((void 0===r||r>this.length)&&(r=this.length),r<=0||(r>>>=0)<=(t>>>=0)))return"";for(e||(e="utf8");;)switch(e){case"hex":return function(e,t,r){var i=e.length;(!t||t<0)&&(t=0),(!r||r<0||r>i)&&(r=i);for(var s="",n=t;n<r;++n)s+=O[e[n]];return s}(this,t,r);case"utf8":case"utf-8":return m(this,t,r);case"ascii":return function(e,t,r){var i="";r=Math.min(e.length,r);for(var s=t;s<r;++s)i+=String.fromCharCode(127&e[s]);return i}(this,t,r);case"latin1":case"binary":return function(e,t,r){var i="";r=Math.min(e.length,r);for(var s=t;s<r;++s)i+=String.fromCharCode(e[s]);return i}(this,t,r);case"base64":return s=t,n=r,0===s&&n===this.length?i.fromByteArray(this):i.fromByteArray(this.slice(s,n));case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return function(e,t,r){for(var i=e.slice(t,r),s="",n=0;n<i.length;n+=2)s+=String.fromCharCode(i[n]+256*i[n+1]);return s}(this,t,r);default:if(o)throw TypeError("Unknown encoding: "+e);e=(e+"").toLowerCase(),o=!0}}function g(e,t,r){var i=e[t];e[t]=e[r],e[r]=i}function y(e,t,r,i,s){var n;if(0===e.length)return -1;if("string"==typeof r?(i=r,r=0):r>**********?r=**********:r<-2147483648&&(r=-2147483648),(n=r=+r)!=n&&(r=s?0:e.length-1),r<0&&(r=e.length+r),r>=e.length){if(s)return -1;r=e.length-1}else if(r<0){if(!s)return -1;r=0}if("string"==typeof t&&(t=a.from(t,i)),a.isBuffer(t))return 0===t.length?-1:v(e,t,r,i,s);if("number"==typeof t)return(t&=255,"function"==typeof Uint8Array.prototype.indexOf)?s?Uint8Array.prototype.indexOf.call(e,t,r):Uint8Array.prototype.lastIndexOf.call(e,t,r):v(e,[t],r,i,s);throw TypeError("val must be string, number or Buffer")}function v(e,t,r,i,s){var n,o=1,a=e.length,l=t.length;if(void 0!==i&&("ucs2"===(i=String(i).toLowerCase())||"ucs-2"===i||"utf16le"===i||"utf-16le"===i)){if(e.length<2||t.length<2)return -1;o=2,a/=2,l/=2,r/=2}function h(e,t){return 1===o?e[t]:e.readUInt16BE(t*o)}if(s){var u=-1;for(n=r;n<a;n++)if(h(e,n)===h(t,-1===u?0:n-u)){if(-1===u&&(u=n),n-u+1===l)return u*o}else -1!==u&&(n-=n-u),u=-1}else for(r+l>a&&(r=a-l),n=r;n>=0;n--){for(var c=!0,d=0;d<l;d++)if(h(e,n+d)!==h(t,d)){c=!1;break}if(c)return n}return -1}function m(e,t,r){r=Math.min(e.length,r);for(var i=[],s=t;s<r;){var n,o,a,l,h=e[s],u=null,c=h>239?4:h>223?3:h>191?2:1;if(s+c<=r)switch(c){case 1:h<128&&(u=h);break;case 2:(192&(n=e[s+1]))==128&&(l=(31&h)<<6|63&n)>127&&(u=l);break;case 3:n=e[s+1],o=e[s+2],(192&n)==128&&(192&o)==128&&(l=(15&h)<<12|(63&n)<<6|63&o)>2047&&(l<55296||l>57343)&&(u=l);break;case 4:n=e[s+1],o=e[s+2],a=e[s+3],(192&n)==128&&(192&o)==128&&(192&a)==128&&(l=(15&h)<<18|(63&n)<<12|(63&o)<<6|63&a)>65535&&l<1114112&&(u=l)}null===u?(u=65533,c=1):u>65535&&(u-=65536,i.push(u>>>10&1023|55296),u=56320|1023&u),i.push(u),s+=c}return function(e){var t=e.length;if(t<=4096)return String.fromCharCode.apply(String,e);for(var r="",i=0;i<t;)r+=String.fromCharCode.apply(String,e.slice(i,i+=4096));return r}(i)}function w(e,t,r){if(e%1!=0||e<0)throw RangeError("offset is not uint");if(e+t>r)throw RangeError("Trying to access beyond buffer length")}function b(e,t,r,i,s,n){if(!a.isBuffer(e))throw TypeError('"buffer" argument must be a Buffer instance');if(t>s||t<n)throw RangeError('"value" argument is out of bounds');if(r+i>e.length)throw RangeError("Index out of range")}function _(e,t,r,i,s,n){if(r+i>e.length||r<0)throw RangeError("Index out of range")}function k(e,t,r,i,n){return t=+t,r>>>=0,n||_(e,t,r,4,34028234663852886e22,-34028234663852886e22),s.write(e,t,r,i,23,4),r+4}function S(e,t,r,i,n){return t=+t,r>>>=0,n||_(e,t,r,8,17976931348623157e292,-17976931348623157e292),s.write(e,t,r,i,52,8),r+8}t.Buffer=a,t.SlowBuffer=function(e){return+e!=e&&(e=0),a.alloc(+e)},t.INSPECT_MAX_BYTES=50,t.kMaxLength=**********,a.TYPED_ARRAY_SUPPORT=function(){try{var e=new Uint8Array(1),t={foo:function(){return 42}};return Object.setPrototypeOf(t,Uint8Array.prototype),Object.setPrototypeOf(e,t),42===e.foo()}catch(e){return!1}}(),a.TYPED_ARRAY_SUPPORT||"undefined"==typeof console||"function"!=typeof console.error||console.error("This browser lacks typed array (Uint8Array) support which is required by `buffer` v5.x. Use `buffer` v4.x if you require old browser support."),Object.defineProperty(a.prototype,"parent",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.buffer}}),Object.defineProperty(a.prototype,"offset",{enumerable:!0,get:function(){if(a.isBuffer(this))return this.byteOffset}}),a.poolSize=8192,a.from=function(e,t,r){return l(e,t,r)},Object.setPrototypeOf(a.prototype,Uint8Array.prototype),Object.setPrototypeOf(a,Uint8Array),a.alloc=function(e,t,r){return(h(e),e<=0)?o(e):void 0!==t?"string"==typeof r?o(e).fill(t,r):o(e).fill(t):o(e)},a.allocUnsafe=function(e){return u(e)},a.allocUnsafeSlow=function(e){return u(e)},a.isBuffer=function(e){return null!=e&&!0===e._isBuffer&&e!==a.prototype},a.compare=function(e,t){if(C(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),C(t,Uint8Array)&&(t=a.from(t,t.offset,t.byteLength)),!a.isBuffer(e)||!a.isBuffer(t))throw TypeError('The "buf1", "buf2" arguments must be one of type Buffer or Uint8Array');if(e===t)return 0;for(var r=e.length,i=t.length,s=0,n=Math.min(r,i);s<n;++s)if(e[s]!==t[s]){r=e[s],i=t[s];break}return r<i?-1:i<r?1:0},a.isEncoding=function(e){switch(String(e).toLowerCase()){case"hex":case"utf8":case"utf-8":case"ascii":case"latin1":case"binary":case"base64":case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return!0;default:return!1}},a.concat=function(e,t){if(!Array.isArray(e))throw TypeError('"list" argument must be an Array of Buffers');if(0===e.length)return a.alloc(0);if(void 0===t)for(r=0,t=0;r<e.length;++r)t+=e[r].length;var r,i=a.allocUnsafe(t),s=0;for(r=0;r<e.length;++r){var n=e[r];if(C(n,Uint8Array)&&(n=a.from(n)),!a.isBuffer(n))throw TypeError('"list" argument must be an Array of Buffers');n.copy(i,s),s+=n.length}return i},a.byteLength=f,a.prototype._isBuffer=!0,a.prototype.swap16=function(){var e=this.length;if(e%2!=0)throw RangeError("Buffer size must be a multiple of 16-bits");for(var t=0;t<e;t+=2)g(this,t,t+1);return this},a.prototype.swap32=function(){var e=this.length;if(e%4!=0)throw RangeError("Buffer size must be a multiple of 32-bits");for(var t=0;t<e;t+=4)g(this,t,t+3),g(this,t+1,t+2);return this},a.prototype.swap64=function(){var e=this.length;if(e%8!=0)throw RangeError("Buffer size must be a multiple of 64-bits");for(var t=0;t<e;t+=8)g(this,t,t+7),g(this,t+1,t+6),g(this,t+2,t+5),g(this,t+3,t+4);return this},a.prototype.toString=function(){var e=this.length;return 0===e?"":0==arguments.length?m(this,0,e):p.apply(this,arguments)},a.prototype.toLocaleString=a.prototype.toString,a.prototype.equals=function(e){if(!a.isBuffer(e))throw TypeError("Argument must be a Buffer");return this===e||0===a.compare(this,e)},a.prototype.inspect=function(){var e="",r=t.INSPECT_MAX_BYTES;return e=this.toString("hex",0,r).replace(/(.{2})/g,"$1 ").trim(),this.length>r&&(e+=" ... "),"<Buffer "+e+">"},n&&(a.prototype[n]=a.prototype.inspect),a.prototype.compare=function(e,t,r,i,s){if(C(e,Uint8Array)&&(e=a.from(e,e.offset,e.byteLength)),!a.isBuffer(e))throw TypeError('The "target" argument must be one of type Buffer or Uint8Array. Received type '+typeof e);if(void 0===t&&(t=0),void 0===r&&(r=e?e.length:0),void 0===i&&(i=0),void 0===s&&(s=this.length),t<0||r>e.length||i<0||s>this.length)throw RangeError("out of range index");if(i>=s&&t>=r)return 0;if(i>=s)return -1;if(t>=r)return 1;if(t>>>=0,r>>>=0,i>>>=0,s>>>=0,this===e)return 0;for(var n=s-i,o=r-t,l=Math.min(n,o),h=this.slice(i,s),u=e.slice(t,r),c=0;c<l;++c)if(h[c]!==u[c]){n=h[c],o=u[c];break}return n<o?-1:o<n?1:0},a.prototype.includes=function(e,t,r){return -1!==this.indexOf(e,t,r)},a.prototype.indexOf=function(e,t,r){return y(this,e,t,r,!0)},a.prototype.lastIndexOf=function(e,t,r){return y(this,e,t,r,!1)},a.prototype.write=function(e,t,r,i){if(void 0===t)i="utf8",r=this.length,t=0;else if(void 0===r&&"string"==typeof t)i=t,r=this.length,t=0;else if(isFinite(t))t>>>=0,isFinite(r)?(r>>>=0,void 0===i&&(i="utf8")):(i=r,r=void 0);else throw Error("Buffer.write(string, encoding, offset[, length]) is no longer supported");var s,n,o,a,l,h,u,c,d,f,p,g,y=this.length-t;if((void 0===r||r>y)&&(r=y),e.length>0&&(r<0||t<0)||t>this.length)throw RangeError("Attempt to write outside buffer bounds");i||(i="utf8");for(var v=!1;;)switch(i){case"hex":return function(e,t,r,i){r=Number(r)||0;var s=e.length-r;i?(i=Number(i))>s&&(i=s):i=s;var n=t.length;i>n/2&&(i=n/2);for(var o=0;o<i;++o){var a=parseInt(t.substr(2*o,2),16);if(a!=a)break;e[r+o]=a}return o}(this,e,t,r);case"utf8":case"utf-8":return l=t,h=r,A(T(e,this.length-l),this,l,h);case"ascii":return u=t,c=r,A(j(e),this,u,c);case"latin1":case"binary":return s=this,n=e,o=t,a=r,A(j(n),s,o,a);case"base64":return d=t,f=r,A(I(e),this,d,f);case"ucs2":case"ucs-2":case"utf16le":case"utf-16le":return p=t,g=r,A(function(e,t){for(var r,i,s=[],n=0;n<e.length&&!((t-=2)<0);++n)i=(r=e.charCodeAt(n))>>8,s.push(r%256),s.push(i);return s}(e,this.length-p),this,p,g);default:if(v)throw TypeError("Unknown encoding: "+i);i=(""+i).toLowerCase(),v=!0}},a.prototype.toJSON=function(){return{type:"Buffer",data:Array.prototype.slice.call(this._arr||this,0)}},a.prototype.slice=function(e,t){var r=this.length;e=~~e,t=void 0===t?r:~~t,e<0?(e+=r)<0&&(e=0):e>r&&(e=r),t<0?(t+=r)<0&&(t=0):t>r&&(t=r),t<e&&(t=e);var i=this.subarray(e,t);return Object.setPrototypeOf(i,a.prototype),i},a.prototype.readUIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var i=this[e],s=1,n=0;++n<t&&(s*=256);)i+=this[e+n]*s;return i},a.prototype.readUIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var i=this[e+--t],s=1;t>0&&(s*=256);)i+=this[e+--t]*s;return i},a.prototype.readUInt8=function(e,t){return e>>>=0,t||w(e,1,this.length),this[e]},a.prototype.readUInt16LE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]|this[e+1]<<8},a.prototype.readUInt16BE=function(e,t){return e>>>=0,t||w(e,2,this.length),this[e]<<8|this[e+1]},a.prototype.readUInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),(this[e]|this[e+1]<<8|this[e+2]<<16)+16777216*this[e+3]},a.prototype.readUInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),16777216*this[e]+(this[e+1]<<16|this[e+2]<<8|this[e+3])},a.prototype.readIntLE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var i=this[e],s=1,n=0;++n<t&&(s*=256);)i+=this[e+n]*s;return i>=(s*=128)&&(i-=Math.pow(2,8*t)),i},a.prototype.readIntBE=function(e,t,r){e>>>=0,t>>>=0,r||w(e,t,this.length);for(var i=t,s=1,n=this[e+--i];i>0&&(s*=256);)n+=this[e+--i]*s;return n>=(s*=128)&&(n-=Math.pow(2,8*t)),n},a.prototype.readInt8=function(e,t){return(e>>>=0,t||w(e,1,this.length),128&this[e])?-((255-this[e]+1)*1):this[e]},a.prototype.readInt16LE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e]|this[e+1]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt16BE=function(e,t){e>>>=0,t||w(e,2,this.length);var r=this[e+1]|this[e]<<8;return 32768&r?4294901760|r:r},a.prototype.readInt32LE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]|this[e+1]<<8|this[e+2]<<16|this[e+3]<<24},a.prototype.readInt32BE=function(e,t){return e>>>=0,t||w(e,4,this.length),this[e]<<24|this[e+1]<<16|this[e+2]<<8|this[e+3]},a.prototype.readFloatLE=function(e,t){return e>>>=0,t||w(e,4,this.length),s.read(this,e,!0,23,4)},a.prototype.readFloatBE=function(e,t){return e>>>=0,t||w(e,4,this.length),s.read(this,e,!1,23,4)},a.prototype.readDoubleLE=function(e,t){return e>>>=0,t||w(e,8,this.length),s.read(this,e,!0,52,8)},a.prototype.readDoubleBE=function(e,t){return e>>>=0,t||w(e,8,this.length),s.read(this,e,!1,52,8)},a.prototype.writeUIntLE=function(e,t,r,i){if(e=+e,t>>>=0,r>>>=0,!i){var s=Math.pow(2,8*r)-1;b(this,e,t,r,s,0)}var n=1,o=0;for(this[t]=255&e;++o<r&&(n*=256);)this[t+o]=e/n&255;return t+r},a.prototype.writeUIntBE=function(e,t,r,i){if(e=+e,t>>>=0,r>>>=0,!i){var s=Math.pow(2,8*r)-1;b(this,e,t,r,s,0)}var n=r-1,o=1;for(this[t+n]=255&e;--n>=0&&(o*=256);)this[t+n]=e/o&255;return t+r},a.prototype.writeUInt8=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,1,255,0),this[t]=255&e,t+1},a.prototype.writeUInt16LE=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,2,65535,0),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeUInt16BE=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,2,65535,0),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeUInt32LE=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,4,4294967295,0),this[t+3]=e>>>24,this[t+2]=e>>>16,this[t+1]=e>>>8,this[t]=255&e,t+4},a.prototype.writeUInt32BE=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,4,4294967295,0),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeIntLE=function(e,t,r,i){if(e=+e,t>>>=0,!i){var s=Math.pow(2,8*r-1);b(this,e,t,r,s-1,-s)}var n=0,o=1,a=0;for(this[t]=255&e;++n<r&&(o*=256);)e<0&&0===a&&0!==this[t+n-1]&&(a=1),this[t+n]=(e/o>>0)-a&255;return t+r},a.prototype.writeIntBE=function(e,t,r,i){if(e=+e,t>>>=0,!i){var s=Math.pow(2,8*r-1);b(this,e,t,r,s-1,-s)}var n=r-1,o=1,a=0;for(this[t+n]=255&e;--n>=0&&(o*=256);)e<0&&0===a&&0!==this[t+n+1]&&(a=1),this[t+n]=(e/o>>0)-a&255;return t+r},a.prototype.writeInt8=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,1,127,-128),e<0&&(e=255+e+1),this[t]=255&e,t+1},a.prototype.writeInt16LE=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,2,32767,-32768),this[t]=255&e,this[t+1]=e>>>8,t+2},a.prototype.writeInt16BE=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,2,32767,-32768),this[t]=e>>>8,this[t+1]=255&e,t+2},a.prototype.writeInt32LE=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,4,**********,-2147483648),this[t]=255&e,this[t+1]=e>>>8,this[t+2]=e>>>16,this[t+3]=e>>>24,t+4},a.prototype.writeInt32BE=function(e,t,r){return e=+e,t>>>=0,r||b(this,e,t,4,**********,-2147483648),e<0&&(e=4294967295+e+1),this[t]=e>>>24,this[t+1]=e>>>16,this[t+2]=e>>>8,this[t+3]=255&e,t+4},a.prototype.writeFloatLE=function(e,t,r){return k(this,e,t,!0,r)},a.prototype.writeFloatBE=function(e,t,r){return k(this,e,t,!1,r)},a.prototype.writeDoubleLE=function(e,t,r){return S(this,e,t,!0,r)},a.prototype.writeDoubleBE=function(e,t,r){return S(this,e,t,!1,r)},a.prototype.copy=function(e,t,r,i){if(!a.isBuffer(e))throw TypeError("argument should be a Buffer");if(r||(r=0),i||0===i||(i=this.length),t>=e.length&&(t=e.length),t||(t=0),i>0&&i<r&&(i=r),i===r||0===e.length||0===this.length)return 0;if(t<0)throw RangeError("targetStart out of bounds");if(r<0||r>=this.length)throw RangeError("Index out of range");if(i<0)throw RangeError("sourceEnd out of bounds");i>this.length&&(i=this.length),e.length-t<i-r&&(i=e.length-t+r);var s=i-r;if(this===e&&"function"==typeof Uint8Array.prototype.copyWithin)this.copyWithin(t,r,i);else if(this===e&&r<t&&t<i)for(var n=s-1;n>=0;--n)e[n+t]=this[n+r];else Uint8Array.prototype.set.call(e,this.subarray(r,i),t);return s},a.prototype.fill=function(e,t,r,i){if("string"==typeof e){if("string"==typeof t?(i=t,t=0,r=this.length):"string"==typeof r&&(i=r,r=this.length),void 0!==i&&"string"!=typeof i)throw TypeError("encoding must be a string");if("string"==typeof i&&!a.isEncoding(i))throw TypeError("Unknown encoding: "+i);if(1===e.length){var s,n=e.charCodeAt(0);("utf8"===i&&n<128||"latin1"===i)&&(e=n)}}else"number"==typeof e?e&=255:"boolean"==typeof e&&(e=Number(e));if(t<0||this.length<t||this.length<r)throw RangeError("Out of range index");if(r<=t)return this;if(t>>>=0,r=void 0===r?this.length:r>>>0,e||(e=0),"number"==typeof e)for(s=t;s<r;++s)this[s]=e;else{var o=a.isBuffer(e)?e:a.from(e,i),l=o.length;if(0===l)throw TypeError('The value "'+e+'" is invalid for argument "value"');for(s=0;s<r-t;++s)this[s+t]=o[s%l]}return this};var E=/[^+/0-9A-Za-z-_]/g;function T(e,t){t=t||1/0;for(var r,i=e.length,s=null,n=[],o=0;o<i;++o){if((r=e.charCodeAt(o))>55295&&r<57344){if(!s){if(r>56319||o+1===i){(t-=3)>-1&&n.push(239,191,189);continue}s=r;continue}if(r<56320){(t-=3)>-1&&n.push(239,191,189),s=r;continue}r=(s-55296<<10|r-56320)+65536}else s&&(t-=3)>-1&&n.push(239,191,189);if(s=null,r<128){if((t-=1)<0)break;n.push(r)}else if(r<2048){if((t-=2)<0)break;n.push(r>>6|192,63&r|128)}else if(r<65536){if((t-=3)<0)break;n.push(r>>12|224,r>>6&63|128,63&r|128)}else if(r<1114112){if((t-=4)<0)break;n.push(r>>18|240,r>>12&63|128,r>>6&63|128,63&r|128)}else throw Error("Invalid code point")}return n}function j(e){for(var t=[],r=0;r<e.length;++r)t.push(255&e.charCodeAt(r));return t}function I(e){return i.toByteArray(function(e){if((e=(e=e.split("=")[0]).trim().replace(E,"")).length<2)return"";for(;e.length%4!=0;)e+="=";return e}(e))}function A(e,t,r,i){for(var s=0;s<i&&!(s+r>=t.length)&&!(s>=e.length);++s)t[s+r]=e[s];return s}function C(e,t){return e instanceof t||null!=e&&null!=e.constructor&&null!=e.constructor.name&&e.constructor.name===t.name}var O=function(){for(var e="0123456789abcdef",t=Array(256),r=0;r<16;++r)for(var i=16*r,s=0;s<16;++s)t[i+s]=e[r]+e[s];return t}()},783:function(e,t){/*! ieee754. BSD-3-Clause License. Feross Aboukhadijeh <https://feross.org/opensource> */t.read=function(e,t,r,i,s){var n,o,a=8*s-i-1,l=(1<<a)-1,h=l>>1,u=-7,c=r?s-1:0,d=r?-1:1,f=e[t+c];for(c+=d,n=f&(1<<-u)-1,f>>=-u,u+=a;u>0;n=256*n+e[t+c],c+=d,u-=8);for(o=n&(1<<-u)-1,n>>=-u,u+=i;u>0;o=256*o+e[t+c],c+=d,u-=8);if(0===n)n=1-h;else{if(n===l)return o?NaN:1/0*(f?-1:1);o+=Math.pow(2,i),n-=h}return(f?-1:1)*o*Math.pow(2,n-i)},t.write=function(e,t,r,i,s,n){var o,a,l,h=8*n-s-1,u=(1<<h)-1,c=u>>1,d=23===s?5960464477539062e-23:0,f=i?0:n-1,p=i?1:-1,g=t<0||0===t&&1/t<0?1:0;for(isNaN(t=Math.abs(t))||t===1/0?(a=isNaN(t)?1:0,o=u):(o=Math.floor(Math.log(t)/Math.LN2),t*(l=Math.pow(2,-o))<1&&(o--,l*=2),o+c>=1?t+=d/l:t+=d*Math.pow(2,1-c),t*l>=2&&(o++,l/=2),o+c>=u?(a=0,o=u):o+c>=1?(a=(t*l-1)*Math.pow(2,s),o+=c):(a=t*Math.pow(2,c-1)*Math.pow(2,s),o=0));s>=8;e[r+f]=255&a,f+=p,a/=256,s-=8);for(o=o<<s|a,h+=s;h>0;e[r+f]=255&o,f+=p,o/=256,h-=8);e[r+f-p]|=128*g}}},r={};function i(e){var s=r[e];if(void 0!==s)return s.exports;var n=r[e]={exports:{}},o=!0;try{t[e](n,n.exports,i),o=!1}finally{o&&delete r[e]}return n.exports}i.ab="//";var s=i(72);e.exports=s}()},8960:function(e){!function(){var t={229:function(e){var t,r,i,s=e.exports={};function n(){throw Error("setTimeout has not been defined")}function o(){throw Error("clearTimeout has not been defined")}function a(e){if(t===setTimeout)return setTimeout(e,0);if((t===n||!t)&&setTimeout)return t=setTimeout,setTimeout(e,0);try{return t(e,0)}catch(r){try{return t.call(null,e,0)}catch(r){return t.call(this,e,0)}}}!function(){try{t="function"==typeof setTimeout?setTimeout:n}catch(e){t=n}try{r="function"==typeof clearTimeout?clearTimeout:o}catch(e){r=o}}();var l=[],h=!1,u=-1;function c(){h&&i&&(h=!1,i.length?l=i.concat(l):u=-1,l.length&&d())}function d(){if(!h){var e=a(c);h=!0;for(var t=l.length;t;){for(i=l,l=[];++u<t;)i&&i[u].run();u=-1,t=l.length}i=null,h=!1,function(e){if(r===clearTimeout)return clearTimeout(e);if((r===o||!r)&&clearTimeout)return r=clearTimeout,clearTimeout(e);try{r(e)}catch(t){try{return r.call(null,e)}catch(t){return r.call(this,e)}}}(e)}}function f(e,t){this.fun=e,this.array=t}function p(){}s.nextTick=function(e){var t=Array(arguments.length-1);if(arguments.length>1)for(var r=1;r<arguments.length;r++)t[r-1]=arguments[r];l.push(new f(e,t)),1!==l.length||h||a(d)},f.prototype.run=function(){this.fun.apply(null,this.array)},s.title="browser",s.browser=!0,s.env={},s.argv=[],s.version="",s.versions={},s.on=p,s.addListener=p,s.once=p,s.off=p,s.removeListener=p,s.removeAllListeners=p,s.emit=p,s.prependListener=p,s.prependOnceListener=p,s.listeners=function(e){return[]},s.binding=function(e){throw Error("process.binding is not supported")},s.cwd=function(){return"/"},s.chdir=function(e){throw Error("process.chdir is not supported")},s.umask=function(){return 0}}},r={};function i(e){var s=r[e];if(void 0!==s)return s.exports;var n=r[e]={exports:{}},o=!0;try{t[e](n,n.exports,i),o=!1}finally{o&&delete r[e]}return n.exports}i.ab="//";var s=i(229);e.exports=s}()},8027:function(e){"use strict";var t={decodeValues:!0,map:!1,silent:!1};function r(e){return"string"==typeof e&&!!e.trim()}function i(e,i){var s,n,o,a,l=e.split(";").filter(r),h=(s=l.shift(),n="",o="",(a=s.split("=")).length>1?(n=a.shift(),o=a.join("=")):o=s,{name:n,value:o}),u=h.name,c=h.value;i=i?Object.assign({},t,i):t;try{c=i.decodeValues?decodeURIComponent(c):c}catch(e){console.error("set-cookie-parser encountered an error while decoding a cookie with value '"+c+"'. Set options.decodeValues to false to disable this feature.",e)}var d={name:u,value:c};return l.forEach(function(e){var t=e.split("="),r=t.shift().trimLeft().toLowerCase(),i=t.join("=");"expires"===r?d.expires=new Date(i):"max-age"===r?d.maxAge=parseInt(i,10):"secure"===r?d.secure=!0:"httponly"===r?d.httpOnly=!0:"samesite"===r?d.sameSite=i:"partitioned"===r?d.partitioned=!0:d[r]=i}),d}function s(e,s){if(s=s?Object.assign({},t,s):t,!e)return s.map?{}:[];if(e.headers){if("function"==typeof e.headers.getSetCookie)e=e.headers.getSetCookie();else if(e.headers["set-cookie"])e=e.headers["set-cookie"];else{var n=e.headers[Object.keys(e.headers).find(function(e){return"set-cookie"===e.toLowerCase()})];n||!e.headers.cookie||s.silent||console.warn("Warning: set-cookie-parser appears to have been called on a request object. It is designed to parse Set-Cookie headers from responses, not Cookie headers from requests. Set the option {silent: true} to suppress this warning."),e=n}}return(Array.isArray(e)||(e=[e]),s.map)?e.filter(r).reduce(function(e,t){var r=i(t,s);return e[r.name]=r,e},{}):e.filter(r).map(function(e){return i(e,s)})}e.exports=s,e.exports.parse=s,e.exports.parseString=i,e.exports.splitCookiesString=function(e){if(Array.isArray(e))return e;if("string"!=typeof e)return[];var t,r,i,s,n,o=[],a=0;function l(){for(;a<e.length&&/\s/.test(e.charAt(a));)a+=1;return a<e.length}for(;a<e.length;){for(t=a,n=!1;l();)if(","===(r=e.charAt(a))){for(i=a,a+=1,l(),s=a;a<e.length&&"="!==(r=e.charAt(a))&&";"!==r&&","!==r;)a+=1;a<e.length&&"="===e.charAt(a)?(n=!0,a=s,o.push(e.substring(t,i)),t=a):a=i+1}else a+=1;(!n||a>=e.length)&&o.push(e.substring(t,e.length))}return o}},626:function(e,t,r){"use strict";r.r(t),r.d(t,{BrowserCookieAuthStorageAdapter:function(){return I},CookieAuthStorageAdapter:function(){return j},DEFAULT_COOKIE_OPTIONS:function(){return S},createSupabaseClient:function(){return A},isBrowser:function(){return k},parseCookies:function(){return C},parseSupabaseCookie:function(){return b},serializeCookie:function(){return O},stringifySupabaseSession:function(){return _}}),new TextEncoder;let i=new TextDecoder,s=e=>{let t=atob(e),r=new Uint8Array(t.length);for(let e=0;e<t.length;e++)r[e]=t.charCodeAt(e);return r},n=e=>{let t=e;t instanceof Uint8Array&&(t=i.decode(t)),t=t.replace(/-/g,"+").replace(/_/g,"/").replace(/\s/g,"");try{return s(t)}catch(e){throw TypeError("The input to be decoded is not correctly encoded.")}};var o,a,l=r(1492),h=Object.create,u=Object.defineProperty,c=Object.getOwnPropertyDescriptor,d=Object.getOwnPropertyNames,f=Object.getPrototypeOf,p=Object.prototype.hasOwnProperty,g=(e,t,r,i)=>{if(t&&"object"==typeof t||"function"==typeof t)for(let s of d(t))p.call(e,s)||s===r||u(e,s,{get:()=>t[s],enumerable:!(i=c(t,s))||i.enumerable});return e},y=(e,t,r)=>(r=null!=e?h(f(e)):{},g(!t&&e&&e.__esModule?r:u(r,"default",{value:e,enumerable:!0}),e)),v=(o={"../../node_modules/.pnpm/cookie@0.5.0/node_modules/cookie/index.js"(e){e.parse=function(e,t){if("string"!=typeof e)throw TypeError("argument str must be a string");for(var r={},s=(t||{}).decode||i,n=0;n<e.length;){var o=e.indexOf("=",n);if(-1===o)break;var a=e.indexOf(";",n);if(-1===a)a=e.length;else if(a<o){n=e.lastIndexOf(";",o-1)+1;continue}var l=e.slice(n,o).trim();if(void 0===r[l]){var h=e.slice(o+1,a).trim();34===h.charCodeAt(0)&&(h=h.slice(1,-1)),r[l]=function(e,t){try{return t(e)}catch(t){return e}}(h,s)}n=a+1}return r},e.serialize=function(e,i,n){var o=n||{},a=o.encode||s;if("function"!=typeof a)throw TypeError("option encode is invalid");if(!r.test(e))throw TypeError("argument name is invalid");var l=a(i);if(l&&!r.test(l))throw TypeError("argument val is invalid");var h=e+"="+l;if(null!=o.maxAge){var u=o.maxAge-0;if(isNaN(u)||!isFinite(u))throw TypeError("option maxAge is invalid");h+="; Max-Age="+Math.floor(u)}if(o.domain){if(!r.test(o.domain))throw TypeError("option domain is invalid");h+="; Domain="+o.domain}if(o.path){if(!r.test(o.path))throw TypeError("option path is invalid");h+="; Path="+o.path}if(o.expires){var c=o.expires;if("[object Date]"!==t.call(c)&&!(c instanceof Date)||isNaN(c.valueOf()))throw TypeError("option expires is invalid");h+="; Expires="+c.toUTCString()}if(o.httpOnly&&(h+="; HttpOnly"),o.secure&&(h+="; Secure"),o.priority)switch("string"==typeof o.priority?o.priority.toLowerCase():o.priority){case"low":h+="; Priority=Low";break;case"medium":h+="; Priority=Medium";break;case"high":h+="; Priority=High";break;default:throw TypeError("option priority is invalid")}if(o.sameSite)switch("string"==typeof o.sameSite?o.sameSite.toLowerCase():o.sameSite){case!0:case"strict":h+="; SameSite=Strict";break;case"lax":h+="; SameSite=Lax";break;case"none":h+="; SameSite=None";break;default:throw TypeError("option sameSite is invalid")}return h};var t=Object.prototype.toString,r=/^[\u0009\u0020-\u007e\u0080-\u00ff]+$/;function i(e){return -1!==e.indexOf("%")?decodeURIComponent(e):e}function s(e){return encodeURIComponent(e)}}},function(){return a||(0,o[d(o)[0]])((a={exports:{}}).exports,a),a.exports}),m=y(v()),w=y(v());function b(e){if(!e)return null;try{let t=JSON.parse(e);if(!t)return null;if("Object"===t.constructor.name)return t;if("Array"!==t.constructor.name)throw Error(`Unexpected format: ${t.constructor.name}`);let[r,i,s]=t[0].split("."),o=n(i),a=new TextDecoder,{exp:l,sub:h,...u}=JSON.parse(a.decode(o));return{expires_at:l,expires_in:l-Math.round(Date.now()/1e3),token_type:"bearer",access_token:t[0],refresh_token:t[1],provider_token:t[2],provider_refresh_token:t[3],user:{id:h,factors:t[4],...u}}}catch(e){return console.warn("Failed to parse cookie string:",e),null}}function _(e){var t;return JSON.stringify([e.access_token,e.refresh_token,e.provider_token,e.provider_refresh_token,(null==(t=e.user)?void 0:t.factors)??null])}function k(){return"undefined"!=typeof window&&void 0!==window.document}var S={path:"/",sameSite:"lax",maxAge:31536e6};function E(e){return RegExp(".{1,"+e+"}","g")}var T=E(3180),j=class{constructor(e){this.cookieOptions={...S,...e,maxAge:S.maxAge}}getItem(e){let t=this.getCookie(e);if(e.endsWith("-code-verifier")&&t)return t;if(t)return JSON.stringify(b(t));let r=function(e,t=()=>null){let r=[];for(let i=0;;i++){let s=t(`${e}.${i}`);if(!s)break;r.push(s)}return r.length?r.join(""):null}(e,e=>this.getCookie(e));return null!==r?JSON.stringify(b(r)):null}setItem(e,t){if(e.endsWith("-code-verifier")){this.setCookie(e,t);return}(function(e,t,r){let i=void 0!==r?E(r):T;if(1===Math.ceil(t.length/(r??3180)))return[{name:e,value:t}];let s=[],n=t.match(i);return null==n||n.forEach((t,r)=>{let i=`${e}.${r}`;s.push({name:i,value:t})}),s})(e,_(JSON.parse(t))).forEach(e=>{this.setCookie(e.name,e.value)})}removeItem(e){this._deleteSingleCookie(e),this._deleteChunkedCookies(e)}_deleteSingleCookie(e){this.getCookie(e)&&this.deleteCookie(e)}_deleteChunkedCookies(e,t=0){for(let r=t;;r++){let t=`${e}.${r}`;if(void 0===this.getCookie(t))break;this.deleteCookie(t)}}},I=class extends j{constructor(e){super(e)}getCookie(e){return k()?(0,m.parse)(document.cookie)[e]:null}setCookie(e,t){if(!k())return null;document.cookie=(0,m.serialize)(e,t,{...this.cookieOptions,httpOnly:!1})}deleteCookie(e){if(!k())return null;document.cookie=(0,m.serialize)(e,"",{...this.cookieOptions,maxAge:0,httpOnly:!1})}};function A(e,t,r){var i;let s=k();return(0,l.eI)(e,t,{...r,auth:{flowType:"pkce",autoRefreshToken:s,detectSessionInUrl:s,persistSession:!0,storage:r.auth.storage,...(null==(i=r.auth)?void 0:i.storageKey)?{storageKey:r.auth.storageKey}:{}}})}var C=w.parse,O=w.serialize;/*! Bundled license information:

cookie/index.js:
  (*!
   * cookie
   * Copyright(c) 2012-2014 Roman Shtylman
   * Copyright(c) 2015 Douglas Christopher Wilson
   * MIT Licensed
   *)
*/}}]);
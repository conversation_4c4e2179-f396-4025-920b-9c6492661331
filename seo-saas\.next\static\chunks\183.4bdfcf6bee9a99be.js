"use strict";(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[183],{5183:function(e,t,a){a.d(t,{createSupabaseComponentClient:function(){return l},signIn:function(){return p},signInWithGitHub:function(){return b},signInWithGoogle:function(){return I},signUp:function(){return h},OQ:function(){return u}});var n=a(1492),i=a(3082);let s={supabaseUrl:"https://xpcbyzcaidfukddqniny.supabase.co",supabaseKey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A",supabaseServiceKey:""},r=function(){let e={NEXT_PUBLIC_SUPABASE_URL:"https://xpcbyzcaidfukddqniny.supabase.co",NEXT_PUBLIC_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A"},t=[],a=[];if(Object.entries(e).forEach(e=>{let[a,n]=e;n&&""!==n.trim()||t.push(a)}),e.NEXT_PUBLIC_SUPABASE_URL)try{new URL(e.NEXT_PUBLIC_SUPABASE_URL)}catch(e){a.push("NEXT_PUBLIC_SUPABASE_URL is not a valid URL")}if(e.NEXT_PUBLIC_SUPABASE_ANON_KEY){let t=e.NEXT_PUBLIC_SUPABASE_ANON_KEY;t.startsWith("eyJ")&&3===t.split(".").length||a.push("NEXT_PUBLIC_SUPABASE_ANON_KEY appears to be invalid (not a JWT token)")}return{isValid:0===t.length&&0===a.length,missingVars:t,errors:a,vars:e}}(),c=(e,t)=>{};r.isValid||(["Supabase configuration error:",...r.missingVars.map(e=>"- Missing: ".concat(e)),...r.errors.map(e=>"- Error: ".concat(e))].join("\n"),c("Environment validation failed",{validation:r}));let o=null;try{s.supabaseUrl&&s.supabaseKey?o=(0,n.eI)(s.supabaseUrl,s.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):c("Cannot create Supabase client: missing URL or key")}catch(e){c("Failed to create Supabase client",e)}try{s.supabaseUrl&&s.supabaseServiceKey}catch(e){c("Failed to create Supabase admin client",e)}let u=o,l=()=>{try{return(0,i.createClientComponentClient)()}catch(e){throw c("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function p(e,t){let a=l(),{data:n,error:i}=await a.auth.signInWithPassword({email:e,password:t});if(i)throw Error(i.message);return n}async function h(e,t,a){let n=l(),{data:i,error:s}=await n.auth.signUp({email:e,password:t,options:{data:{full_name:a},emailRedirectTo:"".concat("http://localhost:3000","/auth/callback")}});if(s)throw Error(s.message);return i}async function I(){let e=l(),{data:t,error:a}=await e.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat("http://localhost:3000","/auth/callback"),queryParams:{access_type:"offline",prompt:"consent"}}});if(a)throw Error(a.message);return t}async function b(){let e=l(),{data:t,error:a}=await e.auth.signInWithOAuth({provider:"github",options:{redirectTo:"".concat("http://localhost:3000","/auth/callback")}});if(a)throw Error(a.message);return t}}}]);
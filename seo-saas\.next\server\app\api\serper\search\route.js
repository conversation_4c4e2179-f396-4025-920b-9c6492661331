"use strict";(()=>{var e={};e.id=663,e.ids=[663],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},2361:e=>{e.exports=require("events")},3685:e=>{e.exports=require("http")},5687:e=>{e.exports=require("https")},1808:e=>{e.exports=require("net")},5477:e=>{e.exports=require("punycode")},2781:e=>{e.exports=require("stream")},4404:e=>{e.exports=require("tls")},7310:e=>{e.exports=require("url")},9796:e=>{e.exports=require("zlib")},1352:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>T,originalPathname:()=>v,patchFetch:()=>M,requestAsyncStorage:()=>q,routeModule:()=>f,serverHooks:()=>R,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>S});var s={};r.r(s),r.d(s,{GET:()=>w,POST:()=>y});var i=r(5419),a=r(9108),o=r(9678),n=r(8070),c=r(5252),u=r(2178),l=r(7734),d=r(9279),p=r(2045);let m=c.Ry({query:c.Z_().min(1,"Query is required").max(200,"Query too long"),location:c.Z_().max(100,"Location too long").optional(),country:c.Z_().length(2,"Country must be 2-letter code").optional(),language:c.Z_().length(2,"Language must be 2-letter code").optional(),device:c.Km(["desktop","mobile"]).default("desktop"),num:c.Rx().min(1).max(100).default(10),page:c.Rx().min(1).max(10).optional(),type:c.Km(["search","images","videos","news","shopping"]).default("search"),safe:c.Km(["active","off"]).optional(),autocorrect:c.O7().default(!0)}),h=c.Ry({query:c.Z_().min(1,"Query is required").max(200,"Query too long"),location:c.Z_().max(100,"Location too long").optional().default(""),analyzeTop:c.Rx().min(1).max(10).default(5),includeContent:c.O7().default(!0),includeMetrics:c.O7().default(!0)});async function y(e){let t=Date.now();try{let r,s,i,a;let{searchParams:o}=new URL(e.url),c=o.get("action")||"search",u=await e.json();"analyze"===c?(r=h.parse(u),s=.05):(r=m.parse(u),s=.01);let y=(0,p.jq)(),{data:{user:w},error:f}=await y.auth.getUser();if(f||!w)return n.Z.json({success:!1,error:"Unauthorized"},{status:401});let{data:q}=await y.from("profiles").select("subscription_tier, credits_remaining").eq("id",w.id).single(),x=q?.subscription_tier||"free";try{await (0,d.dZ)(w.id,x,"apiCalls",async()=>({success:!0}))}catch(e){return n.Z.json({success:!1,error:e.message,code:"SUBSCRIPTION_LIMIT_EXCEEDED"},{status:429})}try{await (0,d.sy)(w.id,x,s,async()=>({success:!0}))}catch(e){return n.Z.json({success:!1,error:e.message,code:"COST_LIMIT_EXCEEDED"},{status:402})}try{await (0,d.er)(w.id,x,"serper",async()=>({success:!0}))}catch(e){return n.Z.json({success:!1,error:e.message,code:"RATE_LIMIT_EXCEEDED"},{status:429})}"analyze"===c?(console.log("Performing competitor analysis for user:",w.id,{query:r.query,location:r.location,analyzeTop:r.analyzeTop}),i=await l.vG.analyzeCompetitors(r.query,r.location,{analyzeTop:r.analyzeTop,includeContent:r.includeContent,includeMetrics:r.includeMetrics},w.id),a=3):(console.log("Performing SERP search for user:",w.id,{query:r.query,location:r.location,device:r.device}),i=await l.vG.search(r,w.id),a=1),await g(y,w.id,a,s);let R=Date.now()-t;if(console.log("Serper operation completed",{userId:w.id,action:c,query:r.query,responseTime:R,resultsCount:"analyze"===c?i.totalResults:i.organic?.length}),"analyze"===c)return n.Z.json({success:!0,data:{query:i.query,location:i.location,difficulty:i.difficulty,totalResults:i.totalResults,averageMetrics:{wordCount:i.averageWordCount,headingStructure:i.averageHeadings,keywordDensity:i.averageKeywordDensity},topCompetitors:i.topCompetitors.map(e=>({position:e.position,domain:e.domain,title:e.title,url:e.url,snippet:e.snippet,wordCount:e.wordCount,keywordDensity:e.keywordDensity,domainAuthority:e.domainAuthority,contentScore:e.contentScore})),contentGaps:i.contentGaps,searchFeatures:i.searchFeatures,commonKeywords:i.commonKeywords},usage:{creditsUsed:a,costUSD:s,responseTime:R}},{status:200,headers:{"X-Response-Time":R.toString(),"X-Difficulty":i.difficulty,"X-Credits-Used":a.toString()}});return n.Z.json({success:!0,data:{searchParameters:i.searchParameters,organic:i.organic,peopleAlsoAsk:i.peopleAlsoAsk,relatedSearches:i.relatedSearches,answerBox:i.answerBox,knowledgeGraph:i.knowledgeGraph,credits:i.credits},usage:{creditsUsed:a,costUSD:s,responseTime:R}},{status:200,headers:{"X-Response-Time":R.toString(),"X-Results-Count":i.organic?.length?.toString()||"0","X-Credits-Used":a.toString()}})}catch(r){if(console.error("Serper API error:",r),r instanceof u.jm)return n.Z.json({success:!1,error:"Invalid request data",code:"VALIDATION_ERROR",details:r.errors.map(e=>({field:e.path.join("."),message:e.message}))},{status:400});let e=r instanceof Error?r.message:"Internal server error",t=e.includes("serper")||e.includes("blocked");return n.Z.json({success:!1,error:e,code:t?"SERPER_API_ERROR":"INTERNAL_ERROR"},{status:t?502:500})}}async function w(e){try{let{searchParams:t}=new URL(e.url),r=t.get("action");if("health"===r){let e=await l.vG.healthCheck();return n.Z.json({success:!0,data:{status:e.status,latency:e.latency,timestamp:new Date().toISOString()}})}if("keywords"===r){let e=t.get("query"),r=t.get("location");if(!e)return n.Z.json({success:!1,error:"Query parameter is required"},{status:400});let s=(0,p.jq)(),{data:{user:i},error:a}=await s.auth.getUser();if(a||!i)return n.Z.json({success:!1,error:"Unauthorized"},{status:401});let o=await l.vG.getKeywordSuggestions(e,r||void 0,i.id);return n.Z.json({success:!0,data:{query:e,location:r,suggestions:o}})}if("paa"===r){let e=t.get("query"),r=t.get("location");if(!e)return n.Z.json({success:!1,error:"Query parameter is required"},{status:400});let s=(0,p.jq)(),{data:{user:i},error:a}=await s.auth.getUser();if(a||!i)return n.Z.json({success:!1,error:"Unauthorized"},{status:401});let o=await l.vG.getPeopleAlsoAsk(e,r||void 0,i.id);return n.Z.json({success:!0,data:{query:e,location:r,peopleAlsoAsk:o}})}return n.Z.json({success:!0,data:{service:"Serper SERP Analysis",version:"1.0.0",endpoints:{search:"POST /api/serper/search",analyze:"POST /api/serper/search?action=analyze",health:"GET /api/serper/search?action=health",keywords:"GET /api/serper/search?action=keywords&query=term",paa:"GET /api/serper/search?action=paa&query=term"}}})}catch(e){return console.error("GET /api/serper/search error:",e),n.Z.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function g(e,t,r,s){let{error:i}=await e.rpc("update_user_usage",{user_id:t,credits_used:r,cost_usd:s});i&&console.error("Failed to update user credits:",i)}let f=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/serper/search/route",pathname:"/api/serper/search",filename:"route",bundlePath:"app/api/serper/search/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/serper/search/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:q,staticGenerationAsyncStorage:x,serverHooks:R,headerHooks:T,staticGenerationBailout:S}=f,v="/api/serper/search/route";function M(){return(0,o.patchFetch)({serverHooks:R,staticGenerationAsyncStorage:x})}},6843:(e,t,r)=>{Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return s}});let s=r(8195).createClientModuleProxy},482:(e,t,r)=>{e.exports=r(399)},8195:(e,t,r)=>{e.exports=r(482).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},9279:(e,t,r)=>{r.d(t,{dZ:()=>o,er:()=>a,sy:()=>n});class s{async get(e){let t=this.store.get(e);return t?t.resetTime<Date.now()?(this.store.delete(e),null):t:null}async increment(e,t){let r=Date.now(),s=await this.get(e);if(s&&s.resetTime>r)return s.count++,this.store.set(e,s),s;let i={count:1,resetTime:r+t};return this.store.set(e,i),i}async reset(e){this.store.delete(e)}cleanup(){let e=Date.now();for(let[t,r]of this.store.entries())r.resetTime<e&&this.store.delete(t)}constructor(){this.store=new Map}}class i{constructor(e){this.subscriptionLimits={free:{daily:10,monthly:100,concurrent:1,apiCalls:50},pro:{daily:100,monthly:2e3,concurrent:3,apiCalls:1e3},enterprise:{daily:1e3,monthly:2e4,concurrent:10,apiCalls:1e4}},this.store=new s,this.defaultRule=e||{windowMs:9e5,maxRequests:100},setInterval(()=>this.store.cleanup(),3e5)}async checkLimit(e,t){let r=t||this.defaultRule,s=r.keyGenerator?.(e)||`rate_limit:${e}`,i=await this.store.increment(s,r.windowMs),a=Math.max(0,r.maxRequests-i.count),o=i.count<=r.maxRequests;return{allowed:o,remaining:a,resetTime:i.resetTime,totalHits:i.count,retryAfter:o?void 0:Math.ceil((i.resetTime-Date.now())/1e3)}}async checkSubscriptionLimit(e,t,r){let s;let i=(this.subscriptionLimits[t]||this.subscriptionLimits.free)[r];switch(r){case"daily":s=864e5;break;case"monthly":s=2592e6;break;case"concurrent":s=6e4;break;default:s=36e5}let a=`subscription_limit:${e}:${r}`,o=await this.store.increment(a,s),n=Math.max(0,i-o.count),c=o.count<=i;return{allowed:c,remaining:n,resetTime:o.resetTime,totalHits:o.count,retryAfter:c?void 0:Math.ceil((o.resetTime-Date.now())/1e3)}}async checkApiLimit(e,t,r){let s={groq:{free:{requests:5,windowMs:6e4},pro:{requests:30,windowMs:6e4},enterprise:{requests:100,windowMs:6e4}},serper:{free:{requests:10,windowMs:6e4},pro:{requests:50,windowMs:6e4},enterprise:{requests:200,windowMs:6e4}},openai:{free:{requests:3,windowMs:6e4},pro:{requests:20,windowMs:6e4},enterprise:{requests:60,windowMs:6e4}}},i=s[t]?.[r]||s[t]?.free;if(!i)throw Error(`No limits defined for API: ${t}`);let a=`api_limit:${e}:${t}`,o=await this.store.increment(a,i.windowMs),n=Math.max(0,i.requests-o.count),c=o.count<=i.requests;return{allowed:c,remaining:n,resetTime:o.resetTime,totalHits:o.count,retryAfter:c?void 0:Math.ceil((o.resetTime-Date.now())/1e3)}}async checkCostLimit(e,t,r){let s={free:5,pro:50,enterprise:500},i=s[t]||s.free,a=`cost_limit:${e}:monthly`,o=Date.now(),n=new Date(o);n.setDate(1),n.setHours(0,0,0,0);let c=`${a}:${n.getTime()}`,u=await this.store.get(c),l=(u?u.count/100:0)+r,d=l<=i;if(d){let e=new Date(n);e.setMonth(e.getMonth()+1),await this.store.increment(c,e.getTime()-o)}return{allowed:d,remainingBudget:Math.max(0,i-l),totalSpent:l}}async resetLimit(e,t){if(t){let r=`${t}:${e}`;await this.store.reset(r)}else for(let t of[`rate_limit:${e}`,`subscription_limit:${e}:*`,`api_limit:${e}:*`,`cost_limit:${e}:*`])await this.store.reset(t)}async getUsageStats(e){return{rateLimit:{},subscriptionLimits:{},apiLimits:{},costUsage:{}}}createMiddleware(e){return async t=>{let r=await this.checkLimit(t,e);if(!r.allowed){let e=Error("Rate limit exceeded");throw e.statusCode=429,e.retryAfter=r.retryAfter,e.resetTime=r.resetTime,e}return{"X-RateLimit-Limit":e?.maxRequests||this.defaultRule.maxRequests,"X-RateLimit-Remaining":r.remaining,"X-RateLimit-Reset":new Date(r.resetTime).toISOString()}}}}async function a(e,t,r,s){let a=new i,o=await a.checkApiLimit(e,r,t);if(!o.allowed){let e=Error(`${r} API rate limit exceeded`);throw e.statusCode=429,e.retryAfter=o.retryAfter,e.resetTime=o.resetTime,e.remaining=o.remaining,e}return s()}async function o(e,t,r,s){let a=new i,o=await a.checkSubscriptionLimit(e,t,r);if(!o.allowed){let e=Error(`${r} subscription limit exceeded`);throw e.statusCode=429,e.retryAfter=o.retryAfter,e.resetTime=o.resetTime,e.remaining=o.remaining,e}return s()}async function n(e,t,r,s){let a=new i,o=await a.checkCostLimit(e,t,r);if(!o.allowed){let e=Error(`Monthly cost limit exceeded. Remaining budget: $${o.remainingBudget.toFixed(2)}`);throw e.statusCode=402,e.remainingBudget=o.remainingBudget,e.totalSpent=o.totalSpent,e}return s()}new i({windowMs:9e5,maxRequests:100}),new i({windowMs:6e4,maxRequests:10}),new i({windowMs:9e5,maxRequests:5})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,280,252,493,734],()=>r(1352));module.exports=s})();
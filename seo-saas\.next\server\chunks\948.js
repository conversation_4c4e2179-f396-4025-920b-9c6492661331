exports.id=948,exports.ids=[948],exports.modules={8359:()=>{},3739:()=>{},8775:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},6315:(e,t,r)=>{Promise.resolve().then(r.bind(r,3750))},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>l});var n=r(2295),a=r(3729),s=r(9247),o=r(1453);let i=(0,s.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),l=a.forwardRef(({className:e,variant:t,size:r,loading:a,children:s,disabled:l,...c},d)=>(0,n.jsxs)("button",{className:(0,o.cn)(i({variant:t,size:r,className:e})),ref:d,disabled:l||a,...c,children:[a&&(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[n.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),n.jsx("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),s]}));l.displayName="Button"},3673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>i,SZ:()=>c,Zb:()=>o,aY:()=>d,ll:()=>l});var n=r(2295),a=r(3729),s=r(1453);let o=a.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));o.displayName="Card";let i=a.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",e),...t}));i.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>n.jsx("h3",{ref:r,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},r)=>n.jsx("p",{ref:r,className:(0,s.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,s.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:(0,s.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},3750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AuthProvider:()=>i,useAuth:()=>l,useAuthGuard:()=>d,useSubscription:()=>c});var n=r(2295),a=r(3729),s=r(8704);let o=(0,a.createContext)(void 0);function i({children:e}){let[t,r]=(0,a.useState)(null),[i,l]=(0,a.useState)(null),[c,d]=(0,a.useState)(null),[u,h]=(0,a.useState)(null),[m,f]=(0,a.useState)(!0),p=(0,s.R9)(),g=async e=>{try{let{data:t,error:r}=await p.from("profiles").select("*").eq("id",e).single();if(r)return console.error("Error fetching profile:",r),null;return t}catch(e){return console.error("Error fetching profile:",e),null}},x=async e=>{try{let{data:t,error:r}=await p.from("user_subscriptions").select("*").eq("user_id",e).single();if(r&&"PGRST116"!==r.code)return console.error("Error fetching subscription:",r),null;return t}catch(e){return console.error("Error fetching subscription:",e),null}};(0,a.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await p.auth.getSession();if(t){console.error("Error getting session:",t),f(!1);return}if(e?.user){l(e),r(e.user);let[t,n]=await Promise.all([g(e.user.id),x(e.user.id)]);d(t),h(n)}}catch(e){console.error("Error initializing auth:",e)}finally{f(!1)}})();let{data:{subscription:e}}=p.auth.onAuthStateChange(async(e,t)=>{if(console.log("Auth state changed:",e,t?.user?.id),l(t),r(t?.user??null),t?.user){let[e,r]=await Promise.all([g(t.user.id),x(t.user.id)]);d(e),h(r)}else d(null),h(null);f(!1)});return()=>{e.unsubscribe()}},[]);let y=async(e,t)=>{let{error:r}=await p.auth.signInWithPassword({email:e,password:t});if(r)throw Error(r.message)},w=async(e,t,r)=>{let{error:n}=await p.auth.signUp({email:e,password:t,options:{data:{full_name:r}}});if(n)throw Error(n.message)},v=async()=>{let{error:e}=await p.auth.signOut();if(e)throw Error(e.message)},b=async e=>{let{error:t}=await p.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/auth/reset-password`});if(t)throw Error(t.message)},S=async e=>{if(!t)throw Error("No user logged in");let{data:r,error:n}=await p.from("profiles").update({...e,updated_at:new Date().toISOString()}).eq("id",t.id).select().single();if(n)throw Error(n.message);d(r)},C=async()=>{t&&d(await g(t.id))},E=async()=>{t&&h(await x(t.id))};return n.jsx(o.Provider,{value:{user:t,session:i,profile:c,subscription:u,loading:m,signIn:y,signUp:w,signOut:v,resetPassword:b,updateProfile:S,refreshProfile:C,refreshSubscription:E},children:e})}function l(){let e=(0,a.useContext)(o);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(){let{subscription:e}=l(),t=e?.status==="active",r=e?.status==="trialing",n=e?.status==="past_due",a=e?.status==="cancelled";return{subscription:e,hasActiveSubscription:t,isOnTrial:r,isPastDue:n,isCancelled:a,canAccessFeature:r=>!!e&&("pro"===r?["pro","enterprise"].includes(e.plan_type)&&t:"enterprise"===r&&"enterprise"===e.plan_type&&t)}}function d(){let{user:e,loading:t}=l();return{isAuthenticated:!!e,isLoading:t,user:e}}},8704:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>s,R9:()=>o});var n=r(6300),a=r(7435);let s=(0,n.eI)("https://zqrmpanonghggoxdjirq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}),o=()=>(0,a.createClientComponentClient)();(0,n.eI)("https://zqrmpanonghggoxdjirq.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}})},1453:(e,t,r)=>{"use strict";r.d(t,{KG:()=>d,cn:()=>s,p6:()=>i,rl:()=>l,uf:()=>o,vQ:()=>c});var n=r(6815),a=r(9377);function s(...e){return(0,a.m6)((0,n.W)(e))}function o(e){return e.toLocaleString()}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function l(e){return`${e.toFixed(1)}%`}function c(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");return t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),Promise.resolve()}function d(e,t,r="text/plain"){let n=new Blob([e],{type:r}),a=URL.createObjectURL(n),s=document.createElement("a");s.href=a,s.download=t,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(a)}},1623:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>h,metadata:()=>u});var n=r(5036),a=r(5968),s=r.n(a);r(5023);var o=r(6843);let i=(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx`),{__esModule:l,$$typeof:c}=i;i.default;let d=(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#AuthProvider`);(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useAuth`),(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useSubscription`),(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useAuthGuard`);let u={title:"SEO Content Generator - Professional SEO Content Creation",description:"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.",keywords:["SEO","content generation","AI writing","keyword optimization","competitor analysis"],authors:[{name:"SEO Content Generator"}],openGraph:{title:"SEO Content Generator - Professional SEO Content Creation",description:"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"SEO Content Generator - Professional SEO Content Creation",description:"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results."},robots:{index:!0,follow:!0},viewport:{width:"device-width",initialScale:1}};function h({children:e}){return n.jsx("html",{lang:"en",className:s().variable,children:n.jsx("body",{className:"font-sans antialiased bg-gray-50 text-gray-900",children:n.jsx(d,{children:e})})})}},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var n=r(337);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,n.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};
// Application initialization and backend integration for SEO Pro
// This file handles the initialization of backend services and authentication

class AppInitializer {
    constructor() {
        this.isInitialized = false;
        this.modules = {
            config: false,
            supabase: false,
            api: false,
            auth: false
        };
        this.init();
    }

    async init() {
        try {
            console.log('Initializing SEO Pro application...');
            
            // Load external dependencies
            await this.loadExternalDependencies();
            
            // Initialize modules in order
            await this.initializeModules();
            
            // Set up global error handling
            this.setupErrorHandling();
            
            // Initialize authentication
            await this.initializeAuthentication();
            
            // Initialize page-specific features
            this.initializePageFeatures();
            
            this.isInitialized = true;
            console.log('SEO Pro application initialized successfully');
            
            // Dispatch initialization complete event
            document.dispatchEvent(new CustomEvent('app:initialized'));
            
        } catch (error) {
            console.error('Failed to initialize application:', error);
            this.showInitializationError(error);
        }
    }

    async loadExternalDependencies() {
        // Load Supabase from CDN if not already loaded
        if (typeof supabase === 'undefined') {
            await this.loadScript('https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2');
            console.log('Supabase library loaded');
        }
    }

    loadScript(src) {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = src;
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    async initializeModules() {
        // Wait for config to be available
        await this.waitForModule('CONFIG', 5000);
        this.modules.config = true;
        
        // Initialize Supabase client
        await this.initializeSupabase();
        this.modules.supabase = true;
        
        // Initialize API client
        await this.initializeAPI();
        this.modules.api = true;
        
        console.log('All modules initialized successfully');
    }

    async waitForModule(moduleName, timeout = 5000) {
        const startTime = Date.now();
        
        while (Date.now() - startTime < timeout) {
            if (window[moduleName]) {
                return window[moduleName];
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error(`Module ${moduleName} failed to load within ${timeout}ms`);
    }

    async initializeSupabase() {
        try {
            // Wait for SupabaseClient class to be available
            await this.waitForModule('SupabaseClient', 3000);
            
            // Create Supabase client instance
            if (!window.supabaseClient) {
                window.supabaseClient = new SupabaseClient();
                await this.waitForSupabaseInit();
            }
            
            console.log('Supabase client initialized');
        } catch (error) {
            console.error('Failed to initialize Supabase:', error);
            throw error;
        }
    }

    async waitForSupabaseInit() {
        const maxWait = 5000;
        const startTime = Date.now();
        
        while (Date.now() - startTime < maxWait) {
            if (window.supabaseClient && window.supabaseClient.initialized) {
                return;
            }
            await new Promise(resolve => setTimeout(resolve, 100));
        }
        
        throw new Error('Supabase client initialization timeout');
    }

    async initializeAPI() {
        try {
            // Wait for APIClient class to be available
            await this.waitForModule('APIClient', 3000);
            
            // Create API client instance
            if (!window.apiClient) {
                window.apiClient = new APIClient();
            }
            
            console.log('API client initialized');
        } catch (error) {
            console.error('Failed to initialize API client:', error);
            throw error;
        }
    }

    setupErrorHandling() {
        // Global error handler
        window.addEventListener('error', (event) => {
            console.error('Global error:', event.error);
            this.handleGlobalError(event.error);
        });

        // Unhandled promise rejection handler
        window.addEventListener('unhandledrejection', (event) => {
            console.error('Unhandled promise rejection:', event.reason);
            this.handleGlobalError(event.reason);
        });
    }

    handleGlobalError(error) {
        // Only show user-friendly errors in production
        if (!CONFIG.APP.DEBUG) {
            this.showUserError('An unexpected error occurred. Please try again.');
        }
    }

    async initializeAuthentication() {
        try {
            if (!window.supabaseClient) {
                throw new Error('Supabase client not available');
            }

            // Check if user is authenticated
            const user = window.supabaseClient.getCurrentUser();
            
            if (user) {
                console.log('User is authenticated:', user.email);
                await this.handleAuthenticatedUser(user);
            } else {
                console.log('User is not authenticated');
                this.handleUnauthenticatedUser();
            }
            
            this.modules.auth = true;
        } catch (error) {
            console.error('Authentication initialization error:', error);
            // Don't throw here as auth errors shouldn't prevent app initialization
        }
    }

    async handleAuthenticatedUser(user) {
        // Load user profile
        try {
            const profile = await window.supabaseClient.getUserProfile();
            if (profile) {
                window.currentUser = { ...user, profile };
                this.updateUIForAuthenticatedUser(profile);
            }
        } catch (error) {
            console.error('Failed to load user profile:', error);
        }
    }

    handleUnauthenticatedUser() {
        // Redirect to login if on protected pages
        const protectedPages = [
            'dashboard.html',
            'content-generator.html',
            'seo-analysis.html',
            'competitor-analysis.html',
            'projects.html',
            'analytics.html',
            'profile.html',
            'settings.html',
            'billing.html'
        ];

        const currentPage = window.location.pathname.split('/').pop();
        
        if (protectedPages.includes(currentPage)) {
            console.log('Redirecting to login - protected page accessed without authentication');
            window.location.href = '/login.html?redirect=' + encodeURIComponent(window.location.pathname);
        }
    }

    updateUIForAuthenticatedUser(profile) {
        // Update user avatar and name in navigation
        const userAvatar = document.querySelector('.user-avatar img');
        const userName = document.querySelector('.user-name');
        
        if (userAvatar && profile.avatar_url) {
            userAvatar.src = profile.avatar_url;
        }
        
        if (userName && profile.full_name) {
            userName.textContent = profile.full_name;
        }

        // Update subscription status
        this.updateSubscriptionUI(profile.subscription_tier || 'free');
    }

    updateSubscriptionUI(subscriptionTier) {
        // Update plan badges and usage indicators
        const planBadges = document.querySelectorAll('.plan-badge');
        planBadges.forEach(badge => {
            badge.textContent = CONFIG.PLANS[subscriptionTier.toUpperCase()]?.name || 'Free';
        });

        // Show/hide features based on plan
        this.toggleFeaturesByPlan(subscriptionTier);
    }

    toggleFeaturesByPlan(planId) {
        const features = ConfigUtils.getPlanFeatures(planId);
        
        // Hide/show export features
        const exportButtons = document.querySelectorAll('.export-btn');
        exportButtons.forEach(btn => {
            btn.style.display = features?.export_formats ? 'inline-flex' : 'none';
        });

        // Hide/show API access
        const apiSections = document.querySelectorAll('.api-section');
        apiSections.forEach(section => {
            section.style.display = features?.api_access ? 'block' : 'none';
        });

        // Update usage limits in UI
        this.updateUsageLimits(planId);
    }

    async updateUsageLimits(planId) {
        if (!window.supabaseClient) return;

        try {
            const usage = await window.supabaseClient.getUsageStats();
            const features = ConfigUtils.getPlanFeatures(planId);

            // Update content generation usage
            this.updateUsageDisplay('content-generations', usage.content_generations || 0, features?.content_generations);
            
            // Update SEO analysis usage
            this.updateUsageDisplay('seo-analyses', usage.seo_analyses || 0, features?.seo_analyses);
            
            // Update API calls usage
            this.updateUsageDisplay('api-calls', usage.api_calls || 0, features?.api_calls);
            
        } catch (error) {
            console.error('Failed to update usage limits:', error);
        }
    }

    updateUsageDisplay(resource, current, limit) {
        const usageElement = document.querySelector(`[data-usage="${resource}"]`);
        if (!usageElement) return;

        const limitText = limit === -1 ? 'Unlimited' : limit.toLocaleString();
        const currentText = current.toLocaleString();
        
        usageElement.innerHTML = `
            <span class="usage-current">${currentText}</span>
            <span class="usage-separator">/</span>
            <span class="usage-limit">${limitText}</span>
        `;

        // Update progress bar if exists
        const progressBar = usageElement.querySelector('.progress-fill');
        if (progressBar && limit !== -1) {
            const percentage = Math.min((current / limit) * 100, 100);
            progressBar.style.width = `${percentage}%`;
            
            // Add warning class if near limit
            if (percentage > 80) {
                progressBar.classList.add('warning');
            }
        }
    }

    initializePageFeatures() {
        const currentPage = window.location.pathname.split('/').pop();
        
        switch (currentPage) {
            case 'dashboard.html':
                this.initializeDashboard();
                break;
            case 'content-generator.html':
                this.initializeContentGenerator();
                break;
            case 'seo-analysis.html':
                this.initializeSEOAnalysis();
                break;
            case 'competitor-analysis.html':
                this.initializeCompetitorAnalysis();
                break;
            case 'projects.html':
                this.initializeProjects();
                break;
            case 'analytics.html':
                this.initializeAnalytics();
                break;
        }
    }

    initializeDashboard() {
        // Load dashboard data
        this.loadDashboardData();
    }

    initializeContentGenerator() {
        // Content generator is initialized by its own module
        console.log('Content generator page detected');
    }

    initializeSEOAnalysis() {
        // Initialize SEO analysis functionality
        console.log('SEO analysis page detected');
    }

    initializeCompetitorAnalysis() {
        // Initialize competitor analysis functionality
        console.log('Competitor analysis page detected');
    }

    initializeProjects() {
        // Initialize projects functionality
        console.log('Projects page detected');
    }

    initializeAnalytics() {
        // Initialize analytics functionality
        console.log('Analytics page detected');
    }

    async loadDashboardData() {
        if (!window.supabaseClient || !window.supabaseClient.isAuthenticated()) {
            return;
        }

        try {
            // Load recent content generations
            const recentContent = await window.supabaseClient.getContentGenerations(5);
            this.updateRecentContentDisplay(recentContent);

            // Load recent SEO analyses
            const recentAnalyses = await window.supabaseClient.getSeoAnalyses(5);
            this.updateRecentAnalysesDisplay(recentAnalyses);

            // Load usage statistics
            const usage = await window.supabaseClient.getUsageStats();
            this.updateDashboardUsage(usage);

        } catch (error) {
            console.error('Failed to load dashboard data:', error);
        }
    }

    updateRecentContentDisplay(content) {
        const container = document.getElementById('recent-content');
        if (!container) return;

        if (content.length === 0) {
            container.innerHTML = '<p class="empty-state">No content generated yet.</p>';
            return;
        }

        container.innerHTML = content.map(item => `
            <div class="recent-item">
                <h4>${item.title}</h4>
                <p>${item.content_type} • ${item.word_count} words</p>
                <span class="date">${new Date(item.created_at).toLocaleDateString()}</span>
            </div>
        `).join('');
    }

    updateRecentAnalysesDisplay(analyses) {
        const container = document.getElementById('recent-analyses');
        if (!container) return;

        if (analyses.length === 0) {
            container.innerHTML = '<p class="empty-state">No analyses performed yet.</p>';
            return;
        }

        container.innerHTML = analyses.map(item => `
            <div class="recent-item">
                <h4>${item.url || item.title}</h4>
                <p>SEO Score: ${item.seo_score || 'N/A'}</p>
                <span class="date">${new Date(item.created_at).toLocaleDateString()}</span>
            </div>
        `).join('');
    }

    updateDashboardUsage(usage) {
        // Update usage cards on dashboard
        Object.keys(usage).forEach(resource => {
            const element = document.querySelector(`[data-dashboard-usage="${resource}"]`);
            if (element) {
                element.textContent = usage[resource].toLocaleString();
            }
        });
    }

    showInitializationError(error) {
        const errorContainer = document.createElement('div');
        errorContainer.className = 'initialization-error';
        errorContainer.innerHTML = `
            <div class="error-content">
                <h3>Application Initialization Failed</h3>
                <p>There was a problem starting the application. Please refresh the page and try again.</p>
                <button onclick="window.location.reload()" class="btn btn-primary">Refresh Page</button>
            </div>
        `;
        
        document.body.insertBefore(errorContainer, document.body.firstChild);
    }

    showUserError(message) {
        // This would typically show a toast notification
        console.error('User error:', message);
    }

    // Public methods for other modules to check initialization status
    isModuleReady(moduleName) {
        return this.modules[moduleName] || false;
    }

    waitForInitialization() {
        return new Promise((resolve) => {
            if (this.isInitialized) {
                resolve();
            } else {
                document.addEventListener('app:initialized', resolve, { once: true });
            }
        });
    }
}

// Initialize the application
let appInitializer;

document.addEventListener('DOMContentLoaded', function() {
    appInitializer = new AppInitializer();
    window.appInitializer = appInitializer;
});

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AppInitializer;
}

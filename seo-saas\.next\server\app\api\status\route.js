"use strict";(()=>{var e={};e.id=492,e.ids=[492],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},2361:e=>{e.exports=require("events")},7147:e=>{e.exports=require("fs")},3685:e=>{e.exports=require("http")},5687:e=>{e.exports=require("https")},1808:e=>{e.exports=require("net")},7561:e=>{e.exports=require("node:fs")},4492:e=>{e.exports=require("node:stream")},1017:e=>{e.exports=require("path")},5477:e=>{e.exports=require("punycode")},2781:e=>{e.exports=require("stream")},4404:e=>{e.exports=require("tls")},7310:e=>{e.exports=require("url")},3837:e=>{e.exports=require("util")},1267:e=>{e.exports=require("worker_threads")},9796:e=>{e.exports=require("zlib")},3930:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>x,originalPathname:()=>S,patchFetch:()=>C,requestAsyncStorage:()=>w,routeModule:()=>v,serverHooks:()=>q,staticGenerationAsyncStorage:()=>f,staticGenerationBailout:()=>b});var a={};r.r(a),r.d(a,{GET:()=>m,POST:()=>k});var s=r(5419),n=r(9108),o=r(9678),c=r(8070),l=r(2982),i=r(7734),u=r(8280),h=r(2045),p=r(917);let d=Date.now();async function m(e){let{searchParams:t}=new URL(e.url),r="true"===t.get("detailed"),a=t.get("service");try{if(a)return await g(a);let e=await y(r);return c.Z.json({success:!0,data:e},{status:"healthy"===e.overall?200:503,headers:{"Cache-Control":"no-cache, no-store, must-revalidate","X-Health-Check":e.overall,"X-Timestamp":e.timestamp}})}catch(e){return console.error("Health check error:",e),c.Z.json({success:!1,error:"Health check failed",timestamp:new Date().toISOString()},{status:500})}}async function y(e=!1){let t=[],r=new Date().toISOString(),a=Date.now()-d;try{let a=Date.now(),s=await l.TU.healthCheck(),n=Date.now()-a;t.push({name:"groq",status:"healthy"===s.status?"healthy":"unhealthy",latency:n,details:e?{model:s.model,apiLatency:s.latency,availableModels:Object.keys(l.TU.getAvailableModels()).length}:{},lastCheck:r})}catch(a){t.push({name:"groq",status:"unhealthy",latency:-1,details:e?{error:a instanceof Error?a.message:"Unknown error"}:{},lastCheck:r})}try{let a=Date.now(),s=await i.vG.healthCheck(),n=Date.now()-a;t.push({name:"serper",status:"healthy"===s.status?"healthy":"unhealthy",latency:n,details:e?{apiLatency:s.latency}:{},lastCheck:r})}catch(a){t.push({name:"serper",status:"unhealthy",latency:-1,details:e?{error:a instanceof Error?a.message:"Unknown error"}:{},lastCheck:r})}try{let a=Date.now(),s=(0,h.jq)(),{data:n,error:o}=await s.from("profiles").select("count").limit(1),c=Date.now()-a;t.push({name:"supabase",status:o?"unhealthy":"healthy",latency:c,details:e?{url:p.vc.supabase.url,connected:!o,error:o?.message}:{},lastCheck:r})}catch(a){t.push({name:"supabase",status:"unhealthy",latency:-1,details:e?{error:a instanceof Error?a.message:"Unknown error"}:{},lastCheck:r})}if(e){let e=u.WH.getServiceHealth();t.push({name:"error-handler",status:"healthy",latency:0,details:{circuitBreakers:e,cacheSize:Object.keys(e).length},lastCheck:r})}let s=t.filter(e=>"healthy"===e.status).length,n=t.length;return{overall:s===n?"healthy":s>=.5*n?"degraded":"unhealthy",services:t,timestamp:r,version:"1.0.0",uptime:a,environment:p.vc.app.environment}}async function g(e){let t=new Date().toISOString();try{let r;switch(e.toLowerCase()){case"groq":let a=Date.now(),s=await l.TU.healthCheck(),n=Date.now()-a;r={name:"groq",status:"healthy"===s.status?"healthy":"unhealthy",latency:n,details:{model:s.model,apiLatency:s.latency,availableModels:l.TU.getAvailableModels(),config:{baseUrl:p.vc.apis.groq.baseUrl,model:p.vc.apis.groq.model,rateLimitPerMinute:p.vc.apis.groq.rateLimitPerMinute}},lastCheck:t};break;case"serper":let o=Date.now(),d=await i.vG.healthCheck(),m=Date.now()-o;r={name:"serper",status:"healthy"===d.status?"healthy":"unhealthy",latency:m,details:{apiLatency:d.latency,config:{baseUrl:p.vc.apis.serper.baseUrl,rateLimitPerMinute:p.vc.apis.serper.rateLimitPerMinute}},lastCheck:t};break;case"supabase":let y=Date.now(),g=(0,h.jq)(),{data:k,error:v}=await g.from("profiles").select("count").limit(1),w=Date.now()-y;r={name:"supabase",status:v?"unhealthy":"healthy",latency:w,details:{url:p.vc.supabase.url,connected:!v,error:v?.message,version:"PostgreSQL via Supabase"},lastCheck:t};break;case"error-handler":let f=u.WH.getServiceHealth();r={name:"error-handler",status:"healthy",latency:0,details:{circuitBreakers:f,cacheSize:Object.keys(f).length,config:{circuitBreakerThreshold:5,circuitBreakerTimeout:6e4,resetTimeout:3e5}},lastCheck:t};break;default:return c.Z.json({success:!1,error:`Unknown service: ${e}`,availableServices:["groq","serper","supabase","error-handler"]},{status:404})}return c.Z.json({success:!0,data:r},{status:"healthy"===r.status?200:503,headers:{"X-Service-Status":r.status,"X-Service-Latency":r.latency.toString()}})}catch(r){return console.error(`Service status check failed for ${e}:`,r),c.Z.json({success:!1,error:`Failed to check ${e} status`,details:r instanceof Error?r.message:"Unknown error",timestamp:t},{status:500})}}async function k(e){try{let{searchParams:t}=new URL(e.url),r=t.get("action"),a=t.get("service");if(e.headers.get("x-admin-key")!==process.env.ADMIN_KEY)return c.Z.json({success:!1,error:"Unauthorized"},{status:401});switch(r){case"reset-circuit-breakers":if(a)return u.WH,c.Z.json({success:!0,message:`Circuit breaker reset requested for ${a}`,timestamp:new Date().toISOString()});return c.Z.json({success:!0,message:"All circuit breakers reset requested",timestamp:new Date().toISOString()});case"clear-cache":return u.WH.cleanupCache(),c.Z.json({success:!0,message:"Cache cleared successfully",timestamp:new Date().toISOString()});case"force-health-check":let s=await y(!0);return c.Z.json({success:!0,message:"Forced health check completed",data:s});default:return c.Z.json({success:!1,error:"Unknown action",availableActions:["reset-circuit-breakers","clear-cache","force-health-check"]},{status:400})}}catch(e){return console.error("POST /api/status error:",e),c.Z.json({success:!1,error:"Internal server error"},{status:500})}}let v=new s.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/status/route",pathname:"/api/status",filename:"route",bundlePath:"app/api/status/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/status/route.ts",nextConfigOutput:"",userland:a}),{requestAsyncStorage:w,staticGenerationAsyncStorage:f,serverHooks:q,headerHooks:x,staticGenerationBailout:b}=v,S="/api/status/route";function C(){return(0,o.patchFetch)({serverHooks:q,staticGenerationAsyncStorage:f})}}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[638,280,244,493,982,734],()=>r(3930));module.exports=a})();
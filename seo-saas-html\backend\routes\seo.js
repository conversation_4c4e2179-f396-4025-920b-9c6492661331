const express = require('express');
const router = express.Router();
const { verifyToken, checkUsageLimit } = require('../middleware/auth');
const MetaTagGenerator = require('../services/metaTagGenerator');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Meta tag generation endpoint
router.post('/meta-tags', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const { 
      content, 
      keyword, 
      contentType = 'article',
      industry = 'general',
      siteName = '',
      siteUrl = '',
      authorName = '',
      organizationName = '',
      language = 'en',
      region = 'US'
    } = req.body;

    if (!content) {
      return res.status(400).json({ error: 'Content is required' });
    }

    if (!keyword) {
      return res.status(400).json({ error: 'Target keyword is required' });
    }

    // Create meta tag generator instance
    const generator = new MetaTagGenerator(content, keyword, {
      contentType,
      industry,
      siteName,
      siteUrl,
      authorName,
      organizationName,
      language,
      region
    });

    // Generate all meta tags
    const metaTags = generator.generateAllMetaTags();

    // Save meta tag generation to database
    const { data: savedGeneration } = await supabase
      .from('seo_generations')
      .insert({
        user_id: req.user.id,
        generation_type: 'meta_tags',
        keyword,
        content_type: contentType,
        industry,
        results: metaTags,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    // Update usage tracking
    await updateUsageTracking(req.user.id, 'generations');

    res.json({
      success: true,
      keyword,
      contentType,
      industry,
      metaTags,
      generationId: savedGeneration?.id
    });

  } catch (error) {
    console.error('Meta tag generation error:', error);
    res.status(500).json({ error: 'Failed to generate meta tags' });
  }
});

// Bulk meta tag generation endpoint
router.post('/meta-tags/bulk', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const { requests } = req.body;

    if (!Array.isArray(requests) || requests.length === 0) {
      return res.status(400).json({ error: 'Requests array is required' });
    }

    if (requests.length > 10) {
      return res.status(400).json({ error: 'Maximum 10 requests allowed per bulk operation' });
    }

    const results = [];
    
    for (const request of requests) {
      const { content, keyword, options = {} } = request;
      
      if (!content || !keyword) {
        results.push({
          keyword: keyword || 'unknown',
          error: 'Content and keyword are required'
        });
        continue;
      }

      try {
        const generator = new MetaTagGenerator(content, keyword, options);
        const metaTags = generator.generateAllMetaTags();
        
        results.push({
          keyword,
          success: true,
          metaTags
        });
      } catch (error) {
        results.push({
          keyword,
          error: error.message
        });
      }
    }

    // Save bulk generation to database
    const { data: savedGeneration } = await supabase
      .from('seo_generations')
      .insert({
        user_id: req.user.id,
        generation_type: 'meta_tags_bulk',
        results: { bulkResults: results },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    // Update usage tracking (count as number of successful generations)
    const successfulCount = results.filter(r => r.success).length;
    for (let i = 0; i < successfulCount; i++) {
      await updateUsageTracking(req.user.id, 'generations');
    }

    res.json({
      success: true,
      totalRequests: requests.length,
      successfulGenerations: successfulCount,
      results,
      generationId: savedGeneration?.id
    });

  } catch (error) {
    console.error('Bulk meta tag generation error:', error);
    res.status(500).json({ error: 'Failed to generate bulk meta tags' });
  }
});

// Title optimization endpoint
router.post('/title-optimize', verifyToken, async (req, res) => {
  try {
    const { content, keyword, contentType = 'article', industry = 'general' } = req.body;

    if (!content || !keyword) {
      return res.status(400).json({ error: 'Content and keyword are required' });
    }

    const generator = new MetaTagGenerator(content, keyword, {
      contentType,
      industry
    });

    const titleData = generator.generateTitle();

    res.json({
      success: true,
      keyword,
      title: titleData
    });

  } catch (error) {
    console.error('Title optimization error:', error);
    res.status(500).json({ error: 'Failed to optimize title' });
  }
});

// Meta description optimization endpoint
router.post('/description-optimize', verifyToken, async (req, res) => {
  try {
    const { content, keyword, contentType = 'article' } = req.body;

    if (!content || !keyword) {
      return res.status(400).json({ error: 'Content and keyword are required' });
    }

    const generator = new MetaTagGenerator(content, keyword, {
      contentType
    });

    const descriptionData = generator.generateMetaDescription();

    res.json({
      success: true,
      keyword,
      description: descriptionData
    });

  } catch (error) {
    console.error('Description optimization error:', error);
    res.status(500).json({ error: 'Failed to optimize description' });
  }
});

// Schema markup generation endpoint
router.post('/schema-markup', verifyToken, async (req, res) => {
  try {
    const { 
      content, 
      keyword, 
      contentType = 'article',
      siteName = '',
      siteUrl = '',
      authorName = '',
      organizationName = ''
    } = req.body;

    if (!content || !keyword) {
      return res.status(400).json({ error: 'Content and keyword are required' });
    }

    const generator = new MetaTagGenerator(content, keyword, {
      contentType,
      siteName,
      siteUrl,
      authorName,
      organizationName
    });

    const schemaMarkup = generator.generateSchemaMarkup();

    res.json({
      success: true,
      keyword,
      contentType,
      schema: schemaMarkup
    });

  } catch (error) {
    console.error('Schema markup generation error:', error);
    res.status(500).json({ error: 'Failed to generate schema markup' });
  }
});

// Helper function to update usage tracking
async function updateUsageTracking(userId, type) {
  const today = new Date().toISOString().split('T')[0];
  
  const { data: existing } = await supabase
    .from('usage_tracking')
    .select('*')
    .eq('user_id', userId)
    .eq('date', today)
    .single();

  if (existing) {
    await supabase
      .from('usage_tracking')
      .update({ 
        [type]: existing[type] + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', existing.id);
  } else {
    await supabase
      .from('usage_tracking')
      .insert({
        user_id: userId,
        date: today,
        [type]: 1,
        created_at: new Date().toISOString()
      });
  }
}

module.exports = router;
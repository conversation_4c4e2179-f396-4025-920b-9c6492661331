const express = require('express');
const router = express.Router();
const axios = require('axios');
const { verifyToken, checkUsageLimit } = require('../middleware/auth');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Supported languages configuration
const SUPPORTED_LANGUAGES = {
  en: {
    name: 'English',
    nativeName: 'English',
    code: 'en',
    region: 'US',
    direction: 'ltr',
    seoGuidelines: {
      avgSentenceLength: 20,
      readabilityTarget: 60,
      keywordDensity: { min: 1, max: 3 },
      titleLength: { min: 30, max: 60 },
      descriptionLength: { min: 120, max: 160 }
    },
    contentPatterns: {
      questionWords: ['what', 'how', 'why', 'when', 'where', 'who'],
      transitionWords: ['however', 'moreover', 'furthermore', 'therefore', 'meanwhile'],
      powerWords: ['ultimate', 'complete', 'essential', 'proven', 'expert']
    }
  },
  es: {
    name: 'Spanish',
    nativeName: 'Español',
    code: 'es',
    region: 'ES',
    direction: 'ltr',
    seoGuidelines: {
      avgSentenceLength: 22,
      readabilityTarget: 55,
      keywordDensity: { min: 1, max: 3 },
      titleLength: { min: 30, max: 60 },
      descriptionLength: { min: 120, max: 160 }
    },
    contentPatterns: {
      questionWords: ['qué', 'cómo', 'por qué', 'cuándo', 'dónde', 'quién'],
      transitionWords: ['sin embargo', 'además', 'por lo tanto', 'mientras tanto'],
      powerWords: ['definitivo', 'completo', 'esencial', 'probado', 'experto']
    }
  },
  fr: {
    name: 'French',
    nativeName: 'Français',
    code: 'fr',
    region: 'FR',
    direction: 'ltr',
    seoGuidelines: {
      avgSentenceLength: 25,
      readabilityTarget: 55,
      keywordDensity: { min: 1, max: 3 },
      titleLength: { min: 30, max: 60 },
      descriptionLength: { min: 120, max: 160 }
    },
    contentPatterns: {
      questionWords: ['que', 'comment', 'pourquoi', 'quand', 'où', 'qui'],
      transitionWords: ['cependant', 'de plus', 'par conséquent', 'pendant ce temps'],
      powerWords: ['ultime', 'complet', 'essentiel', 'prouvé', 'expert']
    }
  },
  de: {
    name: 'German',
    nativeName: 'Deutsch',
    code: 'de',
    region: 'DE',
    direction: 'ltr',
    seoGuidelines: {
      avgSentenceLength: 18,
      readabilityTarget: 50,
      keywordDensity: { min: 1, max: 3 },
      titleLength: { min: 30, max: 60 },
      descriptionLength: { min: 120, max: 160 }
    },
    contentPatterns: {
      questionWords: ['was', 'wie', 'warum', 'wann', 'wo', 'wer'],
      transitionWords: ['jedoch', 'außerdem', 'daher', 'inzwischen'],
      powerWords: ['ultimativ', 'komplett', 'wesentlich', 'bewährt', 'experte']
    }
  },
  it: {
    name: 'Italian',
    nativeName: 'Italiano',
    code: 'it',
    region: 'IT',
    direction: 'ltr',
    seoGuidelines: {
      avgSentenceLength: 23,
      readabilityTarget: 55,
      keywordDensity: { min: 1, max: 3 },
      titleLength: { min: 30, max: 60 },
      descriptionLength: { min: 120, max: 160 }
    },
    contentPatterns: {
      questionWords: ['cosa', 'come', 'perché', 'quando', 'dove', 'chi'],
      transitionWords: ['tuttavia', 'inoltre', 'pertanto', 'nel frattempo'],
      powerWords: ['definitivo', 'completo', 'essenziale', 'provato', 'esperto']
    }
  },
  pt: {
    name: 'Portuguese',
    nativeName: 'Português',
    code: 'pt',
    region: 'PT',
    direction: 'ltr',
    seoGuidelines: {
      avgSentenceLength: 22,
      readabilityTarget: 55,
      keywordDensity: { min: 1, max: 3 },
      titleLength: { min: 30, max: 60 },
      descriptionLength: { min: 120, max: 160 }
    },
    contentPatterns: {
      questionWords: ['o que', 'como', 'por que', 'quando', 'onde', 'quem'],
      transitionWords: ['no entanto', 'além disso', 'portanto', 'enquanto isso'],
      powerWords: ['definitivo', 'completo', 'essencial', 'comprovado', 'especialista']
    }
  }
};

// Get all supported languages
router.get('/', (req, res) => {
  try {
    const languages = Object.values(SUPPORTED_LANGUAGES).map(lang => ({
      code: lang.code,
      name: lang.name,
      nativeName: lang.nativeName,
      region: lang.region,
      direction: lang.direction
    }));

    res.json({
      success: true,
      languages
    });
  } catch (error) {
    console.error('Get languages error:', error);
    res.status(500).json({ error: 'Failed to retrieve languages' });
  }
});

// Get specific language details
router.get('/:code', (req, res) => {
  try {
    const { code } = req.params;
    const language = SUPPORTED_LANGUAGES[code];

    if (!language) {
      return res.status(404).json({ error: 'Language not supported' });
    }

    res.json({
      success: true,
      language
    });
  } catch (error) {
    console.error('Get language error:', error);
    res.status(500).json({ error: 'Failed to retrieve language details' });
  }
});

// Generate content with language-specific optimization
router.post('/generate-content', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const {
      content,
      keyword,
      language = 'en',
      contentType = 'article',
      tone = 'professional',
      wordCount = 1000,
      industry = 'general'
    } = req.body;

    if (!content || !keyword) {
      return res.status(400).json({ error: 'Content and keyword are required' });
    }

    const languageConfig = SUPPORTED_LANGUAGES[language];
    if (!languageConfig) {
      return res.status(400).json({ error: 'Language not supported' });
    }

    // Build language-specific prompt
    const languagePrompt = buildLanguagePrompt(
      content, 
      keyword, 
      languageConfig, 
      { contentType, tone, wordCount, industry }
    );

    // Call content generation API (using existing Groq integration)
    const groqResponse = await axios.post(
      'https://api.groq.com/openai/v1/chat/completions',
      {
        model: process.env.GROQ_MODEL || 'llama-3.1-70b-versatile',
        messages: [
          {
            role: 'system',
            content: languagePrompt.systemPrompt
          },
          {
            role: 'user',
            content: languagePrompt.userPrompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.7
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const generatedContent = groqResponse.data.choices[0].message.content;

    // Save generation to database
    const { data: savedGeneration } = await supabase
      .from('content_generations')
      .insert({
        user_id: req.user.id,
        keyword,
        language,
        content_type: contentType,
        content: generatedContent,
        metadata: {
          originalContent: content,
          language: languageConfig,
          settings: { tone, wordCount, industry }
        },
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    // Update usage tracking
    await updateUsageTracking(req.user.id, 'content_generations');

    res.json({
      success: true,
      content: generatedContent,
      language: languageConfig,
      generationId: savedGeneration?.id,
      metadata: {
        wordCount: generatedContent.split(/\s+/).length,
        language: language,
        seoOptimized: true
      }
    });

  } catch (error) {
    console.error('Language content generation error:', error);
    res.status(500).json({ error: 'Failed to generate language-specific content' });
  }
});

// Translate content between languages
router.post('/translate', verifyToken, checkUsageLimit, async (req, res) => {
  try {
    const {
      content,
      fromLanguage,
      toLanguage,
      keyword,
      maintainSEO = true
    } = req.body;

    if (!content || !fromLanguage || !toLanguage) {
      return res.status(400).json({ 
        error: 'Content, source language, and target language are required' 
      });
    }

    const fromLangConfig = SUPPORTED_LANGUAGES[fromLanguage];
    const toLangConfig = SUPPORTED_LANGUAGES[toLanguage];

    if (!fromLangConfig || !toLangConfig) {
      return res.status(400).json({ error: 'Language not supported' });
    }

    // Build translation prompt with SEO preservation
    const translationPrompt = buildTranslationPrompt(
      content,
      fromLangConfig,
      toLangConfig,
      keyword,
      maintainSEO
    );

    // Call Groq API for translation
    const groqResponse = await axios.post(
      'https://api.groq.com/openai/v1/chat/completions',
      {
        model: process.env.GROQ_MODEL || 'llama-3.1-70b-versatile',
        messages: [
          {
            role: 'system',
            content: translationPrompt.systemPrompt
          },
          {
            role: 'user',
            content: translationPrompt.userPrompt
          }
        ],
        max_tokens: 4000,
        temperature: 0.3 // Lower temperature for more accurate translation
      },
      {
        headers: {
          'Authorization': `Bearer ${process.env.GROQ_API_KEY}`,
          'Content-Type': 'application/json'
        }
      }
    );

    const translatedContent = groqResponse.data.choices[0].message.content;

    // Save translation
    const { data: savedTranslation } = await supabase
      .from('translations')
      .insert({
        user_id: req.user.id,
        original_content: content,
        translated_content: translatedContent,
        from_language: fromLanguage,
        to_language: toLanguage,
        keyword,
        maintain_seo: maintainSEO,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    // Update usage tracking
    await updateUsageTracking(req.user.id, 'translations');

    res.json({
      success: true,
      translatedContent,
      fromLanguage: fromLangConfig,
      toLanguage: toLangConfig,
      translationId: savedTranslation?.id,
      metadata: {
        originalWordCount: content.split(/\s+/).length,
        translatedWordCount: translatedContent.split(/\s+/).length,
        maintainedSEO: maintainSEO
      }
    });

  } catch (error) {
    console.error('Translation error:', error);
    res.status(500).json({ error: 'Failed to translate content' });
  }
});

// Get language-specific SEO guidelines
router.get('/:code/seo-guidelines', (req, res) => {
  try {
    const { code } = req.params;
    const language = SUPPORTED_LANGUAGES[code];

    if (!language) {
      return res.status(404).json({ error: 'Language not supported' });
    }

    res.json({
      success: true,
      language: {
        code: language.code,
        name: language.name
      },
      seoGuidelines: language.seoGuidelines,
      contentPatterns: language.contentPatterns
    });
  } catch (error) {
    console.error('Get SEO guidelines error:', error);
    res.status(500).json({ error: 'Failed to retrieve SEO guidelines' });
  }
});

// Helper function to build language-specific prompts
function buildLanguagePrompt(content, keyword, languageConfig, options) {
  const { contentType, tone, wordCount, industry } = options;

  const systemPrompt = `
You are an expert ${languageConfig.name} content writer and SEO specialist. 
You must write content in ${languageConfig.name} (${languageConfig.nativeName}) that follows these specific guidelines:

LANGUAGE-SPECIFIC SEO GUIDELINES:
- Average sentence length: ${languageConfig.seoGuidelines.avgSentenceLength} words
- Readability target: ${languageConfig.seoGuidelines.readabilityTarget}+ score
- Keyword density: ${languageConfig.seoGuidelines.keywordDensity.min}-${languageConfig.seoGuidelines.keywordDensity.max}%
- Use ${languageConfig.name} grammar and syntax naturally
- Include relevant transition words: ${languageConfig.contentPatterns.transitionWords.slice(0, 3).join(', ')}
- Include power words when appropriate: ${languageConfig.contentPatterns.powerWords.slice(0, 3).join(', ')}

CONTENT REQUIREMENTS:
- Language: ${languageConfig.name}
- Content Type: ${contentType}
- Tone: ${tone}
- Target Word Count: ${wordCount} words
- Industry: ${industry}
- Primary Keyword: "${keyword}"

Write naturally in ${languageConfig.name} while maintaining SEO best practices for this language.
`;

  const userPrompt = `
Please create SEO-optimized content in ${languageConfig.name} based on this source material:

Source Content: ${content}

Target Keyword: ${keyword}

Requirements:
1. Write entirely in ${languageConfig.name}
2. Optimize for the keyword "${keyword}" with ${languageConfig.seoGuidelines.keywordDensity.min}-${languageConfig.seoGuidelines.keywordDensity.max}% density
3. Use natural ${languageConfig.name} language patterns
4. Include proper heading structure (H1, H2, H3)
5. Write approximately ${wordCount} words
6. Maintain ${tone} tone throughout
7. Focus on ${industry} industry context

Create engaging, well-structured content that reads naturally in ${languageConfig.name} while being optimized for search engines.
`;

  return { systemPrompt, userPrompt };
}

// Helper function to build translation prompts
function buildTranslationPrompt(content, fromLang, toLang, keyword, maintainSEO) {
  const systemPrompt = `
You are an expert translator specializing in SEO content translation from ${fromLang.name} to ${toLang.name}.

TRANSLATION REQUIREMENTS:
- Translate from ${fromLang.name} to ${toLang.name}
- Maintain natural ${toLang.name} language flow
- Preserve SEO optimization: ${maintainSEO ? 'YES' : 'NO'}
- Adapt cultural context appropriately
- Keep similar keyword density for: "${keyword}"

LANGUAGE-SPECIFIC GUIDELINES for ${toLang.name}:
- Average sentence length: ${toLang.seoGuidelines.avgSentenceLength} words
- Use appropriate transition words: ${toLang.contentPatterns.transitionWords.slice(0, 3).join(', ')}
- Maintain readability target: ${toLang.seoGuidelines.readabilityTarget}+

Translate accurately while optimizing for ${toLang.name} SEO best practices.
`;

  const userPrompt = `
Translate this content from ${fromLang.name} to ${toLang.name}:

${content}

Key requirements:
1. Maintain the same structure and headings
2. Keep keyword "${keyword}" appropriately integrated
3. Ensure natural ${toLang.name} language flow
4. Preserve SEO elements if maintainSEO is true
5. Adapt cultural references appropriately
6. Maintain professional tone

Provide only the translated content without additional commentary.
`;

  return { systemPrompt, userPrompt };
}

// Helper function to update usage tracking
async function updateUsageTracking(userId, type) {
  const today = new Date().toISOString().split('T')[0];
  
  const { data: existing } = await supabase
    .from('usage_tracking')
    .select('*')
    .eq('user_id', userId)
    .eq('date', today)
    .single();

  if (existing) {
    await supabase
      .from('usage_tracking')
      .update({ 
        [type]: existing[type] + 1,
        updated_at: new Date().toISOString()
      })
      .eq('id', existing.id);
  } else {
    await supabase
      .from('usage_tracking')
      .insert({
        user_id: userId,
        date: today,
        [type]: 1,
        created_at: new Date().toISOString()
      });
  }
}

module.exports = router;
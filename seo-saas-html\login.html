<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - SEO Pro</title>
    <meta name="description" content="Sign in to your SEO Pro account to access AI-powered content generation tools.">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    <link href="css/professional-fixes.css" rel="stylesheet">
</head>
<body class="bg-secondary min-h-screen">
    <!-- Simple Header -->
    <header class="py-6">
        <div class="container">
            <a href="index.html" class="logo text-3xl">
                <span class="logo-text">SEO Pro</span>
            </a>
        </div>
    </header>
    
    <!-- Login Form Section -->
    <section class="py-12">
        <div class="container">
            <div class="max-w-md mx-auto">
                <div class="card animate-fadeInUp">
                    <div class="card-content p-8">
                        <h1 class="text-3xl font-bold text-center mb-2">Welcome Back</h1>
                        <p class="text-gray-600 text-center mb-8">Sign in to your account to continue</p>
                        
                        <form action="#" method="POST" onsubmit="handleLogin(event)">
                            <div class="form-group">
                                <label for="email" class="form-label">Email Address</label>
                                <input 
                                    type="email" 
                                    id="email" 
                                    name="email" 
                                    class="form-input" 
                                    placeholder="<EMAIL>"
                                    required
                                    autocomplete="email"
                                >
                            </div>
                            
                            <div class="form-group">
                                <div class="flex justify-between items-center mb-2">
                                    <label for="password" class="form-label">Password</label>
                                    <a href="forgot-password.html" class="text-sm text-primary hover:text-secondary">
                                        Forgot password?
                                    </a>
                                </div>
                                <input 
                                    type="password" 
                                    id="password" 
                                    name="password" 
                                    class="form-input" 
                                    placeholder="Enter your password"
                                    required
                                    autocomplete="current-password"
                                >
                            </div>
                            
                            <div class="form-group">
                                <label class="flex items-center cursor-pointer">
                                    <input type="checkbox" class="form-checkbox" name="remember">
                                    <span class="text-sm text-gray-600">Remember me for 30 days</span>
                                </label>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-full mb-4">
                                Sign In
                            </button>
                            
                            <div class="relative my-6">
                                <div class="absolute inset-0 flex items-center">
                                    <div class="w-full border-t border-gray-200"></div>
                                </div>
                                <div class="relative flex justify-center text-sm">
                                    <span class="px-4 bg-white text-gray-500">Or continue with</span>
                                </div>
                            </div>
                            
                            <div class="grid grid-cols-2 gap-4 mb-6">
                                <button type="button" class="btn btn-secondary" onclick="handleSocialLogin('google')">
                                    <svg class="w-5 h-5 mr-2" viewBox="0 0 24 24">
                                        <path fill="#4285F4" d="M23.745 12.27c0-.79-.07-1.54-.19-2.27h-11.3v4.51h6.47c-.29 1.48-1.14 2.73-2.4 3.58v3h3.86c2.26-2.09 3.56-5.17 3.56-8.82z"/>
                                        <path fill="#34A853" d="M12.255 24c3.24 0 5.95-1.08 7.93-2.91l-3.86-3c-1.08.72-2.45 1.16-4.07 1.16-3.13 0-5.78-2.11-6.73-4.96h-3.98v3.09C3.515 21.3 7.565 24 12.255 24z"/>
                                        <path fill="#FBBC05" d="M5.525 14.29c-.25-.72-.38-1.49-.38-2.29s.14-1.57.38-2.29V6.62h-3.98a11.86 11.86 0 000 10.76l3.98-3.09z"/>
                                        <path fill="#EA4335" d="M12.255 4.75c1.77 0 3.35.61 4.6 1.8l3.42-3.42C18.205 1.19 15.495 0 12.255 0c-4.69 0-8.74 2.7-10.71 6.62l3.98 3.09c.95-2.85 3.6-4.96 6.73-4.96z"/>
                                    </svg>
                                    Google
                                </button>
                                
                                <button type="button" class="btn btn-secondary" onclick="handleSocialLogin('github')">
                                    <svg class="w-5 h-5 mr-2" fill="currentColor" viewBox="0 0 24 24">
                                        <path d="M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"/>
                                    </svg>
                                    GitHub
                                </button>
                            </div>
                        </form>
                        
                        <p class="text-center text-sm text-gray-600">
                            Don't have an account? 
                            <a href="register.html" class="text-primary font-medium hover:text-secondary">
                                Sign up for free
                            </a>
                        </p>
                    </div>
                </div>
                
                <!-- Security Notice -->
                <div class="mt-8 text-center">
                    <p class="text-sm text-gray-500 flex items-center justify-center">
                        <svg class="w-4 h-4 mr-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
                        </svg>
                        Secure login with 256-bit SSL encryption
                    </p>
                </div>
            </div>
        </div>
    </section>
    
    <!-- Simple Footer -->
    <footer class="py-8 mt-auto">
        <div class="container">
            <div class="text-center text-sm text-gray-600">
                <p>&copy; 2025 SEO Pro. All rights reserved.</p>
                <div class="mt-2">
                    <a href="#" class="hover:text-primary mx-2">Privacy Policy</a>
                    <span class="text-gray-400">•</span>
                    <a href="#" class="hover:text-primary mx-2">Terms of Service</a>
                    <span class="text-gray-400">•</span>
                    <a href="contact.html" class="hover:text-primary mx-2">Contact</a>
                </div>
            </div>
        </div>
    </footer>
    
    <script>
        function handleLogin(event) {
            event.preventDefault();
            const email = document.getElementById('email').value;
            const password = document.getElementById('password').value;
            
            // Show loading state
            const submitBtn = event.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.innerHTML = '<span class="spinner mr-2"></span>Signing in...';
            submitBtn.disabled = true;
            
            // Simulate login process
            setTimeout(() => {
                // In a real application, you would make an API call here
                console.log('Login attempt:', { email, password });
                
                // Redirect to dashboard
                window.location.href = 'dashboard.html';
            }, 1500);
        }
        
        function handleSocialLogin(provider) {
            console.log('Social login with:', provider);
            // In a real application, you would initiate OAuth flow here
            alert(`${provider} login would be initiated here`);
        }
    </script>
</body>
</html>
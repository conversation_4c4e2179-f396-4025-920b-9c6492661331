# ⚡ Quick Start Implementation Guide

**Goal**: Get core functionality working in 4-6 hours  
**Status**: Application is running but features need restoration  

---

## 🎯 **IMMEDIATE PRIORITIES (Next 4 Hours)**

### **HOUR 1: Database Setup ⚡**

#### **Step 1: Execute Database Migration**
```sql
-- 1. Open Supabase Dashboard: https://supabase.com/dashboard
-- 2. Select your project: xpcbyzcaidfukddqniny
-- 3. Go to SQL Editor
-- 4. Copy and execute this SQL:

-- Create profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create content_generations table
CREATE TABLE IF NOT EXISTS content_generations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) NOT NULL,
  title TEXT NOT NULL,
  content TEXT NOT NULL,
  keywords TEXT[],
  industry TEXT,
  content_type TEXT,
  word_count INTEGER,
  seo_score DECIMAL,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable RLS
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_generations ENABLE ROW LEVEL SECURITY;

-- Create policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can view own content" ON content_generations FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can create content" ON content_generations FOR INSERT WITH CHECK (auth.uid() = user_id);
```

#### **Step 2: Verify Database**
- Check Tables tab in Supabase
- Verify profiles and content_generations tables exist
- Test with sample data

### **HOUR 2: Restore Core Components ⚡**

#### **Step 1: Restore Types**
```bash
cd src/temp_backup
mv types ../
```

#### **Step 2: Test Application**
```bash
npm run dev
# Check http://localhost:3000 - should still work
```

#### **Step 3: Restore UI Components**
```bash
mv components ../
```

#### **Step 4: Fix Import Errors**
```typescript
// If you see import errors, update imports in components:
// Change: import { Button } from "@/components/ui/button"
// To: import { Button } from "./ui/button"
```

### **HOUR 3: Authentication Flow ⚡**

#### **Step 1: Restore Contexts**
```bash
mv contexts ../
```

#### **Step 2: Update Layout**
```typescript
// src/app/layout.tsx
import { AuthProvider } from "@/contexts/auth-context";

export default function RootLayout({ children }) {
  return (
    <html lang="en">
      <body>
        <AuthProvider>
          {children}
        </AuthProvider>
      </body>
    </html>
  );
}
```

#### **Step 3: Test Authentication**
- Visit `/auth/signin`
- Try creating an account
- Check Supabase Auth tab for users

### **HOUR 4: Content Generation ⚡**

#### **Step 1: Restore Lib Directory**
```bash
mv lib ../
```

#### **Step 2: Create Simple Content Form**
```typescript
// src/app/generate/page.tsx
'use client';
import { useState } from 'react';

export default function GeneratePage() {
  const [content, setContent] = useState('');
  const [loading, setLoading] = useState(false);

  const handleGenerate = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/groq/generate', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          keyword: 'SEO optimization',
          industry: 'technology',
          contentType: 'blog'
        })
      });
      const data = await response.json();
      setContent(data.content);
    } catch (error) {
      console.error('Error:', error);
    }
    setLoading(false);
  };

  return (
    <div style={{ padding: '2rem' }}>
      <h1>Content Generator</h1>
      <button onClick={handleGenerate} disabled={loading}>
        {loading ? 'Generating...' : 'Generate Content'}
      </button>
      {content && (
        <div style={{ marginTop: '2rem', padding: '1rem', border: '1px solid #ccc' }}>
          <h3>Generated Content:</h3>
          <p>{content}</p>
        </div>
      )}
    </div>
  );
}
```

#### **Step 3: Test Content Generation**
- Visit `/generate`
- Click "Generate Content"
- Verify API call works

---

## 🔧 **TROUBLESHOOTING COMMON ISSUES**

### **Issue 1: Import Errors**
```bash
# Error: Cannot find module '@/components/ui/button'
# Solution: Update import paths or restore components gradually
```

### **Issue 2: Supabase Client Errors**
```typescript
// Error: supabaseKey is required
// Solution: Check environment variables are loaded
console.log(process.env.NEXT_PUBLIC_SUPABASE_URL);
```

### **Issue 3: API Route Errors**
```typescript
// Error: API route not found
// Solution: Check file structure and restart dev server
```

### **Issue 4: TypeScript Errors**
```bash
# Run type check to see specific errors
npx tsc --noEmit
```

---

## 📋 **VERIFICATION CHECKLIST**

### **After Hour 1 (Database)**
- [ ] Supabase tables created
- [ ] RLS policies active
- [ ] Can create test records

### **After Hour 2 (Components)**
- [ ] Application still loads
- [ ] No critical import errors
- [ ] Basic UI components work

### **After Hour 3 (Auth)**
- [ ] Can access auth pages
- [ ] Sign up creates user
- [ ] Sign in works
- [ ] User profile created

### **After Hour 4 (Content)**
- [ ] Content generation form loads
- [ ] API call succeeds
- [ ] Content displays
- [ ] No console errors

---

## 🚀 **NEXT STEPS (Hours 5-8)**

### **Hour 5: Dashboard Layout**
- Restore dashboard components
- Create navigation menu
- Add user profile display

### **Hour 6: SEO Analysis**
- Restore SEO engine
- Create analysis form
- Connect to backend

### **Hour 7: Results Display**
- Create results components
- Add charts and metrics
- Implement export functionality

### **Hour 8: Polish & Testing**
- Fix remaining bugs
- Add loading states
- Test complete workflows

---

## 🎯 **SUCCESS CRITERIA**

By the end of 4 hours, you should have:
1. ✅ Working database with user data
2. ✅ Functional authentication system
3. ✅ Basic content generation working
4. ✅ No critical errors in console
5. ✅ Application stable and usable

By the end of 8 hours, you should have:
1. ✅ Complete dashboard interface
2. ✅ SEO analysis functionality
3. ✅ Results display and export
4. ✅ Professional user experience
5. ✅ Ready for production deployment

---

## 🚨 **EMERGENCY RESET**

If anything breaks during restoration:

```bash
# Reset to working state
cd src
rm -rf components contexts lib types
mv temp_backup/* .
npm run dev
```

Then start over with gradual restoration.

---

**🎯 START NOW**: Begin with database migration, then follow each hour's tasks in sequence.**

// Advanced Heading Structure Analysis System
// Enterprise-grade heading optimization and structure analysis

interface HeadingElement {
  level: number; // 1-6 (H1-H6)
  text: string;
  position: number;
  wordCount: number;
  keywords: string[];
  keywordDensity: number;
  semanticScore: number;
  readabilityScore: number;
}

interface HeadingStructure {
  headings: HeadingElement[];
  hierarchy: HeadingHierarchy;
  optimization: HeadingOptimization;
  recommendations: HeadingRecommendation[];
}

interface HeadingHierarchy {
  isValid: boolean;
  issues: string[];
  depth: number;
  structure: HeadingNode[];
}

interface HeadingNode {
  heading: HeadingElement;
  children: HeadingNode[];
  parent?: HeadingNode;
}

interface HeadingOptimization {
  score: number; // 0-100
  keywordOptimization: number;
  structureScore: number;
  readabilityScore: number;
  competitorAlignment: number;
}

interface HeadingRecommendation {
  type: 'structure' | 'keyword' | 'readability' | 'length';
  priority: 'high' | 'medium' | 'low';
  heading: HeadingElement;
  current: string;
  suggested: string;
  reason: string;
  impact: number; // 1-10
}

interface CompetitorHeadingData {
  url: string;
  headings: HeadingElement[];
  structure: HeadingHierarchy;
  keywordUsage: Record<string, number>;
  averageLength: number;
  optimizationScore: number;
}

export class HeadingAnalyzer {
  private primaryKeyword: string;
  private secondaryKeywords: string[];
  private lsiKeywords: string[];

  constructor(primaryKeyword: string, secondaryKeywords: string[] = [], lsiKeywords: string[] = []) {
    this.primaryKeyword = primaryKeyword.toLowerCase();
    this.secondaryKeywords = secondaryKeywords.map(k => k.toLowerCase());
    this.lsiKeywords = lsiKeywords.map(k => k.toLowerCase());
  }

  /**
   * Analyze heading structure and optimization
   */
  analyzeHeadings(content: string, competitorData?: CompetitorHeadingData[]): HeadingStructure {
    const headings = this.extractHeadings(content);
    const hierarchy = this.analyzeHierarchy(headings);
    const optimization = this.calculateOptimization(headings, competitorData);
    const recommendations = this.generateRecommendations(headings, hierarchy, competitorData);

    return {
      headings,
      hierarchy,
      optimization,
      recommendations
    };
  }

  /**
   * Extract headings from HTML content
   */
  private extractHeadings(content: string): HeadingElement[] {
    const headingRegex = /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi;
    const headings: HeadingElement[] = [];
    let match;
    let position = 0;

    while ((match = headingRegex.exec(content)) !== null) {
      const level = parseInt(match[1]);
      const text = this.cleanText(match[2]);
      const wordCount = text.split(/\s+/).length;
      const keywords = this.extractKeywords(text);
      const keywordDensity = this.calculateKeywordDensity(text, keywords);
      const semanticScore = this.calculateSemanticScore(text);
      const readabilityScore = this.calculateReadabilityScore(text);

      headings.push({
        level,
        text,
        position: position++,
        wordCount,
        keywords,
        keywordDensity,
        semanticScore,
        readabilityScore
      });
    }

    return headings;
  }

  /**
   * Analyze heading hierarchy structure
   */
  private analyzeHierarchy(headings: HeadingElement[]): HeadingHierarchy {
    const issues: string[] = [];
    let isValid = true;
    let depth = 0;

    // Check for H1 presence and uniqueness
    const h1Count = headings.filter(h => h.level === 1).length;
    if (h1Count === 0) {
      issues.push('Missing H1 tag - critical for SEO');
      isValid = false;
    } else if (h1Count > 1) {
      issues.push('Multiple H1 tags found - should be unique');
      isValid = false;
    }

    // Check hierarchy logic
    let previousLevel = 0;
    for (const heading of headings) {
      if (heading.level > previousLevel + 1 && previousLevel > 0) {
        issues.push(`Heading level jump from H${previousLevel} to H${heading.level} - should be sequential`);
        isValid = false;
      }
      previousLevel = heading.level;
      depth = Math.max(depth, heading.level);
    }

    // Build hierarchy tree
    const structure = this.buildHeadingTree(headings);

    return {
      isValid,
      issues,
      depth,
      structure
    };
  }

  /**
   * Build heading tree structure
   */
  private buildHeadingTree(headings: HeadingElement[]): HeadingNode[] {
    const root: HeadingNode[] = [];
    const stack: HeadingNode[] = [];

    for (const heading of headings) {
      const node: HeadingNode = {
        heading,
        children: []
      };

      // Find appropriate parent
      while (stack.length > 0 && stack[stack.length - 1].heading.level >= heading.level) {
        stack.pop();
      }

      if (stack.length === 0) {
        root.push(node);
      } else {
        const parent = stack[stack.length - 1];
        parent.children.push(node);
        node.parent = parent;
      }

      stack.push(node);
    }

    return root;
  }

  /**
   * Calculate heading optimization score
   */
  private calculateOptimization(headings: HeadingElement[], competitorData?: CompetitorHeadingData[]): HeadingOptimization {
    const keywordOptimization = this.calculateKeywordOptimization(headings);
    const structureScore = this.calculateStructureScore(headings);
    const readabilityScore = this.calculateAverageReadability(headings);
    const competitorAlignment = competitorData ? this.calculateCompetitorAlignment(headings, competitorData) : 50;

    const score = Math.round(
      (keywordOptimization * 0.3) +
      (structureScore * 0.25) +
      (readabilityScore * 0.25) +
      (competitorAlignment * 0.2)
    );

    return {
      score,
      keywordOptimization,
      structureScore,
      readabilityScore,
      competitorAlignment
    };
  }

  /**
   * Calculate keyword optimization in headings
   */
  private calculateKeywordOptimization(headings: HeadingElement[]): number {
    let score = 0;
    let maxScore = 0;

    // H1 should contain primary keyword (40 points)
    const h1 = headings.find(h => h.level === 1);
    maxScore += 40;
    if (h1 && this.containsKeyword(h1.text, this.primaryKeyword)) {
      score += 40;
    }

    // H2s should contain keywords (30 points)
    const h2s = headings.filter(h => h.level === 2);
    maxScore += 30;
    const h2WithKeywords = h2s.filter(h => 
      this.containsKeyword(h.text, this.primaryKeyword) || 
      this.secondaryKeywords.some(k => this.containsKeyword(h.text, k))
    );
    score += Math.min(30, (h2WithKeywords.length / Math.max(h2s.length, 1)) * 30);

    // LSI keywords in headings (30 points)
    maxScore += 30;
    const headingsWithLSI = headings.filter(h => 
      this.lsiKeywords.some(k => this.containsKeyword(h.text, k))
    );
    score += Math.min(30, (headingsWithLSI.length / Math.max(headings.length, 1)) * 30);

    return Math.round((score / maxScore) * 100);
  }

  /**
   * Calculate structure score
   */
  private calculateStructureScore(headings: HeadingElement[]): number {
    let score = 100;

    // Penalize for missing H1
    const h1Count = headings.filter(h => h.level === 1).length;
    if (h1Count === 0) score -= 30;
    if (h1Count > 1) score -= 20;

    // Penalize for poor hierarchy
    let previousLevel = 0;
    for (const heading of headings) {
      if (heading.level > previousLevel + 1 && previousLevel > 0) {
        score -= 10;
      }
      previousLevel = heading.level;
    }

    // Penalize for too few or too many headings
    const headingCount = headings.length;
    if (headingCount < 3) score -= 20;
    if (headingCount > 15) score -= 10;

    return Math.max(0, score);
  }

  /**
   * Calculate average readability score
   */
  private calculateAverageReadability(headings: HeadingElement[]): number {
    if (headings.length === 0) return 0;
    
    const totalScore = headings.reduce((sum, h) => sum + h.readabilityScore, 0);
    return Math.round(totalScore / headings.length);
  }

  /**
   * Calculate competitor alignment score
   */
  private calculateCompetitorAlignment(headings: HeadingElement[], competitorData: CompetitorHeadingData[]): number {
    if (competitorData.length === 0) return 50;

    const avgCompetitorHeadingCount = competitorData.reduce((sum, c) => sum + c.headings.length, 0) / competitorData.length;
    const avgCompetitorOptimization = competitorData.reduce((sum, c) => sum + c.optimizationScore, 0) / competitorData.length;

    // Compare heading count (50% weight)
    const headingCountScore = Math.min(100, (headings.length / avgCompetitorHeadingCount) * 100);
    
    // Compare optimization level (50% weight)
    const currentOptimization = this.calculateKeywordOptimization(headings);
    const optimizationScore = Math.min(100, (currentOptimization / avgCompetitorOptimization) * 100);

    return Math.round((headingCountScore * 0.5) + (optimizationScore * 0.5));
  }

  /**
   * Generate optimization recommendations
   */
  private generateRecommendations(
    headings: HeadingElement[], 
    hierarchy: HeadingHierarchy, 
    competitorData?: CompetitorHeadingData[]
  ): HeadingRecommendation[] {
    const recommendations: HeadingRecommendation[] = [];

    // H1 recommendations
    const h1 = headings.find(h => h.level === 1);
    if (!h1) {
      recommendations.push({
        type: 'structure',
        priority: 'high',
        heading: { level: 1, text: '', position: 0, wordCount: 0, keywords: [], keywordDensity: 0, semanticScore: 0, readabilityScore: 0 },
        current: 'No H1 tag found',
        suggested: `Add H1 tag with primary keyword: "${this.primaryKeyword}"`,
        reason: 'H1 tag is critical for SEO and should contain the primary keyword',
        impact: 10
      });
    } else if (!this.containsKeyword(h1.text, this.primaryKeyword)) {
      recommendations.push({
        type: 'keyword',
        priority: 'high',
        heading: h1,
        current: h1.text,
        suggested: this.optimizeHeadingForKeyword(h1.text, this.primaryKeyword),
        reason: 'H1 should contain the primary keyword for better SEO',
        impact: 8
      });
    }

    // Structure recommendations
    if (!hierarchy.isValid) {
      for (const issue of hierarchy.issues) {
        recommendations.push({
          type: 'structure',
          priority: 'medium',
          heading: headings[0] || { level: 1, text: '', position: 0, wordCount: 0, keywords: [], keywordDensity: 0, semanticScore: 0, readabilityScore: 0 },
          current: 'Invalid heading structure',
          suggested: 'Fix heading hierarchy',
          reason: issue,
          impact: 6
        });
      }
    }

    return recommendations.sort((a, b) => b.impact - a.impact);
  }

  // Helper methods
  private cleanText(html: string): string {
    return html.replace(/<[^>]*>/g, '').trim();
  }

  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase().split(/\s+/);
    return [...this.secondaryKeywords, ...this.lsiKeywords].filter(keyword => 
      words.some(word => word.includes(keyword) || keyword.includes(word))
    );
  }

  private calculateKeywordDensity(text: string, keywords: string[]): number {
    const words = text.toLowerCase().split(/\s+/);
    const keywordCount = keywords.reduce((count, keyword) => {
      return count + words.filter(word => word.includes(keyword)).length;
    }, 0);
    return words.length > 0 ? (keywordCount / words.length) * 100 : 0;
  }

  private calculateSemanticScore(text: string): number {
    // Simplified semantic scoring based on keyword relevance
    const relevantWords = [this.primaryKeyword, ...this.secondaryKeywords, ...this.lsiKeywords];
    const words = text.toLowerCase().split(/\s+/);
    const relevantCount = words.filter(word => 
      relevantWords.some(relevant => word.includes(relevant) || relevant.includes(word))
    ).length;
    return Math.min(100, (relevantCount / words.length) * 100);
  }

  private calculateReadabilityScore(text: string): number {
    const words = text.split(/\s+/);
    const avgWordLength = words.reduce((sum, word) => sum + word.length, 0) / words.length;
    
    // Optimal heading length: 6-10 words, average word length 4-6 characters
    let score = 100;
    if (words.length < 3) score -= 20;
    if (words.length > 12) score -= 15;
    if (avgWordLength > 8) score -= 10;
    if (avgWordLength < 3) score -= 10;
    
    return Math.max(0, score);
  }

  private containsKeyword(text: string, keyword: string): boolean {
    return text.toLowerCase().includes(keyword.toLowerCase());
  }

  private optimizeHeadingForKeyword(currentText: string, keyword: string): string {
    // Simple optimization - prepend keyword if not present
    if (!this.containsKeyword(currentText, keyword)) {
      return `${keyword} - ${currentText}`;
    }
    return currentText;
  }
}

export const headingAnalyzer = new HeadingAnalyzer('', [], []);

"use strict";(()=>{var e={};e.id=941,e.ids=[941],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},2361:e=>{e.exports=require("events")},7147:e=>{e.exports=require("fs")},3685:e=>{e.exports=require("http")},5687:e=>{e.exports=require("https")},1808:e=>{e.exports=require("net")},7561:e=>{e.exports=require("node:fs")},4492:e=>{e.exports=require("node:stream")},1017:e=>{e.exports=require("path")},5477:e=>{e.exports=require("punycode")},2781:e=>{e.exports=require("stream")},4404:e=>{e.exports=require("tls")},7310:e=>{e.exports=require("url")},3837:e=>{e.exports=require("util")},1267:e=>{e.exports=require("worker_threads")},9796:e=>{e.exports=require("zlib")},5131:(e,t,i)=>{i.r(t),i.d(t,{headerHooks:()=>q,originalPathname:()=>D,patchFetch:()=>b,requestAsyncStorage:()=>x,routeModule:()=>v,serverHooks:()=>M,staticGenerationAsyncStorage:()=>A,staticGenerationBailout:()=>R});var o={};i.r(o),i.d(o,{GET:()=>k,POST:()=>f});var r=i(5419),n=i(9108),s=i(9678),a=i(8070),c=i(5252),l=i(2178),d=i(2982),u=i(7734),m=i(8280);class p{static getInstance(){return p.instance||(p.instance=new p),p.instance}async analyzeSEO(e,t){return(0,m.bi)("seo-engine",async()=>{let i,o;let r=Date.now(),n=[];console.log("Starting comprehensive SEO analysis...",{keyword:e.keyword,location:e.location,contentType:e.contentType});try{if(e.includeCompetitorAnalysis){let o=Date.now();console.log("Step 1: Analyzing competitors..."),i=await u.vG.analyzeCompetitors(e.keyword,e.location,{analyzeTop:5,includeContent:!0,includeMetrics:!0},t),n.push({step:"Competitor Analysis",duration:Date.now()-o,status:"completed",details:`Analyzed ${i.topCompetitors.length} competitors`})}else n.push({step:"Competitor Analysis",duration:0,status:"skipped",details:"Competitor analysis not requested"});if(e.includeContentGeneration){let r=Date.now();console.log("Step 2: Generating optimized content...");let s={keyword:e.keyword,location:e.location,industry:e.industry,contentType:e.contentType,tone:e.tone,intent:e.intent,targetWordCount:e.targetWordCount||i?.averageWordCount||800,includeImages:!0,includeSchema:!0,includeFaq:!0,competitorData:i};o=await d.TU.generateContent(s,t),n.push({step:"Content Generation",duration:Date.now()-r,status:"completed",details:`Generated ${o.seoMetrics.wordCount} words`})}else n.push({step:"Content Generation",duration:0,status:"skipped",details:"Content generation not requested"});let s=Date.now();console.log("Step 3: Creating insights and recommendations...");let a=this.createCompetitorInsights(i),c=this.createContentRecommendations(e,i),l=this.calculateSEOScore(o,i,e),m=this.generateOptimizationSuggestions(l,a,c);n.push({step:"Analysis & Insights",duration:Date.now()-s,status:"completed",details:"Generated recommendations and scoring"});let p=Date.now(),h=p-r,y={keyword:e.keyword,location:e.location,competitorAnalysis:a,contentRecommendations:c,generatedContent:o?{title:o.title,metaDescription:o.metaDescription,content:o.content,outline:this.transformOutline(o.outline),seoMetrics:this.transformSEOMetrics(o.seoMetrics),optimizationApplied:this.identifyOptimizationsApplied(o)}:void 0,seoScore:l,optimization:m,timeline:{startTime:r,endTime:p,steps:n,totalDuration:h}};return console.log("SEO analysis completed",{totalDuration:h,seoScore:l.total,difficulty:a.difficulty}),y}catch(e){throw console.error("SEO analysis failed:",e),e}},{method:"POST",endpoint:"analyze",...e},t)}createCompetitorInsights(e){if(!e)return{difficulty:"medium",averageMetrics:this.getDefaultMetrics(),topCompetitors:[],contentGaps:[],searchFeatures:[],recommendations:["Enable competitor analysis for detailed insights"]};let t=e.topCompetitors.map((e,t)=>({domain:e.domain,position:e.position,title:e.title,strengths:this.identifyCompetitorStrengths(e),weaknesses:this.identifyCompetitorWeaknesses(e),opportunityScore:this.calculateOpportunityScore(e,t)})),i=e.contentGaps.map(e=>({type:"missing_topic",description:e,priority:"medium",opportunity:"Address this content gap to differentiate from competitors"})),o=e.searchFeatures.map(e=>({feature:e.type,present:e.present,opportunity:!e.present,strategy:this.getFeatureStrategy(e.type,e.present)}));return{difficulty:e.difficulty,averageMetrics:{wordCount:e.averageWordCount,headingStructure:e.averageHeadings,keywordDensity:e.averageKeywordDensity,domainAuthority:this.calculateAverageDomainAuthority(e.topCompetitors),contentScore:this.calculateAverageContentScore(e.topCompetitors),technicalScore:this.calculateAverageTechnicalScore(e.topCompetitors)},topCompetitors:t,contentGaps:i,searchFeatures:o,recommendations:this.generateCompetitorRecommendations(e)}}createContentRecommendations(e,t){let i=e.targetWordCount||t?.averageWordCount||800,o=this.recommendHeadingStructure(i,t?.averageHeadings,e.contentType),r=this.generateKeywordTargets(e.keyword,t?.commonKeywords||[]),n=this.recommendContentStructure(e,i,r);return{optimalWordCount:i,headingStructure:o,keywordTargets:r,contentStructure:n,technicalOptimization:this.generateTechnicalRecommendations(e),schemaMarkup:this.generateSchemaRecommendations(e.contentType)}}calculateSEOScore(e,t,i){let o=this.scoreKeywordOptimization(e,i.keyword),r=this.scoreContentStructure(e,t),n={keywordOptimization:o,contentStructure:r,technicalSEO:this.scoreTechnicalSEO(e),competitorAlignment:this.scoreCompetitorAlignment(e,t),userIntent:this.scoreUserIntent(e,i.intent)},s={keywordOptimization:.25,contentStructure:.2,technicalSEO:.2,competitorAlignment:.2,userIntent:.15},a=Math.round(Object.entries(n).reduce((e,[t,i])=>{let o=s[t];return e+i.score/i.maxScore*100*o},0)),c=this.createCompetitorComparison(a,t),l=100-a;return{total:a,breakdown:n,comparison:c,improvementPotential:l}}generateOptimizationSuggestions(e,t,i){let o=[],r=[],n=[];e.breakdown.keywordOptimization.score<80&&o.push({action:"Optimize keyword density",description:"Adjust keyword usage to match competitor averages",impact:"high",effort:"easy",timeline:"Immediate",expectedImprovement:10}),e.breakdown.technicalSEO.score<70&&o.push({action:"Fix technical SEO issues",description:"Update meta tags, headings, and technical elements",impact:"high",effort:"easy",timeline:"Immediate",expectedImprovement:15}),e.breakdown.contentStructure.score<75&&r.push({action:"Improve content structure",description:"Reorganize content with better heading hierarchy",impact:"medium",effort:"medium",timeline:"1-7 days",expectedImprovement:12}),t.contentGaps.length>0&&r.push({action:"Address content gaps",description:"Add missing topics identified in competitor analysis",impact:"high",effort:"medium",timeline:"3-7 days",expectedImprovement:18}),("high"===t.difficulty||"very-high"===t.difficulty)&&n.push({action:"Build domain authority",description:"Implement link building and authority-building strategies",impact:"high",effort:"hard",timeline:"1-6 months",expectedImprovement:25}),n.push({action:"Monitor and iterate",description:"Track rankings and continuously optimize based on performance",impact:"medium",effort:"medium",timeline:"Ongoing",expectedImprovement:20});let s=this.calculatePriorityScore(o,r,n);return{immediate:o,shortTerm:r,longTerm:n,priorityScore:s}}scoreKeywordOptimization(e,t){if(!e)return{score:0,maxScore:100,status:"poor",factors:[{name:"No content provided",score:0,impact:"high",description:"Content generation is required for analysis"}]};let i=[],o=0,r=e.seoMetrics?.keywordDensity||0;r>=1&&r<=3?(i.push({name:"Keyword Density",score:25,impact:"high",description:"Optimal keyword density achieved"}),o+=25):r>0&&(i.push({name:"Keyword Density",score:15,impact:"high",description:"Keyword density needs adjustment"}),o+=15),e.title?.toLowerCase().includes(t.toLowerCase())&&(i.push({name:"Keyword in Title",score:20,impact:"high",description:"Primary keyword found in title"}),o+=20),e.metaDescription?.toLowerCase().includes(t.toLowerCase())&&(i.push({name:"Keyword in Meta Description",score:15,impact:"medium",description:"Primary keyword found in meta description"}),o+=15),e.seoMetrics?.lsiKeywords?.length>0&&(i.push({name:"LSI Keywords",score:20,impact:"medium",description:"Related keywords identified and used"}),o+=20),e.seoMetrics?.keywordVariations?.length>0&&(i.push({name:"Keyword Variations",score:20,impact:"medium",description:"Keyword variations used naturally"}),o+=20);let n=o>=80?"excellent":o>=60?"good":o>=40?"needs_improvement":"poor";return{score:o,maxScore:100,status:n,factors:i}}scoreContentStructure(e,t){if(!e)return{score:0,maxScore:100,status:"poor",factors:[{name:"No content provided",score:0,impact:"high",description:"Content generation is required for analysis"}]};let i=[],o=0,r=(e.seoMetrics?.wordCount||0)/(t?.averageWordCount||800);r>=.9&&r<=1.2?(i.push({name:"Word Count",score:25,impact:"high",description:"Word count aligns with competitors"}),o+=25):r>=.7&&r<=1.5&&(i.push({name:"Word Count",score:15,impact:"high",description:"Word count is acceptable but could be optimized"}),o+=15);let n=e.seoMetrics?.headingCount||{};1===n.h1&&(i.push({name:"H1 Structure",score:15,impact:"high",description:"Proper H1 structure"}),o+=15),n.h2>=2&&(i.push({name:"H2 Structure",score:20,impact:"medium",description:"Good H2 heading distribution"}),o+=20),(e.seoMetrics?.readabilityScore||0)>=60&&(i.push({name:"Readability",score:20,impact:"medium",description:"Good readability score"}),o+=20),e.outline?.headings?.length>0&&(i.push({name:"Content Organization",score:20,impact:"medium",description:"Well-organized content structure"}),o+=20);let s=o>=80?"excellent":o>=60?"good":o>=40?"needs_improvement":"poor";return{score:o,maxScore:100,status:s,factors:i}}scoreTechnicalSEO(e){let t=[],i=0;if(!e)return{score:0,maxScore:100,status:"poor",factors:[{name:"No content provided",score:0,impact:"high",description:"Content generation is required for analysis"}]};e.title&&e.title.length>=30&&e.title.length<=60&&(t.push({name:"Title Length",score:20,impact:"high",description:"Optimal title length"}),i+=20),e.metaDescription&&e.metaDescription.length>=120&&e.metaDescription.length<=160&&(t.push({name:"Meta Description Length",score:20,impact:"high",description:"Optimal meta description length"}),i+=20),e.schemaMarkup&&(t.push({name:"Schema Markup",score:25,impact:"medium",description:"Schema markup included"}),i+=25),e.internalLinks&&e.internalLinks.length>0&&(t.push({name:"Internal Links",score:15,impact:"medium",description:"Internal linking suggestions provided"}),i+=15),e.externalLinks&&e.externalLinks.length>0&&(t.push({name:"External Links",score:10,impact:"low",description:"External linking suggestions provided"}),i+=10),e.imagePrompts&&e.imagePrompts.length>0&&(t.push({name:"Image Optimization",score:10,impact:"low",description:"Image suggestions with alt text"}),i+=10);let o=i>=80?"excellent":i>=60?"good":i>=40?"needs_improvement":"poor";return{score:i,maxScore:100,status:o,factors:t}}scoreCompetitorAlignment(e,t){if(!t)return{score:50,maxScore:100,status:"needs_improvement",factors:[{name:"No competitor data",score:50,impact:"medium",description:"Enable competitor analysis for better scoring"}]};let i=[],o=0,r=Math.abs((e?.seoMetrics?.keywordDensity||0)-(t.averageKeywordDensity||2));r<=.5?(i.push({name:"Keyword Density Alignment",score:30,impact:"high",description:"Keyword density matches competitors"}),o+=30):r<=1&&(i.push({name:"Keyword Density Alignment",score:20,impact:"high",description:"Keyword density is close to competitors"}),o+=20);let n=(e?.seoMetrics?.wordCount||0)/(t.averageWordCount||800);n>=.9&&n<=1.1&&(i.push({name:"Word Count Alignment",score:25,impact:"high",description:"Word count aligns with top competitors"}),o+=25);let s=t.commonKeywords||[],a=e?.seoMetrics?.lsiKeywords||[];s.filter(e=>a.some(t=>t.includes(e))).length>=.7*s.length&&(i.push({name:"Topic Coverage",score:25,impact:"medium",description:"Good coverage of competitor topics"}),o+=25),(t.contentGaps||[]).length>0&&(i.push({name:"Content Gaps",score:20,impact:"medium",description:"Opportunity to address competitor content gaps"}),o+=20);let c=o>=80?"excellent":o>=60?"good":o>=40?"needs_improvement":"poor";return{score:o,maxScore:100,status:c,factors:i}}scoreUserIntent(e,t){let i=[],o=0;if(!e)return{score:0,maxScore:100,status:"poor",factors:[{name:"No content provided",score:0,impact:"high",description:"Content generation is required for analysis"}]};switch(t){case"informational":(e.content?.includes("how to")||e.content?.includes("what is"))&&(i.push({name:"Informational Intent",score:30,impact:"high",description:"Content addresses informational queries"}),o+=30);break;case"commercial":(e.content?.includes("comparison")||e.content?.includes("review"))&&(i.push({name:"Commercial Intent",score:30,impact:"high",description:"Content supports commercial research"}),o+=30);break;case"transactional":(e.content?.includes("buy")||e.content?.includes("service"))&&(i.push({name:"Transactional Intent",score:30,impact:"high",description:"Content supports transactional queries"}),o+=30)}"informational"===t&&e.faqSection&&(i.push({name:"FAQ Section",score:25,impact:"medium",description:"FAQ section addresses common questions"}),o+=25),(e.content?.includes("contact")||e.content?.includes("learn more"))&&(i.push({name:"Call to Action",score:25,impact:"medium",description:"Appropriate calls to action included"}),o+=25),(e.seoMetrics?.wordCount||0)>=500&&(i.push({name:"Content Depth",score:20,impact:"medium",description:"Sufficient content depth for user intent"}),o+=20);let r=o>=80?"excellent":o>=60?"good":o>=40?"needs_improvement":"poor";return{score:o,maxScore:100,status:r,factors:i}}getDefaultMetrics(){return{wordCount:800,headingStructure:{h1:1,h2:3,h3:2,h4:1},keywordDensity:2,domainAuthority:50,contentScore:65,technicalScore:70}}identifyCompetitorStrengths(e){let t=[];return e.contentScore>80&&t.push("High-quality content"),e.technicalScore>80&&t.push("Strong technical SEO"),e.domainAuthority>70&&t.push("High domain authority"),e.wordCount>1500&&t.push("Comprehensive content"),t}identifyCompetitorWeaknesses(e){let t=[];return e.contentScore<60&&t.push("Poor content quality"),e.technicalScore<60&&t.push("Technical SEO issues"),e.keywordDensity<1&&t.push("Low keyword optimization"),e.wordCount<500&&t.push("Thin content"),t}calculateOpportunityScore(e,t){let i=100-15*t;return Math.max(0,Math.min(100,i+=10*this.identifyCompetitorWeaknesses(e).length))}getFeatureStrategy(e,t){return t?`Compete for ${e} position`:`Opportunity to capture ${e} feature`}generateCompetitorRecommendations(e){let t=[];return"low"===e.difficulty?t.push("Target this keyword - low competition detected"):"high"===e.difficulty&&t.push("Consider long-tail variations due to high competition"),e.averageWordCount<800&&t.push("Opportunity to create more comprehensive content"),e.contentGaps.length>0&&t.push("Address identified content gaps for competitive advantage"),t}calculateAverageDomainAuthority(e){return e.length?e.reduce((e,t)=>e+(t.domainAuthority||50),0)/e.length:50}calculateAverageContentScore(e){return e.length?e.reduce((e,t)=>e+(t.contentScore||65),0)/e.length:65}calculateAverageTechnicalScore(e){return e.length?e.reduce((e,t)=>e+(t.technicalScore||70),0)/e.length:70}recommendHeadingStructure(e,t,i){let o=[];o.push({level:1,count:1,keywords:["primary"],examples:["Main title with primary keyword"]}),o.push({level:2,count:Math.max(2,Math.floor(e/300)),keywords:["primary","secondary"],examples:["Major section headings"]});let r=Math.max(0,Math.floor(e/500));return r>0&&o.push({level:3,count:r,keywords:["secondary","lsi"],examples:["Subsection headings"]}),o}generateKeywordTargets(e,t){let i=[];return i.push({keyword:e,type:"primary",targetDensity:2,placement:["title","h1","first-paragraph","conclusion"],priority:10}),t.slice(0,5).forEach((e,t)=>{i.push({keyword:e,type:"secondary",targetDensity:1,placement:["h2","body"],priority:9-t})}),i}recommendContentStructure(e,t,i){let o=Math.floor(t/6);return{introduction:{title:"Introduction",purpose:"Hook readers and introduce main topic",keypoints:["Problem statement","Overview of solution","What readers will learn"],wordCount:o,keywords:[e.keyword]},mainSections:[{title:`What is ${e.keyword}?`,purpose:"Define and explain the main concept",keypoints:["Clear definition","Key characteristics","Why it matters"],wordCount:o,keywords:i.slice(0,2).map(e=>e.keyword)},{title:`Benefits of ${e.keyword}`,purpose:"Highlight value proposition",keypoints:["Primary benefits","Real-world examples","Supporting evidence"],wordCount:o,keywords:i.slice(1,3).map(e=>e.keyword)},{title:`How to Choose ${e.keyword}`,purpose:"Guide decision-making process",keypoints:["Selection criteria","Comparison factors","Best practices"],wordCount:o,keywords:i.slice(2,4).map(e=>e.keyword)}],conclusion:{title:"Conclusion",purpose:"Summarize and provide next steps",keypoints:["Key takeaways","Action items","Call to action"],wordCount:o,keywords:[e.keyword]},additionalElements:[{type:"faq",description:"Frequently Asked Questions section",benefit:"Addresses common queries and improves featured snippet chances",implementation:"Add 5-8 relevant questions with detailed answers"},{type:"table",description:"Comparison or feature table",benefit:"Improves content scannability and user experience",implementation:"Create tables for comparisons or feature lists"}]}}generateTechnicalRecommendations(e){return[{type:"meta",description:"Optimize title tag and meta description",implementation:"Include primary keyword in title (30-60 chars) and meta description (120-160 chars)",impact:"high"},{type:"url",description:"Create SEO-friendly URL structure",implementation:`Use URL like /${e.keyword.replace(/\s+/g,"-").toLowerCase()}`,impact:"medium"},{type:"internal_links",description:"Add strategic internal links",implementation:"Link to 3-5 related pages with keyword-rich anchor text",impact:"medium"},{type:"images",description:"Optimize images for SEO",implementation:"Add descriptive alt text and file names with keywords",impact:"low"}]}generateSchemaRecommendations(e){let t=[];switch(e){case"service":t.push({type:"Service",description:"Service schema markup",code:JSON.stringify({"@context":"https://schema.org","@type":"Service",name:"Service Name",description:"Service Description"},null,2),benefit:"Helps search engines understand your service offering"});break;case"blog":t.push({type:"Article",description:"Article schema markup",code:JSON.stringify({"@context":"https://schema.org","@type":"Article",headline:"Article Title",author:"Author Name"},null,2),benefit:"Improves article visibility in search results"})}return t}transformOutline(e){return{structure:e?.headings?.map((e,t)=>({level:e.level,title:e.text,keywords:e.keywords||[],wordCount:e.wordCount||100,purpose:this.getHeadingPurpose(e.level,t)}))||[],keywordDistribution:[],estimatedReadingTime:e?.estimatedReadingTime||4,technicalElements:["meta tags","headings","internal links"]}}transformSEOMetrics(e){return{overallScore:e?.seoScore||75,keywordOptimization:this.calculateKeywordScore(e),contentQuality:this.calculateContentScore(e),technicalSEO:this.calculateTechnicalScore(e),userExperience:this.calculateUXScore(e),competitiveness:70}}identifyOptimizationsApplied(e){let t=[];return e.title&&t.push("Title optimization"),e.metaDescription&&t.push("Meta description optimization"),e.schemaMarkup&&t.push("Schema markup"),e.internalLinks&&t.push("Internal linking"),e.imagePrompts&&t.push("Image optimization"),e.faqSection&&t.push("FAQ section"),t}getHeadingPurpose(e,t){switch(e){case 1:return"Main page title";case 2:return"Major section heading";case 3:return"Subsection heading";default:return"Supporting heading"}}calculateKeywordScore(e){let t=0;return e?.keywordDensity>=1&&e?.keywordDensity<=3&&(t+=30),e?.keywordVariations?.length>0&&(t+=20),e?.lsiKeywords?.length>0&&(t+=25),e?.entities?.length>0&&(t+=25),t}calculateContentScore(e){let t=0;return e?.wordCount>=500&&(t+=25),e?.readabilityScore>=60&&(t+=25),e?.headingCount?.h1===1&&(t+=15),e?.headingCount?.h2>=2&&(t+=20),e?.headingCount?.h3>=1&&(t+=15),t}calculateTechnicalScore(e){return 75}calculateUXScore(e){let t=50;return e?.readabilityScore>=70&&(t+=25),e?.wordCount>=800&&(t+=25),t}createCompetitorComparison(e,t){let i=t?this.calculateAverageContentScore(t.topCompetitors):65,o=t?.topCompetitors?.length||5;return{averageScore:i,ranking:e>i?Math.max(1,Math.floor(.3*o)):Math.ceil(.7*o),totalCompetitors:o,strengths:e>i?["Above average optimization"]:[],weaknesses:e<i?["Below competitor average"]:[]}}calculatePriorityScore(e,t,i){return Math.round(e.reduce((e,t)=>e+t.expectedImprovement,0)+.8*t.reduce((e,t)=>e+t.expectedImprovement,0)+.6*i.reduce((e,t)=>e+t.expectedImprovement,0))}}let h=p.getInstance();var y=i(9279),g=i(2045);let w=c.Ry({keyword:c.Z_().min(1,"Keyword is required").max(100,"Keyword too long"),location:c.Z_().max(100,"Location too long").optional().default(""),industry:c.Z_().min(1,"Industry is required").max(50,"Industry too long"),contentType:c.Km(["service","blog","product","landing","category","faq"]),tone:c.Km(["professional","conversational","authoritative","friendly","technical","casual"]),intent:c.Km(["informational","commercial","transactional","navigational"]),targetWordCount:c.Rx().min(200).max(5e3).optional(),includeCompetitorAnalysis:c.O7().default(!0),includeContentGeneration:c.O7().default(!0),customRequirements:c.IX(c.Z_()).optional()});async function f(e){let t=Date.now();try{let i;let o=await e.json(),r=w.parse(o),n=(0,g.jq)(),{data:{user:s},error:c}=await n.auth.getUser();if(c||!s)return a.Z.json({success:!1,error:"Unauthorized"},{status:401});let{data:l}=await n.from("profiles").select("subscription_tier, credits_remaining, monthly_spend").eq("id",s.id).single(),d=l?.subscription_tier||"free";l?.credits_remaining,l?.monthly_spend;let u=function(e){let t;if(t=.05,e.includeCompetitorAnalysis&&(t+=.03),e.includeContentGeneration){let i=e.targetWordCount||800;t+=Math.floor(1.3*i)/1e3*.05}return Math.round(100*t)/100}(r),m=(i=1,r.includeCompetitorAnalysis&&(i+=1),r.includeContentGeneration&&(i+=2),i),p=await (0,y.dZ)(s.id,d,"daily",async()=>({allowed:!0})).catch(e=>({allowed:!1,error:e.message}));if(!p.allowed)return a.Z.json({success:!1,error:("error"in p?p.error:null)||"Subscription limit exceeded",usage:{creditsUsed:0,costUSD:0,rateLimitRemaining:0}},{status:429});let f=await (0,y.sy)(s.id,d,u,async()=>({allowed:!0})).catch(e=>({allowed:!1,error:e.message}));if(!f.allowed)return a.Z.json({success:!1,error:("error"in f?f.error:null)||"Cost limit exceeded",usage:{creditsUsed:0,costUSD:u,rateLimitRemaining:0}},{status:402});let k=await (0,y.er)(s.id,d,"groq",async()=>({allowed:!0})).catch(e=>({allowed:!1,error:e.message}));if(!k.allowed)return a.Z.json({success:!1,error:("error"in k?k.error:null)||"Rate limit exceeded",usage:{creditsUsed:0,costUSD:0,rateLimitRemaining:0}},{status:429});console.log("Starting SEO analysis for user:",s.id,{keyword:r.keyword,contentType:r.contentType,estimatedCost:u});let v=await h.analyzeSEO(r,s.id);await S(n,s.id,m,u),await C(n,s.id,r,v,u);let x=Date.now()-t,A={success:!0,data:{keyword:v.keyword,location:v.location,competitorAnalysis:{difficulty:v.competitorAnalysis.difficulty,averageMetrics:{wordCount:v.competitorAnalysis.averageMetrics.wordCount,keywordDensity:v.competitorAnalysis.averageMetrics.keywordDensity,domainAuthority:v.competitorAnalysis.averageMetrics.domainAuthority},topCompetitors:v.competitorAnalysis.topCompetitors.map(e=>({domain:e.domain,position:e.position,title:e.title,opportunityScore:e.opportunityScore}))},contentRecommendations:v.contentRecommendations,generatedContent:v.generatedContent,seoScore:{total:v.seoScore.total,breakdown:v.seoScore.breakdown,comparison:v.seoScore.comparison,improvementPotential:v.seoScore.improvementPotential},optimization:v.optimization,timeline:{totalDuration:v.timeline.totalDuration,steps:v.timeline.steps}},usage:{creditsUsed:m,costUSD:u,rateLimitRemaining:100}};return console.log("SEO analysis completed successfully",{userId:s.id,keyword:r.keyword,responseTime:x,seoScore:v.seoScore.total}),a.Z.json(A,{status:200,headers:{"X-Response-Time":x.toString(),"X-SEO-Score":v.seoScore.total.toString(),"X-Credits-Used":m.toString()}})}catch(t){if(console.error("SEO analysis error:",t),t instanceof l.jm)return a.Z.json({success:!1,error:"Invalid request data",details:t.errors.map(e=>({field:e.path.join("."),message:e.message}))},{status:400});let e=t instanceof Error?t.message:"Internal server error";return a.Z.json({success:!1,error:e,usage:{creditsUsed:0,costUSD:0,rateLimitRemaining:0}},{status:500})}}async function S(e,t,i,o){let{error:r}=await e.rpc("update_user_usage",{user_id:t,credits_used:i,cost_usd:o});r&&console.error("Failed to update user usage:",r)}async function C(e,t,i,o,r){let{error:n}=await e.from("seo_analyses").insert({user_id:t,keyword:i.keyword,location:i.location,industry:i.industry,content_type:i.contentType,seo_score:o.seoScore.total,difficulty:o.competitorAnalysis.difficulty,cost_usd:r,created_at:new Date().toISOString()});n&&console.error("Failed to log analysis usage:",n)}async function k(e){try{let{searchParams:t}=new URL(e.url),i=parseInt(t.get("limit")||"10"),o=parseInt(t.get("offset")||"0"),r=(0,g.jq)(),{data:{user:n},error:s}=await r.auth.getUser();if(s||!n)return a.Z.json({success:!1,error:"Unauthorized"},{status:401});let{data:c,error:l}=await r.from("seo_analyses").select(`
        id,
        keyword,
        location,
        industry,
        content_type,
        seo_score,
        difficulty,
        cost_usd,
        created_at
      `).eq("user_id",n.id).order("created_at",{ascending:!1}).range(o,o+i-1);if(l)return console.error("Failed to fetch analysis history:",l),a.Z.json({success:!1,error:"Failed to fetch analysis history"},{status:500});let{data:d}=await r.from("profiles").select("credits_remaining, monthly_spend, total_analyses").eq("id",n.id).single();return a.Z.json({success:!0,data:{analyses:c,stats:{creditsRemaining:d?.credits_remaining||0,monthlySpend:d?.monthly_spend||0,totalAnalyses:d?.total_analyses||0},pagination:{limit:i,offset:o,hasMore:c.length===i}}})}catch(e){return console.error("GET /api/seo/analyze error:",e),a.Z.json({success:!1,error:"Internal server error"},{status:500})}}c.Ry({success:c.O7(),data:c.Ry({keyword:c.Z_(),location:c.Z_(),competitorAnalysis:c.Ry({difficulty:c.Km(["low","medium","high","very-high"]),averageMetrics:c.Ry({wordCount:c.Rx(),keywordDensity:c.Rx(),domainAuthority:c.Rx()}),topCompetitors:c.IX(c.Ry({domain:c.Z_(),position:c.Rx(),title:c.Z_(),opportunityScore:c.Rx()}))}),seoScore:c.Ry({total:c.Rx(),improvementPotential:c.Rx()}),timeline:c.Ry({totalDuration:c.Rx(),steps:c.IX(c.Ry({step:c.Z_(),duration:c.Rx(),status:c.Km(["completed","failed","skipped"])}))})}).optional(),error:c.Z_().optional(),usage:c.Ry({creditsUsed:c.Rx(),costUSD:c.Rx(),rateLimitRemaining:c.Rx()}).optional()});let v=new r.AppRouteRouteModule({definition:{kind:n.x.APP_ROUTE,page:"/api/seo/analyze/route",pathname:"/api/seo/analyze",filename:"route",bundlePath:"app/api/seo/analyze/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/seo/analyze/route.ts",nextConfigOutput:"",userland:o}),{requestAsyncStorage:x,staticGenerationAsyncStorage:A,serverHooks:M,headerHooks:q,staticGenerationBailout:R}=v,D="/api/seo/analyze/route";function b(){return(0,s.patchFetch)({serverHooks:M,staticGenerationAsyncStorage:A})}},9279:(e,t,i)=>{i.d(t,{dZ:()=>s,er:()=>n,sy:()=>a});class o{async get(e){let t=this.store.get(e);return t?t.resetTime<Date.now()?(this.store.delete(e),null):t:null}async increment(e,t){let i=Date.now(),o=await this.get(e);if(o&&o.resetTime>i)return o.count++,this.store.set(e,o),o;let r={count:1,resetTime:i+t};return this.store.set(e,r),r}async reset(e){this.store.delete(e)}cleanup(){let e=Date.now();for(let[t,i]of this.store.entries())i.resetTime<e&&this.store.delete(t)}constructor(){this.store=new Map}}class r{constructor(e){this.subscriptionLimits={free:{daily:10,monthly:100,concurrent:1,apiCalls:50},pro:{daily:100,monthly:2e3,concurrent:3,apiCalls:1e3},enterprise:{daily:1e3,monthly:2e4,concurrent:10,apiCalls:1e4}},this.store=new o,this.defaultRule=e||{windowMs:9e5,maxRequests:100},setInterval(()=>this.store.cleanup(),3e5)}async checkLimit(e,t){let i=t||this.defaultRule,o=i.keyGenerator?.(e)||`rate_limit:${e}`,r=await this.store.increment(o,i.windowMs),n=Math.max(0,i.maxRequests-r.count),s=r.count<=i.maxRequests;return{allowed:s,remaining:n,resetTime:r.resetTime,totalHits:r.count,retryAfter:s?void 0:Math.ceil((r.resetTime-Date.now())/1e3)}}async checkSubscriptionLimit(e,t,i){let o;let r=(this.subscriptionLimits[t]||this.subscriptionLimits.free)[i];switch(i){case"daily":o=864e5;break;case"monthly":o=2592e6;break;case"concurrent":o=6e4;break;default:o=36e5}let n=`subscription_limit:${e}:${i}`,s=await this.store.increment(n,o),a=Math.max(0,r-s.count),c=s.count<=r;return{allowed:c,remaining:a,resetTime:s.resetTime,totalHits:s.count,retryAfter:c?void 0:Math.ceil((s.resetTime-Date.now())/1e3)}}async checkApiLimit(e,t,i){let o={groq:{free:{requests:5,windowMs:6e4},pro:{requests:30,windowMs:6e4},enterprise:{requests:100,windowMs:6e4}},serper:{free:{requests:10,windowMs:6e4},pro:{requests:50,windowMs:6e4},enterprise:{requests:200,windowMs:6e4}},openai:{free:{requests:3,windowMs:6e4},pro:{requests:20,windowMs:6e4},enterprise:{requests:60,windowMs:6e4}}},r=o[t]?.[i]||o[t]?.free;if(!r)throw Error(`No limits defined for API: ${t}`);let n=`api_limit:${e}:${t}`,s=await this.store.increment(n,r.windowMs),a=Math.max(0,r.requests-s.count),c=s.count<=r.requests;return{allowed:c,remaining:a,resetTime:s.resetTime,totalHits:s.count,retryAfter:c?void 0:Math.ceil((s.resetTime-Date.now())/1e3)}}async checkCostLimit(e,t,i){let o={free:5,pro:50,enterprise:500},r=o[t]||o.free,n=`cost_limit:${e}:monthly`,s=Date.now(),a=new Date(s);a.setDate(1),a.setHours(0,0,0,0);let c=`${n}:${a.getTime()}`,l=await this.store.get(c),d=(l?l.count/100:0)+i,u=d<=r;if(u){let e=new Date(a);e.setMonth(e.getMonth()+1),await this.store.increment(c,e.getTime()-s)}return{allowed:u,remainingBudget:Math.max(0,r-d),totalSpent:d}}async resetLimit(e,t){if(t){let i=`${t}:${e}`;await this.store.reset(i)}else for(let t of[`rate_limit:${e}`,`subscription_limit:${e}:*`,`api_limit:${e}:*`,`cost_limit:${e}:*`])await this.store.reset(t)}async getUsageStats(e){return{rateLimit:{},subscriptionLimits:{},apiLimits:{},costUsage:{}}}createMiddleware(e){return async t=>{let i=await this.checkLimit(t,e);if(!i.allowed){let e=Error("Rate limit exceeded");throw e.statusCode=429,e.retryAfter=i.retryAfter,e.resetTime=i.resetTime,e}return{"X-RateLimit-Limit":e?.maxRequests||this.defaultRule.maxRequests,"X-RateLimit-Remaining":i.remaining,"X-RateLimit-Reset":new Date(i.resetTime).toISOString()}}}}async function n(e,t,i,o){let n=new r,s=await n.checkApiLimit(e,i,t);if(!s.allowed){let e=Error(`${i} API rate limit exceeded`);throw e.statusCode=429,e.retryAfter=s.retryAfter,e.resetTime=s.resetTime,e.remaining=s.remaining,e}return o()}async function s(e,t,i,o){let n=new r,s=await n.checkSubscriptionLimit(e,t,i);if(!s.allowed){let e=Error(`${i} subscription limit exceeded`);throw e.statusCode=429,e.retryAfter=s.retryAfter,e.resetTime=s.resetTime,e.remaining=s.remaining,e}return o()}async function a(e,t,i,o){let n=new r,s=await n.checkCostLimit(e,t,i);if(!s.allowed){let e=Error(`Monthly cost limit exceeded. Remaining budget: $${s.remainingBudget.toFixed(2)}`);throw e.statusCode=402,e.remainingBudget=s.remainingBudget,e.totalSpent=s.totalSpent,e}return o()}new r({windowMs:9e5,maxRequests:100}),new r({windowMs:6e4,maxRequests:10}),new r({windowMs:9e5,maxRequests:5})}};var t=require("../../../../webpack-runtime.js");t.C(e);var i=e=>t(t.s=e),o=t.X(0,[638,280,252,554,493,982,734],()=>i(5131));module.exports=o})();
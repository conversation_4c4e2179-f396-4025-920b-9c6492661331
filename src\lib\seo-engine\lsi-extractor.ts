// Advanced LSI Keyword Extraction Engine
// Semantic analysis and related keyword discovery system

interface LSIKeyword {
  keyword: string;
  relevanceScore: number;
  frequency: number;
  semanticDistance: number;
  category: 'primary' | 'secondary' | 'supporting' | 'entity';
  context: string[];
  variations: string[];
}

interface SemanticCluster {
  theme: string;
  keywords: LSI<PERSON>eyword[];
  strength: number;
  coverage: number;
}

interface LSIAnalysisResult {
  primaryKeyword: string;
  lsiKeywords: LSIKeyword[];
  semanticClusters: SemanticCluster[];
  entityKeywords: LSIKeyword[];
  recommendations: LSIRecommendation[];
  competitorGaps: string[];
}

interface LSIRecommendation {
  type: 'add' | 'optimize' | 'remove';
  keyword: string;
  priority: 'high' | 'medium' | 'low';
  reason: string;
  expectedImpact: number;
  placement: 'heading' | 'content' | 'meta' | 'alt';
}

interface CompetitorLSIData {
  url: string;
  keywords: LSIKeyword[];
  clusters: SemanticCluster[];
  coverage: number;
}

export class LSIExtractor {
  private primaryKeyword: string;
  private industry: string;
  private contentType: string;

  // Common LSI patterns by industry
  private industryPatterns: Record<string, string[]> = {
    technology: ['software', 'digital', 'innovation', 'solution', 'platform', 'system', 'development'],
    healthcare: ['medical', 'treatment', 'patient', 'care', 'health', 'clinical', 'therapy'],
    finance: ['financial', 'investment', 'banking', 'money', 'capital', 'market', 'economic'],
    education: ['learning', 'student', 'academic', 'course', 'training', 'knowledge', 'skill'],
    retail: ['product', 'customer', 'shopping', 'purchase', 'brand', 'quality', 'price'],
    'real-estate': ['property', 'home', 'house', 'location', 'market', 'investment', 'neighborhood'],
    automotive: ['vehicle', 'car', 'automotive', 'engine', 'performance', 'safety', 'maintenance'],
    travel: ['destination', 'vacation', 'hotel', 'flight', 'tourism', 'experience', 'booking'],
    legal: ['law', 'legal', 'attorney', 'court', 'case', 'rights', 'consultation'],
    marketing: ['brand', 'campaign', 'audience', 'strategy', 'promotion', 'engagement', 'conversion']
  };

  // Semantic relationship patterns
  private semanticPatterns = {
    synonyms: ['also known as', 'similar to', 'equivalent to', 'same as'],
    related: ['related to', 'associated with', 'connected to', 'linked with'],
    categories: ['type of', 'kind of', 'category of', 'classification of'],
    processes: ['process of', 'method of', 'way to', 'approach to'],
    benefits: ['benefit of', 'advantage of', 'value of', 'importance of'],
    problems: ['problem with', 'issue with', 'challenge of', 'difficulty with']
  };

  constructor(primaryKeyword: string, industry: string = 'general', contentType: string = 'general') {
    this.primaryKeyword = primaryKeyword.toLowerCase();
    this.industry = industry.toLowerCase();
    this.contentType = contentType.toLowerCase();
  }

  /**
   * Extract LSI keywords from content and competitor analysis
   */
  async extractLSIKeywords(
    content: string, 
    competitorData?: CompetitorLSIData[]
  ): Promise<LSIAnalysisResult> {
    // Extract keywords from content
    const contentKeywords = this.extractFromContent(content);
    
    // Generate semantic keywords
    const semanticKeywords = this.generateSemanticKeywords();
    
    // Extract entity keywords
    const entityKeywords = this.extractEntityKeywords(content);
    
    // Combine and score all keywords
    const allKeywords = this.combineAndScore([
      ...contentKeywords,
      ...semanticKeywords,
      ...entityKeywords
    ]);

    // Create semantic clusters
    const semanticClusters = this.createSemanticClusters(allKeywords);
    
    // Generate recommendations
    const recommendations = this.generateRecommendations(allKeywords, competitorData);
    
    // Find competitor gaps
    const competitorGaps = this.findCompetitorGaps(allKeywords, competitorData);

    return {
      primaryKeyword: this.primaryKeyword,
      lsiKeywords: allKeywords,
      semanticClusters,
      entityKeywords: entityKeywords,
      recommendations,
      competitorGaps
    };
  }

  /**
   * Extract keywords from content using NLP techniques
   */
  private extractFromContent(content: string): LSIKeyword[] {
    const keywords: LSIKeyword[] = [];
    const words = this.tokenizeContent(content);
    const phrases = this.extractPhrases(content);
    
    // Analyze word frequency and context
    const wordFreq = this.calculateWordFrequency(words);
    const phraseFreq = this.calculatePhraseFrequency(phrases);
    
    // Extract single-word keywords
    for (const [word, frequency] of Object.entries(wordFreq)) {
      if (this.isValidKeyword(word) && frequency > 1) {
        const relevanceScore = this.calculateRelevanceScore(word, content);
        const semanticDistance = this.calculateSemanticDistance(word, this.primaryKeyword);
        
        keywords.push({
          keyword: word,
          relevanceScore,
          frequency,
          semanticDistance,
          category: this.categorizeKeyword(word),
          context: this.extractContext(word, content),
          variations: this.findVariations(word)
        });
      }
    }

    // Extract phrase keywords
    for (const [phrase, frequency] of Object.entries(phraseFreq)) {
      if (this.isValidPhrase(phrase) && frequency > 1) {
        const relevanceScore = this.calculateRelevanceScore(phrase, content);
        const semanticDistance = this.calculateSemanticDistance(phrase, this.primaryKeyword);
        
        keywords.push({
          keyword: phrase,
          relevanceScore,
          frequency,
          semanticDistance,
          category: this.categorizeKeyword(phrase),
          context: this.extractContext(phrase, content),
          variations: this.findVariations(phrase)
        });
      }
    }

    return keywords.sort((a, b) => b.relevanceScore - a.relevanceScore);
  }

  /**
   * Generate semantic keywords based on industry and content type
   */
  private generateSemanticKeywords(): LSIKeyword[] {
    const keywords: LSIKeyword[] = [];
    const industryTerms = this.industryPatterns[this.industry] || [];
    
    // Generate industry-specific LSI keywords
    for (const term of industryTerms) {
      const combinedKeywords = [
        `${this.primaryKeyword} ${term}`,
        `${term} ${this.primaryKeyword}`,
        `best ${this.primaryKeyword} ${term}`,
        `${this.primaryKeyword} for ${term}`,
        `professional ${this.primaryKeyword} ${term}`
      ];

      for (const keyword of combinedKeywords) {
        keywords.push({
          keyword,
          relevanceScore: this.calculateSemanticRelevance(keyword),
          frequency: 0,
          semanticDistance: this.calculateSemanticDistance(keyword, this.primaryKeyword),
          category: 'secondary',
          context: [],
          variations: this.findVariations(keyword)
        });
      }
    }

    // Generate content-type specific keywords
    const contentTypeKeywords = this.generateContentTypeKeywords();
    keywords.push(...contentTypeKeywords);

    return keywords.filter(k => k.relevanceScore > 0.3);
  }

  /**
   * Extract entity keywords (people, places, organizations, etc.)
   */
  private extractEntityKeywords(content: string): LSIKeyword[] {
    const keywords: LSIKeyword[] = [];
    
    // Simple entity extraction patterns
    const entityPatterns = {
      organizations: /\b[A-Z][a-z]+ (?:Inc|LLC|Corp|Company|Corporation|Ltd|Limited)\b/g,
      locations: /\b[A-Z][a-z]+ (?:City|State|Country|County|Province)\b/g,
      brands: /\b[A-Z][a-z]*[A-Z][a-z]*\b/g,
      technologies: /\b[A-Z]{2,}\b/g
    };

    for (const [type, pattern] of Object.entries(entityPatterns)) {
      const matches = content.match(pattern) || [];
      
      for (const match of matches) {
        const cleanMatch = match.trim();
        if (cleanMatch.length > 2) {
          keywords.push({
            keyword: cleanMatch,
            relevanceScore: 0.7,
            frequency: (content.match(new RegExp(cleanMatch, 'gi')) || []).length,
            semanticDistance: this.calculateSemanticDistance(cleanMatch, this.primaryKeyword),
            category: 'entity',
            context: this.extractContext(cleanMatch, content),
            variations: []
          });
        }
      }
    }

    return keywords;
  }

  /**
   * Create semantic clusters from keywords
   */
  private createSemanticClusters(keywords: LSIKeyword[]): SemanticCluster[] {
    const clusters: SemanticCluster[] = [];
    const themes = this.identifyThemes(keywords);

    for (const theme of themes) {
      const themeKeywords = keywords.filter(k => 
        k.keyword.includes(theme) || 
        k.context.some(c => c.includes(theme))
      );

      if (themeKeywords.length > 2) {
        const strength = themeKeywords.reduce((sum, k) => sum + k.relevanceScore, 0) / themeKeywords.length;
        const coverage = themeKeywords.length / keywords.length;

        clusters.push({
          theme,
          keywords: themeKeywords,
          strength,
          coverage
        });
      }
    }

    return clusters.sort((a, b) => b.strength - a.strength);
  }

  /**
   * Generate LSI recommendations
   */
  private generateRecommendations(
    keywords: LSIKeyword[], 
    competitorData?: CompetitorLSIData[]
  ): LSIRecommendation[] {
    const recommendations: LSIRecommendation[] = [];

    // Recommend high-value missing keywords
    const highValueKeywords = keywords.filter(k => 
      k.relevanceScore > 0.7 && k.frequency === 0
    );

    for (const keyword of highValueKeywords.slice(0, 10)) {
      recommendations.push({
        type: 'add',
        keyword: keyword.keyword,
        priority: keyword.relevanceScore > 0.8 ? 'high' : 'medium',
        reason: `High semantic relevance (${Math.round(keyword.relevanceScore * 100)}%) but not present in content`,
        expectedImpact: Math.round(keyword.relevanceScore * 10),
        placement: this.recommendPlacement(keyword)
      });
    }

    // Recommend optimization for existing keywords
    const underOptimizedKeywords = keywords.filter(k => 
      k.frequency > 0 && k.frequency < 3 && k.relevanceScore > 0.6
    );

    for (const keyword of underOptimizedKeywords.slice(0, 5)) {
      recommendations.push({
        type: 'optimize',
        keyword: keyword.keyword,
        priority: 'medium',
        reason: `Present but underutilized (${keyword.frequency} occurrences)`,
        expectedImpact: Math.round(keyword.relevanceScore * 7),
        placement: this.recommendPlacement(keyword)
      });
    }

    return recommendations.sort((a, b) => b.expectedImpact - a.expectedImpact);
  }

  /**
   * Find gaps compared to competitors
   */
  private findCompetitorGaps(
    keywords: LSIKeyword[], 
    competitorData?: CompetitorLSIData[]
  ): string[] {
    if (!competitorData || competitorData.length === 0) return [];

    const myKeywords = new Set(keywords.map(k => k.keyword.toLowerCase()));
    const competitorKeywords = new Set<string>();

    // Collect all competitor keywords
    for (const competitor of competitorData) {
      for (const keyword of competitor.keywords) {
        if (keyword.relevanceScore > 0.5) {
          competitorKeywords.add(keyword.keyword.toLowerCase());
        }
      }
    }

    // Find keywords that competitors have but we don't
    const gaps: string[] = [];
    for (const keyword of competitorKeywords) {
      if (!myKeywords.has(keyword)) {
        gaps.push(keyword);
      }
    }

    return gaps.slice(0, 20); // Return top 20 gaps
  }

  // Helper methods
  private tokenizeContent(content: string): string[] {
    return content
      .toLowerCase()
      .replace(/[^\w\s]/g, ' ')
      .split(/\s+/)
      .filter(word => word.length > 2);
  }

  private extractPhrases(content: string): string[] {
    const phrases: string[] = [];
    const sentences = content.split(/[.!?]+/);

    for (const sentence of sentences) {
      const words = sentence.trim().split(/\s+/);
      
      // Extract 2-3 word phrases
      for (let i = 0; i < words.length - 1; i++) {
        if (i < words.length - 1) {
          phrases.push(`${words[i]} ${words[i + 1]}`.toLowerCase());
        }
        if (i < words.length - 2) {
          phrases.push(`${words[i]} ${words[i + 1]} ${words[i + 2]}`.toLowerCase());
        }
      }
    }

    return phrases;
  }

  private calculateWordFrequency(words: string[]): Record<string, number> {
    const freq: Record<string, number> = {};
    for (const word of words) {
      freq[word] = (freq[word] || 0) + 1;
    }
    return freq;
  }

  private calculatePhraseFrequency(phrases: string[]): Record<string, number> {
    const freq: Record<string, number> = {};
    for (const phrase of phrases) {
      freq[phrase] = (freq[phrase] || 0) + 1;
    }
    return freq;
  }

  private isValidKeyword(word: string): boolean {
    const stopWords = ['the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must', 'can', 'this', 'that', 'these', 'those'];
    return word.length > 2 && !stopWords.includes(word) && /^[a-z]+$/.test(word);
  }

  private isValidPhrase(phrase: string): boolean {
    const words = phrase.split(' ');
    return words.length >= 2 && words.length <= 4 && words.every(w => this.isValidKeyword(w));
  }

  private calculateRelevanceScore(keyword: string, content: string): number {
    // Simplified relevance calculation
    const keywordWords = keyword.split(' ');
    const primaryWords = this.primaryKeyword.split(' ');
    
    let score = 0;
    
    // Boost if contains primary keyword words
    for (const word of keywordWords) {
      if (primaryWords.includes(word)) {
        score += 0.3;
      }
    }
    
    // Boost if industry-related
    const industryTerms = this.industryPatterns[this.industry] || [];
    for (const term of industryTerms) {
      if (keyword.includes(term)) {
        score += 0.2;
      }
    }
    
    // Boost based on frequency in content
    const frequency = (content.toLowerCase().match(new RegExp(keyword, 'g')) || []).length;
    score += Math.min(0.3, frequency * 0.05);
    
    return Math.min(1, score);
  }

  private calculateSemanticDistance(keyword: string, primary: string): number {
    // Simplified semantic distance calculation
    const keywordWords = new Set(keyword.toLowerCase().split(' '));
    const primaryWords = new Set(primary.toLowerCase().split(' '));
    
    const intersection = new Set([...keywordWords].filter(x => primaryWords.has(x)));
    const union = new Set([...keywordWords, ...primaryWords]);
    
    return 1 - (intersection.size / union.size);
  }

  private categorizeKeyword(keyword: string): 'primary' | 'secondary' | 'supporting' | 'entity' {
    if (keyword.includes(this.primaryKeyword)) return 'primary';
    
    const industryTerms = this.industryPatterns[this.industry] || [];
    if (industryTerms.some(term => keyword.includes(term))) return 'secondary';
    
    if (/^[A-Z]/.test(keyword)) return 'entity';
    
    return 'supporting';
  }

  private extractContext(keyword: string, content: string): string[] {
    const sentences = content.split(/[.!?]+/);
    const contexts: string[] = [];
    
    for (const sentence of sentences) {
      if (sentence.toLowerCase().includes(keyword.toLowerCase())) {
        contexts.push(sentence.trim());
      }
    }
    
    return contexts.slice(0, 3); // Return up to 3 contexts
  }

  private findVariations(keyword: string): string[] {
    const variations: string[] = [];
    
    // Add plural/singular variations
    if (keyword.endsWith('s')) {
      variations.push(keyword.slice(0, -1));
    } else {
      variations.push(keyword + 's');
    }
    
    // Add common variations
    variations.push(keyword.replace(/ing$/, ''));
    variations.push(keyword.replace(/ed$/, ''));
    
    return variations.filter(v => v !== keyword && v.length > 2);
  }

  private calculateSemanticRelevance(keyword: string): number {
    // Calculate relevance based on semantic patterns
    let score = 0.5; // Base score
    
    if (keyword.includes(this.primaryKeyword)) score += 0.3;
    
    const industryTerms = this.industryPatterns[this.industry] || [];
    if (industryTerms.some(term => keyword.includes(term))) score += 0.2;
    
    return Math.min(1, score);
  }

  private generateContentTypeKeywords(): LSIKeyword[] {
    const keywords: LSIKeyword[] = [];
    
    const contentTypePatterns: Record<string, string[]> = {
      service: ['professional', 'expert', 'quality', 'reliable', 'experienced'],
      blog: ['guide', 'tips', 'how to', 'best practices', 'tutorial'],
      product: ['features', 'benefits', 'specifications', 'reviews', 'comparison'],
      landing: ['solution', 'results', 'proven', 'guaranteed', 'effective']
    };
    
    const patterns = contentTypePatterns[this.contentType] || [];
    
    for (const pattern of patterns) {
      keywords.push({
        keyword: `${pattern} ${this.primaryKeyword}`,
        relevanceScore: 0.6,
        frequency: 0,
        semanticDistance: 0.3,
        category: 'supporting',
        context: [],
        variations: []
      });
    }
    
    return keywords;
  }

  private identifyThemes(keywords: LSIKeyword[]): string[] {
    const themes = new Set<string>();
    
    for (const keyword of keywords) {
      const words = keyword.keyword.split(' ');
      for (const word of words) {
        if (word.length > 3) {
          themes.add(word);
        }
      }
    }
    
    return Array.from(themes).slice(0, 10);
  }

  private recommendPlacement(keyword: LSIKeyword): 'heading' | 'content' | 'meta' | 'alt' {
    if (keyword.category === 'primary') return 'heading';
    if (keyword.category === 'entity') return 'alt';
    if (keyword.relevanceScore > 0.8) return 'meta';
    return 'content';
  }
}

export const lsiExtractor = new LSIExtractor('', 'general', 'general');

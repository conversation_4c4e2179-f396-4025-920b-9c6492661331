// Comprehensive SEO Engine Test Suite
// Enterprise-grade testing for all SEO components

import { describe, it, expect, beforeEach } from '@jest/globals';
import {
  SEOEngine,
  KeywordDensityAnalyzer,
  HeadingAnalyzer,
  LSIExtractor,
  ContentOptimizer,
  CompetitorAnalyzer,
  QualityScorer
} from '../lib/seo-engine';

describe('SEO Engine Test Suite', () => {
  let seoEngine: SEOEngine;
  const primaryKeyword = 'SEO services';
  const secondaryKeywords = ['search engine optimization', 'digital marketing'];
  const lsiKeywords = ['SERP', 'rankings', 'organic traffic', 'keyword research'];
  const industry = 'technology';
  const contentType = 'service';

  beforeEach(() => {
    seoEngine = new SEOEngine(
      primaryKeyword,
      secondaryKeywords,
      lsiKeywords,
      industry,
      contentType,
      'business'
    );
  });

  describe('Keyword Density Analyzer', () => {
    let analyzer: KeywordDensityAnalyzer;

    beforeEach(() => {
      analyzer = new KeywordDensityAnalyzer(primaryKeyword, secondaryKeywords);
    });

    it('should calculate keyword density correctly', () => {
      const content = `
        SEO services are essential for businesses. Our SEO services help improve rankings.
        Search engine optimization is crucial for online success. Digital marketing includes SEO.
      `;

      const result = analyzer.analyzeKeywordDensity(content);

      expect(result.primaryKeyword.density).toBeGreaterThan(0);
      expect(result.primaryKeyword.count).toBe(2);
      expect(result.score).toBeGreaterThan(0);
      expect(result.recommendations).toBeDefined();
    });

    it('should identify over-optimization', () => {
      const content = `
        SEO services SEO services SEO services SEO services SEO services.
        SEO services are the best SEO services for SEO services optimization.
        Our SEO services provide excellent SEO services results.
      `;

      const result = analyzer.analyzeKeywordDensity(content);

      expect(result.primaryKeyword.density).toBeGreaterThan(5);
      expect(result.recommendations.some(r => r.type === 'reduce')).toBe(true);
    });

    it('should handle empty content gracefully', () => {
      const result = analyzer.analyzeKeywordDensity('');

      expect(result.primaryKeyword.density).toBe(0);
      expect(result.primaryKeyword.count).toBe(0);
      expect(result.score).toBe(0);
    });
  });

  describe('Heading Analyzer', () => {
    let analyzer: HeadingAnalyzer;

    beforeEach(() => {
      analyzer = new HeadingAnalyzer(primaryKeyword, secondaryKeywords, lsiKeywords);
    });

    it('should analyze heading structure correctly', () => {
      const content = `
        <h1>Professional SEO Services for Your Business</h1>
        <h2>Search Engine Optimization Strategies</h2>
        <h3>Keyword Research and Analysis</h3>
        <h2>Digital Marketing Solutions</h2>
        <h3>SERP Rankings Improvement</h3>
      `;

      const result = analyzer.analyzeHeadings(content);

      expect(result.headings).toHaveLength(5);
      expect(result.hierarchy.isValid).toBe(true);
      expect(result.optimization.score).toBeGreaterThan(0);
      expect(result.headings[0].level).toBe(1);
      expect(result.headings[0].text).toContain('SEO Services');
    });

    it('should detect hierarchy issues', () => {
      const content = `
        <h1>SEO Services</h1>
        <h4>Skipped H2 and H3</h4>
        <h2>Back to H2</h2>
      `;

      const result = analyzer.analyzeHeadings(content);

      expect(result.hierarchy.isValid).toBe(false);
      expect(result.hierarchy.issues).toContain(
        expect.stringContaining('Heading level jump')
      );
    });

    it('should identify missing H1', () => {
      const content = `
        <h2>Search Engine Optimization</h2>
        <h3>Digital Marketing</h3>
      `;

      const result = analyzer.analyzeHeadings(content);

      expect(result.hierarchy.isValid).toBe(false);
      expect(result.hierarchy.issues).toContain('Missing H1 tag - critical for SEO');
    });
  });

  describe('LSI Extractor', () => {
    let extractor: LSIExtractor;

    beforeEach(() => {
      extractor = new LSIExtractor(primaryKeyword, industry, contentType);
    });

    it('should extract LSI keywords from content', async () => {
      const content = `
        SEO services help businesses improve their search engine rankings.
        Our digital marketing team specializes in organic traffic growth.
        We provide comprehensive keyword research and SERP analysis.
        Search engine optimization requires technical expertise and strategy.
      `;

      const result = await extractor.extractLSIKeywords(content);

      expect(result.lsiKeywords).toBeDefined();
      expect(result.lsiKeywords.length).toBeGreaterThan(0);
      expect(result.semanticClusters).toBeDefined();
      expect(result.recommendations).toBeDefined();
    });

    it('should generate semantic clusters', async () => {
      const content = `
        SEO services include keyword research, content optimization, and link building.
        Search engine optimization helps improve website rankings and organic traffic.
        Digital marketing encompasses SEO, PPC, social media, and content marketing.
      `;

      const result = await extractor.extractLSIKeywords(content);

      expect(result.semanticClusters.length).toBeGreaterThan(0);
      expect(result.semanticClusters[0]).toHaveProperty('theme');
      expect(result.semanticClusters[0]).toHaveProperty('keywords');
      expect(result.semanticClusters[0]).toHaveProperty('strength');
    });
  });

  describe('Content Optimizer', () => {
    let optimizer: ContentOptimizer;

    beforeEach(() => {
      optimizer = new ContentOptimizer(
        primaryKeyword,
        secondaryKeywords,
        lsiKeywords,
        industry,
        contentType,
        'business'
      );
    });

    it('should optimize content structure', async () => {
      const content = `
        SEO services are important for businesses. They help improve rankings.
        Search engine optimization involves many techniques.
      `;

      const result = await optimizer.optimizeContent(content);

      expect(result.score).toBeGreaterThan(0);
      expect(result.improvements).toBeDefined();
      expect(result.optimizedContent).toBeDefined();
      expect(result.metadata).toBeDefined();
      expect(result.performance).toBeDefined();
    });

    it('should generate optimized metadata', async () => {
      const content = `
        Professional SEO services help businesses improve their search engine rankings.
        Our team provides comprehensive search engine optimization solutions.
      `;

      const result = await optimizer.optimizeContent(content);

      expect(result.metadata.title).toContain(primaryKeyword);
      expect(result.metadata.description).toBeDefined();
      expect(result.metadata.keywords).toContain(primaryKeyword);
      expect(result.metadata.schema).toBeDefined();
    });
  });

  describe('Competitor Analyzer', () => {
    let analyzer: CompetitorAnalyzer;

    beforeEach(() => {
      analyzer = new CompetitorAnalyzer(primaryKeyword, secondaryKeywords, industry);
    });

    it('should analyze competitor data', () => {
      const competitorData = [
        {
          url: 'https://competitor1.com',
          title: 'SEO Services - Competitor 1',
          metaDescription: 'Professional SEO services',
          wordCount: 1500,
          headingStructure: {
            h1Count: 1,
            h2Count: 5,
            h3Count: 8,
            h4Count: 2,
            h5Count: 0,
            h6Count: 0,
            totalHeadings: 16,
            keywordOptimizedHeadings: 6,
            averageHeadingLength: 45
          },
          keywordMetrics: {
            primaryKeywordDensity: 2.1,
            secondaryKeywordDensity: 1.5,
            lsiKeywordCount: 12,
            keywordVariations: 8,
            keywordInTitle: true,
            keywordInMetaDescription: true,
            keywordInH1: true,
            keywordInFirstParagraph: true
          },
          technicalSEO: {
            loadTime: 2.3,
            mobileOptimized: true,
            httpsEnabled: true,
            schemaMarkup: ['Organization', 'Service'],
            internalLinks: 15,
            externalLinks: 8,
            imageCount: 6,
            imagesWithAlt: 6,
            canonicalTag: true,
            metaRobots: 'index,follow'
          },
          contentQuality: {
            readabilityScore: 75,
            sentenceLength: 18,
            paragraphLength: 85,
            uniqueWords: 450,
            contentDepth: 8,
            expertiseSignals: 5,
            trustSignals: 7,
            freshnessScore: 85
          },
          performanceMetrics: {
            estimatedTraffic: 5000,
            estimatedRanking: 3,
            clickThroughRate: 0.15,
            bounceRate: 0.35,
            timeOnPage: 180,
            socialShares: 25
          },
          backlinks: {
            totalBacklinks: 150,
            domainAuthority: 65,
            referringDomains: 45,
            qualityScore: 7.5
          },
          socialSignals: {
            facebookShares: 12,
            twitterShares: 8,
            linkedinShares: 5,
            totalShares: 25
          }
        }
      ];

      const result = analyzer.analyzeCompetitors(competitorData);

      expect(result.averages).toBeDefined();
      expect(result.topPerformers).toBeDefined();
      expect(result.contentGaps).toBeDefined();
      expect(result.opportunities).toBeDefined();
      expect(result.benchmarkTargets).toBeDefined();
    });
  });

  describe('Quality Scorer', () => {
    let scorer: QualityScorer;

    beforeEach(() => {
      scorer = new QualityScorer(primaryKeyword, industry, contentType, 'business');
    });

    it('should analyze content quality with E-E-A-T', () => {
      const content = `
        <h1>Professional SEO Services by Certified Experts</h1>
        
        Our team has 10 years of experience providing SEO services to businesses.
        We are certified Google Partners with proven expertise in search engine optimization.
        
        <h2>Our Experience and Expertise</h2>
        We have helped over 500 clients improve their search rankings through our proven SEO strategies.
        Our case studies demonstrate real results and measurable improvements.
        
        <h2>Why Trust Our SEO Services</h2>
        We provide transparent reporting, guarantee our work, and maintain strict privacy policies.
        Contact <NAME_EMAIL> or call (555) 123-4567 for a consultation.
        
        Customer testimonials and reviews consistently rate our services 5 stars.
      `;

      const result = scorer.analyzeQuality(content);

      expect(result.score.overall).toBeGreaterThan(0);
      expect(result.score.experience).toBeGreaterThan(0);
      expect(result.score.expertise).toBeGreaterThan(0);
      expect(result.score.authoritativeness).toBeGreaterThan(0);
      expect(result.score.trustworthiness).toBeGreaterThan(0);
      expect(result.eeatCompliance).toBeDefined();
      expect(result.recommendations).toBeDefined();
    });

    it('should identify E-E-A-T signals', () => {
      const content = `
        I am a certified SEO expert with 15 years of experience.
        I have been featured in Search Engine Journal and quoted by industry leaders.
        My clients include Fortune 500 companies and I guarantee results.
        Contact <NAME_EMAIL> for professional consultation.
      `;

      const result = scorer.analyzeQuality(content);

      expect(result.eeatCompliance.experience.indicators.length).toBeGreaterThan(0);
      expect(result.eeatCompliance.expertise.indicators.length).toBeGreaterThan(0);
      expect(result.eeatCompliance.authoritativeness.indicators.length).toBeGreaterThan(0);
      expect(result.eeatCompliance.trustworthiness.indicators.length).toBeGreaterThan(0);
    });
  });

  describe('SEO Engine Integration', () => {
    it('should perform comprehensive content analysis', async () => {
      const content = `
        <h1>Professional SEO Services for Business Growth</h1>
        
        <h2>Expert Search Engine Optimization</h2>
        Our SEO services help businesses improve their search engine rankings and organic traffic.
        We provide comprehensive digital marketing solutions including keyword research and SERP analysis.
        
        <h2>Proven SEO Strategies</h2>
        With 10 years of experience, our certified team delivers measurable results.
        We have helped over 1000 clients achieve top rankings through our proven methodologies.
        
        <h3>Our SEO Process</h3>
        1. Comprehensive keyword research and competitor analysis
        2. On-page optimization and content strategy
        3. Technical SEO improvements and site optimization
        4. Link building and authority development
        5. Ongoing monitoring and performance reporting
        
        Contact us today for a free SEO consultation and see how we can help your business grow.
      `;

      const result = await seoEngine.analyzeContent(content);

      expect(result.keywordAnalysis).toBeDefined();
      expect(result.headingAnalysis).toBeDefined();
      expect(result.lsiAnalysis).toBeDefined();
      expect(result.qualityAnalysis).toBeDefined();
      expect(result.optimizationResult).toBeDefined();
    });

    it('should generate comprehensive SEO report', async () => {
      const content = `
        <h1>SEO Services</h1>
        <h2>Search Engine Optimization</h2>
        Professional SEO services for digital marketing success.
      `;

      const analysisResults = await seoEngine.analyzeContent(content);
      const report = seoEngine.generateSEOReport(analysisResults);

      expect(report.overallScore).toBeGreaterThan(0);
      expect(report.recommendations).toBeDefined();
      expect(report.strengths).toBeDefined();
      expect(report.weaknesses).toBeDefined();
      expect(report.nextSteps).toBeDefined();
    });

    it('should perform quick health check', () => {
      const content = `
        <h1>SEO Services for Business</h1>
        <h2>Professional Search Engine Optimization</h2>
        <img src="seo-image.jpg" alt="SEO services illustration">
        Our comprehensive SEO services help businesses improve their search engine rankings.
        We provide expert digital marketing solutions with proven results.
      `;

      const healthCheck = seoEngine.quickHealthCheck(content);

      expect(healthCheck.score).toBeGreaterThan(0);
      expect(healthCheck.issues).toBeDefined();
      expect(healthCheck.quickFixes).toBeDefined();
    });

    it('should handle edge cases gracefully', async () => {
      // Test with minimal content
      const minimalContent = 'SEO';
      const result = await seoEngine.analyzeContent(minimalContent);

      expect(result).toBeDefined();
      expect(result.keywordAnalysis).toBeDefined();
      expect(result.optimizationResult).toBeDefined();

      // Test with empty content
      const emptyResult = await seoEngine.analyzeContent('');
      expect(emptyResult).toBeDefined();

      // Test health check with poor content
      const poorContent = 'This content has no keywords or structure.';
      const healthCheck = seoEngine.quickHealthCheck(poorContent);
      expect(healthCheck.score).toBeLessThan(50);
      expect(healthCheck.issues.length).toBeGreaterThan(0);
    });
  });

  describe('Performance and Reliability', () => {
    it('should handle large content efficiently', async () => {
      const largeContent = `
        <h1>Comprehensive SEO Services Guide</h1>
        ${Array(100).fill(`
          <h2>SEO Section</h2>
          <p>Professional SEO services help businesses improve search engine rankings.
          Our digital marketing team provides comprehensive search engine optimization solutions.
          We specialize in keyword research, content optimization, and technical SEO improvements.</p>
        `).join('')}
      `;

      const startTime = Date.now();
      const result = await seoEngine.analyzeContent(largeContent);
      const endTime = Date.now();

      expect(result).toBeDefined();
      expect(endTime - startTime).toBeLessThan(5000); // Should complete within 5 seconds
    });

    it('should maintain consistency across multiple runs', async () => {
      const content = `
        <h1>SEO Services</h1>
        <h2>Search Engine Optimization</h2>
        Professional SEO services for business growth.
      `;

      const result1 = await seoEngine.analyzeContent(content);
      const result2 = await seoEngine.analyzeContent(content);

      expect(result1.keywordAnalysis.score).toBe(result2.keywordAnalysis.score);
      expect(result1.qualityAnalysis.score.overall).toBe(result2.qualityAnalysis.score.overall);
    });
  });
});

// Export test utilities for other test files
export const createMockCompetitorData = () => ({
  url: 'https://example.com',
  title: 'Test SEO Services',
  metaDescription: 'Test description',
  wordCount: 1000,
  headingStructure: {
    h1Count: 1,
    h2Count: 3,
    h3Count: 5,
    h4Count: 0,
    h5Count: 0,
    h6Count: 0,
    totalHeadings: 9,
    keywordOptimizedHeadings: 4,
    averageHeadingLength: 40
  },
  keywordMetrics: {
    primaryKeywordDensity: 1.5,
    secondaryKeywordDensity: 1.0,
    lsiKeywordCount: 8,
    keywordVariations: 5,
    keywordInTitle: true,
    keywordInMetaDescription: true,
    keywordInH1: true,
    keywordInFirstParagraph: true
  },
  technicalSEO: {
    loadTime: 2.0,
    mobileOptimized: true,
    httpsEnabled: true,
    schemaMarkup: ['Article'],
    internalLinks: 10,
    externalLinks: 5,
    imageCount: 3,
    imagesWithAlt: 3,
    canonicalTag: true,
    metaRobots: 'index,follow'
  },
  contentQuality: {
    readabilityScore: 70,
    sentenceLength: 15,
    paragraphLength: 80,
    uniqueWords: 300,
    contentDepth: 6,
    expertiseSignals: 3,
    trustSignals: 4,
    freshnessScore: 80
  },
  performanceMetrics: {
    estimatedTraffic: 1000,
    estimatedRanking: 5,
    clickThroughRate: 0.10,
    bounceRate: 0.40,
    timeOnPage: 120,
    socialShares: 10
  },
  backlinks: {
    totalBacklinks: 50,
    domainAuthority: 50,
    referringDomains: 20,
    qualityScore: 6.0
  },
  socialSignals: {
    facebookShares: 5,
    twitterShares: 3,
    linkedinShares: 2,
    totalShares: 10
  }
});

export const createTestContent = (keyword: string = 'SEO services') => `
  <h1>Professional ${keyword} for Business Growth</h1>
  
  <h2>Expert Search Engine Optimization</h2>
  Our ${keyword} help businesses improve their search engine rankings.
  We provide comprehensive digital marketing solutions.
  
  <h2>Why Choose Our ${keyword}</h2>
  With years of experience, our certified team delivers results.
  We guarantee improved rankings and increased organic traffic.
  
  <img src="seo-image.jpg" alt="${keyword} illustration">
  
  Contact us today for a free consultation.
`;

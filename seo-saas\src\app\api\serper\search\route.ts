// Serper SERP Analysis API Route
// Direct access to Serper search and competitor analysis service

import { NextRequest, NextResponse } from 'next/server';
import { z } from 'zod';
import { serperService } from '@/lib/services/serper';
import { withRateLimit, withSubscriptionLimit, withCostLimit } from '@/lib/rate-limiter';
import { createSupabaseServer } from '@/lib/supabase';

// Request validation schema for search
const serperSearchSchema = z.object({
  query: z.string().min(1, 'Query is required').max(200, 'Query too long'),
  location: z.string().max(100, 'Location too long').optional(),
  country: z.string().length(2, 'Country must be 2-letter code').optional(),
  language: z.string().length(2, 'Language must be 2-letter code').optional(),
  device: z.enum(['desktop', 'mobile']).default('desktop'),
  num: z.number().min(1).max(100).default(10),
  page: z.number().min(1).max(10).optional(),
  type: z.enum(['search', 'images', 'videos', 'news', 'shopping']).default('search'),
  safe: z.enum(['active', 'off']).optional(),
  autocorrect: z.boolean().default(true),
});

// Request validation schema for competitor analysis
const competitorAnalysisSchema = z.object({
  query: z.string().min(1, 'Query is required').max(200, 'Query too long'),
  location: z.string().max(100, 'Location too long').optional().default(''),
  analyzeTop: z.number().min(1).max(10).default(5),
  includeContent: z.boolean().default(true),
  includeMetrics: z.boolean().default(true),
});

export async function POST(request: NextRequest) {
  const startTime = Date.now();

  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'search';

    // Parse and validate request body based on action
    const body = await request.json();
    
    let validatedData: any;
    let estimatedCost: number;

    if (action === 'analyze') {
      validatedData = competitorAnalysisSchema.parse(body);
      estimatedCost = 0.05; // Higher cost for analysis
    } else {
      validatedData = serperSearchSchema.parse(body);
      estimatedCost = 0.01; // Lower cost for simple search
    }

    // Get user session
    const supabase = createSupabaseServer();
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    if (authError || !user) {
      return NextResponse.json(
        { success: false, error: 'Unauthorized' },
        { status: 401 }
      );
    }

    // Get user subscription tier
    const { data: profile } = await supabase
      .from('profiles')
      .select('subscription_tier, credits_remaining')
      .eq('id', user.id)
      .single();

    const subscriptionTier = profile?.subscription_tier || 'free';

    // Check subscription limits
    try {
      await withSubscriptionLimit(
        user.id,
        subscriptionTier,
        'apiCalls',
        async () => ({ success: true })
      );
    } catch (error: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          code: 'SUBSCRIPTION_LIMIT_EXCEEDED'
        },
        { status: 429 }
      );
    }

    // Check cost limits
    try {
      await withCostLimit(
        user.id,
        subscriptionTier,
        estimatedCost,
        async () => ({ success: true })
      );
    } catch (error: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          code: 'COST_LIMIT_EXCEEDED'
        },
        { status: 402 }
      );
    }

    // Check API rate limits
    try {
      await withRateLimit(
        user.id,
        subscriptionTier,
        'serper',
        async () => ({ success: true })
      );
    } catch (error: any) {
      return NextResponse.json(
        { 
          success: false, 
          error: error.message,
          code: 'RATE_LIMIT_EXCEEDED'
        },
        { status: 429 }
      );
    }

    let result: any;
    let creditsUsed: number;

    if (action === 'analyze') {
      // Perform competitor analysis
      console.log('Performing competitor analysis for user:', user.id, {
        query: validatedData.query,
        location: validatedData.location,
        analyzeTop: validatedData.analyzeTop,
      });

      result = await serperService.analyzeCompetitors(
        validatedData.query,
        validatedData.location,
        {
          analyzeTop: validatedData.analyzeTop,
          includeContent: validatedData.includeContent,
          includeMetrics: validatedData.includeMetrics,
        },
        user.id
      );

      creditsUsed = 3; // Higher credit cost for analysis
      
    } else {
      // Perform regular search
      console.log('Performing SERP search for user:', user.id, {
        query: validatedData.query,
        location: validatedData.location,
        device: validatedData.device,
      });

      result = await serperService.search(validatedData, user.id);
      creditsUsed = 1; // Lower credit cost for search
    }

    // Update user credits
    await updateUserCredits(supabase, user.id, creditsUsed, estimatedCost);

    const responseTime = Date.now() - startTime;

    console.log('Serper operation completed', {
      userId: user.id,
      action,
      query: validatedData.query,
      responseTime,
      resultsCount: action === 'analyze' ? result.totalResults : result.organic?.length,
    });

    // Return response based on action
    if (action === 'analyze') {
      return NextResponse.json({
        success: true,
        data: {
          query: result.query,
          location: result.location,
          difficulty: result.difficulty,
          totalResults: result.totalResults,
          averageMetrics: {
            wordCount: result.averageWordCount,
            headingStructure: result.averageHeadings,
            keywordDensity: result.averageKeywordDensity,
          },
          topCompetitors: result.topCompetitors.map((comp: any) => ({
            position: comp.position,
            domain: comp.domain,
            title: comp.title,
            url: comp.url,
            snippet: comp.snippet,
            wordCount: comp.wordCount,
            keywordDensity: comp.keywordDensity,
            domainAuthority: comp.domainAuthority,
            contentScore: comp.contentScore,
          })),
          contentGaps: result.contentGaps,
          searchFeatures: result.searchFeatures,
          commonKeywords: result.commonKeywords,
        },
        usage: {
          creditsUsed,
          costUSD: estimatedCost,
          responseTime,
        },
      }, {
        status: 200,
        headers: {
          'X-Response-Time': responseTime.toString(),
          'X-Difficulty': result.difficulty,
          'X-Credits-Used': creditsUsed.toString(),
        },
      });
    } else {
      return NextResponse.json({
        success: true,
        data: {
          searchParameters: result.searchParameters,
          organic: result.organic,
          peopleAlsoAsk: result.peopleAlsoAsk,
          relatedSearches: result.relatedSearches,
          answerBox: result.answerBox,
          knowledgeGraph: result.knowledgeGraph,
          credits: result.credits,
        },
        usage: {
          creditsUsed,
          costUSD: estimatedCost,
          responseTime,
        },
      }, {
        status: 200,
        headers: {
          'X-Response-Time': responseTime.toString(),
          'X-Results-Count': result.organic?.length?.toString() || '0',
          'X-Credits-Used': creditsUsed.toString(),
        },
      });
    }

  } catch (error) {
    console.error('Serper API error:', error);

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: 'Invalid request data',
          code: 'VALIDATION_ERROR',
          details: error.errors.map(err => ({
            field: err.path.join('.'),
            message: err.message,
          })),
        },
        { status: 400 }
      );
    }

    // Handle other errors
    const errorMessage = error instanceof Error ? error.message : 'Internal server error';
    const isSerperError = errorMessage.includes('serper') || errorMessage.includes('blocked');
    
    return NextResponse.json(
      {
        success: false,
        error: errorMessage,
        code: isSerperError ? 'SERPER_API_ERROR' : 'INTERNAL_ERROR',
      },
      { status: isSerperError ? 502 : 500 }
    );
  }
}

// GET endpoint for service health and info
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action');

    if (action === 'health') {
      // Health check
      const healthCheck = await serperService.healthCheck();
      
      return NextResponse.json({
        success: true,
        data: {
          status: healthCheck.status,
          latency: healthCheck.latency,
          timestamp: new Date().toISOString(),
        },
      });
    }

    if (action === 'keywords') {
      // Get keyword suggestions (requires query parameter)
      const query = searchParams.get('query');
      const location = searchParams.get('location');

      if (!query) {
        return NextResponse.json(
          { success: false, error: 'Query parameter is required' },
          { status: 400 }
        );
      }

      // Simple auth check for GET requests
      const supabase = createSupabaseServer();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const suggestions = await serperService.getKeywordSuggestions(query, location || undefined, user.id);
      
      return NextResponse.json({
        success: true,
        data: {
          query,
          location,
          suggestions,
        },
      });
    }

    if (action === 'paa') {
      // Get People Also Ask questions
      const query = searchParams.get('query');
      const location = searchParams.get('location');

      if (!query) {
        return NextResponse.json(
          { success: false, error: 'Query parameter is required' },
          { status: 400 }
        );
      }

      // Simple auth check for GET requests
      const supabase = createSupabaseServer();
      const { data: { user }, error: authError } = await supabase.auth.getUser();

      if (authError || !user) {
        return NextResponse.json(
          { success: false, error: 'Unauthorized' },
          { status: 401 }
        );
      }

      const paaQuestions = await serperService.getPeopleAlsoAsk(query, location || undefined, user.id);
      
      return NextResponse.json({
        success: true,
        data: {
          query,
          location,
          peopleAlsoAsk: paaQuestions,
        },
      });
    }

    // Default: return service info
    return NextResponse.json({
      success: true,
      data: {
        service: 'Serper SERP Analysis',
        version: '1.0.0',
        endpoints: {
          search: 'POST /api/serper/search',
          analyze: 'POST /api/serper/search?action=analyze',
          health: 'GET /api/serper/search?action=health',
          keywords: 'GET /api/serper/search?action=keywords&query=term',
          paa: 'GET /api/serper/search?action=paa&query=term',
        },
      },
    });

  } catch (error) {
    console.error('GET /api/serper/search error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        code: 'INTERNAL_ERROR'
      },
      { status: 500 }
    );
  }
}

// Helper function to update user credits
async function updateUserCredits(
  supabase: any,
  userId: string,
  creditsUsed: number,
  costUSD: number
) {
  const { error } = await supabase.rpc('update_user_usage', {
    user_id: userId,
    credits_used: creditsUsed,
    cost_usd: costUSD,
  });

  if (error) {
    console.error('Failed to update user credits:', error);
  }
}
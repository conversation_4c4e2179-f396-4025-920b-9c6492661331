(()=>{var e={};e.id=616,e.ids=[616],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},1017:e=>{"use strict";e.exports=require("path")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},2886:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(482),a=r(9108),i=r(2563),n=r.n(i),o=r(8300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["auth",{children:["verify-email",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,586)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/verify-email/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,6343)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/verify-email/page.tsx"],u="/auth/verify-email/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/verify-email/page",pathname:"/auth/verify-email",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},6335:(e,t,r)=>{Promise.resolve().then(r.bind(r,7680))},7680:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>u});var s=r(2295),a=r(3729),i=r(783),n=r.n(i),o=r(5094),l=r(3673),c=r(2416),d=r(1179);function u(){let[e,t]=a.useState(!1),[i,u]=a.useState(!1),m=async()=>{t(!0);try{let{createSupabaseComponentClient:e}=await Promise.resolve().then(r.bind(r,9299)),t=e(),s=localStorage.getItem("pending_verification_email")||new URLSearchParams(window.location.search).get("email");if(!s)throw Error("Email address not found. Please try signing up again.");let{error:a}=await t.auth.resend({type:"signup",email:s,options:{emailRedirectTo:`${window.location.origin}/auth/callback`}});if(a)throw Error(a.message);u(!0)}catch(e){console.error("Failed to resend verification email:",e)}finally{t(!1)}};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx(n(),{href:"/",className:"inline-block",children:s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"SEO SAAS"})}),s.jsx("p",{className:"text-gray-600",children:"Email verification required"})]}),(0,s.jsxs)(l.Zb,{className:"shadow-lg",children:[(0,s.jsxs)(l.Ol,{className:"space-y-1 text-center",children:[s.jsx("div",{className:"mx-auto mb-4 flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full",children:s.jsx(c.Z,{className:"h-8 w-8 text-blue-600"})}),s.jsx(l.ll,{className:"text-2xl font-bold",children:"Check Your Email"}),s.jsx(l.SZ,{children:"We've sent a verification link to your email address"})]}),(0,s.jsxs)(l.aY,{className:"space-y-6",children:[(0,s.jsxs)("div",{className:"text-center space-y-4",children:[s.jsx("p",{className:"text-sm text-gray-600",children:"To complete your registration, please click the verification link in the email we just sent you."}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-500",children:[s.jsx(d.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"Check your inbox"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-500",children:[s.jsx(d.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"Check your spam folder"})]}),(0,s.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-500",children:[s.jsx(d.Z,{className:"h-4 w-4"}),s.jsx("span",{children:"Click the verification link"})]})]})]}),s.jsx("div",{className:"space-y-4",children:i?s.jsx("div",{className:"p-3 bg-green-50 border border-green-200 rounded-md",children:s.jsx("p",{className:"text-sm text-green-600 text-center",children:"Verification email sent successfully!"})}):(0,s.jsxs)("div",{className:"text-center",children:[s.jsx("p",{className:"text-sm text-gray-600 mb-3",children:"Didn't receive the email?"}),s.jsx(o.z,{variant:"outline",onClick:m,loading:e,disabled:e,className:"w-full",children:e?"Sending...":"Resend Verification Email"})]})}),s.jsx("div",{className:"text-center pt-4 border-t",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Already verified?"," ",s.jsx(n(),{href:"/auth/signin",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in to your account"})]})})]})]}),s.jsx("div",{className:"mt-8 text-center",children:(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["Having trouble? Contact our"," ",s.jsx(n(),{href:"/support",className:"text-blue-600 hover:text-blue-500",children:"support team"})]})})]})})}},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var s=r(2295),a=r(3729),i=r(5877),n=r(9247),o=r(1453);let l=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:r,loading:a,children:n,disabled:c,asChild:d=!1,...u},m)=>{let x=d?i.g7:"button";return(0,s.jsxs)(x,{className:(0,o.cn)(l({variant:t,size:r,className:e})),ref:m,disabled:c||a,...u,children:[a&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),n]})});c.displayName="Button"},3673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>o,SZ:()=>c,Zb:()=>n,aY:()=>d,ll:()=>l});var s=r(2295),a=r(3729),i=r(1453);let n=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));o.displayName="CardHeader";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));l.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},1453:(e,t,r)=>{"use strict";r.d(t,{KG:()=>d,cn:()=>i,p6:()=>o,rl:()=>l,uf:()=>n,vQ:()=>c});var s=r(6815),a=r(9377);function i(...e){return(0,a.m6)((0,s.W)(e))}function n(e){return e.toLocaleString()}function o(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function l(e){return`${e.toFixed(1)}%`}function c(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");return t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),Promise.resolve()}function d(e,t,r="text/plain"){let s=new Blob([e],{type:r}),a=URL.createObjectURL(s),i=document.createElement("a");i.href=a,i.download=t,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(a)}},586:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let s=(0,r(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/verify-email/page.tsx`),{__esModule:a,$$typeof:i}=s,n=s.default},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(337);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},1179:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(3729);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))})},2416:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(3729);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,972,337,35,783,129],()=>r(2886));module.exports=s})();
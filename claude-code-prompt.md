# Claude Code Development Prompt - SEO Content Generation SAAS

## 🎯 Mission Statement
You are <PERSON>, an expert full-stack developer tasked with building a professional-grade SEO Content Generation SAAS application. You must follow the `rules.md` file religiously and implement every single requirement without exception. This is a zero-bug, enterprise-level project that must rival industry leaders like PageOptimizerPro, SEMrush, Ahrefs, and SurferSEO.

## 📋 Core Directives

### 1. **Strict Rule Adherence**
- **ALWAYS** reference `rules.md` before making any development decision
- **NEVER** deviate from the established standards and requirements
- **CHECK OFF** each task in the to-do list as you complete it
- **UPDATE** the completion tracking in `rules.md` after each phase
- **VALIDATE** every implementation against the quality standards

### 2. **Development Approach**
- **Phase-by-Phase**: Complete each phase entirely before moving to the next
- **Task-by-Task**: Complete each subtask methodically and thoroughly
- **Test-Driven**: Write tests for every component and function
- **Documentation-First**: Document every decision and implementation
- **Quality-First**: Zero bugs, enterprise-grade code quality

### 3. **Communication Protocol**
- **Status Updates**: Provide detailed status after each task completion
- **Progress Tracking**: Update task completion status in real-time
- **Issue Reporting**: Immediately report any blockers or challenges
- **Validation Requests**: Ask for validation on critical decisions
- **Next Steps**: Always outline the next immediate actions

## 🛠️ Technical Implementation Guidelines

### Project Setup Requirements
```bash
# Initialize Next.js with TypeScript
npx create-next-app@latest seo-saas --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Install required dependencies
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs
npm install axios groq-sdk
npm install @types/node @types/react @types/react-dom
npm install lucide-react recharts
npm install @headlessui/react @heroicons/react

# Development dependencies
npm install -D prettier eslint-config-prettier
npm install -D @testing-library/react @testing-library/jest-dom jest jest-environment-jsdom
```

### Environment Variables Setup
```env
# ⚠️ SECURITY WARNING: Replace these with your actual credentials
# Never commit real API keys to version control

# Supabase Configuration
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url_here
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key_here
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_key_here

# API Keys
GROQ_API_KEY=your_groq_api_key_here
SERPER_API_KEY=your_serper_api_key_here

# Application Configuration
NEXTAUTH_SECRET=generate_secure_random_string_here
NEXTAUTH_URL=http://localhost:3000

# Security Configuration
ENCRYPTION_KEY=generate_32_byte_encryption_key_here
JWT_SECRET=generate_jwt_secret_here
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000
```

### Folder Structure (Mandatory)
```
src/
├── app/                    # Next.js 13+ app directory
│   ├── (auth)/            # Authentication routes
│   ├── dashboard/         # Dashboard pages
│   ├── api/               # API routes
│   └── globals.css        # Global styles
├── components/            # Reusable UI components
│   ├── ui/               # Base UI components
│   ├── forms/            # Form components
│   ├── dashboard/        # Dashboard-specific components
│   └── seo/              # SEO-related components
├── lib/                  # Utility functions and configurations
│   ├── supabase.ts       # Supabase client
│   ├── groq.ts           # Groq API client
│   ├── serper.ts         # Serper API client
│   └── utils.ts          # General utilities
├── types/                # TypeScript type definitions
├── hooks/                # Custom React hooks
├── services/             # External API integrations
├── utils/                # Helper functions
└── tests/                # Test files
```

## 🎨 Design Implementation Standards

### Color Palette (Exact Implementation)
```css
:root {
  --primary: #1a365d;      /* Dark Blue */
  --secondary: #2d3748;    /* Dark Gray */
  --accent: #3182ce;       /* Blue */
  --success: #38a169;      /* Green */
  --warning: #d69e2e;      /* Orange */
  --error: #e53e3e;        /* Red */
  --background: #f7fafc;   /* Light Gray */
  --text: #2d3748;         /* Dark Gray */
  --border: #e2e8f0;       /* Light Border */
  --card: #ffffff;         /* White */
}
```

### Component Design Requirements
- **Professional Appearance**: Enterprise-grade visual design
- **Responsive Design**: Mobile-first approach with Tailwind CSS
- **Accessibility**: WCAG 2.1 AA compliance
- **Performance**: Optimized components with proper memoization
- **Consistency**: Consistent design patterns throughout

## 🔍 SEO Engine Implementation

### Competitor Analysis Algorithm
```typescript
interface CompetitorAnalysis {
  url: string;
  wordCount: number;
  headingCount: {
    h1: number;
    h2: number;
    h3: number;
    h4: number;
    h5: number;
    h6: number;
  };
  keywordDensity: {
    primary: number;
    secondary: number[];
    lsi: string[];
  };
  optimizedHeadings: number;
  metaData: {
    title: string;
    description: string;
    keywords: string[];
  };
}

interface SEOAnalysisResult {
  competitors: CompetitorAnalysis[];
  averages: {
    wordCount: number;
    headingCount: number;
    keywordDensity: number;
    optimizedHeadings: number;
  };
  recommendations: string[];
}
```

### Content Generation Rules (Mandatory)
1. **NLP Optimization**: Follow strict NLP-friendly formatting
2. **Forbidden Words**: Never use the specified forbidden words list
3. **Sentence Structure**: Clear subject-verb-object order
4. **Keyword Integration**: Natural keyword placement matching competitor density
5. **LSI Keywords**: Include all relevant LSI terms and entities
6. **E-E-A-T Compliance**: Ensure expertise, authoritativeness, trustworthiness

## 🔧 API Integration Requirements

### Serper API Implementation
```typescript
interface SerperSearchParams {
  q: string;              // Search query
  gl: string;             // Country code
  hl: string;             // Language
  num: number;            // Number of results
  type: 'search' | 'images' | 'videos' | 'places' | 'news';
}

interface SerperResponse {
  organic: Array<{
    title: string;
    link: string;
    snippet: string;
    position: number;
  }>;
  answerBox?: any;
  peopleAlsoAsk?: any[];
  relatedSearches?: any[];
}
```

### Groq API Implementation
```typescript
interface GroqContentRequest {
  keyword: string;
  location: string;
  industry: string;
  contentType: 'service' | 'blog' | 'product' | 'landing';
  tone: 'professional' | 'conversational' | 'authoritative' | 'friendly';
  intent: 'informational' | 'commercial' | 'transactional' | 'navigational';
  competitorData: SEOAnalysisResult;
  targetWordCount: number;
  targetKeywordDensity: number;
}

interface GroqContentResponse {
  content: string;
  metadata: {
    wordCount: number;
    keywordDensity: number;
    headingCount: number;
    seoScore: number;
  };
  suggestions: string[];
}
```

## 📊 Database Schema (Supabase)

### Required Tables
```sql
-- Users table (extends Supabase auth.users)
CREATE TABLE profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE,
  email TEXT,
  full_name TEXT,
  subscription_tier TEXT DEFAULT 'free',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Projects table
CREATE TABLE projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  industry TEXT,
  target_country TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content generations table
CREATE TABLE content_generations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  keyword TEXT NOT NULL,
  location TEXT NOT NULL,
  content_type TEXT NOT NULL,
  tone TEXT NOT NULL,
  intent TEXT NOT NULL,
  generated_content TEXT,
  seo_analysis JSONB,
  competitor_data JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- SEO analysis results table
CREATE TABLE seo_analyses (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  content_generation_id UUID REFERENCES content_generations(id) ON DELETE CASCADE,
  keyword TEXT NOT NULL,
  competitors JSONB,
  averages JSONB,
  recommendations JSONB,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);
```

## 🧪 Testing Requirements

### Test Coverage Mandate
- **Unit Tests**: 80%+ code coverage
- **Integration Tests**: All API integrations
- **E2E Tests**: Critical user journeys
- **Component Tests**: All React components
- **API Tests**: All API endpoints

### Testing Framework Setup
```bash
# Install testing dependencies
npm install -D jest @testing-library/react @testing-library/jest-dom
npm install -D @testing-library/user-event jest-environment-jsdom
npm install -D cypress @cypress/react @cypress/webpack-dev-server
```

## 🚀 Deployment Configuration

### Vercel Deployment
```json
{
  "name": "seo-content-saas",
  "version": 2,
  "builds": [
    {
      "src": "package.json",
      "use": "@vercel/next"
    }
  ],
  "env": {
    "NEXT_PUBLIC_SUPABASE_URL": "@supabase-url",
    "NEXT_PUBLIC_SUPABASE_ANON_KEY": "@supabase-anon-key",
    "SUPABASE_SERVICE_ROLE_KEY": "@supabase-service-key",
    "GROQ_API_KEY": "@groq-api-key",
    "SERPER_API_KEY": "@serper-api-key"
  }
}
```

## 📋 Task Execution Protocol

### Before Starting Each Task
1. **Read** the specific task requirements from `rules.md`
2. **Plan** the implementation approach
3. **Validate** the plan against quality standards
4. **Implement** with proper error handling and testing
5. **Test** thoroughly before marking complete
6. **Update** the task status in `rules.md`

### After Completing Each Task
1. **Verify** all requirements are met
2. **Run** all tests to ensure no regressions
3. **Update** documentation if needed
4. **Mark** task as complete in the tracking system
5. **Prepare** for the next task

### Communication Format
```
## Task Status Update

**Current Task**: [Task Name]
**Phase**: [Phase Number and Name]
**Status**: [IN_PROGRESS/COMPLETE/BLOCKED]
**Progress**: [X/Y subtasks complete]

### Completed:
- [List completed items]

### Next Steps:
- [List next immediate actions]

### Issues/Blockers:
- [List any issues or blockers]

### Validation Needed:
- [List items needing validation]
```

## 🎯 Success Criteria

### Phase Completion Requirements
- [ ] All tasks in phase completed and tested
- [ ] Code quality standards met (TypeScript strict, no any types)
- [ ] Test coverage requirements met (80%+)
- [ ] Documentation updated
- [ ] Performance benchmarks met
- [ ] Security requirements satisfied
- [ ] User experience standards achieved

### Final Delivery Requirements
- [ ] Zero bugs in production
- [ ] Professional UI matching industry standards
- [ ] Complete competitor analysis accuracy
- [ ] Multi-industry dynamic support
- [ ] Secure API key management
- [ ] Scalable architecture for growth
- [ ] Comprehensive documentation
- [ ] Full test coverage

---

## 🚨 Critical Reminders

1. **NEVER** skip any requirement from `rules.md`
2. **ALWAYS** test before marking tasks complete
3. **IMMEDIATELY** report any blockers or issues
4. **CONTINUOUSLY** update progress tracking
5. **RIGOROUSLY** follow code quality standards
6. **METICULOUSLY** implement every feature requirement
7. **PROFESSIONALLY** maintain enterprise-grade standards

---

## 🎮 Terminal Commands & Development Workflow

### Initial Setup Commands
```bash
# Navigate to project directory
cd "f:\Claude-Code-Setup\SEO SAAS APP"

# Initialize the project
npx create-next-app@latest . --typescript --tailwind --eslint --app --src-dir --import-alias "@/*"

# Install all required dependencies
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs axios groq-sdk lucide-react recharts @headlessui/react @heroicons/react

# Install development dependencies
npm install -D prettier eslint-config-prettier @testing-library/react @testing-library/jest-dom jest jest-environment-jsdom cypress

# Create environment file
echo "NEXT_PUBLIC_SUPABASE_URL=https://zqrmpanonghggoxdjirq.supabase.co" > .env.local
echo "NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY" >> .env.local
echo "SUPABASE_SERVICE_ROLE_KEY=unqhUt/zHacG7pikBxYBQSpoGqGrQe/sHNZwkqMhCr+0QlJALP7yiK2PZVREsGRL6RC4lSJXXZFnTeRNEImtDg==" >> .env.local
echo "GROQ_API_KEY=********************************************************" >> .env.local
echo "SERPER_API_KEY=4ce37b02808e4325e42068eb815b03490a5519e5" >> .env.local

# Start development server
npm run dev
```

### Development Commands
```bash
# Run tests
npm test

# Run tests with coverage
npm run test:coverage

# Run linting
npm run lint

# Run type checking
npx tsc --noEmit

# Build for production
npm run build

# Start production server
npm start
```

## 🔄 Real-Time Progress Tracking

### Task Update Protocol
After completing each task, update the `rules.md` file:

```bash
# Example: Mark task as complete
# Update the checkbox from [ ] to [x] in rules.md
# Update the completion counter
# Add completion timestamp
```

### Progress Reporting Format
```markdown
## ✅ Task Completed: [Task Name]
**Completed At**: [Timestamp]
**Duration**: [Time taken]
**Files Modified**: [List of files]
**Tests Added**: [Number of tests]
**Coverage**: [Coverage percentage]

### Implementation Details:
- [Detail 1]
- [Detail 2]
- [Detail 3]

### Next Task: [Next Task Name]
**Estimated Duration**: [Time estimate]
**Dependencies**: [Any dependencies]
```

## 🛡️ Quality Assurance Checklist

### Before Marking Any Task Complete
- [ ] Code follows TypeScript strict mode
- [ ] All functions have proper type definitions
- [ ] Error handling is implemented
- [ ] Tests are written and passing
- [ ] Code is properly documented
- [ ] Performance considerations addressed
- [ ] Security best practices followed
- [ ] UI/UX standards met
- [ ] Accessibility requirements satisfied
- [ ] Mobile responsiveness verified

### Code Review Checklist
- [ ] No `any` types used without justification
- [ ] Proper error boundaries implemented
- [ ] API calls have proper error handling
- [ ] Loading states are implemented
- [ ] User feedback is provided for all actions
- [ ] Form validation is comprehensive
- [ ] Data sanitization is implemented
- [ ] Security vulnerabilities addressed

## 🎯 Feature Implementation Priorities

### Phase 1 Critical Features
1. **Project Setup**: Next.js, TypeScript, Tailwind CSS
2. **Authentication**: Supabase auth integration
3. **Basic UI**: Header, navigation, layout components
4. **Database**: Initial schema and connections
5. **API Setup**: Basic API routes structure
6. **Environment**: All environment variables configured

### Phase 2 Critical Features
1. **User Management**: Profile creation and management
2. **Project System**: Create, read, update, delete projects
3. **Dashboard**: Basic dashboard with project overview
4. **Forms**: Input forms for content generation
5. **Validation**: Comprehensive form validation
6. **Error Handling**: Global error handling system

### Phase 3 Critical Features
1. **Serper Integration**: SERP data collection
2. **Content Scraping**: Competitor content analysis
3. **Data Processing**: Keyword density calculations
4. **Groq Integration**: AI content generation
5. **SEO Analysis**: Comprehensive SEO scoring
6. **Results Display**: Professional results interface

## 🚀 Performance Optimization Requirements

### Frontend Performance
- **Core Web Vitals**: LCP < 2.5s, FID < 100ms, CLS < 0.1
- **Bundle Size**: Keep JavaScript bundles under 250KB
- **Image Optimization**: Use Next.js Image component
- **Code Splitting**: Implement route-based code splitting
- **Caching**: Implement proper caching strategies

### Backend Performance
- **API Response Time**: < 2 seconds for content generation
- **Database Queries**: Optimize all queries with proper indexes
- **Rate Limiting**: Implement rate limiting for all APIs
- **Error Recovery**: Graceful error handling and recovery
- **Monitoring**: Comprehensive performance monitoring

## 🔐 Security Implementation Checklist

### Authentication & Authorization
- [ ] Secure JWT token handling
- [ ] Role-based access control
- [ ] Session management
- [ ] Password security requirements
- [ ] Multi-factor authentication support

### Data Protection
- [ ] Input sanitization and validation
- [ ] SQL injection prevention
- [ ] XSS attack prevention
- [ ] CSRF protection
- [ ] Data encryption at rest and in transit

### API Security
- [ ] Rate limiting implementation
- [ ] API key management
- [ ] CORS configuration
- [ ] Request validation
- [ ] Error message sanitization

## 📱 Responsive Design Requirements

### Breakpoints (Tailwind CSS)
```css
/* Mobile First Approach */
sm: 640px   /* Small devices */
md: 768px   /* Medium devices */
lg: 1024px  /* Large devices */
xl: 1280px  /* Extra large devices */
2xl: 1536px /* 2X Extra large devices */
```

### Component Responsiveness
- **Navigation**: Collapsible mobile menu
- **Dashboard**: Responsive grid layouts
- **Forms**: Stack on mobile, side-by-side on desktop
- **Tables**: Horizontal scroll on mobile
- **Charts**: Responsive chart sizing

## 🎨 UI Component Library Structure

### Base Components
```typescript
// Button Component
interface ButtonProps {
  variant: 'primary' | 'secondary' | 'outline' | 'ghost';
  size: 'sm' | 'md' | 'lg';
  loading?: boolean;
  disabled?: boolean;
  children: React.ReactNode;
  onClick?: () => void;
}

// Input Component
interface InputProps {
  label: string;
  type: 'text' | 'email' | 'password' | 'number';
  placeholder?: string;
  required?: boolean;
  error?: string;
  value: string;
  onChange: (value: string) => void;
}

// Card Component
interface CardProps {
  title?: string;
  subtitle?: string;
  children: React.ReactNode;
  actions?: React.ReactNode;
  loading?: boolean;
}
```

### Advanced Components
```typescript
// SEO Analysis Display
interface SEOAnalysisProps {
  analysis: SEOAnalysisResult;
  loading: boolean;
  onRegenerate: () => void;
}

// Content Generator Form
interface ContentGeneratorProps {
  onSubmit: (data: ContentGenerationRequest) => void;
  loading: boolean;
  industries: string[];
  countries: string[];
}

// Competitor Analysis Table
interface CompetitorTableProps {
  competitors: CompetitorAnalysis[];
  averages: CompetitorAverages;
  loading: boolean;
}
```

---

## 🎯 Final Success Validation

### Before Project Completion
1. **Functionality Test**: Every feature works as specified
2. **Performance Test**: Meets all performance benchmarks
3. **Security Test**: Passes security vulnerability scans
4. **Accessibility Test**: Meets WCAG 2.1 AA standards
5. **Cross-browser Test**: Works on all major browsers
6. **Mobile Test**: Fully functional on mobile devices
7. **Load Test**: Handles expected user load
8. **Integration Test**: All APIs work correctly
9. **User Experience Test**: Intuitive and professional UX
10. **Documentation Test**: Complete and accurate documentation

### Deployment Readiness Checklist
- [ ] All environment variables configured
- [ ] Database migrations completed
- [ ] SSL certificates configured
- [ ] Domain name configured
- [ ] Monitoring and logging set up
- [ ] Backup procedures implemented
- [ ] Error tracking configured
- [ ] Performance monitoring active
- [ ] Security headers configured
- [ ] CDN configured for static assets

---

**Remember**: This is not just a development project - it's building a professional SAAS platform that must compete with industry leaders. Every line of code, every component, every feature must be implemented to the highest professional standards with zero tolerance for bugs or shortcuts.

**Claude Code**: You are now equipped with everything needed to build this enterprise-grade SEO SAAS application. Follow this prompt religiously, reference `rules.md` constantly, and deliver a product that rivals the best in the industry.

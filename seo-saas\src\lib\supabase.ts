// Supabase Client Configuration
import { createClient } from '@supabase/supabase-js';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { Database } from '@/types/database';
import { config } from './config';

// Client-side Supabase client (for use in components)
export const supabase = createClient<Database>(
  config.supabase.url,
  config.supabase.anonKey,
  {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'X-Client-Info': 'seo-saas@1.0.0',
      },
    },
  }
);

// Client component client (for use in client components)
export const createSupabaseComponentClient = () =>
  createClientComponentClient<Database>();

// Server component client is in a separate server-only file

// Service role client (for admin operations)
export const supabaseAdmin = createClient<Database>(
  config.supabase.url,
  config.supabase.serviceRole<PERSON>ey,
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
    db: {
      schema: 'public',
    },
    global: {
      headers: {
        'X-Client-Info': 'seo-saas-admin@1.0.0',
      },
    },
  }
);

// Auth helpers
export async function getSession() {
  const supabase = createSupabaseComponentClient();
  const { data: { session }, error } = await supabase.auth.getSession();
  
  if (error) {
    console.error('Error getting session:', error);
    return null;
  }
  
  return session;
}

export async function getUser() {
  const supabase = createSupabaseComponentClient();
  const { data: { user }, error } = await supabase.auth.getUser();
  
  if (error) {
    console.error('Error getting user:', error);
    return null;
  }
  
  return user;
}

export async function signIn(email: string, password: string) {
  const supabase = createSupabaseComponentClient();
  
  const { data, error } = await supabase.auth.signInWithPassword({
    email,
    password,
  });
  
  if (error) {
    throw new Error(error.message);
  }
  
  return data;
}

export async function signUp(email: string, password: string, fullName?: string) {
  const supabase = createSupabaseComponentClient();
  
  const { data, error } = await supabase.auth.signUp({
    email,
    password,
    options: {
      data: {
        full_name: fullName,
      },
    },
  });
  
  if (error) {
    throw new Error(error.message);
  }
  
  return data;
}

export async function signOut() {
  const supabase = createSupabaseComponentClient();
  
  const { error } = await supabase.auth.signOut();
  
  if (error) {
    throw new Error(error.message);
  }
}

export async function resetPassword(email: string) {
  const supabase = createSupabaseComponentClient();
  
  const { error } = await supabase.auth.resetPasswordForEmail(email, {
    redirectTo: `${config.app.url}/auth/reset-password`,
  });
  
  if (error) {
    throw new Error(error.message);
  }
}

export async function updatePassword(newPassword: string) {
  const supabase = createSupabaseComponentClient();
  
  const { error } = await supabase.auth.updateUser({
    password: newPassword,
  });
  
  if (error) {
    throw new Error(error.message);
  }
}

// Profile helpers
export async function getProfile(userId: string) {
  const supabase = createSupabaseComponentClient();
  
  const { data, error } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', userId)
    .single();
  
  if (error) {
    console.error('Error getting profile:', error);
    return null;
  }
  
  return data;
}

export async function updateProfile(userId: string, updates: {
  full_name?: string;
  avatar_url?: string;
  preferences?: any;
}) {
  const supabase = createSupabaseComponentClient();
  
  const { data, error } = await supabase
    .from('profiles')
    .update({
      ...updates,
      updated_at: new Date().toISOString(),
    })
    .eq('id', userId)
    .select()
    .single();
  
  if (error) {
    throw new Error(error.message);
  }
  
  return data;
}

// Subscription helpers
export async function getUserSubscription(userId: string) {
  const supabase = createSupabaseComponentClient();
  
  const { data, error } = await supabase
    .from('user_subscriptions')
    .select('*')
    .eq('user_id', userId)
    .single();
  
  if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
    console.error('Error getting subscription:', error);
    return null;
  }
  
  return data;
}

export async function createSubscription(subscription: {
  user_id: string;
  plan_type: 'free' | 'pro' | 'enterprise';
  status: 'active' | 'cancelled' | 'past_due' | 'trialing' | 'incomplete';
  stripe_customer_id?: string;
  stripe_subscription_id?: string;
}) {
  const supabase = createSupabaseComponentClient();
  
  const { data, error } = await supabase
    .from('user_subscriptions')
    .insert(subscription)
    .select()
    .single();
  
  if (error) {
    throw new Error(error.message);
  }
  
  return data;
}

// API usage tracking
export async function logApiUsage(logData: {
  user_id?: string;
  content_generation_id?: string;
  api_name: 'groq' | 'serper' | 'supabase' | 'openai';
  endpoint?: string;
  method?: string;
  tokens_used?: number;
  cost?: number;
  response_time_ms?: number;
  status_code?: number;
  error_message?: string;
}) {
  const supabase = createSupabaseComponentClient();
  
  const { error } = await supabase
    .from('api_usage_logs')
    .insert(logData);
  
  if (error) {
    console.error('Error logging API usage:', error);
  }
}

// Activity logging
export async function logUserActivity(activity: {
  user_id?: string;
  action_type: string;
  action_details?: any;
  ip_address?: string;
  user_agent?: string;
  session_id?: string;
}) {
  const supabase = createSupabaseComponentClient();
  
  const { error } = await supabase
    .from('user_activity_logs')
    .insert(activity);
  
  if (error) {
    console.error('Error logging user activity:', error);
  }
}

// Real-time subscriptions
export function subscribeToContentGenerations(
  userId: string,
  callback: (payload: any) => void
) {
  const supabase = createSupabaseComponentClient();
  
  return supabase
    .channel('content_generations')
    .on(
      'postgres_changes',
      {
        event: '*',
        schema: 'public',
        table: 'content_generations',
        filter: `user_id=eq.${userId}`,
      },
      callback
    )
    .subscribe();
}

export function subscribeToApiUsage(
  userId: string,
  callback: (payload: any) => void
) {
  const supabase = createSupabaseComponentClient();
  
  return supabase
    .channel('api_usage')
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'api_usage_logs',
        filter: `user_id=eq.${userId}`,
      },
      callback
    )
    .subscribe();
}

// Error handling helper
export function handleSupabaseError(error: any): string {
  if (error?.message) {
    return error.message;
  }
  
  if (error?.code) {
    switch (error.code) {
      case 'auth/invalid-email':
        return 'Invalid email address';
      case 'auth/user-disabled':
        return 'This account has been disabled';
      case 'auth/user-not-found':
        return 'No account found with this email';
      case 'auth/wrong-password':
        return 'Incorrect password';
      case 'auth/too-many-requests':
        return 'Too many attempts. Please try again later';
      case 'auth/weak-password':
        return 'Password should be at least 6 characters';
      case 'auth/email-already-in-use':
        return 'An account with this email already exists';
      default:
        return 'An unexpected error occurred';
    }
  }
  
  return 'An unexpected error occurred';
}

export default supabase;
"use strict";(()=>{var e={};e.id=749,e.ids=[749],e.modules={2934:e=>{e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},517:e=>{e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{e.exports=require("buffer")},6113:e=>{e.exports=require("crypto")},2361:e=>{e.exports=require("events")},7147:e=>{e.exports=require("fs")},3685:e=>{e.exports=require("http")},5687:e=>{e.exports=require("https")},1808:e=>{e.exports=require("net")},7561:e=>{e.exports=require("node:fs")},4492:e=>{e.exports=require("node:stream")},1017:e=>{e.exports=require("path")},5477:e=>{e.exports=require("punycode")},2781:e=>{e.exports=require("stream")},4404:e=>{e.exports=require("tls")},7310:e=>{e.exports=require("url")},3837:e=>{e.exports=require("util")},1267:e=>{e.exports=require("worker_threads")},9796:e=>{e.exports=require("zlib")},2144:(e,t,r)=>{r.r(t),r.d(t,{headerHooks:()=>T,originalPathname:()=>R,patchFetch:()=>k,requestAsyncStorage:()=>f,routeModule:()=>y,serverHooks:()=>x,staticGenerationAsyncStorage:()=>q,staticGenerationBailout:()=>M});var s={};r.r(s),r.d(s,{GET:()=>g,POST:()=>w});var i=r(5419),o=r(9108),n=r(9678),a=r(8070),c=r(5252),u=r(2178),l=r(2982),d=r(9279),m=r(2045);let p=c.Ry({keyword:c.Z_().min(1,"Keyword is required").max(100,"Keyword too long"),location:c.Z_().max(100,"Location too long").optional().default(""),industry:c.Z_().min(1,"Industry is required").max(50,"Industry too long"),contentType:c.Km(["service","blog","product","landing","category","faq"]),tone:c.Km(["professional","conversational","authoritative","friendly","technical","casual"]),intent:c.Km(["informational","commercial","transactional","navigational"]),targetWordCount:c.Rx().min(200).max(5e3).default(800),includeImages:c.O7().default(!1),includeSchema:c.O7().default(!1),includeFaq:c.O7().default(!1),competitorData:c.Yj().optional(),templateStructure:c.Yj().optional(),brandVoice:c.Z_().max(500).optional(),targetAudience:c.Z_().max(200).optional()});async function w(e){let t=Date.now();try{let r=await e.json(),s=p.parse(r),i=(0,m.jq)(),{data:{user:o},error:n}=await i.auth.getUser();if(n||!o)return a.Z.json({success:!1,error:"Unauthorized"},{status:401});let{data:c}=await i.from("profiles").select("subscription_tier, credits_remaining").eq("id",o.id).single(),u=c?.subscription_tier||"free",w=Math.floor(1.3*s.targetWordCount),g=w/1e3*.05;try{await (0,d.dZ)(o.id,u,"apiCalls",async()=>({success:!0}))}catch(e){return a.Z.json({success:!1,error:e.message,code:"SUBSCRIPTION_LIMIT_EXCEEDED"},{status:429})}try{await (0,d.sy)(o.id,u,g,async()=>({success:!0}))}catch(e){return a.Z.json({success:!1,error:e.message,code:"COST_LIMIT_EXCEEDED"},{status:402})}try{await (0,d.er)(o.id,u,"groq",async()=>({success:!0}))}catch(e){return a.Z.json({success:!1,error:e.message,code:"RATE_LIMIT_EXCEEDED"},{status:429})}console.log("Generating content with Groq for user:",o.id,{keyword:s.keyword,contentType:s.contentType,wordCount:s.targetWordCount});let y=await l.TU.generateContent(s,o.id);await h(i,o.id,2,g);let f=Date.now()-t;return console.log("Content generation completed",{userId:o.id,keyword:s.keyword,wordCount:y.seoMetrics.wordCount,responseTime:f}),a.Z.json({success:!0,data:{title:y.title,metaDescription:y.metaDescription,content:y.content,outline:y.outline,seoMetrics:{keywordDensity:y.seoMetrics.keywordDensity,wordCount:y.seoMetrics.wordCount,readabilityScore:y.seoMetrics.readabilityScore,seoScore:y.seoMetrics.seoScore,headingCount:y.seoMetrics.headingCount},suggestions:y.suggestions,schemaMarkup:y.schemaMarkup,imagePrompts:y.imagePrompts,faqSection:y.faqSection,internalLinks:y.internalLinks,externalLinks:y.externalLinks},usage:{creditsUsed:2,costUSD:g,tokensUsed:w,responseTime:f}},{status:200,headers:{"X-Response-Time":f.toString(),"X-Word-Count":y.seoMetrics.wordCount.toString(),"X-Credits-Used":"2"}})}catch(r){if(console.error("Groq generation error:",r),r instanceof u.jm)return a.Z.json({success:!1,error:"Invalid request data",code:"VALIDATION_ERROR",details:r.errors.map(e=>({field:e.path.join("."),message:e.message}))},{status:400});let e=r instanceof Error?r.message:"Internal server error",t=e.includes("groq")||e.includes("rate limit");return a.Z.json({success:!1,error:e,code:t?"GROQ_API_ERROR":"INTERNAL_ERROR"},{status:t?502:500})}}async function g(e){try{let{searchParams:t}=new URL(e.url),r=t.get("action");if("health"===r){let e=await l.TU.healthCheck();return a.Z.json({success:!0,data:{status:e.status,latency:e.latency,model:e.model,timestamp:new Date().toISOString()}})}if("models"===r){let e=l.TU.getAvailableModels();return a.Z.json({success:!0,data:{models:Object.entries(e).map(([e,t])=>({id:e,name:t.name,maxTokens:t.maxTokens,bestFor:t.bestFor,costPer1kTokens:t.costPer1kTokens}))}})}return a.Z.json({success:!0,data:{service:"Groq Content Generation",version:"1.0.0",endpoints:{generate:"POST /api/groq/generate",health:"GET /api/groq/generate?action=health",models:"GET /api/groq/generate?action=models"}}})}catch(e){return console.error("GET /api/groq/generate error:",e),a.Z.json({success:!1,error:"Internal server error",code:"INTERNAL_ERROR"},{status:500})}}async function h(e,t,r,s){let{error:i}=await e.rpc("update_user_usage",{user_id:t,credits_used:r,cost_usd:s});i&&console.error("Failed to update user credits:",i)}let y=new i.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/groq/generate/route",pathname:"/api/groq/generate",filename:"route",bundlePath:"app/api/groq/generate/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/groq/generate/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:f,staticGenerationAsyncStorage:q,serverHooks:x,headerHooks:T,staticGenerationBailout:M}=y,R="/api/groq/generate/route";function k(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:q})}},9279:(e,t,r)=>{r.d(t,{dZ:()=>n,er:()=>o,sy:()=>a});class s{async get(e){let t=this.store.get(e);return t?t.resetTime<Date.now()?(this.store.delete(e),null):t:null}async increment(e,t){let r=Date.now(),s=await this.get(e);if(s&&s.resetTime>r)return s.count++,this.store.set(e,s),s;let i={count:1,resetTime:r+t};return this.store.set(e,i),i}async reset(e){this.store.delete(e)}cleanup(){let e=Date.now();for(let[t,r]of this.store.entries())r.resetTime<e&&this.store.delete(t)}constructor(){this.store=new Map}}class i{constructor(e){this.subscriptionLimits={free:{daily:10,monthly:100,concurrent:1,apiCalls:50},pro:{daily:100,monthly:2e3,concurrent:3,apiCalls:1e3},enterprise:{daily:1e3,monthly:2e4,concurrent:10,apiCalls:1e4}},this.store=new s,this.defaultRule=e||{windowMs:9e5,maxRequests:100},setInterval(()=>this.store.cleanup(),3e5)}async checkLimit(e,t){let r=t||this.defaultRule,s=r.keyGenerator?.(e)||`rate_limit:${e}`,i=await this.store.increment(s,r.windowMs),o=Math.max(0,r.maxRequests-i.count),n=i.count<=r.maxRequests;return{allowed:n,remaining:o,resetTime:i.resetTime,totalHits:i.count,retryAfter:n?void 0:Math.ceil((i.resetTime-Date.now())/1e3)}}async checkSubscriptionLimit(e,t,r){let s;let i=(this.subscriptionLimits[t]||this.subscriptionLimits.free)[r];switch(r){case"daily":s=864e5;break;case"monthly":s=2592e6;break;case"concurrent":s=6e4;break;default:s=36e5}let o=`subscription_limit:${e}:${r}`,n=await this.store.increment(o,s),a=Math.max(0,i-n.count),c=n.count<=i;return{allowed:c,remaining:a,resetTime:n.resetTime,totalHits:n.count,retryAfter:c?void 0:Math.ceil((n.resetTime-Date.now())/1e3)}}async checkApiLimit(e,t,r){let s={groq:{free:{requests:5,windowMs:6e4},pro:{requests:30,windowMs:6e4},enterprise:{requests:100,windowMs:6e4}},serper:{free:{requests:10,windowMs:6e4},pro:{requests:50,windowMs:6e4},enterprise:{requests:200,windowMs:6e4}},openai:{free:{requests:3,windowMs:6e4},pro:{requests:20,windowMs:6e4},enterprise:{requests:60,windowMs:6e4}}},i=s[t]?.[r]||s[t]?.free;if(!i)throw Error(`No limits defined for API: ${t}`);let o=`api_limit:${e}:${t}`,n=await this.store.increment(o,i.windowMs),a=Math.max(0,i.requests-n.count),c=n.count<=i.requests;return{allowed:c,remaining:a,resetTime:n.resetTime,totalHits:n.count,retryAfter:c?void 0:Math.ceil((n.resetTime-Date.now())/1e3)}}async checkCostLimit(e,t,r){let s={free:5,pro:50,enterprise:500},i=s[t]||s.free,o=`cost_limit:${e}:monthly`,n=Date.now(),a=new Date(n);a.setDate(1),a.setHours(0,0,0,0);let c=`${o}:${a.getTime()}`,u=await this.store.get(c),l=(u?u.count/100:0)+r,d=l<=i;if(d){let e=new Date(a);e.setMonth(e.getMonth()+1),await this.store.increment(c,e.getTime()-n)}return{allowed:d,remainingBudget:Math.max(0,i-l),totalSpent:l}}async resetLimit(e,t){if(t){let r=`${t}:${e}`;await this.store.reset(r)}else for(let t of[`rate_limit:${e}`,`subscription_limit:${e}:*`,`api_limit:${e}:*`,`cost_limit:${e}:*`])await this.store.reset(t)}async getUsageStats(e){return{rateLimit:{},subscriptionLimits:{},apiLimits:{},costUsage:{}}}createMiddleware(e){return async t=>{let r=await this.checkLimit(t,e);if(!r.allowed){let e=Error("Rate limit exceeded");throw e.statusCode=429,e.retryAfter=r.retryAfter,e.resetTime=r.resetTime,e}return{"X-RateLimit-Limit":e?.maxRequests||this.defaultRule.maxRequests,"X-RateLimit-Remaining":r.remaining,"X-RateLimit-Reset":new Date(r.resetTime).toISOString()}}}}async function o(e,t,r,s){let o=new i,n=await o.checkApiLimit(e,r,t);if(!n.allowed){let e=Error(`${r} API rate limit exceeded`);throw e.statusCode=429,e.retryAfter=n.retryAfter,e.resetTime=n.resetTime,e.remaining=n.remaining,e}return s()}async function n(e,t,r,s){let o=new i,n=await o.checkSubscriptionLimit(e,t,r);if(!n.allowed){let e=Error(`${r} subscription limit exceeded`);throw e.statusCode=429,e.retryAfter=n.retryAfter,e.resetTime=n.resetTime,e.remaining=n.remaining,e}return s()}async function a(e,t,r,s){let o=new i,n=await o.checkCostLimit(e,t,r);if(!n.allowed){let e=Error(`Monthly cost limit exceeded. Remaining budget: $${n.remainingBudget.toFixed(2)}`);throw e.statusCode=402,e.remainingBudget=n.remainingBudget,e.totalSpent=n.totalSpent,e}return s()}new i({windowMs:9e5,maxRequests:100}),new i({windowMs:6e4,maxRequests:10}),new i({windowMs:9e5,maxRequests:5})}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,280,252,554,493,982],()=>r(2144));module.exports=s})();
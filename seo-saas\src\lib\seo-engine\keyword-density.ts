// Advanced Keyword Density Calculation System
// Enterprise-grade NLP analysis for keyword optimization

export interface KeywordAnalysis {
  keyword: string;
  density: number;
  frequency: number;
  positions: KeywordPosition[];
  variations: KeywordVariation[];
  context: KeywordContext[];
  semanticRelevance: number;
  optimization: KeywordOptimization;
}

export interface KeywordPosition {
  start: number;
  end: number;
  section: 'title' | 'meta' | 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6' | 'paragraph' | 'list' | 'caption';
  sentence: string;
  proximity: ProximityScore[];
  weight: number; // Importance weight based on section
}

export interface KeywordVariation {
  term: string;
  type: 'exact' | 'partial' | 'stemmed' | 'synonym' | 'related';
  frequency: number;
  semanticSimilarity: number;
  density: number;
}

export interface KeywordContext {
  beforeWords: string[];
  afterWords: string[];
  sentiment: number; // 1 = positive, 0 = neutral, -1 = negative
  topicRelevance: number;
  coOccurrence: string[];
}

export interface ProximityScore {
  nearbyKeyword: string;
  distance: number;
  frequency: number;
  relevance: number;
}

export interface KeywordOptimization {
  currentScore: number;
  optimalDensity: number;
  recommendations: OptimizationRecommendation[];
  competitorComparison: CompetitorDensityData;
  riskAssessment: RiskAssessment;
}

export interface OptimizationRecommendation {
  action: 'increase' | 'decrease' | 'redistribute' | 'add_variations' | 'improve_context';
  priority: 'high' | 'medium' | 'low';
  description: string;
  expectedImpact: number;
  implementation: string;
  locations: string[];
}

export interface CompetitorDensityData {
  averageDensity: number;
  minDensity: number;
  maxDensity: number;
  standardDeviation: number;
  topPerformers: PerformerData[];
}

export interface PerformerData {
  rank: number;
  density: number;
  frequency: number;
  domain: string;
}

export interface RiskAssessment {
  overOptimizationRisk: number;
  keywordStuffingRisk: number;
  unnaturalDistributionRisk: number;
  recommendations: string[];
}

// Missing interfaces for exports
export interface KeywordDensityResult {
  primaryKeyword: KeywordAnalysis;
  secondaryKeywords: KeywordAnalysis[];
  overallScore: number;
  recommendations: OptimizationRecommendation[];
  competitorComparison: CompetitorDensityData;
}

export interface DensityRecommendation {
  type: 'increase' | 'decrease' | 'redistribute';
  priority: 'high' | 'medium' | 'low';
  description: string;
  expectedImpact: number;
  targetDensity: number;
}

// NLP Processing interfaces
interface TextAnalysis {
  sentences: ProcessedSentence[];
  paragraphs: ProcessedParagraph[];
  headings: ProcessedHeading[];
  totalWords: number;
  readabilityScore: number;
  topicCoherence: number;
}

interface ProcessedSentence {
  text: string;
  wordCount: number;
  position: number;
  sentiment: number;
  complexity: number;
  keywords: string[];
}

interface ProcessedParagraph {
  text: string;
  sentences: ProcessedSentence[];
  topic: string;
  coherenceScore: number;
  keywordDensity: number;
}

interface ProcessedHeading {
  level: number;
  text: string;
  keywords: string[];
  section: string;
  semanticValue: number;
}

export class KeywordDensityAnalyzer {
  private static instance: KeywordDensityAnalyzer;
  
  // Advanced NLP configurations
  private readonly OPTIMAL_DENSITY_RANGE = { min: 0.5, max: 3.0 };
  private readonly SECTION_WEIGHTS = {
    title: 10.0,
    meta: 8.0,
    h1: 9.0,
    h2: 7.0,
    h3: 5.0,
    h4: 3.0,
    h5: 2.0,
    h6: 1.5,
    paragraph: 1.0,
    list: 1.2,
    caption: 1.1,
  };
  
  private readonly STOP_WORDS = new Set([
    'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
    'from', 'as', 'is', 'was', 'are', 'were', 'been', 'be', 'have', 'has', 'had',
    'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must',
    'can', 'this', 'that', 'these', 'those', 'what', 'when', 'where', 'why', 'how',
    'which', 'who', 'whom', 'whose', 'a', 'an', 'i', 'you', 'he', 'she', 'it', 'we',
    'they', 'me', 'him', 'her', 'us', 'them', 'my', 'your', 'his', 'her', 'its',
    'our', 'their', 'myself', 'yourself', 'himself', 'herself', 'itself', 'ourselves',
    'yourselves', 'themselves'
  ]);

  static getInstance(): KeywordDensityAnalyzer {
    if (!KeywordDensityAnalyzer.instance) {
      KeywordDensityAnalyzer.instance = new KeywordDensityAnalyzer();
    }
    return KeywordDensityAnalyzer.instance;
  }

  // Main analysis method
  async analyzeKeywordDensity(
    content: string,
    primaryKeyword: string,
    competitorData?: CompetitorDensityData,
    options: {
      includeVariations?: boolean;
      semanticAnalysis?: boolean;
      proximityAnalysis?: boolean;
      contextAnalysis?: boolean;
    } = {}
  ): Promise<KeywordAnalysis> {
    console.log('Starting advanced keyword density analysis...', {
      primaryKeyword,
      contentLength: content.length,
      options,
    });

    // Step 1: Parse and structure content
    const textAnalysis = await this.parseContent(content);
    
    // Step 2: Extract keyword positions and variations
    const positions = await this.findKeywordPositions(content, primaryKeyword, textAnalysis);
    
    // Step 3: Find keyword variations if enabled
    const variations = options.includeVariations 
      ? await this.findKeywordVariations(content, primaryKeyword, textAnalysis)
      : [];

    // Step 4: Analyze context if enabled
    const context = options.contextAnalysis
      ? await this.analyzeKeywordContext(content, primaryKeyword, positions)
      : [];

    // Step 5: Calculate density metrics
    const frequency = positions.length;
    const totalWords = textAnalysis.totalWords;
    const density = (frequency / totalWords) * 100;

    // Step 6: Calculate semantic relevance
    const semanticRelevance = options.semanticAnalysis
      ? await this.calculateSemanticRelevance(content, primaryKeyword, textAnalysis)
      : 0.8; // Default relevance

    // Step 7: Proximity analysis
    if (options.proximityAnalysis) {
      await this.analyzeKeywordProximity(positions, variations);
    }

    // Step 8: Generate optimization recommendations
    const optimization = await this.generateOptimization(
      density,
      frequency,
      positions,
      variations,
      competitorData,
      textAnalysis
    );

    const result: KeywordAnalysis = {
      keyword: primaryKeyword,
      density: Math.round(density * 100) / 100,
      frequency,
      positions,
      variations,
      context,
      semanticRelevance: Math.round(semanticRelevance * 100) / 100,
      optimization,
    };

    console.log('Keyword density analysis completed', {
      keyword: primaryKeyword,
      density: result.density,
      frequency: result.frequency,
      variations: result.variations.length,
      score: result.optimization.currentScore,
    });

    return result;
  }

  // Parse content into structured format
  private async parseContent(content: string): Promise<TextAnalysis> {
    // Remove HTML tags for text analysis
    const cleanText = this.cleanHtmlContent(content);
    
    // Extract headings with hierarchy
    const headings = this.extractHeadings(content);
    
    // Split into sentences
    const sentences = this.processSentences(cleanText);
    
    // Group into paragraphs
    const paragraphs = this.processParagraphs(cleanText, sentences);
    
    // Calculate metrics
    const totalWords = this.countWords(cleanText);
    const readabilityScore = this.calculateReadability(sentences);
    const topicCoherence = this.calculateTopicCoherence(paragraphs);

    return {
      sentences,
      paragraphs,
      headings,
      totalWords,
      readabilityScore,
      topicCoherence,
    };
  }

  // Find all keyword positions with context
  private async findKeywordPositions(
    content: string,
    keyword: string,
    textAnalysis: TextAnalysis
  ): Promise<KeywordPosition[]> {
    const positions: KeywordPosition[] = [];
    const keywordRegex = new RegExp(`\\b${this.escapeRegex(keyword)}\\b`, 'gi');
    const cleanContent = this.cleanHtmlContent(content);
    
    let match;
    while ((match = keywordRegex.exec(cleanContent)) !== null) {
      const position: KeywordPosition = {
        start: match.index,
        end: match.index + match[0].length,
        section: this.determineSection(content, match.index),
        sentence: this.extractSentence(cleanContent, match.index),
        proximity: [],
        weight: 0,
      };
      
      // Set weight based on section
      position.weight = this.SECTION_WEIGHTS[position.section] || 1.0;
      
      positions.push(position);
    }

    return positions;
  }

  // Find keyword variations and related terms
  private async findKeywordVariations(
    content: string,
    primaryKeyword: string,
    textAnalysis: TextAnalysis
  ): Promise<KeywordVariation[]> {
    const variations: KeywordVariation[] = [];
    const words = primaryKeyword.toLowerCase().split(/\s+/);
    
    // Find exact matches
    const exactMatches = this.findExactVariations(content, primaryKeyword);
    variations.push(...exactMatches);
    
    // Find partial matches
    const partialMatches = this.findPartialVariations(content, words);
    variations.push(...partialMatches);
    
    // Find stemmed variations
    const stemmedMatches = this.findStemmedVariations(content, words);
    variations.push(...stemmedMatches);
    
    // Find semantic variations
    const semanticMatches = await this.findSemanticVariations(content, primaryKeyword);
    variations.push(...semanticMatches);

    return variations.sort((a, b) => b.frequency - a.frequency);
  }

  // Analyze keyword context and co-occurrence
  private async analyzeKeywordContext(
    content: string,
    keyword: string,
    positions: KeywordPosition[]
  ): Promise<KeywordContext[]> {
    const contexts: KeywordContext[] = [];
    const cleanContent = this.cleanHtmlContent(content);
    
    for (const position of positions) {
      const windowSize = 50; // Words before and after
      const beforeStart = Math.max(0, position.start - windowSize * 6);
      const afterEnd = Math.min(cleanContent.length, position.end + windowSize * 6);
      
      const beforeText = cleanContent.substring(beforeStart, position.start);
      const afterText = cleanContent.substring(position.end, afterEnd);
      
      const beforeWords = this.extractWords(beforeText).slice(-windowSize);
      const afterWords = this.extractWords(afterText).slice(0, windowSize);
      
      // Analyze sentiment of surrounding context
      const contextText = beforeText + ' ' + afterText;
      const sentiment = this.analyzeSentiment(contextText);
      
      // Calculate topic relevance
      const topicRelevance = this.calculateTopicRelevance(
        keyword,
        beforeWords.concat(afterWords)
      );
      
      // Find co-occurring terms
      const coOccurrence = this.findCoOccurringTerms(
        beforeWords.concat(afterWords),
        keyword
      );

      contexts.push({
        beforeWords,
        afterWords,
        sentiment,
        topicRelevance,
        coOccurrence,
      });
    }

    return contexts;
  }

  // Calculate semantic relevance using advanced NLP
  private async calculateSemanticRelevance(
    content: string,
    keyword: string,
    textAnalysis: TextAnalysis
  ): Promise<number> {
    // Extract content themes
    const themes = this.extractContentThemes(textAnalysis);
    
    // Calculate keyword-theme alignment
    const themeAlignment = this.calculateThemeAlignment(keyword, themes);
    
    // Analyze semantic consistency
    const consistency = this.calculateSemanticConsistency(
      keyword,
      textAnalysis.sentences
    );
    
    // Calculate overall semantic relevance
    const relevance = (themeAlignment * 0.6) + (consistency * 0.4);
    
    return Math.max(0, Math.min(1, relevance));
  }

  // Analyze keyword proximity to other important terms
  private async analyzeKeywordProximity(
    positions: KeywordPosition[],
    variations: KeywordVariation[]
  ): Promise<void> {
    for (const position of positions) {
      const proximityScores: ProximityScore[] = [];
      
      // Analyze proximity to variations
      for (const variation of variations) {
        if (variation.type !== 'exact') {
          const distance = this.calculateProximityDistance(
            position,
            variation.term,
            position.sentence
          );
          
          if (distance < 20) { // Within 20 words
            proximityScores.push({
              nearbyKeyword: variation.term,
              distance,
              frequency: variation.frequency,
              relevance: variation.semanticSimilarity,
            });
          }
        }
      }
      
      position.proximity = proximityScores.sort((a, b) => a.distance - b.distance);
    }
  }

  // Generate comprehensive optimization recommendations
  private async generateOptimization(
    currentDensity: number,
    frequency: number,
    positions: KeywordPosition[],
    variations: KeywordVariation[],
    competitorData?: CompetitorDensityData,
    textAnalysis?: TextAnalysis
  ): Promise<KeywordOptimization> {
    const recommendations: OptimizationRecommendation[] = [];
    
    // Calculate optimal density based on competitors or defaults
    const optimalDensity = competitorData 
      ? this.calculateOptimalDensity(competitorData)
      : this.OPTIMAL_DENSITY_RANGE.min + 
        ((this.OPTIMAL_DENSITY_RANGE.max - this.OPTIMAL_DENSITY_RANGE.min) / 2);

    // Analyze current performance
    const currentScore = this.calculateKeywordScore(
      currentDensity,
      frequency,
      positions,
      variations,
      optimalDensity
    );

    // Generate density recommendations
    if (currentDensity < optimalDensity * 0.8) {
      recommendations.push({
        action: 'increase',
        priority: 'high',
        description: `Increase keyword density from ${currentDensity}% to ${optimalDensity}%`,
        expectedImpact: 15,
        implementation: 'Add keyword naturally in body paragraphs and subheadings',
        locations: ['h2', 'h3', 'paragraph'],
      });
    } else if (currentDensity > optimalDensity * 1.3) {
      recommendations.push({
        action: 'decrease',
        priority: 'high',
        description: `Reduce keyword density from ${currentDensity}% to avoid over-optimization`,
        expectedImpact: -5,
        implementation: 'Replace some exact matches with synonyms or variations',
        locations: this.identifyOverOptimizedSections(positions),
      });
    }

    // Analyze distribution
    const distributionAnalysis = this.analyzeKeywordDistribution(positions);
    if (distributionAnalysis.unevenDistribution) {
      recommendations.push({
        action: 'redistribute',
        priority: 'medium',
        description: 'Improve keyword distribution across content sections',
        expectedImpact: 8,
        implementation: 'Add keywords to underrepresented sections',
        locations: distributionAnalysis.underrepresentedSections,
      });
    }

    // Variation recommendations
    if (variations.length < 3) {
      recommendations.push({
        action: 'add_variations',
        priority: 'medium',
        description: 'Add more keyword variations and LSI terms',
        expectedImpact: 12,
        implementation: 'Include synonyms, related terms, and semantic variations',
        locations: ['paragraph', 'h3'],
      });
    }

    // Context improvement
    const contextQuality = this.assessContextQuality(positions, textAnalysis);
    if (contextQuality < 0.7) {
      recommendations.push({
        action: 'improve_context',
        priority: 'low',
        description: 'Improve context relevance around keywords',
        expectedImpact: 5,
        implementation: 'Use more topically relevant supporting terms',
        locations: this.identifyWeakContextSections(positions),
      });
    }

    // Risk assessment
    const riskAssessment = this.assessOptimizationRisks(
      currentDensity,
      frequency,
      positions,
      textAnalysis
    );

    return {
      currentScore: Math.round(currentScore * 100) / 100,
      optimalDensity: Math.round(optimalDensity * 100) / 100,
      recommendations: recommendations.sort((a, b) => {
        const priorityOrder = { high: 3, medium: 2, low: 1 };
        return priorityOrder[b.priority] - priorityOrder[a.priority];
      }),
      competitorComparison: competitorData || this.getDefaultCompetitorData(),
      riskAssessment,
    };
  }

  // Helper methods for content processing

  private cleanHtmlContent(html: string): string {
    return html
      .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
      .replace(/<style\b[^<]*(?:(?!<\/style>)<[^<]*)*<\/style>/gi, '')
      .replace(/<[^>]+>/g, ' ')
      .replace(/\s+/g, ' ')
      .trim();
  }

  private extractHeadings(content: string): ProcessedHeading[] {
    const headings: ProcessedHeading[] = [];
    const headingRegex = /<(h[1-6])[^>]*>(.*?)<\/\1>/gi;
    
    let match;
    while ((match = headingRegex.exec(content)) !== null) {
      const level = parseInt(match[1].charAt(1));
      const text = this.cleanHtmlContent(match[2]);
      const keywords = this.extractKeywords(text);
      
      headings.push({
        level,
        text,
        keywords,
        section: match[1],
        semanticValue: this.calculateSemanticValue(text, level),
      });
    }

    return headings;
  }

  private processSentences(text: string): ProcessedSentence[] {
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    
    return sentences.map((sentence, index) => ({
      text: sentence.trim(),
      wordCount: this.countWords(sentence),
      position: index,
      sentiment: this.analyzeSentiment(sentence),
      complexity: this.calculateSentenceComplexity(sentence),
      keywords: this.extractKeywords(sentence),
    }));
  }

  private processParagraphs(text: string, sentences: ProcessedSentence[]): ProcessedParagraph[] {
    const paragraphs = text.split(/\n\s*\n/).filter(p => p.trim().length > 50);
    
    return paragraphs.map(paragraph => ({
      text: paragraph.trim(),
      sentences: sentences.filter(s => paragraph.includes(s.text)),
      topic: this.extractMainTopic(paragraph),
      coherenceScore: this.calculateParagraphCoherence(paragraph),
      keywordDensity: this.calculateParagraphKeywordDensity(paragraph),
    }));
  }

  private countWords(text: string): number {
    return text.trim().split(/\s+/).filter(word => word.length > 0).length;
  }

  private calculateReadability(sentences: ProcessedSentence[]): number {
    if (sentences.length === 0) return 0;
    
    const avgWordsPerSentence = sentences.reduce((sum, s) => sum + s.wordCount, 0) / sentences.length;
    const avgComplexity = sentences.reduce((sum, s) => sum + s.complexity, 0) / sentences.length;
    
    // Simplified readability score (0-100, higher is better)
    const score = 100 - (avgWordsPerSentence * 1.5) - (avgComplexity * 20);
    return Math.max(0, Math.min(100, score));
  }

  private calculateTopicCoherence(paragraphs: ProcessedParagraph[]): number {
    if (paragraphs.length === 0) return 0;
    
    const avgCoherence = paragraphs.reduce((sum, p) => sum + p.coherenceScore, 0) / paragraphs.length;
    return Math.max(0, Math.min(1, avgCoherence));
  }

  private escapeRegex(string: string): string {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  private determineSection(content: string, position: number): KeywordPosition['section'] {
    const beforeContent = content.substring(0, position);
    
    // Check for headings
    const headingMatch = beforeContent.match(/<(h[1-6])[^>]*>[^<]*$/i);
    if (headingMatch) {
      return headingMatch[1].toLowerCase() as KeywordPosition['section'];
    }
    
    // Check for title
    if (beforeContent.includes('<title>') && !beforeContent.includes('</title>')) {
      return 'title';
    }
    
    // Check for meta description
    if (beforeContent.includes('meta') && beforeContent.includes('description')) {
      return 'meta';
    }
    
    // Default to paragraph
    return 'paragraph';
  }

  private extractSentence(content: string, position: number): string {
    const beforeContent = content.substring(0, position);
    const afterContent = content.substring(position);
    
    const sentenceStart = beforeContent.lastIndexOf('.') + 1;
    const sentenceEnd = afterContent.indexOf('.') + position;
    
    return content.substring(sentenceStart, sentenceEnd).trim();
  }

  private findExactVariations(content: string, keyword: string): KeywordVariation[] {
    const variations: KeywordVariation[] = [];
    const regex = new RegExp(`\\b${this.escapeRegex(keyword)}\\b`, 'gi');
    const matches = content.match(regex) || [];
    
    if (matches.length > 0) {
      variations.push({
        term: keyword,
        type: 'exact',
        frequency: matches.length,
        semanticSimilarity: 1.0,
        density: (matches.length / this.countWords(content)) * 100,
      });
    }
    
    return variations;
  }

  private findPartialVariations(content: string, words: string[]): KeywordVariation[] {
    const variations: KeywordVariation[] = [];
    const wordSet = new Set(words);
    
    for (const word of words) {
      const regex = new RegExp(`\\b${this.escapeRegex(word)}\\b`, 'gi');
      const matches = content.match(regex) || [];
      
      if (matches.length > 0) {
        variations.push({
          term: word,
          type: 'partial',
          frequency: matches.length,
          semanticSimilarity: 0.7,
          density: (matches.length / this.countWords(content)) * 100,
        });
      }
    }
    
    return variations;
  }

  private findStemmedVariations(content: string, words: string[]): KeywordVariation[] {
    const variations: KeywordVariation[] = [];
    
    for (const word of words) {
      const stem = this.stemWord(word);
      const regex = new RegExp(`\\b${this.escapeRegex(stem)}\\w*\\b`, 'gi');
      const matches = content.match(regex) || [];
      
      const uniqueMatches = Array.from(new Set(matches.map(m => m.toLowerCase())));
      
      for (const match of uniqueMatches) {
        if (match !== word.toLowerCase()) {
          const frequency = (content.match(new RegExp(`\\b${this.escapeRegex(match)}\\b`, 'gi')) || []).length;
          
          variations.push({
            term: match,
            type: 'stemmed',
            frequency,
            semanticSimilarity: 0.8,
            density: (frequency / this.countWords(content)) * 100,
          });
        }
      }
    }
    
    return variations;
  }

  private async findSemanticVariations(content: string, keyword: string): Promise<KeywordVariation[]> {
    // Simplified semantic analysis - in production, would use advanced NLP
    const synonyms = this.getSynonyms(keyword);
    const variations: KeywordVariation[] = [];
    
    for (const synonym of synonyms) {
      const regex = new RegExp(`\\b${this.escapeRegex(synonym)}\\b`, 'gi');
      const matches = content.match(regex) || [];
      
      if (matches.length > 0) {
        variations.push({
          term: synonym,
          type: 'synonym',
          frequency: matches.length,
          semanticSimilarity: 0.6,
          density: (matches.length / this.countWords(content)) * 100,
        });
      }
    }
    
    return variations;
  }

  private extractWords(text: string): string[] {
    return text.toLowerCase()
      .split(/\W+/)
      .filter(word => word.length > 2 && !this.STOP_WORDS.has(word));
  }

  private analyzeSentiment(text: string): number {
    // Simplified sentiment analysis
    const positiveWords = ['good', 'great', 'excellent', 'best', 'amazing', 'wonderful'];
    const negativeWords = ['bad', 'terrible', 'worst', 'awful', 'horrible', 'poor'];
    
    const words = this.extractWords(text);
    const positiveCount = words.filter(word => positiveWords.includes(word)).length;
    const negativeCount = words.filter(word => negativeWords.includes(word)).length;
    
    // Return sentiment as a number: 1 = positive, 0 = neutral, -1 = negative
    if (positiveCount > negativeCount) return 1;
    if (negativeCount > positiveCount) return -1;
    return 0;
  }

  private calculateTopicRelevance(keyword: string, contextWords: string[]): number {
    const keywordWords = keyword.toLowerCase().split(/\s+/);
    const relevantWords = contextWords.filter(word => 
      !this.STOP_WORDS.has(word) && !keywordWords.includes(word)
    );
    
    // Calculate semantic overlap (simplified)
    const semanticScore = this.calculateSemanticOverlap(keywordWords, relevantWords);
    return Math.max(0, Math.min(1, semanticScore));
  }

  private findCoOccurringTerms(words: string[], keyword: string): string[] {
    const keywordWords = keyword.toLowerCase().split(/\s+/);
    const coOccurring = words.filter(word => 
      !this.STOP_WORDS.has(word) && 
      !keywordWords.includes(word) &&
      word.length > 3
    );
    
    // Count frequency and return top co-occurring terms
    const frequency = new Map<string, number>();
    coOccurring.forEach(word => {
      frequency.set(word, (frequency.get(word) || 0) + 1);
    });
    
    return Array.from(frequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 10)
      .map(([word]) => word);
  }

  private extractContentThemes(textAnalysis: TextAnalysis): string[] {
    const allWords = textAnalysis.sentences
      .flatMap(s => s.keywords)
      .filter(word => !this.STOP_WORDS.has(word));
    
    const frequency = new Map<string, number>();
    allWords.forEach(word => {
      frequency.set(word, (frequency.get(word) || 0) + 1);
    });
    
    return Array.from(frequency.entries())
      .sort((a, b) => b[1] - a[1])
      .slice(0, 20)
      .map(([word]) => word);
  }

  private calculateThemeAlignment(keyword: string, themes: string[]): number {
    const keywordWords = keyword.toLowerCase().split(/\s+/);
    const matches = themes.filter(theme => 
      keywordWords.some(kw => theme.includes(kw) || kw.includes(theme))
    );
    
    return Math.min(1, matches.length / Math.max(keywordWords.length, 1));
  }

  private calculateSemanticConsistency(keyword: string, sentences: ProcessedSentence[]): number {
    const keywordSentences = sentences.filter(s => 
      s.text.toLowerCase().includes(keyword.toLowerCase())
    );
    
    if (keywordSentences.length === 0) return 0;
    
    const avgSentiment = keywordSentences.reduce((sum, s) => sum + s.sentiment, 0) / keywordSentences.length;
    const sentimentVariance = keywordSentences.reduce((sum, s) => 
      sum + Math.pow(s.sentiment - avgSentiment, 2), 0) / keywordSentences.length;
    
    // Lower variance = higher consistency
    return Math.max(0, 1 - Math.sqrt(sentimentVariance));
  }

  private calculateProximityDistance(position: KeywordPosition, term: string, sentence: string): number {
    const termIndex = sentence.toLowerCase().indexOf(term.toLowerCase());
    if (termIndex === -1) return Infinity;
    
    const sentenceWords = sentence.split(/\s+/);
    const keywordWordIndex = sentence.substring(0, position.start).split(/\s+/).length;
    const termWordIndex = sentence.substring(0, termIndex).split(/\s+/).length;
    
    return Math.abs(keywordWordIndex - termWordIndex);
  }

  private calculateOptimalDensity(competitorData: CompetitorDensityData): number {
    // Target slightly above average but below top performers
    const target = competitorData.averageDensity * 1.1;
    const maxSafe = Math.max(...competitorData.topPerformers.map(p => p.density)) * 0.9;
    
    return Math.min(target, maxSafe, this.OPTIMAL_DENSITY_RANGE.max);
  }

  private calculateKeywordScore(
    density: number,
    frequency: number,
    positions: KeywordPosition[],
    variations: KeywordVariation[],
    optimalDensity: number
  ): number {
    let score = 0;
    
    // Density score (40 points max)
    const densityRatio = density / optimalDensity;
    if (densityRatio >= 0.8 && densityRatio <= 1.2) {
      score += 40;
    } else if (densityRatio >= 0.6 && densityRatio <= 1.5) {
      score += 30;
    } else if (densityRatio >= 0.4 && densityRatio <= 2.0) {
      score += 20;
    } else {
      score += 10;
    }
    
    // Position score (30 points max)
    const weightedPositions = positions.reduce((sum, pos) => sum + pos.weight, 0);
    const avgWeight = weightedPositions / Math.max(positions.length, 1);
    score += Math.min(30, avgWeight * 3);
    
    // Variation score (20 points max)
    const variationBonus = Math.min(20, variations.length * 4);
    score += variationBonus;
    
    // Distribution score (10 points max)
    const sectionTypes = new Set(positions.map(p => p.section));
    const distributionBonus = Math.min(10, sectionTypes.size * 2);
    score += distributionBonus;
    
    return Math.max(0, Math.min(100, score));
  }

  private analyzeKeywordDistribution(positions: KeywordPosition[]): {
    unevenDistribution: boolean;
    underrepresentedSections: string[];
  } {
    const sectionCounts = new Map<string, number>();
    positions.forEach(pos => {
      sectionCounts.set(pos.section, (sectionCounts.get(pos.section) || 0) + 1);
    });
    
    const totalPositions = positions.length;
    const expectedPerSection = totalPositions / 8; // Assuming 8 main sections
    
    const underrepresented = Array.from(sectionCounts.entries())
      .filter(([section, count]) => count < expectedPerSection * 0.5)
      .map(([section]) => section);
    
    return {
      unevenDistribution: underrepresented.length > 0,
      underrepresentedSections: underrepresented,
    };
  }

  private identifyOverOptimizedSections(positions: KeywordPosition[]): string[] {
    const sectionCounts = new Map<string, number>();
    positions.forEach(pos => {
      sectionCounts.set(pos.section, (sectionCounts.get(pos.section) || 0) + 1);
    });
    
    const totalPositions = positions.length;
    const avgPerSection = totalPositions / sectionCounts.size;
    
    return Array.from(sectionCounts.entries())
      .filter(([section, count]) => count > avgPerSection * 2)
      .map(([section]) => section);
  }

  private assessContextQuality(positions: KeywordPosition[], textAnalysis?: TextAnalysis): number {
    if (!textAnalysis || positions.length === 0) return 0.5;
    
    // Simplified context quality assessment
    const avgSentenceComplexity = textAnalysis.sentences.reduce((sum, s) => sum + s.complexity, 0) / textAnalysis.sentences.length;
    const topicCoherence = textAnalysis.topicCoherence;
    
    return (avgSentenceComplexity * 0.3) + (topicCoherence * 0.7);
  }

  private identifyWeakContextSections(positions: KeywordPosition[]): string[] {
    // Return sections that might need context improvement
    return positions
      .filter(pos => pos.weight < 2.0)
      .map(pos => pos.section);
  }

  private assessOptimizationRisks(
    density: number,
    frequency: number,
    positions: KeywordPosition[],
    textAnalysis?: TextAnalysis
  ): RiskAssessment {
    let overOptimizationRisk = 0;
    let keywordStuffingRisk = 0;
    let unnaturalDistributionRisk = 0;
    const recommendations: string[] = [];
    
    // Over-optimization risk
    if (density > 4.0) {
      overOptimizationRisk = 0.8;
      recommendations.push('Consider reducing keyword density to avoid over-optimization penalties');
    } else if (density > 3.0) {
      overOptimizationRisk = 0.4;
    }
    
    // Keyword stuffing risk
    const avgWordsPerKeyword = textAnalysis ? textAnalysis.totalWords / frequency : 100;
    if (avgWordsPerKeyword < 50) {
      keywordStuffingRisk = 0.9;
      recommendations.push('Keywords appear too frequently - risk of keyword stuffing');
    } else if (avgWordsPerKeyword < 100) {
      keywordStuffingRisk = 0.5;
    }
    
    // Unnatural distribution risk
    const sectionTypes = new Set(positions.map(p => p.section));
    if (sectionTypes.size < 3) {
      unnaturalDistributionRisk = 0.7;
      recommendations.push('Distribute keywords more naturally across different content sections');
    } else if (sectionTypes.size < 5) {
      unnaturalDistributionRisk = 0.3;
    }
    
    return {
      overOptimizationRisk,
      keywordStuffingRisk,
      unnaturalDistributionRisk,
      recommendations,
    };
  }

  private getDefaultCompetitorData(): CompetitorDensityData {
    return {
      averageDensity: 2.0,
      minDensity: 0.5,
      maxDensity: 4.0,
      standardDeviation: 0.8,
      topPerformers: [
        { rank: 1, density: 2.8, frequency: 15, domain: 'example1.com' },
        { rank: 2, density: 2.2, frequency: 12, domain: 'example2.com' },
        { rank: 3, density: 1.9, frequency: 10, domain: 'example3.com' },
      ],
    };
  }

  // Additional helper methods
  private extractKeywords(text: string): string[] {
    return this.extractWords(text).slice(0, 10);
  }

  private calculateSemanticValue(text: string, level: number): number {
    const wordCount = this.countWords(text);
    const baseValue = 10 - level; // H1 = 9, H2 = 8, etc.
    const lengthBonus = Math.min(5, wordCount / 2);
    return baseValue + lengthBonus;
  }

  private calculateSentenceComplexity(sentence: string): number {
    const wordCount = this.countWords(sentence);
    const commaCount = (sentence.match(/,/g) || []).length;
    const complexWords = sentence.split(/\s+/).filter(word => word.length > 7).length;
    
    return (wordCount * 0.1) + (commaCount * 0.2) + (complexWords * 0.3);
  }

  private extractMainTopic(paragraph: string): string {
    const words = this.extractWords(paragraph);
    const frequency = new Map<string, number>();
    
    words.forEach(word => {
      frequency.set(word, (frequency.get(word) || 0) + 1);
    });
    
    const topWord = Array.from(frequency.entries())
      .sort((a, b) => b[1] - a[1])[0];
    
    return topWord ? topWord[0] : 'general';
  }

  private calculateParagraphCoherence(paragraph: string): number {
    const sentences = paragraph.split(/[.!?]+/).filter(s => s.trim().length > 10);
    if (sentences.length < 2) return 1;
    
    const keywords = sentences.map(s => new Set(this.extractWords(s)));
    let overlapSum = 0;
    let comparisons = 0;
    
    for (let i = 0; i < keywords.length - 1; i++) {
      for (let j = i + 1; j < keywords.length; j++) {
        const intersection = new Set([...keywords[i]].filter(x => keywords[j].has(x)));
        const union = new Set([...keywords[i], ...keywords[j]]);
        overlapSum += intersection.size / union.size;
        comparisons++;
      }
    }
    
    return comparisons > 0 ? overlapSum / comparisons : 0;
  }

  private calculateParagraphKeywordDensity(paragraph: string): number {
    const words = this.extractWords(paragraph);
    const uniqueWords = new Set(words);
    return uniqueWords.size / Math.max(words.length, 1);
  }

  private stemWord(word: string): string {
    // Simplified stemming - remove common suffixes
    const suffixes = ['ing', 'ed', 'er', 'est', 'ly', 's'];
    let stemmed = word.toLowerCase();
    
    for (const suffix of suffixes) {
      if (stemmed.endsWith(suffix) && stemmed.length > suffix.length + 2) {
        stemmed = stemmed.slice(0, -suffix.length);
        break;
      }
    }
    
    return stemmed;
  }

  private getSynonyms(keyword: string): string[] {
    // Simplified synonym dictionary - in production, would use external API
    const synonymMap: { [key: string]: string[] } = {
      'marketing': ['advertising', 'promotion', 'branding'],
      'business': ['company', 'enterprise', 'organization'],
      'technology': ['tech', 'digital', 'innovation'],
      'service': ['solution', 'offering', 'assistance'],
      'product': ['item', 'goods', 'merchandise'],
    };
    
    const words = keyword.toLowerCase().split(/\s+/);
    const synonyms: string[] = [];
    
    words.forEach(word => {
      if (synonymMap[word]) {
        synonyms.push(...synonymMap[word]);
      }
    });
    
    return synonyms;
  }

  private calculateSemanticOverlap(keywordWords: string[], contextWords: string[]): number {
    if (keywordWords.length === 0 || contextWords.length === 0) return 0;
    
    const stemmedKeywords = keywordWords.map(w => this.stemWord(w));
    const stemmedContext = contextWords.map(w => this.stemWord(w));
    
    const matches = stemmedKeywords.filter(kw => 
      stemmedContext.some(cw => cw.includes(kw) || kw.includes(cw))
    );
    
    return matches.length / keywordWords.length;
  }
}

// Export singleton instance
export const keywordDensityAnalyzer = KeywordDensityAnalyzer.getInstance();
export default KeywordDensityAnalyzer;
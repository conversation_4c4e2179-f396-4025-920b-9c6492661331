(()=>{var e={};e.id=436,e.ids=[436],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},9718:(e,r,t)=>{"use strict";t.r(r),t.d(r,{headerHooks:()=>h,originalPathname:()=>b,patchFetch:()=>f,requestAsyncStorage:()=>S,routeModule:()=>g,serverHooks:()=>v,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>y});var s={};t.r(s),t.d(s,{POST:()=>m});var a=t(5419),o=t(9108),i=t(9678),n=t(8070),u=t(2045),c=t(5252),l=t(2178),p=t(9393);let d=c.Ry({email:c.Z_().email("Invalid email address")});async function m(e){try{let r=await e.json(),{email:t}=d.parse(r),s=(0,u.jq)(),{error:a}=await s.auth.resetPasswordForEmail(t,{redirectTo:`${e.nextUrl.origin}/auth/reset-password`});if(a)return await (0,p.Tj)({action_type:"password_reset_failed",action_details:{email:t,error:a.message},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),n.Z.json({error:a.message},{status:400});return await (0,p.Tj)({action_type:"password_reset_requested",action_details:{email:t},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),n.Z.json({success:!0,message:"Password reset email sent. Please check your inbox."})}catch(e){if(console.error("Password reset error:",e),e instanceof l.jm)return n.Z.json({error:"Invalid input",details:e.errors},{status:400});return n.Z.json({error:"Internal server error"},{status:500})}}let g=new a.AppRouteRouteModule({definition:{kind:o.x.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/auth/reset-password/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:S,staticGenerationAsyncStorage:x,serverHooks:v,headerHooks:h,staticGenerationBailout:y}=g,b="/api/auth/reset-password/route";function f(){return(0,i.patchFetch)({serverHooks:v,staticGenerationAsyncStorage:x})}},6843:(e,r,t)=>{"use strict";Object.defineProperty(r,"__esModule",{value:!0}),Object.defineProperty(r,"createProxy",{enumerable:!0,get:function(){return s}});let s=t(8195).createClientModuleProxy},482:(e,r,t)=>{"use strict";e.exports=t(399)},8195:(e,r,t)=>{"use strict";e.exports=t(482).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},2045:(e,r,t)=>{"use strict";t.d(r,{jq:()=>o});var s=t(7699),a=t(2455);let o=()=>(0,s.createServerComponentClient)({cookies:a.cookies})},9393:(e,r,t)=>{"use strict";t.d(r,{xc:()=>v,Tj:()=>h,pR:()=>S});var s=t(1971),a=t(7699),o=t(6843);let i=(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts`),{__esModule:n,$$typeof:u}=i;i.default;let c=(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#validateEnvironment`),l=(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#getEnvironment`);(0,o.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#logEnvironmentStatus`);let p=l(),d=c(),m=(e,r)=>{};d.isValid||(["Supabase configuration error:",...d.missingVars.map(e=>`- Missing: ${e}`),...d.errors.map(e=>`- Error: ${e}`)].join("\n"),m("Environment validation failed",{validation:d}));let g=null;try{p.supabaseUrl&&p.supabaseKey?(0,s.eI)(p.supabaseUrl,p.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):m("Cannot create Supabase client: missing URL or key")}catch(e){m("Failed to create Supabase client",e)}try{p.supabaseUrl&&p.supabaseServiceKey&&(g=(0,s.eI)(p.supabaseUrl,p.supabaseServiceKey,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}}))}catch(e){m("Failed to create Supabase admin client",e)}let S=g,x=()=>{try{return(0,a.createClientComponentClient)()}catch(e){throw m("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function v(e){let r=x(),{error:t}=await r.from("api_usage_logs").insert(e);t&&console.error("Error logging API usage:",t)}async function h(e){let r=x(),{error:t}=await r.from("user_activity_logs").insert(e);t&&console.error("Error logging user activity:",t)}}};var r=require("../../../../webpack-runtime.js");r.C(e);var t=e=>r(r.s=e),s=r.X(0,[638,280,252],()=>t(9718));module.exports=s})();
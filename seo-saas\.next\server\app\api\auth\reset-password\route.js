(()=>{var e={};e.id=436,e.ids=[436],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},948:()=>{},9718:(e,t,s)=>{"use strict";s.r(t),s.d(t,{headerHooks:()=>q,originalPathname:()=>f,patchFetch:()=>w,requestAsyncStorage:()=>g,routeModule:()=>m,serverHooks:()=>I,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>y});var r={};s.r(r),s.d(r,{POST:()=>h});var a=s(5419),i=s(9108),o=s(9678),n=s(8070),u=s(2045),c=s(5252),p=s(2178),d=s(6517);let l=c.Ry({email:c.Z_().email("Invalid email address")});async function h(e){try{let t=await e.json(),{email:s}=l.parse(t),r=(0,u.jq)(),{error:a}=await r.auth.resetPasswordForEmail(s,{redirectTo:`${e.nextUrl.origin}/auth/reset-password`});if(a)return await (0,d.Tj)({action_type:"password_reset_failed",action_details:{email:s,error:a.message},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),n.Z.json({error:a.message},{status:400});return await (0,d.Tj)({action_type:"password_reset_requested",action_details:{email:s},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),n.Z.json({success:!0,message:"Password reset email sent. Please check your inbox."})}catch(e){if(console.error("Password reset error:",e),e instanceof p.jm)return n.Z.json({error:"Invalid input",details:e.errors},{status:400});return n.Z.json({error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/reset-password/route",pathname:"/api/auth/reset-password",filename:"route",bundlePath:"app/api/auth/reset-password/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/auth/reset-password/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:g,staticGenerationAsyncStorage:x,serverHooks:I,headerHooks:q,staticGenerationBailout:y}=m,f="/api/auth/reset-password/route";function w(){return(0,o.patchFetch)({serverHooks:I,staticGenerationAsyncStorage:x})}},2045:(e,t,s)=>{"use strict";s.d(t,{jq:()=>i});var r=s(7699),a=s(2455);let i=()=>(0,r.createServerComponentClient)({cookies:a.cookies})},6517:(e,t,s)=>{"use strict";s.d(t,{Tj:()=>u,pR:()=>o,xc:()=>n});var r=s(1971),a=s(7699);(0,r.eI)("https://xpcbyzcaidfukddqniny.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}});let i=()=>(0,a.createClientComponentClient)(),o=(0,r.eI)("https://xpcbyzcaidfukddqniny.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY||"",{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}});async function n(e){let t=i(),{error:s}=await t.from("api_usage_logs").insert(e);s&&console.error("Error logging API usage:",s)}async function u(e){let t=i(),{error:s}=await t.from("user_activity_logs").insert(e);s&&console.error("Error logging user activity:",s)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,280,252],()=>s(9718));module.exports=r})();
(()=>{var e={};e.id=98,e.ids=[98],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},1017:e=>{"use strict";e.exports=require("path")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},3798:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>n.a,__next_app__:()=>m,originalPathname:()=>u,pages:()=>d,routeModule:()=>x,tree:()=>c});var s=r(482),a=r(9108),i=r(2563),n=r.n(i),l=r(8300),o={};for(let e in l)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(o[e]=()=>l[e]);r.d(t,o);let c=["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,4841)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/signin/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,6343)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/signin/page.tsx"],u="/auth/signin/page",m={require:r,loadChunk:()=>Promise.resolve()},x=new s.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},1361:(e,t,r)=>{Promise.resolve().then(r.bind(r,5995))},5995:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p});var s=r(2295),a=r(3729),i=r(783),n=r.n(i),l=r(5094),o=r(6540),c=r(3673),d=r(2416),u=r(3969),m=r(7623),x=r(7993);function p(){let[e,t]=a.useState({email:"",password:""}),[i,p]=a.useState(!1),[h,f]=a.useState(!1),[g,v]=a.useState({}),b=(e,r)=>{t(t=>({...t,[e]:r})),g[e]&&v(t=>({...t,[e]:""}))},y=()=>{let t={};return e.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||(t.email="Please enter a valid email address"):t.email="Email is required",e.password?e.password.length<6&&(t.password="Password must be at least 6 characters"):t.password="Password is required",v(t),0===Object.keys(t).length},w=async t=>{if(t.preventDefault(),y()){f(!0);try{let{signIn:t}=await Promise.resolve().then(r.bind(r,9299));await t(e.email,e.password),window.location.href="/dashboard"}catch(e){v({general:e.message||"Invalid email or password. Please try again."})}finally{f(!1)}}},j=async()=>{try{let{signInWithGoogle:e}=await Promise.resolve().then(r.bind(r,9299));await e()}catch(e){v({general:e.message||"Failed to sign in with Google."})}},N=async()=>{try{let{signInWithGitHub:e}=await Promise.resolve().then(r.bind(r,9299));await e()}catch(e){v({general:e.message||"Failed to sign in with GitHub."})}};return s.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[s.jsx(n(),{href:"/",className:"inline-block",children:s.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"SEO Pro"})}),s.jsx("p",{className:"text-gray-600",children:"Welcome back! Sign in to your account"})]}),(0,s.jsxs)(c.Zb,{className:"shadow-lg",children:[(0,s.jsxs)(c.Ol,{className:"space-y-1",children:[s.jsx(c.ll,{className:"text-2xl font-bold text-center",children:"Sign In"}),s.jsx(c.SZ,{className:"text-center",children:"Enter your credentials to access your account"})]}),(0,s.jsxs)(c.aY,{children:[(0,s.jsxs)("form",{onSubmit:w,className:"space-y-4",children:[g.general&&s.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md",children:s.jsx("p",{className:"text-sm text-red-600",children:g.general})}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})}),s.jsx(o.I,{id:"email",type:"email",placeholder:"Enter your email",value:e.email,onChange:e=>b("email",e.target.value),error:g.email,className:"pl-10",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[s.jsx("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:s.jsx(u.Z,{className:"h-5 w-5 text-gray-400"})}),s.jsx(o.I,{id:"password",type:i?"text":"password",placeholder:"Enter your password",value:e.password,onChange:e=>b("password",e.target.value),error:g.password,className:"pl-10 pr-10",required:!0}),s.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>p(!i),children:i?s.jsx(m.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):s.jsx(x.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,s.jsxs)("div",{className:"flex items-center justify-between",children:[(0,s.jsxs)("label",{className:"flex items-center",children:[s.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),s.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Remember me"})]}),s.jsx(n(),{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"Forgot password?"})]}),s.jsx(l.z,{type:"submit",className:"w-full",loading:h,disabled:h,children:h?"Signing In...":"Sign In"})]}),s.jsx("div",{className:"mt-6",children:(0,s.jsxs)("div",{className:"relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center",children:s.jsx("div",{className:"w-full border-t border-gray-300"})}),s.jsx("div",{className:"relative flex justify-center text-sm",children:s.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]})}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,s.jsxs)(l.z,{variant:"outline",className:"w-full",onClick:j,type:"button",children:[(0,s.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[s.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),s.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),s.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),s.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]}),(0,s.jsxs)(l.z,{variant:"outline",className:"w-full",onClick:N,type:"button",children:[s.jsx("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{d:"M12 0c-6.626 0-12 5.373-12 12 0 5.302 3.438 9.8 8.207 11.387.599.111.793-.261.793-.577v-2.234c-3.338.726-4.033-1.416-4.033-1.416-.546-1.387-1.333-1.756-1.333-1.756-1.089-.745.083-.729.083-.729 1.205.084 1.839 1.237 1.839 1.237 1.07 1.834 2.807 1.304 3.492.997.107-.775.418-1.305.762-1.604-2.665-.305-5.467-1.334-5.467-5.931 0-1.311.469-2.381 1.236-3.221-.124-.303-.535-1.524.117-3.176 0 0 1.008-.322 3.301 1.23.957-.266 1.983-.399 3.003-.404 1.02.005 2.047.138 3.006.404 2.291-1.552 3.297-1.23 3.297-1.23.653 1.653.242 2.874.118 3.176.77.84 1.235 1.911 1.235 3.221 0 4.609-2.807 5.624-5.479 5.921.43.372.823 1.102.823 2.222v3.293c0 .319.192.694.801.576 4.765-1.589 8.199-6.086 8.199-11.386 0-6.627-5.373-12-12-12z"})}),"GitHub"]})]}),s.jsx("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",s.jsx(n(),{href:"/auth/signup",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up for free"})]})})]})]}),s.jsx("div",{className:"mt-8 text-center",children:(0,s.jsxs)("p",{className:"text-xs text-gray-500",children:["By signing in, you agree to our"," ",s.jsx(n(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",s.jsx(n(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})})]})})}},5094:(e,t,r)=>{"use strict";r.d(t,{z:()=>c});var s=r(2295),a=r(3729),i=r(5877),n=r(9247),l=r(1453);let o=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),c=a.forwardRef(({className:e,variant:t,size:r,loading:a,children:n,disabled:c,asChild:d=!1,...u},m)=>{let x=d?i.g7:"button";return(0,s.jsxs)(x,{className:(0,l.cn)(o({variant:t,size:r,className:e})),ref:m,disabled:c||a,...u,children:[a&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),n]})});c.displayName="Button"},3673:(e,t,r)=>{"use strict";r.d(t,{Ol:()=>l,SZ:()=>c,Zb:()=>n,aY:()=>d,ll:()=>o});var s=r(2295),a=r(3729),i=r(1453);let n=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));n.displayName="Card";let l=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex flex-col space-y-1.5 p-6",e),...t}));l.displayName="CardHeader";let o=a.forwardRef(({className:e,...t},r)=>s.jsx("h3",{ref:r,className:(0,i.cn)("text-2xl font-semibold leading-none tracking-tight",e),...t}));o.displayName="CardTitle";let c=a.forwardRef(({className:e,...t},r)=>s.jsx("p",{ref:r,className:(0,i.cn)("text-sm text-muted-foreground",e),...t}));c.displayName="CardDescription";let d=a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("p-6 pt-0",e),...t}));d.displayName="CardContent",a.forwardRef(({className:e,...t},r)=>s.jsx("div",{ref:r,className:(0,i.cn)("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter"},6540:(e,t,r)=>{"use strict";r.d(t,{I:()=>n});var s=r(2295),a=r(3729),i=r(1453);let n=a.forwardRef(({className:e,type:t,label:r,error:n,helperText:l,...o},c)=>{let d=a.useId();return(0,s.jsxs)("div",{className:"space-y-2",children:[r&&(0,s.jsxs)("label",{htmlFor:d,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[r,o.required&&s.jsx("span",{className:"text-red-500 ml-1",children:"*"})]}),s.jsx("input",{id:d,type:t,className:(0,i.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",n&&"border-red-500 focus-visible:ring-red-500",e),ref:c,...o}),n&&s.jsx("p",{className:"text-sm text-red-600",children:n}),l&&!n&&s.jsx("p",{className:"text-sm text-muted-foreground",children:l})]})});n.displayName="Input"},1453:(e,t,r)=>{"use strict";r.d(t,{KG:()=>d,cn:()=>i,p6:()=>l,rl:()=>o,uf:()=>n,vQ:()=>c});var s=r(6815),a=r(9377);function i(...e){return(0,a.m6)((0,s.W)(e))}function n(e){return e.toLocaleString()}function l(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function o(e){return`${e.toFixed(1)}%`}function c(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");return t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),Promise.resolve()}function d(e,t,r="text/plain"){let s=new Blob([e],{type:r}),a=URL.createObjectURL(s),i=document.createElement("a");i.href=a,i.download=t,document.body.appendChild(i),i.click(),document.body.removeChild(i),URL.revokeObjectURL(a)}},4841:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>i,__esModule:()=>a,default:()=>n});let s=(0,r(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/signin/page.tsx`),{__esModule:a,$$typeof:i}=s,n=s.default},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>a});var s=r(337);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,s.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},2416:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(3729);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))})},7993:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(3729);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))})},7623:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(3729);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))})},3969:(e,t,r)=>{"use strict";r.d(t,{Z:()=>a});var s=r(3729);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))})}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,972,337,35,783,129],()=>r(3798));module.exports=s})();
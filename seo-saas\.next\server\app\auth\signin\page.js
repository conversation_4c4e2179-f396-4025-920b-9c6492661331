(()=>{var e={};e.id=98,e.ids=[98],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},3798:(e,s,t)=>{"use strict";t.r(s),t.d(s,{GlobalError:()=>i.a,__next_app__:()=>x,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>o});var a=t(482),r=t(9108),l=t(2563),i=t.n(l),n=t(8300),c={};for(let e in n)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(c[e]=()=>n[e]);t.d(s,c);let o=["",{children:["auth",{children:["signin",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(t.bind(t,4841)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/signin/page.tsx"]}]},{}]},{metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(t.bind(t,1342)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(t.t.bind(t,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(t.bind(t,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/signin/page.tsx"],m="/auth/signin/page",x={require:t,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:r.x.APP_PAGE,page:"/auth/signin/page",pathname:"/auth/signin",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:o}})},1361:(e,s,t)=>{Promise.resolve().then(t.bind(t,5995))},5995:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>p});var a=t(2295),r=t(3729),l=t(783),i=t.n(l),n=t(5094),c=t(6540),o=t(3673),d=t(2416),m=t(3969),x=t(7623),u=t(7993);function p(){let[e,s]=r.useState({email:"",password:""}),[t,l]=r.useState(!1),[p,h]=r.useState(!1),[g,j]=r.useState({}),v=(e,t)=>{s(s=>({...s,[e]:t})),g[e]&&j(s=>({...s,[e]:""}))},f=()=>{let s={};return e.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||(s.email="Please enter a valid email address"):s.email="Email is required",e.password?e.password.length<6&&(s.password="Password must be at least 6 characters"):s.password="Password is required",j(s),0===Object.keys(s).length},y=async e=>{if(e.preventDefault(),f()){h(!0);try{await new Promise(e=>setTimeout(e,1500)),window.location.href="/dashboard"}catch(e){j({general:"Invalid email or password. Please try again."})}finally{h(!1)}}};return a.jsx("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:(0,a.jsxs)("div",{className:"w-full max-w-md",children:[(0,a.jsxs)("div",{className:"text-center mb-8",children:[a.jsx(i(),{href:"/",className:"inline-block",children:a.jsx("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"SEO Pro"})}),a.jsx("p",{className:"text-gray-600",children:"Welcome back! Sign in to your account"})]}),(0,a.jsxs)(o.Zb,{className:"shadow-lg",children:[(0,a.jsxs)(o.Ol,{className:"space-y-1",children:[a.jsx(o.ll,{className:"text-2xl font-bold text-center",children:"Sign In"}),a.jsx(o.SZ,{className:"text-center",children:"Enter your credentials to access your account"})]}),(0,a.jsxs)(o.aY,{children:[(0,a.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[g.general&&a.jsx("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md",children:a.jsx("p",{className:"text-sm text-red-600",children:g.general})}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(d.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx(c.I,{id:"email",type:"email",placeholder:"Enter your email",value:e.email,onChange:e=>v("email",e.target.value),error:g.email,className:"pl-10",required:!0})]})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:a.jsx(m.Z,{className:"h-5 w-5 text-gray-400"})}),a.jsx(c.I,{id:"password",type:t?"text":"password",placeholder:"Enter your password",value:e.password,onChange:e=>v("password",e.target.value),error:g.password,className:"pl-10 pr-10",required:!0}),a.jsx("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>l(!t),children:t?a.jsx(x.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):a.jsx(u.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,a.jsxs)("div",{className:"flex items-center justify-between",children:[(0,a.jsxs)("label",{className:"flex items-center",children:[a.jsx("input",{type:"checkbox",className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500"}),a.jsx("span",{className:"ml-2 text-sm text-gray-600",children:"Remember me"})]}),a.jsx(i(),{href:"/auth/forgot-password",className:"text-sm text-blue-600 hover:text-blue-500",children:"Forgot password?"})]}),a.jsx(n.z,{type:"submit",className:"w-full",loading:p,disabled:p,children:p?"Signing In...":"Sign In"})]}),a.jsx("div",{className:"mt-6",children:(0,a.jsxs)("div",{className:"relative",children:[a.jsx("div",{className:"absolute inset-0 flex items-center",children:a.jsx("div",{className:"w-full border-t border-gray-300"})}),a.jsx("div",{className:"relative flex justify-center text-sm",children:a.jsx("span",{className:"px-2 bg-white text-gray-500",children:"Or continue with"})})]})}),(0,a.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,a.jsxs)(n.z,{variant:"outline",className:"w-full",children:[(0,a.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[a.jsx("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),a.jsx("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),a.jsx("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),a.jsx("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]}),(0,a.jsxs)(n.z,{variant:"outline",className:"w-full",children:[a.jsx("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:a.jsx("path",{d:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"})}),"Twitter"]})]}),a.jsx("div",{className:"mt-6 text-center",children:(0,a.jsxs)("p",{className:"text-sm text-gray-600",children:["Don't have an account?"," ",a.jsx(i(),{href:"/auth/signup",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign up for free"})]})})]})]}),a.jsx("div",{className:"mt-8 text-center",children:(0,a.jsxs)("p",{className:"text-xs text-gray-500",children:["By signing in, you agree to our"," ",a.jsx(i(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",a.jsx(i(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})})]})})}},4841:(e,s,t)=>{"use strict";t.r(s),t.d(s,{$$typeof:()=>l,__esModule:()=>r,default:()=>i});let a=(0,t(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/auth/signin/page.tsx`),{__esModule:r,$$typeof:l}=a,i=a.default}};var s=require("../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),a=s.X(0,[638,312,337,439,783,72],()=>t(3798));module.exports=a})();
import { NextRequest, NextResponse } from 'next/server';
import { createSupabaseServerClient } from '@/lib/supabase-server';
import { logUserActivity } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    const supabase = createSupabaseServerClient();
    
    // Get current user before signing out
    const { data: { user } } = await supabase.auth.getUser();
    
    // Sign out
    const { error } = await supabase.auth.signOut();
    
    if (error) {
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }
    
    // Log sign-out
    if (user) {
      await logUserActivity({
        user_id: user.id,
        action_type: 'signout',
        action_details: {
          email: user.email,
        },
        ip_address: request.ip,
        user_agent: request.headers.get('user-agent') || undefined,
      });
    }
    
    return NextResponse.json({
      success: true,
      message: 'Signed out successfully',
    });
    
  } catch (error) {
    console.error('Sign-out error:', error);
    
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
(()=>{var e={};e.id=267,e.ids=[267],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},931:(e,s,t)=>{"use strict";t.r(s),t.d(s,{headerHooks:()=>I,originalPathname:()=>_,patchFetch:()=>j,requestAsyncStorage:()=>h,routeModule:()=>m,serverHooks:()=>q,staticGenerationAsyncStorage:()=>x,staticGenerationBailout:()=>v});var r={};t.r(r),t.d(r,{POST:()=>g});var i=t(5419),a=t(9108),n=t(9678),o=t(8070),u=t(2045),c=t(5252),p=t(2178),l=t(6517);let d=c.Ry({email:c.Z_().email("Invalid email address"),password:c.Z_().min(6,"Password must be at least 6 characters")});async function g(e){try{let s=await e.json(),{email:t,password:r}=d.parse(s),i=(0,u.jq)(),{data:a,error:n}=await i.auth.signInWithPassword({email:t,password:r});if(n)return await (0,l.Tj)({action_type:"signin_failed",action_details:{email:t,error:n.message},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),o.Z.json({error:n.message},{status:401});return await (0,l.Tj)({user_id:a.user?.id,action_type:"signin_success",action_details:{email:t,method:"email_password"},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),o.Z.json({success:!0,user:a.user,session:a.session})}catch(e){if(console.error("Sign-in error:",e),e instanceof p.jm)return o.Z.json({error:"Invalid input",details:e.errors},{status:400});return o.Z.json({error:"Internal server error"},{status:500})}}let m=new i.AppRouteRouteModule({definition:{kind:a.x.APP_ROUTE,page:"/api/auth/signin/route",pathname:"/api/auth/signin",filename:"route",bundlePath:"app/api/auth/signin/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/auth/signin/route.ts",nextConfigOutput:"",userland:r}),{requestAsyncStorage:h,staticGenerationAsyncStorage:x,serverHooks:q,headerHooks:I,staticGenerationBailout:v}=m,_="/api/auth/signin/route";function j(){return(0,n.patchFetch)({serverHooks:q,staticGenerationAsyncStorage:x})}},2045:(e,s,t)=>{"use strict";t.d(s,{jq:()=>a});var r=t(7699),i=t(2455);let a=()=>(0,r.createServerComponentClient)({cookies:i.cookies})},6517:(e,s,t)=>{"use strict";t.d(s,{Tj:()=>u,pR:()=>n,xc:()=>o});var r=t(1971),i=t(7699);(0,r.eI)("https://zqrmpanonghggoxdjirq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}});let a=()=>(0,i.createClientComponentClient)(),n=(0,r.eI)("https://zqrmpanonghggoxdjirq.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}});async function o(e){let s=a(),{error:t}=await s.from("api_usage_logs").insert(e);t&&console.error("Error logging API usage:",t)}async function u(e){let s=a(),{error:t}=await s.from("user_activity_logs").insert(e);t&&console.error("Error logging user activity:",t)}}};var s=require("../../../../webpack-runtime.js");s.C(e);var t=e=>s(s.s=e),r=s.X(0,[638,280,252],()=>t(931));module.exports=r})();
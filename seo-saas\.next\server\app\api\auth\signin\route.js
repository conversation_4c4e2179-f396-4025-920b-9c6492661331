(()=>{var e={};e.id=267,e.ids=[267],e.modules={2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},517:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-route.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},931:(e,t,r)=>{"use strict";r.r(t),r.d(t,{headerHooks:()=>h,originalPathname:()=>y,patchFetch:()=>f,requestAsyncStorage:()=>S,routeModule:()=>m,serverHooks:()=>x,staticGenerationAsyncStorage:()=>v,staticGenerationBailout:()=>b});var s={};r.r(s),r.d(s,{POST:()=>g});var a=r(5419),i=r(9108),n=r(9678),o=r(8070),u=r(2045),c=r(5252),l=r(2178),p=r(9393);let d=c.Ry({email:c.Z_().email("Invalid email address"),password:c.Z_().min(6,"Password must be at least 6 characters")});async function g(e){try{let t=await e.json(),{email:r,password:s}=d.parse(t),a=(0,u.jq)(),{data:i,error:n}=await a.auth.signInWithPassword({email:r,password:s});if(n)return await (0,p.Tj)({action_type:"signin_failed",action_details:{email:r,error:n.message},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),o.Z.json({error:n.message},{status:401});return await (0,p.Tj)({user_id:i.user?.id,action_type:"signin_success",action_details:{email:r,method:"email_password"},ip_address:e.ip,user_agent:e.headers.get("user-agent")||void 0}),o.Z.json({success:!0,user:i.user,session:i.session})}catch(e){if(console.error("Sign-in error:",e),e instanceof l.jm)return o.Z.json({error:"Invalid input",details:e.errors},{status:400});return o.Z.json({error:"Internal server error"},{status:500})}}let m=new a.AppRouteRouteModule({definition:{kind:i.x.APP_ROUTE,page:"/api/auth/signin/route",pathname:"/api/auth/signin",filename:"route",bundlePath:"app/api/auth/signin/route"},resolvedPagePath:"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/api/auth/signin/route.ts",nextConfigOutput:"",userland:s}),{requestAsyncStorage:S,staticGenerationAsyncStorage:v,serverHooks:x,headerHooks:h,staticGenerationBailout:b}=m,y="/api/auth/signin/route";function f(){return(0,n.patchFetch)({serverHooks:x,staticGenerationAsyncStorage:v})}},6843:(e,t,r)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"createProxy",{enumerable:!0,get:function(){return s}});let s=r(8195).createClientModuleProxy},482:(e,t,r)=>{"use strict";e.exports=r(399)},8195:(e,t,r)=>{"use strict";e.exports=r(482).vendored["react-rsc"].ReactServerDOMWebpackServerEdge},2045:(e,t,r)=>{"use strict";r.d(t,{jq:()=>i});var s=r(7699),a=r(2455);let i=()=>(0,s.createServerComponentClient)({cookies:a.cookies})},9393:(e,t,r)=>{"use strict";r.d(t,{xc:()=>x,Tj:()=>h,pR:()=>S});var s=r(1971),a=r(7699),i=r(6843);let n=(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts`),{__esModule:o,$$typeof:u}=n;n.default;let c=(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#validateEnvironment`),l=(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#getEnvironment`);(0,i.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/lib/env.ts#logEnvironmentStatus`);let p=l(),d=c(),g=(e,t)=>{};d.isValid||(["Supabase configuration error:",...d.missingVars.map(e=>`- Missing: ${e}`),...d.errors.map(e=>`- Error: ${e}`)].join("\n"),g("Environment validation failed",{validation:d}));let m=null;try{p.supabaseUrl&&p.supabaseKey?(0,s.eI)(p.supabaseUrl,p.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):g("Cannot create Supabase client: missing URL or key")}catch(e){g("Failed to create Supabase client",e)}try{p.supabaseUrl&&p.supabaseServiceKey&&(m=(0,s.eI)(p.supabaseUrl,p.supabaseServiceKey,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}}))}catch(e){g("Failed to create Supabase admin client",e)}let S=m,v=()=>{try{return(0,a.createClientComponentClient)()}catch(e){throw g("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function x(e){let t=v(),{error:r}=await t.from("api_usage_logs").insert(e);r&&console.error("Error logging API usage:",r)}async function h(e){let t=v(),{error:r}=await t.from("user_activity_logs").insert(e);r&&console.error("Error logging user activity:",r)}}};var t=require("../../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),s=t.X(0,[638,280,252],()=>r(931));module.exports=s})();
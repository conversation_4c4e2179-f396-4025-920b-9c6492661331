(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[453,183],{1092:function(e,t,n){Promise.resolve().then(n.bind(n,4798))},4798:function(e,t,n){"use strict";n.r(t),n.d(t,{default:function(){return o}});var r=n(7437),a=n(2265),i=n(4033),s=n(5183);function o(){let e=(0,i.useRouter)(),t=(0,s.createSupabaseComponentClient)();return(0,a.useEffect)(()=>{(async()=>{try{let{data:n,error:r}=await t.auth.getSession();if(r){console.error("Auth callback error:",r),e.push("/auth/signin?error=callback_error");return}n.session?e.push("/dashboard"):e.push("/auth/signin")}catch(t){console.error("Unexpected error during auth callback:",t),e.push("/auth/signin?error=unexpected_error")}})()},[e,t.auth]),(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center",children:(0,r.jsxs)("div",{className:"text-center",children:[(0,r.jsx)("div",{className:"inline-block animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"}),(0,r.jsx)("p",{className:"mt-4 text-gray-600",children:"Completing authentication..."})]})})}},5183:function(e,t,n){"use strict";n.d(t,{createSupabaseComponentClient:function(){return l},signIn:function(){return p},signInWithGitHub:function(){return d},signInWithGoogle:function(){return f},signUp:function(){return h},OQ:function(){return u}});var r=n(1492),a=n(3082);let i={supabaseUrl:"https://xpcbyzcaidfukddqniny.supabase.co",supabaseKey:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A",supabaseServiceKey:""},s=function(){let e={NEXT_PUBLIC_SUPABASE_URL:"https://xpcbyzcaidfukddqniny.supabase.co",NEXT_PUBLIC_SUPABASE_ANON_KEY:"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A"},t=[],n=[];if(Object.entries(e).forEach(e=>{let[n,r]=e;r&&""!==r.trim()||t.push(n)}),e.NEXT_PUBLIC_SUPABASE_URL)try{new URL(e.NEXT_PUBLIC_SUPABASE_URL)}catch(e){n.push("NEXT_PUBLIC_SUPABASE_URL is not a valid URL")}if(e.NEXT_PUBLIC_SUPABASE_ANON_KEY){let t=e.NEXT_PUBLIC_SUPABASE_ANON_KEY;t.startsWith("eyJ")&&3===t.split(".").length||n.push("NEXT_PUBLIC_SUPABASE_ANON_KEY appears to be invalid (not a JWT token)")}return{isValid:0===t.length&&0===n.length,missingVars:t,errors:n,vars:e}}(),o=(e,t)=>{};s.isValid||(["Supabase configuration error:",...s.missingVars.map(e=>"- Missing: ".concat(e)),...s.errors.map(e=>"- Error: ".concat(e))].join("\n"),o("Environment validation failed",{validation:s}));let c=null;try{i.supabaseUrl&&i.supabaseKey?c=(0,r.eI)(i.supabaseUrl,i.supabaseKey,{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}):o("Cannot create Supabase client: missing URL or key")}catch(e){o("Failed to create Supabase client",e)}try{i.supabaseUrl&&i.supabaseServiceKey}catch(e){o("Failed to create Supabase admin client",e)}let u=c,l=()=>{try{return(0,a.createClientComponentClient)()}catch(e){throw o("Failed to create component client",e),Error("Supabase configuration error. Please check your environment variables.")}};async function p(e,t){let n=l(),{data:r,error:a}=await n.auth.signInWithPassword({email:e,password:t});if(a)throw Error(a.message);return r}async function h(e,t,n){let r=l(),{data:a,error:i}=await r.auth.signUp({email:e,password:t,options:{data:{full_name:n},emailRedirectTo:"".concat("http://localhost:3000","/auth/callback")}});if(i)throw Error(i.message);return a}async function f(){let e=l(),{data:t,error:n}=await e.auth.signInWithOAuth({provider:"google",options:{redirectTo:"".concat("http://localhost:3000","/auth/callback"),queryParams:{access_type:"offline",prompt:"consent"}}});if(n)throw Error(n.message);return t}async function d(){let e=l(),{data:t,error:n}=await e.auth.signInWithOAuth({provider:"github",options:{redirectTo:"".concat("http://localhost:3000","/auth/callback")}});if(n)throw Error(n.message);return t}},622:function(e,t,n){"use strict";var r=n(2265),a=Symbol.for("react.element"),i=Symbol.for("react.fragment"),s=Object.prototype.hasOwnProperty,o=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function u(e,t,n){var r,i={},u=null,l=null;for(r in void 0!==n&&(u=""+n),void 0!==t.key&&(u=""+t.key),void 0!==t.ref&&(l=t.ref),t)s.call(t,r)&&!c.hasOwnProperty(r)&&(i[r]=t[r]);if(e&&e.defaultProps)for(r in t=e.defaultProps)void 0===i[r]&&(i[r]=t[r]);return{$$typeof:a,type:e,key:u,ref:l,props:i,_owner:o.current}}t.Fragment=i,t.jsx=u,t.jsxs=u},7437:function(e,t,n){"use strict";e.exports=n(622)},4033:function(e,t,n){e.exports=n(5313)}},function(e){e.O(0,[82,971,938,744],function(){return e(e.s=1092)}),_N_E=e.O()}]);
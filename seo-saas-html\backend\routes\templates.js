const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth');

// Content templates for different industries and content types
const contentTemplates = {
  // Technology Industry Templates
  technology: {
    'blog-post': {
      structure: [
        'Introduction with problem statement',
        'Technical background and context',
        'Solution analysis with code examples',
        'Implementation best practices',
        'Performance considerations',
        'Troubleshooting common issues',
        'Future developments and trends',
        'Conclusion with actionable next steps'
      ],
      keyElements: [
        'Technical specifications',
        'Code snippets and examples',
        'Performance benchmarks',
        'Compatibility information',
        'Security considerations',
        'Integration guides'
      ],
      toneGuidelines: 'Technical yet accessible, precise, authoritative',
      seoFocus: 'Technical keywords, developer tools, programming languages'
    },
    'product-description': {
      structure: [
        'Product overview and key benefits',
        'Technical specifications',
        'Feature breakdown with use cases',
        'Integration capabilities',
        'Performance metrics',
        'Pricing and plans',
        'Customer testimonials',
        'Getting started guide'
      ],
      keyElements: [
        'Feature list with benefits',
        'Technical requirements',
        'API documentation links',
        'Compatibility matrix',
        'Use case scenarios',
        'ROI calculations'
      ],
      toneGuidelines: 'Professional, benefit-focused, technically accurate',
      seoFocus: 'Product keywords, feature terms, comparison keywords'
    }
  },

  // Healthcare Industry Templates
  healthcare: {
    'blog-post': {
      structure: [
        'Introduction with health context',
        'Medical background and research',
        'Evidence-based analysis',
        'Treatment options and approaches',
        'Risk factors and considerations',
        'Prevention and lifestyle factors',
        'Expert recommendations',
        'Conclusion with medical disclaimer'
      ],
      keyElements: [
        'Peer-reviewed research citations',
        'Medical terminology explanations',
        'Treatment efficacy data',
        'Side effects and contraindications',
        'Professional medical advice',
        'FDA approvals and regulations'
      ],
      toneGuidelines: 'Authoritative, empathetic, medically accurate',
      seoFocus: 'Medical conditions, treatments, symptoms, prevention'
    },
    'article': {
      structure: [
        'Medical overview and significance',
        'Current research and studies',
        'Clinical findings and data',
        'Expert opinions and guidelines',
        'Patient considerations',
        'Healthcare provider perspectives',
        'Future research directions',
        'Medical disclaimer and resources'
      ],
      keyElements: [
        'Clinical study results',
        'Medical expert quotes',
        'Statistical health data',
        'Treatment protocols',
        'Patient safety information',
        'Healthcare guidelines'
      ],
      toneGuidelines: 'Professional, evidence-based, trustworthy',
      seoFocus: 'Medical keywords, condition names, treatment terms'
    }
  },

  // Finance Industry Templates
  finance: {
    'blog-post': {
      structure: [
        'Financial context and market overview',
        'Economic background and trends',
        'Analysis of financial instruments',
        'Risk assessment and management',
        'Investment strategies and options',
        'Regulatory considerations',
        'Market predictions and forecasts',
        'Financial planning recommendations'
      ],
      keyElements: [
        'Market data and statistics',
        'Financial calculations and examples',
        'Regulatory compliance information',
        'Risk disclaimers',
        'Investment performance data',
        'Economic indicators'
      ],
      toneGuidelines: 'Professional, analytical, risk-aware',
      seoFocus: 'Financial terms, investment keywords, market analysis'
    },
    'landing-page': {
      structure: [
        'Value proposition and benefits',
        'Service offerings overview',
        'Expertise and credentials',
        'Client success stories',
        'Regulatory compliance badges',
        'Fee structure transparency',
        'Contact and consultation CTA',
        'Risk disclosures and disclaimers'
      ],
      keyElements: [
        'Regulatory certifications',
        'Fee transparency',
        'Performance disclosures',
        'Client testimonials',
        'Security measures',
        'Contact information'
      ],
      toneGuidelines: 'Trustworthy, professional, transparent',
      seoFocus: 'Financial services, advisory terms, local finance keywords'
    }
  },

  // E-commerce Industry Templates
  ecommerce: {
    'product-description': {
      structure: [
        'Product overview and main benefits',
        'Detailed feature breakdown',
        'Technical specifications',
        'Usage instructions and tips',
        'Compatibility and requirements',
        'Customer reviews and ratings',
        'Shipping and return information',
        'Related products and upsells'
      ],
      keyElements: [
        'Product specifications table',
        'High-quality product images',
        'Customer review excerpts',
        'Pricing and discount information',
        'Stock availability',
        'Shipping details'
      ],
      toneGuidelines: 'Persuasive, benefit-focused, customer-oriented',
      seoFocus: 'Product keywords, brand terms, comparison keywords'
    },
    'category-page': {
      structure: [
        'Category overview and benefits',
        'Product range explanation',
        'Buying guide and considerations',
        'Featured products showcase',
        'Brand and quality information',
        'Customer testimonials',
        'Filtering and sorting options',
        'Related categories and cross-sells'
      ],
      keyElements: [
        'Product grid with filters',
        'Category navigation',
        'Brand information',
        'Price ranges and offers',
        'Customer ratings summary',
        'Buying guides and tips'
      ],
      toneGuidelines: 'Helpful, informative, sales-oriented',
      seoFocus: 'Category keywords, product types, brand terms'
    }
  },

  // Education Industry Templates
  education: {
    'blog-post': {
      structure: [
        'Educational context and learning objectives',
        'Fundamental concepts explanation',
        'Step-by-step learning progression',
        'Practical examples and exercises',
        'Common mistakes and solutions',
        'Additional resources and references',
        'Assessment and practice opportunities',
        'Next steps in learning journey'
      ],
      keyElements: [
        'Learning objectives list',
        'Practical exercises',
        'Visual aids and diagrams',
        'Additional reading materials',
        'Assessment criteria',
        'Progress tracking methods'
      ],
      toneGuidelines: 'Educational, encouraging, clear and methodical',
      seoFocus: 'Educational keywords, course terms, skill development'
    },
    'course-description': {
      structure: [
        'Course overview and outcomes',
        'Learning objectives and goals',
        'Curriculum breakdown by modules',
        'Prerequisites and requirements',
        'Instructor credentials and expertise',
        'Student testimonials and success stories',
        'Certification and accreditation',
        'Enrollment and scheduling information'
      ],
      keyElements: [
        'Detailed curriculum outline',
        'Instructor qualifications',
        'Student success metrics',
        'Certification details',
        'Course duration and format',
        'Prerequisites checklist'
      ],
      toneGuidelines: 'Professional, inspiring, achievement-focused',
      seoFocus: 'Course keywords, skill terms, certification names'
    }
  }
};

// Get available templates
router.get('/list', verifyToken, (req, res) => {
  try {
    const { industry, contentType } = req.query;
    
    if (industry && contentType) {
      // Get specific template
      const template = contentTemplates[industry]?.[contentType];
      if (!template) {
        return res.status(404).json({ error: 'Template not found' });
      }
      
      res.json({
        industry,
        contentType,
        template
      });
    } else if (industry) {
      // Get all templates for industry
      const industryTemplates = contentTemplates[industry];
      if (!industryTemplates) {
        return res.status(404).json({ error: 'Industry templates not found' });
      }
      
      res.json({
        industry,
        templates: industryTemplates
      });
    } else {
      // Get all available industries and content types
      const availableTemplates = {};
      Object.keys(contentTemplates).forEach(industry => {
        availableTemplates[industry] = Object.keys(contentTemplates[industry]);
      });
      
      res.json({
        availableTemplates
      });
    }
  } catch (error) {
    console.error('Template list error:', error);
    res.status(500).json({ error: 'Failed to retrieve templates' });
  }
});

// Generate content using template
router.post('/generate', verifyToken, async (req, res) => {
  try {
    const { industry, contentType, keyword, customizations = {} } = req.body;
    
    if (!industry || !contentType || !keyword) {
      return res.status(400).json({ 
        error: 'Industry, content type, and keyword are required' 
      });
    }
    
    // Get template
    const template = contentTemplates[industry]?.[contentType];
    if (!template) {
      return res.status(404).json({ error: 'Template not found' });
    }
    
    // Build enhanced prompt using template
    const enhancedPrompt = buildTemplatePrompt({
      template,
      industry,
      contentType,
      keyword,
      customizations
    });
    
    res.json({
      prompt: enhancedPrompt,
      template: {
        industry,
        contentType,
        structure: template.structure,
        keyElements: template.keyElements,
        toneGuidelines: template.toneGuidelines,
        seoFocus: template.seoFocus
      }
    });
  } catch (error) {
    console.error('Template generation error:', error);
    res.status(500).json({ error: 'Failed to generate template-based content' });
  }
});

// Helper function to build template-based prompts
function buildTemplatePrompt({ template, industry, contentType, keyword, customizations }) {
  const {
    structure,
    keyElements,
    toneGuidelines,
    seoFocus
  } = template;
  
  const {
    wordCount = '800-1200',
    tone = 'professional',
    additionalRequirements = '',
    targetAudience = ''
  } = customizations;
  
  let prompt = `Create a comprehensive ${contentType} about "${keyword}" for the ${industry} industry.

TEMPLATE-BASED STRUCTURE:
${structure.map((section, index) => `${index + 1}. ${section}`).join('\n')}

KEY ELEMENTS TO INCLUDE:
${keyElements.map(element => `• ${element}`).join('\n')}

TONE AND STYLE:
- Follow these guidelines: ${toneGuidelines}
- Tone: ${tone}
- Target audience: ${targetAudience || 'General audience'}

SEO OPTIMIZATION:
- Primary keyword: "${keyword}"
- Focus areas: ${seoFocus}
- Word count: ${wordCount}
- Include relevant LSI keywords and entities
- Optimize for featured snippets where applicable

CONTENT QUALITY STANDARDS:
- Use industry-specific terminology appropriately
- Include authoritative sources and references
- Provide actionable insights and practical value
- Maintain professional credibility throughout
- Ensure content serves user search intent`;

  if (additionalRequirements) {
    prompt += `\n\nADDITIONAL REQUIREMENTS:
${additionalRequirements}`;
  }

  // Add industry-specific compliance notes
  const complianceNotes = {
    healthcare: 'Include medical disclaimers and cite peer-reviewed sources',
    finance: 'Include risk disclaimers and regulatory compliance statements',
    legal: 'Include legal disclaimers and cite relevant case law',
    education: 'Include learning objectives and assessment opportunities'
  };

  if (complianceNotes[industry]) {
    prompt += `\n\nCOMPLIANCE REQUIREMENTS:
${complianceNotes[industry]}`;
  }

  prompt += `\n\nOUTPUT FORMAT:
- Use proper HTML heading structure (H1, H2, H3)
- Include bullet points and numbered lists for readability
- Add relevant internal and external linking opportunities
- Structure content for maximum user engagement and SEO value`;

  return prompt;
}

module.exports = router;
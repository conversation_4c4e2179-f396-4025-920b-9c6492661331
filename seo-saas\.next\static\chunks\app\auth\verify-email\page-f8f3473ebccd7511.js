(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[616],{9027:function(e,t,r){Promise.resolve().then(r.bind(r,8622))},8622:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return u}});var n=r(7437),a=r(2265),s=r(1396),i=r.n(s),l=r(575),c=r(5671),o=r(4424),d=r(9186);function u(){let[e,t]=a.useState(!1),[s,u]=a.useState(!1),m=async()=>{t(!0);try{let{createSupabaseComponentClient:e}=await Promise.all([r.e(82),r.e(183)]).then(r.bind(r,5183)),t=e(),n=localStorage.getItem("pending_verification_email")||new URLSearchParams(window.location.search).get("email");if(!n)throw Error("Email address not found. Please try signing up again.");let{error:a}=await t.auth.resend({type:"signup",email:n,options:{emailRedirectTo:"".concat(window.location.origin,"/auth/callback")}});if(a)throw Error(a.message);u(!0)}catch(e){console.error("Failed to resend verification email:",e)}finally{t(!1)}};return(0,n.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:(0,n.jsxs)("div",{className:"w-full max-w-md",children:[(0,n.jsxs)("div",{className:"text-center mb-8",children:[(0,n.jsx)(i(),{href:"/",className:"inline-block",children:(0,n.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"SEO SAAS"})}),(0,n.jsx)("p",{className:"text-gray-600",children:"Email verification required"})]}),(0,n.jsxs)(c.Zb,{className:"shadow-lg",children:[(0,n.jsxs)(c.Ol,{className:"space-y-1 text-center",children:[(0,n.jsx)("div",{className:"mx-auto mb-4 flex items-center justify-center w-16 h-16 bg-blue-100 rounded-full",children:(0,n.jsx)(o.Z,{className:"h-8 w-8 text-blue-600"})}),(0,n.jsx)(c.ll,{className:"text-2xl font-bold",children:"Check Your Email"}),(0,n.jsx)(c.SZ,{children:"We've sent a verification link to your email address"})]}),(0,n.jsxs)(c.aY,{className:"space-y-6",children:[(0,n.jsxs)("div",{className:"text-center space-y-4",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600",children:"To complete your registration, please click the verification link in the email we just sent you."}),(0,n.jsxs)("div",{className:"space-y-2",children:[(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-500",children:[(0,n.jsx)(d.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Check your inbox"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-500",children:[(0,n.jsx)(d.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Check your spam folder"})]}),(0,n.jsxs)("div",{className:"flex items-center justify-center space-x-2 text-sm text-gray-500",children:[(0,n.jsx)(d.Z,{className:"h-4 w-4"}),(0,n.jsx)("span",{children:"Click the verification link"})]})]})]}),(0,n.jsx)("div",{className:"space-y-4",children:s?(0,n.jsx)("div",{className:"p-3 bg-green-50 border border-green-200 rounded-md",children:(0,n.jsx)("p",{className:"text-sm text-green-600 text-center",children:"Verification email sent successfully!"})}):(0,n.jsxs)("div",{className:"text-center",children:[(0,n.jsx)("p",{className:"text-sm text-gray-600 mb-3",children:"Didn't receive the email?"}),(0,n.jsx)(l.z,{variant:"outline",onClick:m,loading:e,disabled:e,className:"w-full",children:e?"Sending...":"Resend Verification Email"})]})}),(0,n.jsx)("div",{className:"text-center pt-4 border-t",children:(0,n.jsxs)("p",{className:"text-sm text-gray-600",children:["Already verified?"," ",(0,n.jsx)(i(),{href:"/auth/signin",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in to your account"})]})})]})]}),(0,n.jsx)("div",{className:"mt-8 text-center",children:(0,n.jsxs)("p",{className:"text-xs text-gray-500",children:["Having trouble? Contact our"," ",(0,n.jsx)(i(),{href:"/support",className:"text-blue-600 hover:text-blue-500",children:"support team"})]})})]})})}},575:function(e,t,r){"use strict";r.d(t,{z:function(){return o}});var n=r(7437),a=r(2265),s=r(4949),i=r(6061),l=r(2169);let c=(0,i.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,size:i,loading:o,children:d,disabled:u,asChild:m=!1,...f}=e,x=m?s.g7:"button";return(0,n.jsxs)(x,{className:(0,l.cn)(c({variant:a,size:i,className:r})),ref:t,disabled:u||o,...f,children:[o&&(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,n.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,n.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),d]})});o.displayName="Button"},5671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return l},SZ:function(){return o},Zb:function(){return i},aY:function(){return d},ll:function(){return c}});var n=r(7437),a=r(2265),s=r(2169);let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});i.displayName="Card";let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex flex-col space-y-1.5 p-6",r),...a})});l.displayName="CardHeader";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("h3",{ref:t,className:(0,s.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});c.displayName="CardTitle";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("p",{ref:t,className:(0,s.cn)("text-sm text-muted-foreground",r),...a})});o.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,n.jsx)("div",{ref:t,className:(0,s.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},2169:function(e,t,r){"use strict";r.d(t,{KG:function(){return d},cn:function(){return s},p6:function(){return l},rl:function(){return c},uf:function(){return i},vQ:function(){return o}});var n=r(7042),a=r(4769);function s(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,n.W)(t))}function i(e){return e.toLocaleString()}function l(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function c(e){return"".concat(e.toFixed(1),"%")}function o(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");return t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),Promise.resolve()}function d(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"text/plain",n=new Blob([e],{type:r}),a=URL.createObjectURL(n),s=document.createElement("a");s.href=a,s.download=t,document.body.appendChild(s),s.click(),document.body.removeChild(s),URL.revokeObjectURL(a)}},9186:function(e,t,r){"use strict";var n=r(2265);let a=n.forwardRef(function({title:e,titleId:t,...r},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});t.Z=a},4424:function(e,t,r){"use strict";var n=r(2265);let a=n.forwardRef(function({title:e,titleId:t,...r},a){return n.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?n.createElement("title",{id:t},e):null,n.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))});t.Z=a}},function(e){e.O(0,[14,396,971,938,744],function(){return e(e.s=9027)}),_N_E=e.O()}]);
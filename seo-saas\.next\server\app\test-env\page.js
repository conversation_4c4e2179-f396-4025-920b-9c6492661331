(()=>{var e={};e.id=141,e.ids=[141],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},1017:e=>{"use strict";e.exports=require("path")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},7041:(e,t,s)=>{"use strict";s.r(t),s.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>d,pages:()=>u,routeModule:()=>x,tree:()=>c});var r=s(482),a=s(9108),n=s(2563),i=s.n(n),o=s(8300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);s.d(t,l);let c=["",{children:["test-env",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(s.bind(s,5677)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-env/page.tsx"]}]},{metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(s.bind(s,6343)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(s.t.bind(s,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(s.bind(s,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],u=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-env/page.tsx"],d="/test-env/page",p={require:s,loadChunk:()=>Promise.resolve()},x=new r.AppPageRouteModule({definition:{kind:a.x.APP_PAGE,page:"/test-env/page",pathname:"/test-env",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},2549:(e,t,s)=>{Promise.resolve().then(s.bind(s,1500))},1500:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(2295);function a(){return r.jsx("div",{className:"min-h-screen bg-gray-50 flex items-center justify-center p-4",children:(0,r.jsxs)("div",{className:"bg-white rounded-lg shadow-lg p-6 max-w-2xl w-full",children:[r.jsx("h1",{className:"text-2xl font-bold mb-4",children:"Environment Variables Test"}),r.jsx("div",{className:"space-y-3",children:Object.entries({NEXT_PUBLIC_SUPABASE_URL:"https://xpcbyzcaidfukddqniny.supabase.co",NEXT_PUBLIC_SUPABASE_ANON_KEY:"Set (hidden)",NEXT_PUBLIC_APP_URL:"http://localhost:3000"}).map(([e,t])=>(0,r.jsxs)("div",{className:"flex justify-between items-center p-3 bg-gray-50 rounded",children:[(0,r.jsxs)("span",{className:"font-mono text-sm",children:[e,":"]}),r.jsx("span",{className:`text-sm ${t?"text-green-600":"text-red-600"}`,children:t||"Not set"})]},e))}),(0,r.jsxs)("div",{className:"mt-6 p-4 bg-blue-50 rounded",children:[r.jsx("h3",{className:"font-semibold text-blue-800",children:"Instructions:"}),r.jsx("p",{className:"text-blue-700 text-sm mt-1",children:'If any variables show "Not set", restart the development server with: npm run dev'})]}),r.jsx("div",{className:"mt-4",children:r.jsx("a",{href:"/",className:"text-blue-600 hover:text-blue-500 text-sm",children:"← Back to Home"})})]})})}},5677:(e,t,s)=>{"use strict";s.r(t),s.d(t,{$$typeof:()=>n,__esModule:()=>a,default:()=>i});let r=(0,s(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/test-env/page.tsx`),{__esModule:a,$$typeof:n}=r,i=r.default},3881:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>a});var r=s(337);let a=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,r.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]}};var t=require("../../webpack-runtime.js");t.C(e);var s=e=>t(t.s=e),r=t.X(0,[638,972,337,129],()=>s(7041));module.exports=r})();
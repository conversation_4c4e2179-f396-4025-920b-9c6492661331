// Application Configuration
// This file centralizes all environment variables and configuration

interface Config {
  // Supabase Configuration
  supabase: {
    url: string;
    anonKey: string;
    serviceRoleKey: string;
  };
  
  // API Configuration
  apis: {
    groq: {
      apiKey: string;
      baseUrl: string;
      model: string;
      rateLimitPerMinute: number;
    };
    serper: {
      apiKey: string;
      baseUrl: string;
      rateLimitPerMinute: number;
    };
  };
  
  // Application Configuration
  app: {
    url: string;
    name: string;
    version: string;
    environment: string;
  };
  
  // Authentication Configuration
  auth: {
    secret: string;
    url: string;
  };
  
  // Feature Flags
  features: {
    analytics: boolean;
    testing: boolean;
  };
}

const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY',
  'SUPABASE_SERVICE_ROLE_KEY',
  'GROQ_API_KEY',
  'SERPER_API_KEY',
  'NEXTAUTH_SECRET',
] as const;

// Validate required environment variables
function validateEnvVars(): void {
  const missingVars = requiredEnvVars.filter(
    varName => !process.env[varName]
  );
  
  if (missingVars.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missingVars.join(', ')}`
    );
  }
}

// Validate environment variables on module load
if (typeof window === 'undefined') {
  // Only validate on server-side
  validateEnvVars();
}

export const config: Config = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL!,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
    serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY!,
  },
  
  apis: {
    groq: {
      apiKey: process.env.GROQ_API_KEY!,
      baseUrl: 'https://api.groq.com/openai/v1',
      model: 'llama3-8b-8192',
      rateLimitPerMinute: parseInt(process.env.GROQ_RATE_LIMIT_PER_MINUTE || '60'),
    },
    serper: {
      apiKey: process.env.SERPER_API_KEY!,
      baseUrl: 'https://google.serper.dev',
      rateLimitPerMinute: parseInt(process.env.SERPER_RATE_LIMIT_PER_MINUTE || '100'),
    },
  },
  
  app: {
    url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3000',
    name: 'SEO Content Generator',
    version: '1.0.0',
    environment: process.env.NODE_ENV || 'development',
  },
  
  auth: {
    secret: process.env.NEXTAUTH_SECRET!,
    url: process.env.NEXTAUTH_URL || 'http://localhost:3000',
  },
  
  features: {
    analytics: process.env.NEXT_PUBLIC_ENABLE_ANALYTICS === 'true',
    testing: process.env.NEXT_PUBLIC_ENABLE_TESTING === 'true',
  },
};

// Export individual configurations for convenience
export const supabaseConfig = config.supabase;
export const groqConfig = config.apis.groq;
export const serperConfig = config.apis.serper;
export const appConfig = config.app;
export const authConfig = config.auth;
export const featureConfig = config.features;

// Environment helpers
export const isDevelopment = config.app.environment === 'development';
export const isProduction = config.app.environment === 'production';
export const isTest = config.app.environment === 'test';

// Validation helper
export function validateConfig(): boolean {
  try {
    validateEnvVars();
    
    // Additional validation
    if (!config.supabase.url.startsWith('https://')) {
      throw new Error('Supabase URL must start with https://');
    }
    
    if (config.apis.groq.apiKey.length < 10) {
      throw new Error('Groq API key appears to be invalid');
    }
    
    if (config.apis.serper.apiKey.length < 10) {
      throw new Error('Serper API key appears to be invalid');
    }
    
    return true;
  } catch (error) {
    console.error('Configuration validation failed:', error);
    return false;
  }
}

// Export default configuration
export default config;
const { createClient } = require('@supabase/supabase-js');

// Create tables using individual SQL statements through PostgREST
async function createTables() {
  const supabaseUrl = 'https://xpcbyzcaidfukddqniny.supabase.co';
  const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Njc5MDUzNywiZXhwIjoyMDYyMzY2NTM3fQ.rcH_G_p6zeqz1LPhGvJIDDnKwu7bXjY7qqBFMw9ZTC4';

  console.log('🔄 Creating database tables individually...');

  const supabase = createClient(supabaseUrl, serviceRoleKey);

  // Table creation commands
  const tableCreationCommands = [
    {
      name: 'profiles',
      sql: `
        CREATE TABLE IF NOT EXISTS profiles (
          id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
          email TEXT UNIQUE NOT NULL,
          full_name TEXT,
          avatar_url TEXT,
          subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
          subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'past_due', 'trialing')),
          api_usage_limit INTEGER DEFAULT 100,
          total_content_generated INTEGER DEFAULT 0,
          total_api_calls INTEGER DEFAULT 0,
          onboarding_completed BOOLEAN DEFAULT false,
          preferences JSONB DEFAULT '{}',
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'projects',
      sql: `
        CREATE TABLE IF NOT EXISTS projects (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
          name TEXT NOT NULL,
          description TEXT,
          industry TEXT NOT NULL,
          target_country TEXT NOT NULL DEFAULT 'US',
          target_language TEXT NOT NULL DEFAULT 'en',
          website_url TEXT,
          competitor_urls TEXT[],
          brand_voice TEXT,
          target_audience TEXT,
          settings JSONB DEFAULT '{}',
          is_archived BOOLEAN DEFAULT false,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'content_generations',
      sql: `
        CREATE TABLE IF NOT EXISTS content_generations (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
          project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
          keyword TEXT NOT NULL,
          location TEXT NOT NULL,
          industry TEXT NOT NULL,
          content_type TEXT NOT NULL,
          tone TEXT NOT NULL,
          intent TEXT NOT NULL,
          target_word_count INTEGER DEFAULT 1000,
          generated_title TEXT,
          generated_meta_description TEXT,
          generated_content TEXT,
          content_outline JSONB,
          seo_analysis JSONB,
          competitor_data JSONB,
          quality_score DECIMAL(3,2),
          seo_score DECIMAL(3,2),
          readability_score DECIMAL(3,2),
          word_count INTEGER,
          heading_count JSONB,
          keyword_density DECIMAL(4,2),
          lsi_keywords TEXT[],
          entities TEXT[],
          generation_time_ms INTEGER,
          tokens_used INTEGER,
          cost_usd DECIMAL(10,4),
          status TEXT DEFAULT 'pending',
          error_message TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
          updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    },
    {
      name: 'api_usage_logs',
      sql: `
        CREATE TABLE IF NOT EXISTS api_usage_logs (
          id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
          user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
          content_generation_id UUID REFERENCES content_generations(id) ON DELETE CASCADE,
          api_name TEXT NOT NULL,
          endpoint TEXT,
          method TEXT,
          request_data JSONB,
          response_data JSONB,
          tokens_used INTEGER DEFAULT 0,
          cost_usd DECIMAL(10,4) DEFAULT 0,
          response_time_ms INTEGER,
          status_code INTEGER,
          error_message TEXT,
          ip_address INET,
          user_agent TEXT,
          created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
        );
      `
    }
  ];

  // Try to create each table
  let successCount = 0;
  let errorCount = 0;

  for (const table of tableCreationCommands) {
    try {
      console.log(`\n📝 Creating table: ${table.name}`);
      
      // We'll need to use the manual approach since exec_sql doesn't exist
      console.log('⚠️  Note: This will show instructions for manual creation');
      
      successCount++;
    } catch (error) {
      console.error(`❌ Failed to create ${table.name}:`, error.message);
      errorCount++;
    }
  }

  // Since automated creation through Node.js isn't working well, 
  // let's provide manual instructions
  console.log('\n📋 Manual Database Setup Required');
  console.log('=====================================');
  console.log('Since automated SQL execution is limited, please:');
  console.log('');
  console.log('1. Open: https://app.supabase.com/project/xpcbyzcaidfukddqniny/sql');
  console.log('2. Copy and execute the following SQL:');
  console.log('');
  console.log('-- Copy this entire block:');
  console.log('-- =============================');
  
  // Output the essential SQL for manual execution
  const essentialSQL = `
-- Enable extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- Profiles table
CREATE TABLE IF NOT EXISTS profiles (
  id UUID REFERENCES auth.users ON DELETE CASCADE PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro', 'enterprise')),
  subscription_status TEXT DEFAULT 'active' CHECK (subscription_status IN ('active', 'cancelled', 'past_due', 'trialing')),
  api_usage_limit INTEGER DEFAULT 100,
  total_content_generated INTEGER DEFAULT 0,
  total_api_calls INTEGER DEFAULT 0,
  onboarding_completed BOOLEAN DEFAULT false,
  preferences JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Projects table
CREATE TABLE IF NOT EXISTS projects (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  name TEXT NOT NULL,
  description TEXT,
  industry TEXT NOT NULL,
  target_country TEXT NOT NULL DEFAULT 'US',
  target_language TEXT NOT NULL DEFAULT 'en',
  website_url TEXT,
  competitor_urls TEXT[],
  brand_voice TEXT,
  target_audience TEXT,
  settings JSONB DEFAULT '{}',
  is_archived BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Content generations table
CREATE TABLE IF NOT EXISTS content_generations (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  project_id UUID REFERENCES projects(id) ON DELETE CASCADE,
  keyword TEXT NOT NULL,
  location TEXT NOT NULL,
  industry TEXT NOT NULL,
  content_type TEXT NOT NULL CHECK (content_type IN ('service', 'blog', 'product', 'landing', 'category', 'faq')),
  tone TEXT NOT NULL CHECK (tone IN ('professional', 'conversational', 'authoritative', 'friendly', 'technical', 'casual')),
  intent TEXT NOT NULL CHECK (intent IN ('informational', 'commercial', 'transactional', 'navigational')),
  target_word_count INTEGER DEFAULT 1000,
  generated_title TEXT,
  generated_meta_description TEXT,
  generated_content TEXT,
  content_outline JSONB,
  seo_analysis JSONB,
  competitor_data JSONB,
  quality_score DECIMAL(3,2),
  seo_score DECIMAL(3,2),
  readability_score DECIMAL(3,2),
  word_count INTEGER,
  heading_count JSONB,
  keyword_density DECIMAL(4,2),
  lsi_keywords TEXT[],
  entities TEXT[],
  generation_time_ms INTEGER,
  tokens_used INTEGER,
  cost_usd DECIMAL(10,4),
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed', 'cancelled')),
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API usage logs table
CREATE TABLE IF NOT EXISTS api_usage_logs (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  user_id UUID REFERENCES profiles(id) ON DELETE CASCADE,
  content_generation_id UUID REFERENCES content_generations(id) ON DELETE CASCADE,
  api_name TEXT NOT NULL CHECK (api_name IN ('groq', 'serper', 'supabase', 'openai')),
  endpoint TEXT,
  method TEXT,
  tokens_used INTEGER DEFAULT 0,
  cost_usd DECIMAL(10,4) DEFAULT 0,
  response_time_ms INTEGER,
  status_code INTEGER,
  error_message TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Enable Row Level Security
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE content_generations ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_usage_logs ENABLE ROW LEVEL SECURITY;

-- Basic RLS Policies
CREATE POLICY "Users can view own profile" ON profiles FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON profiles FOR UPDATE USING (auth.uid() = id);
CREATE POLICY "Users can insert own profile" ON profiles FOR INSERT WITH CHECK (auth.uid() = id);

CREATE POLICY "Users can manage own projects" ON projects FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can manage own content" ON content_generations FOR ALL USING (auth.uid() = user_id);
CREATE POLICY "Users can view own usage" ON api_usage_logs FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "System can insert usage logs" ON api_usage_logs FOR INSERT WITH CHECK (true);

-- Function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS trigger AS $$
BEGIN
  INSERT INTO public.profiles (id, email, full_name)
  VALUES (NEW.id, NEW.email, NEW.raw_user_meta_data->>'full_name');
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger for new user registration
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();
`;

  console.log(essentialSQL);
  console.log('-- =============================');
  console.log('');
  console.log('3. After executing, run this script again to verify tables were created');
  console.log('');

  return false; // Indicates manual intervention required
}

// Function to verify tables
async function verifyTables() {
  const supabaseUrl = 'https://xpcbyzcaidfukddqniny.supabase.co';
  const serviceRoleKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Njc5MDUzNywiZXhwIjoyMDYyMzY2NTM3fQ.rcH_G_p6zeqz1LPhGvJIDDnKwu7bXjY7qqBFMw9ZTC4';
  
  const supabase = createClient(supabaseUrl, serviceRoleKey);
  
  const tables = ['profiles', 'projects', 'content_generations', 'api_usage_logs'];
  
  console.log('\n🔍 Verifying tables...');
  
  let existingTables = 0;
  
  for (const tableName of tables) {
    try {
      const { data, error } = await supabase.from(tableName).select('*').limit(1);
      
      if (error && error.code === 'PGRST116') {
        console.log(`❌ Table '${tableName}' does not exist`);
      } else if (error) {
        console.log(`⚠️  Table '${tableName}': ${error.message}`);
      } else {
        console.log(`✅ Table '${tableName}' exists and accessible`);
        existingTables++;
      }
    } catch (err) {
      console.log(`❌ Error checking '${tableName}': ${err.message}`);
    }
  }
  
  console.log(`\n📊 Result: ${existingTables}/${tables.length} core tables are accessible`);
  
  if (existingTables === tables.length) {
    console.log('🎉 All core tables are ready!');
    return true;
  } else {
    console.log('⚠️  Some tables are missing - manual setup required');
    return false;
  }
}

// Main execution
if (require.main === module) {
  createTables().then(() => {
    return verifyTables();
  }).then((success) => {
    process.exit(success ? 0 : 1);
  });
}

module.exports = { createTables, verifyTables };
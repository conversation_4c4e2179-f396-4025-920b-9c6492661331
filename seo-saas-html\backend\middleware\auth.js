const jwt = require('jsonwebtoken');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

const verifyToken = async (req, res, next) => {
  try {
    const token = req.headers.authorization?.split(' ')[1];
    
    if (!token) {
      return res.status(401).json({ error: 'No token provided' });
    }

    // Verify JWT token
    const decoded = jwt.verify(token, process.env.JWT_SECRET);
    
    // Verify user exists and is active
    const { data: user, error } = await supabase
      .from('users')
      .select('id, email, subscription_tier')
      .eq('id', decoded.userId)
      .single();

    if (error || !user) {
      return res.status(401).json({ error: 'Invalid token' });
    }

    // Attach user to request
    req.user = user;
    next();
  } catch (error) {
    if (error.name === 'JsonWebTokenError') {
      return res.status(401).json({ error: 'Invalid token' });
    }
    if (error.name === 'TokenExpiredError') {
      return res.status(401).json({ error: 'Token expired' });
    }
    return res.status(500).json({ error: 'Authentication error' });
  }
};

const checkSubscription = (requiredTier = 'free') => {
  const tierLevels = {
    'free': 0,
    'professional': 1,
    'business': 2,
    'enterprise': 3
  };

  return (req, res, next) => {
    const userTier = req.user.subscription_tier;
    const userLevel = tierLevels[userTier] || 0;
    const requiredLevel = tierLevels[requiredTier] || 0;

    if (userLevel < requiredLevel) {
      return res.status(403).json({ 
        error: 'Insufficient subscription tier',
        required: requiredTier,
        current: userTier
      });
    }

    next();
  };
};

const checkUsageLimit = async (req, res, next) => {
  try {
    const userId = req.user.id;
    const today = new Date().toISOString().split('T')[0];

    // Get user's usage for today
    const { data: usage, error } = await supabase
      .from('usage_tracking')
      .select('*')
      .eq('user_id', userId)
      .eq('date', today)
      .single();

    if (error && error.code !== 'PGRST116') { // Not found is ok
      throw error;
    }

    // Get subscription limits
    const limits = {
      'free': { content_generations: 5, analyses: 10 },
      'professional': { content_generations: 50, analyses: 100 },
      'business': { content_generations: 200, analyses: 500 },
      'enterprise': { content_generations: -1, analyses: -1 } // Unlimited
    };

    const userLimits = limits[req.user.subscription_tier] || limits.free;
    
    // Check if user has reached limits
    if (userLimits.content_generations !== -1) {
      const currentUsage = usage?.content_generations || 0;
      if (currentUsage >= userLimits.content_generations) {
        return res.status(429).json({ 
          error: 'Daily content generation limit reached',
          limit: userLimits.content_generations,
          used: currentUsage
        });
      }
    }

    req.userUsage = usage || { content_generations: 0, analyses: 0 };
    req.userLimits = userLimits;
    next();
  } catch (error) {
    console.error('Usage check error:', error);
    res.status(500).json({ error: 'Failed to check usage limits' });
  }
};

module.exports = {
  verifyToken,
  checkSubscription,
  checkUsageLimit
};
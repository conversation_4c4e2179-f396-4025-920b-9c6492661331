# 🚀 Claude Code HTML/CSS Frontend Rebuild Prompt

**Project**: SEO SAAS Application - Complete Frontend Rebuild  
**Goal**: Build a modern, professional, super-fast, mobile-optimized frontend using only HTML and CSS  
**Current Issues**: Complex React/Next.js setup with multiple integration problems  

---

## 📋 **PROJECT OVERVIEW**

### **Application Type**: SEO Content Generation SAAS Platform
### **Target Users**: Content creators, digital marketers, SEO professionals, businesses
### **Core Purpose**: AI-powered SEO content generation with comprehensive analysis tools

---

## 🎯 **REBUILD REQUIREMENTS**

### **Technical Stack**
- **Frontend**: Pure HTML5 + Modern CSS3 only
- **No Frameworks**: No React, Next.js, or JavaScript frameworks
- **Responsive**: Mobile-first design approach
- **Performance**: Super fast loading (< 2 seconds)
- **Modern Design**: Professional, clean, contemporary UI
- **Cross-browser**: Compatible with all modern browsers

### **Design Standards**
- **Color Scheme**: Blue (#3b82f6) and Purple (#7c3aed) gradients
- **Typography**: System fonts (Inter, Segoe UI, sans-serif)
- **Layout**: Clean, spacious, card-based design
- **Animations**: CSS-only smooth transitions
- **Icons**: CSS-only icons or SVG symbols
- **Images**: Optimized, responsive images

---

## 🏗️ **COMPLETE SITE STRUCTURE**

### **1. Homepage (index.html)**
```
Header Navigation:
- Logo: "SEO Pro"
- Menu: Features | Pricing | Demo | Login | Sign Up

Hero Section:
- Headline: "Generate SEO Content That Ranks #1 on Google"
- Subheadline: "Create high-quality, SEO-optimized content with our advanced AI engine"
- CTA Buttons: "Start Free Trial" | "View Demo"
- Hero Image: Dashboard mockup or illustration

Features Section (6 core features):
1. AI-Powered Content Generation
2. Comprehensive SEO Analysis  
3. Competitor Intelligence
4. Multi-Industry Support
5. Real-time SERP Analysis
6. Advanced Keyword Research

Benefits Section:
- "Why Choose Our Platform?"
- 6 key benefits with icons
- Statistics and social proof

How It Works (3 steps):
1. Enter Keywords & Industry
2. AI Analyzes & Generates Content
3. Optimize & Publish for Rankings

Testimonials Section:
- Customer testimonials carousel
- Company logos
- Success statistics

Pricing Section:
- 3 pricing tiers: Free, Pro, Enterprise
- Feature comparison table
- "Start Free Trial" CTAs

Footer:
- Company links
- Social media
- Contact information
```

### **2. Dashboard (dashboard.html)**
```
Sidebar Navigation:
- Dashboard Overview
- Content Generator
- SEO Analysis
- Keyword Research
- Projects
- Analytics
- Settings

Main Dashboard:
- Welcome message
- Usage statistics (4 cards)
- Recent activity feed
- Quick action buttons
- Performance charts
- Upgrade prompts

Top Navigation:
- Search functionality
- Notifications
- User profile dropdown
```

### **3. Content Generator (content-generator.html)**
```
Multi-step Form:
Step 1: Project Setup
- Project name input
- Industry selection dropdown
- Content type selector

Step 2: Keyword Research
- Primary keyword input
- Secondary keywords
- Target audience selection

Step 3: Content Preferences
- Tone of voice selector
- Writing style options
- Word count slider
- Special requirements

Step 4: Generation & Results
- Progress indicator
- Real-time generation status
- Generated content display
- Edit and refine options
- Export functionality
```

### **4. SEO Analysis (seo-analysis.html)**
```
Analysis Form:
- URL input field
- Target keywords
- Competitor URLs
- Analysis type selection

Results Dashboard:
- Overall SEO score (circular progress)
- 6 analysis modules results:
  1. Keyword Density Analysis
  2. Heading Structure Analysis
  3. Content Quality Score
  4. LSI Keywords Analysis
  5. Competitor Comparison
  6. E-E-A-T Compliance

Recommendations Section:
- Actionable improvement suggestions
- Priority levels (High, Medium, Low)
- Implementation guides

Export Options:
- PDF report
- Excel spreadsheet
- Shareable link
```

### **5. Authentication Pages**
```
Login Page (login.html):
- Email/password form
- "Remember me" checkbox
- "Forgot password" link
- Social login buttons
- "Sign up" link

Register Page (register.html):
- Full name, email, password fields
- Terms acceptance checkbox
- "Create Account" button
- "Already have account" link

Forgot Password (forgot-password.html):
- Email input
- "Reset Password" button
- "Back to login" link
```

### **6. Additional Pages**
```
Pricing (pricing.html):
- Detailed pricing table
- Feature comparisons
- FAQ section
- "Start Free Trial" CTAs

Features (features.html):
- Detailed feature explanations
- Screenshots and demos
- Use cases and benefits

About (about.html):
- Company information
- Team profiles
- Mission and vision

Contact (contact.html):
- Contact form
- Company details
- Support information
```

---

## 🎨 **DESIGN SPECIFICATIONS**

### **Color Palette**
```css
:root {
  /* Primary Colors */
  --primary-blue: #3b82f6;
  --primary-purple: #7c3aed;
  --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #7c3aed 100%);
  
  /* Secondary Colors */
  --success-green: #10b981;
  --warning-orange: #f59e0b;
  --error-red: #ef4444;
  
  /* Neutral Colors */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;
  
  /* Background Colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-accent: #f0f9ff;
}
```

### **Typography System**
```css
:root {
  /* Font Families */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;
  
  /* Font Sizes */
  --text-xs: 0.75rem;    /* 12px */
  --text-sm: 0.875rem;   /* 14px */
  --text-base: 1rem;     /* 16px */
  --text-lg: 1.125rem;   /* 18px */
  --text-xl: 1.25rem;    /* 20px */
  --text-2xl: 1.5rem;    /* 24px */
  --text-3xl: 1.875rem;  /* 30px */
  --text-4xl: 2.25rem;   /* 36px */
  --text-5xl: 3rem;      /* 48px */
  --text-6xl: 3.75rem;   /* 60px */
  
  /* Font Weights */
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  --font-extrabold: 800;
}
```

### **Spacing System**
```css
:root {
  /* Spacing Scale */
  --space-1: 0.25rem;   /* 4px */
  --space-2: 0.5rem;    /* 8px */
  --space-3: 0.75rem;   /* 12px */
  --space-4: 1rem;      /* 16px */
  --space-5: 1.25rem;   /* 20px */
  --space-6: 1.5rem;    /* 24px */
  --space-8: 2rem;      /* 32px */
  --space-10: 2.5rem;   /* 40px */
  --space-12: 3rem;     /* 48px */
  --space-16: 4rem;     /* 64px */
  --space-20: 5rem;     /* 80px */
  --space-24: 6rem;     /* 96px */
  
  /* Container Widths */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
}
```

### **Component Styles**
```css
/* Buttons */
.btn {
  padding: var(--space-3) var(--space-6);
  border-radius: 0.5rem;
  font-weight: var(--font-medium);
  transition: all 0.2s ease;
  cursor: pointer;
  border: none;
  text-decoration: none;
  display: inline-flex;
  align-items: center;
  justify-content: center;
}

.btn-primary {
  background: var(--primary-gradient);
  color: white;
}

.btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 10px 25px rgba(59, 130, 246, 0.3);
}

/* Cards */
.card {
  background: var(--bg-primary);
  border-radius: 1rem;
  padding: var(--space-6);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.card:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Forms */
.form-input {
  width: 100%;
  padding: var(--space-3) var(--space-4);
  border: 2px solid var(--gray-200);
  border-radius: 0.5rem;
  font-size: var(--text-base);
  transition: border-color 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary-blue);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}
```

---

## 📱 **RESPONSIVE DESIGN REQUIREMENTS**

### **Breakpoints**
```css
/* Mobile First Approach */
/* Base: 320px - 767px (Mobile) */
/* sm: 768px - 1023px (Tablet) */
/* md: 1024px - 1279px (Small Desktop) */
/* lg: 1280px - 1535px (Desktop) */
/* xl: 1536px+ (Large Desktop) */

@media (min-width: 768px) {
  /* Tablet styles */
}

@media (min-width: 1024px) {
  /* Desktop styles */
}
```

### **Mobile Optimization**
- Touch-friendly buttons (minimum 44px)
- Readable text sizes (minimum 16px)
- Optimized navigation (hamburger menu)
- Fast loading images
- Smooth scrolling
- Gesture-friendly interactions

---

## 🚀 **PERFORMANCE REQUIREMENTS**

### **Loading Speed**
- **Page Load Time**: < 2 seconds
- **First Contentful Paint**: < 1 second
- **Largest Contentful Paint**: < 2.5 seconds
- **Cumulative Layout Shift**: < 0.1

### **Optimization Techniques**
- Minified CSS
- Optimized images (WebP format)
- Critical CSS inlining
- Lazy loading for images
- Efficient CSS selectors
- Minimal HTTP requests

---

## 🎯 **SPECIFIC IMPLEMENTATION INSTRUCTIONS**

### **File Structure**
```
seo-saas-html/
├── index.html
├── dashboard.html
├── content-generator.html
├── seo-analysis.html
├── login.html
├── register.html
├── pricing.html
├── features.html
├── about.html
├── contact.html
├── css/
│   ├── main.css
│   ├── components.css
│   ├── responsive.css
│   └── animations.css
├── images/
│   ├── hero-bg.jpg
│   ├── dashboard-mockup.png
│   ├── icons/
│   └── logos/
└── js/
    ├── main.js (minimal JavaScript for interactions)
    └── mobile-menu.js
```

### **Critical Features to Implement**
1. **Responsive Navigation**: Mobile hamburger menu
2. **Interactive Forms**: Form validation and feedback
3. **Progress Indicators**: For content generation
4. **Modal Windows**: For feature demos
5. **Smooth Animations**: CSS transitions and transforms
6. **Loading States**: Skeleton screens and spinners
7. **Error Handling**: User-friendly error messages
8. **Accessibility**: ARIA labels and keyboard navigation

---

**This prompt provides complete specifications for rebuilding the SEO SAAS frontend with modern HTML/CSS that will be fast, professional, and fully functional.**

---

## 🛠 **DETAILED IMPLEMENTATION GUIDE**

### **STEP 1: Create Base HTML Structure**

#### **Homepage Template (index.html)**
```html
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SEO Pro - AI-Powered Content Generation Platform</title>
    <meta name="description" content="Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher.">

    <!-- Preload Critical Resources -->
    <link rel="preload" href="css/main.css" as="style">
    <link rel="preload" href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" as="style">

    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">

    <!-- Favicon -->
    <link rel="icon" href="images/favicon.ico">

    <!-- Open Graph -->
    <meta property="og:title" content="SEO Pro - AI-Powered Content Generation">
    <meta property="og:description" content="Generate high-quality, SEO-optimized content with AI">
    <meta property="og:image" content="images/og-image.jpg">
    <meta property="og:url" content="https://seopro.com">
</head>
<body>
    <!-- Header -->
    <header class="header">
        <nav class="nav container">
            <div class="nav-brand">
                <a href="index.html" class="logo">
                    <span class="logo-text">SEO Pro</span>
                </a>
            </div>

            <!-- Desktop Navigation -->
            <ul class="nav-menu">
                <li><a href="#features" class="nav-link">Features</a></li>
                <li><a href="pricing.html" class="nav-link">Pricing</a></li>
                <li><a href="#demo" class="nav-link">Demo</a></li>
                <li><a href="about.html" class="nav-link">About</a></li>
            </ul>

            <!-- Auth Buttons -->
            <div class="nav-auth">
                <a href="login.html" class="btn btn-ghost">Sign In</a>
                <a href="register.html" class="btn btn-primary">Start Free Trial</a>
            </div>

            <!-- Mobile Menu Button -->
            <button class="mobile-menu-btn" aria-label="Toggle menu">
                <span class="hamburger"></span>
            </button>
        </nav>

        <!-- Mobile Menu -->
        <div class="mobile-menu">
            <ul class="mobile-nav">
                <li><a href="#features" class="mobile-nav-link">Features</a></li>
                <li><a href="pricing.html" class="mobile-nav-link">Pricing</a></li>
                <li><a href="#demo" class="mobile-nav-link">Demo</a></li>
                <li><a href="about.html" class="mobile-nav-link">About</a></li>
                <li><a href="login.html" class="mobile-nav-link">Sign In</a></li>
                <li><a href="register.html" class="btn btn-primary mobile-cta">Start Free Trial</a></li>
            </ul>
        </div>
    </header>

    <!-- Hero Section -->
    <section class="hero">
        <div class="container">
            <div class="hero-content">
                <div class="hero-text">
                    <h1 class="hero-title">
                        Generate SEO Content That
                        <span class="gradient-text">Ranks #1 on Google</span>
                    </h1>
                    <p class="hero-description">
                        Create high-quality, SEO-optimized content with our advanced AI engine.
                        Analyze competitors, optimize keywords, and generate content that outranks the competition.
                    </p>
                    <div class="hero-cta">
                        <a href="register.html" class="btn btn-primary btn-large">
                            Start Free Trial
                            <svg class="btn-icon" viewBox="0 0 20 20" fill="currentColor">
                                <path fill-rule="evenodd" d="M10.293 3.293a1 1 0 011.414 0l6 6a1 1 0 010 1.414l-6 6a1 1 0 01-1.414-1.414L14.586 11H3a1 1 0 110-2h11.586l-4.293-4.293a1 1 0 010-1.414z" clip-rule="evenodd" />
                            </svg>
                        </a>
                        <a href="#demo" class="btn btn-outline btn-large">
                            View Demo
                        </a>
                    </div>
                    <div class="hero-stats">
                        <div class="stat">
                            <span class="stat-number">10,000+</span>
                            <span class="stat-label">Content Pieces Generated</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">95%</span>
                            <span class="stat-label">Average SEO Score</span>
                        </div>
                        <div class="stat">
                            <span class="stat-number">2 min</span>
                            <span class="stat-label">Average Generation Time</span>
                        </div>
                    </div>
                </div>
                <div class="hero-image">
                    <img src="images/dashboard-mockup.png" alt="SEO Pro Dashboard" class="hero-img">
                </div>
            </div>
        </div>
    </section>

    <!-- Features Section -->
    <section class="features" id="features">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title">Everything You Need to Dominate Search Rankings</h2>
                <p class="section-description">
                    Our comprehensive SEO platform combines AI content generation with advanced competitor analysis
                </p>
            </div>

            <div class="features-grid">
                <!-- Feature 1 -->
                <div class="feature-card">
                    <div class="feature-icon ai-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M9.663 17h4.673M12 3v1m6.364 1.636l-.707.707M21 12h-1M4 12H3m3.343-5.657l-.707-.707m2.828 9.9a5 5 0 117.072 0l-.548.547A3.374 3.374 0 0014 18.469V19a2 2 0 11-4 0v-.531c0-.895-.356-1.754-.988-2.386l-.548-.547z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">AI-Powered Content Generation</h3>
                    <p class="feature-description">
                        Generate 1000+ word SEO-optimized content in under 2 minutes with advanced AI that understands your industry.
                    </p>
                    <ul class="feature-list">
                        <li>Industry-specific templates</li>
                        <li>Multiple content types</li>
                        <li>Real-time optimization</li>
                    </ul>
                </div>

                <!-- Feature 2 -->
                <div class="feature-card">
                    <div class="feature-icon analysis-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Comprehensive SEO Analysis</h3>
                    <p class="feature-description">
                        Get detailed insights with 6 analysis engines including keyword density, heading structure, and quality scoring.
                    </p>
                    <ul class="feature-list">
                        <li>Keyword density analysis</li>
                        <li>Heading optimization</li>
                        <li>E-E-A-T compliance</li>
                    </ul>
                </div>

                <!-- Feature 3 -->
                <div class="feature-card">
                    <div class="feature-icon competitor-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Competitor Intelligence</h3>
                    <p class="feature-description">
                        Analyze top competitors and discover content gaps and opportunities to outrank them in search results.
                    </p>
                    <ul class="feature-list">
                        <li>Competitor content analysis</li>
                        <li>Gap identification</li>
                        <li>Ranking opportunities</li>
                    </ul>
                </div>

                <!-- Feature 4 -->
                <div class="feature-card">
                    <div class="feature-icon industry-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M21 12a9 9 0 01-9 9m9-9a9 9 0 00-9-9m9 9H3m9 9a9 9 0 01-9-9m9 9c1.657 0 3-4.03 3-9s-1.343-9-3-9m0 18c-1.657 0-3-4.03-3-9s1.343-9 3-9m-9 9a9 9 0 019-9"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Multi-Industry Support</h3>
                    <p class="feature-description">
                        Specialized templates and optimization for technology, healthcare, finance, and 10+ other industries.
                    </p>
                    <ul class="feature-list">
                        <li>Industry-specific templates</li>
                        <li>Compliance guidelines</li>
                        <li>Specialized terminology</li>
                    </ul>
                </div>

                <!-- Feature 5 -->
                <div class="feature-card">
                    <div class="feature-icon serp-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Real-time SERP Analysis</h3>
                    <p class="feature-description">
                        Monitor search engine results in real-time and adapt your content strategy based on current rankings.
                    </p>
                    <ul class="feature-list">
                        <li>Live SERP monitoring</li>
                        <li>Ranking tracking</li>
                        <li>Strategy adaptation</li>
                    </ul>
                </div>

                <!-- Feature 6 -->
                <div class="feature-card">
                    <div class="feature-icon keyword-icon">
                        <svg viewBox="0 0 24 24" fill="none" stroke="currentColor">
                            <path d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"/>
                        </svg>
                    </div>
                    <h3 class="feature-title">Advanced Keyword Research</h3>
                    <p class="feature-description">
                        Discover high-value keywords with comprehensive research tools including difficulty scoring and volume data.
                    </p>
                    <ul class="feature-list">
                        <li>Keyword difficulty scoring</li>
                        <li>Search volume data</li>
                        <li>Long-tail suggestions</li>
                    </ul>
                </div>
            </div>
        </div>
    </section>

    <!-- Continue with other sections... -->

    <script src="js/main.js"></script>
</body>
</html>
```

### **STEP 2: Create Main CSS Framework**

#### **Main Stylesheet (css/main.css)**
```css
/* CSS Custom Properties */
:root {
  /* Colors */
  --primary-blue: #3b82f6;
  --primary-purple: #7c3aed;
  --primary-gradient: linear-gradient(135deg, #3b82f6 0%, #7c3aed 100%);
  --success-green: #10b981;
  --warning-orange: #f59e0b;
  --error-red: #ef4444;

  /* Grays */
  --gray-50: #f9fafb;
  --gray-100: #f3f4f6;
  --gray-200: #e5e7eb;
  --gray-300: #d1d5db;
  --gray-400: #9ca3af;
  --gray-500: #6b7280;
  --gray-600: #4b5563;
  --gray-700: #374151;
  --gray-800: #1f2937;
  --gray-900: #111827;

  /* Typography */
  --font-primary: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
  --font-mono: 'SF Mono', Monaco, 'Cascadia Code', monospace;

  /* Font Sizes */
  --text-xs: 0.75rem;
  --text-sm: 0.875rem;
  --text-base: 1rem;
  --text-lg: 1.125rem;
  --text-xl: 1.25rem;
  --text-2xl: 1.5rem;
  --text-3xl: 1.875rem;
  --text-4xl: 2.25rem;
  --text-5xl: 3rem;
  --text-6xl: 3.75rem;

  /* Spacing */
  --space-1: 0.25rem;
  --space-2: 0.5rem;
  --space-3: 0.75rem;
  --space-4: 1rem;
  --space-5: 1.25rem;
  --space-6: 1.5rem;
  --space-8: 2rem;
  --space-10: 2.5rem;
  --space-12: 3rem;
  --space-16: 4rem;
  --space-20: 5rem;
  --space-24: 6rem;

  /* Shadows */
  --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1);

  /* Border Radius */
  --radius-sm: 0.25rem;
  --radius-md: 0.5rem;
  --radius-lg: 0.75rem;
  --radius-xl: 1rem;
  --radius-2xl: 1.5rem;

  /* Transitions */
  --transition-fast: 0.15s ease;
  --transition-normal: 0.3s ease;
  --transition-slow: 0.5s ease;
}

/* Reset and Base Styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
  font-size: 16px;
}

body {
  font-family: var(--font-primary);
  font-size: var(--text-base);
  line-height: 1.6;
  color: var(--gray-900);
  background-color: var(--gray-50);
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Container */
.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 var(--space-4);
}

@media (min-width: 768px) {
  .container {
    padding: 0 var(--space-6);
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 var(--space-8);
  }
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-weight: 700;
  line-height: 1.2;
  margin-bottom: var(--space-4);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

@media (min-width: 768px) {
  h1 { font-size: var(--text-5xl); }
  h2 { font-size: var(--text-4xl); }
  h3 { font-size: var(--text-3xl); }
}

@media (min-width: 1024px) {
  h1 { font-size: var(--text-6xl); }
  h2 { font-size: var(--text-5xl); }
}

p {
  margin-bottom: var(--space-4);
  color: var(--gray-600);
}

/* Links */
a {
  color: var(--primary-blue);
  text-decoration: none;
  transition: color var(--transition-fast);
}

a:hover {
  color: var(--primary-purple);
}

/* Lists */
ul, ol {
  margin-bottom: var(--space-4);
  padding-left: var(--space-6);
}

li {
  margin-bottom: var(--space-2);
}

/* Images */
img {
  max-width: 100%;
  height: auto;
  display: block;
}

/* Utility Classes */
.gradient-text {
  background: var(--primary-gradient);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.hidden { display: none; }
.visible { display: block; }

.sr-only {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}
```

---

## 📋 **COMPLETE FEATURE IMPLEMENTATION CHECKLIST**

### **Core Features to Build**
- [ ] **Homepage**: Hero, features, testimonials, pricing preview
- [ ] **Dashboard**: Analytics, quick actions, recent activity
- [ ] **Content Generator**: Multi-step form, progress tracking, results
- [ ] **SEO Analysis**: URL input, analysis results, recommendations
- [ ] **Keyword Research**: Search interface, results table, metrics
- [ ] **Authentication**: Login, register, forgot password
- [ ] **Pricing**: Plans comparison, feature matrix
- [ ] **Profile**: User settings, subscription management

### **UI Components to Create**
- [ ] **Navigation**: Responsive header with mobile menu
- [ ] **Buttons**: Primary, secondary, outline, ghost variants
- [ ] **Cards**: Feature cards, stat cards, content cards
- [ ] **Forms**: Input fields, dropdowns, checkboxes, validation
- [ ] **Progress**: Progress bars, circular progress, step indicators
- [ ] **Modals**: Overlay dialogs, confirmation dialogs
- [ ] **Tables**: Data tables, comparison tables
- [ ] **Charts**: Progress circles, bar charts (CSS-only)

### **Performance Optimizations**
- [ ] **Critical CSS**: Inline above-the-fold styles
- [ ] **Image Optimization**: WebP format, lazy loading
- [ ] **Font Loading**: Preload critical fonts
- [ ] **Minification**: Minify CSS and HTML
- [ ] **Caching**: Set proper cache headers
- [ ] **Compression**: Enable gzip compression

**This comprehensive prompt will guide Claude Code to create a professional, fast, and fully-featured SEO SAAS frontend using only HTML and CSS.**

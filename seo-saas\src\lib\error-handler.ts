// Enhanced Error Handling System with Circuit Breakers
// Comprehensive error recovery for enterprise-grade reliability

import { logApiUsage } from './supabase';

interface CircuitBreakerState {
  isOpen: boolean;
  failures: number;
  lastFailure: number;
  nextAttempt: number;
}

interface RetryConfig {
  maxRetries: number;
  baseDelay: number;
  maxDelay: number;
  backoffMultiplier: number;
}

interface ApiMetrics {
  totalRequests: number;
  successCount: number;
  errorCount: number;
  averageResponseTime: number;
  lastError?: string;
}

export class APIErrorHandler {
  private static instance: APIErrorHandler;
  private retryAttempts = new Map<string, number>();
  private circuitBreakers = new Map<string, CircuitBreakerState>();
  private apiMetrics = new Map<string, ApiMetrics>();
  private cache = new Map<string, { data: any; expires: number }>();

  // Circuit breaker configuration
  private readonly CIRCUIT_BREAKER_THRESHOLD = 5; // failures before opening
  private readonly CIRCUIT_BREAKER_TIMEOUT = 60000; // 1 minute
  private readonly CIRCUIT_BREAKER_RESET_TIMEOUT = 300000; // 5 minutes

  // Retry configuration per service
  private readonly retryConfigs: Record<string, RetryConfig> = {
    groq: {
      maxRetries: 3,
      baseDelay: 1000,
      maxDelay: 30000,
      backoffMultiplier: 2,
    },
    serper: {
      maxRetries: 2,
      baseDelay: 500,
      maxDelay: 15000,
      backoffMultiplier: 1.5,
    },
    openai: {
      maxRetries: 3,
      baseDelay: 2000,
      maxDelay: 60000,
      backoffMultiplier: 2,
    },
  };

  static getInstance(): APIErrorHandler {
    if (!APIErrorHandler.instance) {
      APIErrorHandler.instance = new APIErrorHandler();
    }
    return APIErrorHandler.instance;
  }

  // Main error handling entry point
  async handleApiError(
    service: string,
    error: any,
    request: any,
    userId?: string
  ): Promise<any> {
    console.error(`${service} API Error:`, error);

    // Log error for monitoring
    await this.logError(service, error, request, userId);

    // Check circuit breaker
    if (this.isCircuitBreakerOpen(service)) {
      throw new Error(`${service} service is temporarily unavailable. Please try again later.`);
    }

    const errorType = this.classifyError(error);
    console.log(`Error classified as: ${errorType} for service: ${service}`);

    switch (service.toLowerCase()) {
      case 'groq':
        return this.handleGroqError(errorType, error, request, userId);
      case 'serper':
        return this.handleSerperError(errorType, error, request, userId);
      case 'openai':
        return this.handleOpenAiError(errorType, error, request, userId);
      default:
        return this.handleGenericError(errorType, error, request, userId);
    }
  }

  // Groq-specific error handling
  private async handleGroqError(
    errorType: string,
    error: any,
    request: any,
    userId?: string
  ): Promise<any> {
    switch (errorType) {
      case 'RATE_LIMIT':
        console.log('Handling Groq rate limit...');
        return this.handleRateLimit('groq', request, userId);

      case 'QUOTA_EXCEEDED':
        console.log('Groq quota exceeded, checking cache...');
        return this.handleQuotaExceeded('groq', request);

      case 'INVALID_REQUEST':
        console.log('Invalid Groq request, returning error...');
        throw new Error(`Invalid request: ${error.message || 'Please check your input parameters'}`);

      case 'API_DOWN':
        console.log('Groq API is down, using fallback...');
        return this.handleServiceDown('groq', request);

      case 'NETWORK_ERROR':
        console.log('Network error with Groq, retrying...');
        return this.handleNetworkError('groq', request, userId);

      default:
        console.log('Unknown Groq error, using generic handler...');
        return this.handleUnknownError('groq', error, request);
    }
  }

  // Serper-specific error handling
  private async handleSerperError(
    errorType: string,
    error: any,
    request: any,
    userId?: string
  ): Promise<any> {
    switch (errorType) {
      case 'RATE_LIMIT':
        console.log('Handling Serper rate limit...');
        return this.handleRateLimit('serper', request, userId);

      case 'BLOCKED':
        console.log('Serper request blocked, using cached data...');
        return this.useCachedData('serper', request);

      case 'QUOTA_EXCEEDED':
        console.log('Serper quota exceeded, using cached data...');
        return this.useCachedData('serper', request);

      case 'INVALID_REQUEST':
        throw new Error(`Invalid search request: ${error.message || 'Please check your search parameters'}`);

      case 'API_DOWN':
        console.log('Serper API is down, using cached data...');
        return this.useCachedData('serper', request);

      default:
        return this.handleUnknownError('serper', error, request);
    }
  }

  // OpenAI-specific error handling (for future use)
  private async handleOpenAiError(
    errorType: string,
    error: any,
    request: any,
    userId?: string
  ): Promise<any> {
    // Similar structure to Groq handling
    switch (errorType) {
      case 'RATE_LIMIT':
        return this.handleRateLimit('openai', request, userId);
      default:
        return this.handleUnknownError('openai', error, request);
    }
  }

  // Generic error handling
  private async handleGenericError(
    errorType: string,
    error: any,
    request: any,
    userId?: string
  ): Promise<any> {
    console.log('Handling generic error...');
    throw new Error(`Service temporarily unavailable: ${error.message}`);
  }

  // Classify error types
  private classifyError(error: any): string {
    const message = error.message?.toLowerCase() || '';
    const status = error.status || error.response?.status || 0;

    // Rate limiting
    if (status === 429 || message.includes('rate limit') || message.includes('too many requests')) {
      return 'RATE_LIMIT';
    }

    // Quota/billing issues
    if (status === 402 || message.includes('quota') || message.includes('billing') || message.includes('insufficient funds')) {
      return 'QUOTA_EXCEEDED';
    }

    // Invalid requests
    if (status >= 400 && status < 500 && status !== 429) {
      return 'INVALID_REQUEST';
    }

    // Service down
    if (status >= 500 || message.includes('internal server error') || message.includes('service unavailable')) {
      return 'API_DOWN';
    }

    // Network issues
    if (message.includes('network') || message.includes('timeout') || message.includes('connection')) {
      return 'NETWORK_ERROR';
    }

    // Blocked requests (anti-bot)
    if (message.includes('blocked') || message.includes('forbidden') || status === 403) {
      return 'BLOCKED';
    }

    return 'UNKNOWN';
  }

  // Handle rate limiting with exponential backoff
  private async handleRateLimit(service: string, request: any, userId?: string): Promise<any> {
    const config = this.retryConfigs[service];
    const attempts = this.retryAttempts.get(service) || 0;

    if (attempts >= config.maxRetries) {
      this.retryAttempts.delete(service);
      throw new Error(`${service} rate limit exceeded. Please try again later.`);
    }

    const delay = this.calculateBackoffDelay(service, attempts);
    console.log(`Rate limited. Retrying ${service} in ${delay}ms (attempt ${attempts + 1}/${config.maxRetries})`);
    
    this.retryAttempts.set(service, attempts + 1);
    await this.sleep(delay);

    // This would normally retry the original request
    // For now, we'll throw an error to prevent infinite loops
    throw new Error(`${service} rate limit exceeded after ${attempts + 1} attempts`);
  }

  // Handle quota exceeded
  private async handleQuotaExceeded(service: string, request: any): Promise<any> {
    console.log(`${service} quota exceeded, checking for cached data...`);
    
    // Try to use cached data first
    const cachedResult = this.getCachedResult(service, request);
    if (cachedResult) {
      console.log(`Returning cached ${service} data due to quota exceeded`);
      return cachedResult;
    }

    throw new Error(`${service} quota exceeded and no cached data available. Please upgrade your plan or try again later.`);
  }

  // Handle service down
  private async handleServiceDown(service: string, request: any): Promise<any> {
    console.log(`${service} service is down, checking alternatives...`);
    
    // Update circuit breaker
    this.recordFailure(service);

    // Try cached data
    const cachedResult = this.getCachedResult(service, request);
    if (cachedResult) {
      console.log(`Returning cached ${service} data due to service down`);
      return cachedResult;
    }

    throw new Error(`${service} service is currently unavailable. Please try again later.`);
  }

  // Handle network errors
  private async handleNetworkError(service: string, request: any, userId?: string): Promise<any> {
    const config = this.retryConfigs[service];
    const attempts = this.retryAttempts.get(service) || 0;

    if (attempts >= config.maxRetries) {
      this.retryAttempts.delete(service);
      throw new Error(`Network error communicating with ${service}. Please check your connection.`);
    }

    const delay = this.calculateBackoffDelay(service, attempts);
    console.log(`Network error. Retrying ${service} in ${delay}ms (attempt ${attempts + 1}/${config.maxRetries})`);
    
    this.retryAttempts.set(service, attempts + 1);
    await this.sleep(delay);

    throw new Error(`Network error after ${attempts + 1} attempts`);
  }

  // Handle unknown errors
  private async handleUnknownError(service: string, error: any, request: any): Promise<any> {
    console.error(`Unknown ${service} error:`, error);
    this.recordFailure(service);

    // Try cached data as last resort
    const cachedResult = this.getCachedResult(service, request);
    if (cachedResult) {
      console.log(`Returning cached ${service} data due to unknown error`);
      return cachedResult;
    }

    throw new Error(`${service} service error: ${error.message || 'Unknown error occurred'}`);
  }

  // Use cached data
  private useCachedData(service: string, request: any): any {
    const cachedResult = this.getCachedResult(service, request);
    if (cachedResult) {
      console.log(`Returning cached ${service} data`);
      return cachedResult;
    }

    throw new Error(`${service} service unavailable and no cached data found. Please try again later.`);
  }

  // Calculate exponential backoff delay
  private calculateBackoffDelay(service: string, attempts: number): number {
    const config = this.retryConfigs[service];
    const delay = config.baseDelay * Math.pow(config.backoffMultiplier, attempts);
    
    // Add jitter to prevent thundering herd
    const jitter = Math.random() * 1000;
    
    return Math.min(delay + jitter, config.maxDelay);
  }

  // Sleep utility
  private async sleep(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  // Circuit breaker methods
  private isCircuitBreakerOpen(service: string): boolean {
    const state = this.circuitBreakers.get(service);
    if (!state) return false;

    const now = Date.now();

    // If circuit is open and timeout has passed, move to half-open state
    if (state.isOpen && now > state.nextAttempt) {
      state.isOpen = false;
      state.failures = 0;
      console.log(`Circuit breaker for ${service} moved to half-open state`);
    }

    return state.isOpen;
  }

  private recordFailure(service: string): void {
    const now = Date.now();
    let state = this.circuitBreakers.get(service);

    if (!state) {
      state = {
        isOpen: false,
        failures: 0,
        lastFailure: now,
        nextAttempt: now,
      };
      this.circuitBreakers.set(service, state);
    }

    state.failures++;
    state.lastFailure = now;

    // Open circuit breaker if threshold reached
    if (state.failures >= this.CIRCUIT_BREAKER_THRESHOLD) {
      state.isOpen = true;
      state.nextAttempt = now + this.CIRCUIT_BREAKER_RESET_TIMEOUT;
      console.warn(`Circuit breaker opened for ${service} after ${state.failures} failures`);
    }
  }

  private recordSuccess(service: string): void {
    const state = this.circuitBreakers.get(service);
    if (state) {
      state.failures = 0;
      state.isOpen = false;
      console.log(`Circuit breaker reset for ${service} after successful request`);
    }
  }

  // Cache methods
  private getCachedResult(service: string, request: any): any {
    const cacheKey = this.generateCacheKey(service, request);
    const cached = this.cache.get(cacheKey);
    
    if (cached && cached.expires > Date.now()) {
      return cached.data;
    }

    // Remove expired cache
    if (cached) {
      this.cache.delete(cacheKey);
    }

    return null;
  }

  private setCachedResult(service: string, request: any, data: any, ttlMs: number = 3600000): void {
    const cacheKey = this.generateCacheKey(service, request);
    this.cache.set(cacheKey, {
      data,
      expires: Date.now() + ttlMs,
    });
  }

  private generateCacheKey(service: string, request: any): string {
    const requestStr = JSON.stringify(request);
    return `${service}:${Buffer.from(requestStr).toString('base64')}`;
  }

  // Logging and monitoring
  private async logError(service: string, error: any, request: any, userId?: string): Promise<void> {
    try {
      await logApiUsage({
        user_id: userId,
        api_name: service as any,
        endpoint: request.endpoint || 'unknown',
        method: request.method || 'POST',
        tokens_used: 0,
        cost: 0,
        status_code: error.status || 500,
        error_message: error.message || 'Unknown error',
      });
    } catch (logError) {
      console.error('Failed to log API error:', logError);
    }
  }

  // Metrics methods
  updateMetrics(service: string, success: boolean, responseTime: number, error?: string): void {
    let metrics = this.apiMetrics.get(service);
    
    if (!metrics) {
      metrics = {
        totalRequests: 0,
        successCount: 0,
        errorCount: 0,
        averageResponseTime: 0,
      };
      this.apiMetrics.set(service, metrics);
    }

    metrics.totalRequests++;
    
    if (success) {
      metrics.successCount++;
      this.recordSuccess(service);
    } else {
      metrics.errorCount++;
      this.recordFailure(service);
      if (error) {
        metrics.lastError = error;
      }
    }

    // Update average response time
    metrics.averageResponseTime = 
      (metrics.averageResponseTime * (metrics.totalRequests - 1) + responseTime) / metrics.totalRequests;
  }

  getMetrics(service: string): ApiMetrics | null {
    return this.apiMetrics.get(service) || null;
  }

  // Health check
  getServiceHealth(): Record<string, any> {
    const health: Record<string, any> = {};

    for (const [service, metrics] of this.apiMetrics.entries()) {
      const circuitBreaker = this.circuitBreakers.get(service);
      
      health[service] = {
        ...metrics,
        circuitBreakerOpen: circuitBreaker?.isOpen || false,
        uptime: metrics.totalRequests > 0 ? (metrics.successCount / metrics.totalRequests) * 100 : 100,
        status: circuitBreaker?.isOpen ? 'down' : 'up',
      };
    }

    return health;
  }

  // Cleanup expired cache entries
  cleanupCache(): void {
    const now = Date.now();
    for (const [key, cached] of this.cache.entries()) {
      if (cached.expires <= now) {
        this.cache.delete(key);
      }
    }
  }
}

// Export singleton instance
export const apiErrorHandler = APIErrorHandler.getInstance();

// Export utility functions
export const withErrorHandling = async <T>(
  service: string,
  operation: () => Promise<T>,
  request: any,
  userId?: string
): Promise<T> => {
  const startTime = Date.now();
  
  try {
    const result = await operation();
    const responseTime = Date.now() - startTime;
    
    apiErrorHandler.updateMetrics(service, true, responseTime);
    return result;
  } catch (error) {
    const responseTime = Date.now() - startTime;
    apiErrorHandler.updateMetrics(service, false, responseTime, error instanceof Error ? error.message : String(error));
    
    return apiErrorHandler.handleApiError(service, error, request, userId);
  }
};

export default APIErrorHandler;
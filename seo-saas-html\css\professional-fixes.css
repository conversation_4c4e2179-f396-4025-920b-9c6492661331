/* Professional Design Fixes for SEO Pro */
/* This file contains all the professional design improvements */

/* ==================== Professional Typography ==================== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    letter-spacing: -0.025em;
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* ==================== Professional Icons ==================== */
/* Override all icon sizes to be professional */
svg {
    width: 1rem !important;
    height: 1rem !important;
    flex-shrink: 0;
    vertical-align: middle;
}

/* Specific icon contexts */
.btn svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
    margin-right: 0.5rem;
}

.btn-sm svg {
    width: 0.75rem !important;
    height: 0.75rem !important;
    margin-right: 0.375rem;
}

.btn-lg svg {
    width: 1rem !important;
    height: 1rem !important;
    margin-right: 0.625rem;
}

.sidebar-link svg {
    width: 1rem !important;
    height: 1rem !important;
    margin-right: 0.75rem;
}

.nav svg {
    width: 1rem !important;
    height: 1rem !important;
}

.mobile-menu-btn svg {
    width: 1.125rem !important;
    height: 1.125rem !important;
}

.feature-icon svg {
    width: 1.25rem !important;
    height: 1.25rem !important;
}

.card-icon svg {
    width: 1rem !important;
    height: 1rem !important;
}

.table svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
}

.notification-icon svg {
    width: 1rem !important;
    height: 1rem !important;
}

/* ==================== Professional Spacing ==================== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.section {
    padding: 4rem 0;
}

.section-sm {
    padding: 2rem 0;
}

.section-lg {
    padding: 6rem 0;
}

/* ==================== Professional Cards ==================== */
.card {
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    border: 1px solid #f3f4f6 !important;
}

.card:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.card-header {
    padding: 1.25rem 1.5rem !important;
    background: #f9fafb !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.card-body, .card-content {
    padding: 1.5rem !important;
}

.card-footer {
    padding: 1.25rem 1.5rem !important;
    background: #f9fafb !important;
    border-top: 1px solid #f3f4f6 !important;
}

/* ==================== Professional Buttons ==================== */
.btn {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    padding: 0.625rem 1.25rem !important;
    border-radius: 0.5rem !important;
    min-height: 2.5rem !important;
    gap: 0.5rem !important;
    border: 1px solid transparent !important;
}

.btn-sm {
    font-size: 0.8125rem !important;
    padding: 0.5rem 1rem !important;
    min-height: 2rem !important;
    gap: 0.375rem !important;
}

.btn-lg {
    font-size: 1rem !important;
    padding: 0.75rem 1.5rem !important;
    min-height: 3rem !important;
    gap: 0.625rem !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.btn-secondary {
    background: white !important;
    color: #374151 !important;
    border-color: #d1d5db !important;
}

.btn-secondary:hover {
    background: #f9fafb !important;
    border-color: #9ca3af !important;
}

/* ==================== Professional Forms ==================== */
.form-input, .form-textarea, .form-select {
    font-size: 0.875rem !important;
    padding: 0.75rem 1rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    min-height: 2.5rem !important;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.form-label {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
    margin-bottom: 0.5rem !important;
}

.form-group {
    margin-bottom: 1.25rem !important;
}

/* ==================== Professional Navigation ==================== */
.header {
    height: 3.5rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.nav {
    height: 3.5rem !important;
    padding: 0 1.5rem !important;
}

.nav-link {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #6b7280 !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem !important;
}

.nav-link:hover {
    color: #111827 !important;
    background: #f3f4f6 !important;
}

.nav-link.active {
    color: #3b82f6 !important;
    background: #eff6ff !important;
}

/* ==================== Professional Sidebar ==================== */
.sidebar-link {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    padding: 0.75rem 1rem !important;
    border-radius: 0.5rem !important;
    margin-bottom: 0.25rem !important;
    color: #6b7280 !important;
}

.sidebar-link:hover {
    background: #f3f4f6 !important;
    color: #111827 !important;
}

.sidebar-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* ==================== Professional Tables ==================== */
.table {
    font-size: 0.875rem !important;
}

.table th {
    font-weight: 600 !important;
    color: #374151 !important;
    background: #f9fafb !important;
    padding: 0.75rem 1rem !important;
}

.table td {
    padding: 0.75rem 1rem !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

/* ==================== Professional Badges ==================== */
.badge {
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    padding: 0.25rem 0.75rem !important;
    border-radius: 9999px !important;
}

.badge-primary {
    background: #eff6ff !important;
    color: #1d4ed8 !important;
}

.badge-success {
    background: #f0fdf4 !important;
    color: #166534 !important;
}

.badge-warning {
    background: #fffbeb !important;
    color: #92400e !important;
}

.badge-danger {
    background: #fef2f2 !important;
    color: #dc2626 !important;
}

/* ==================== Professional Alerts ==================== */
.alert {
    padding: 1rem 1.25rem !important;
    border-radius: 0.5rem !important;
    font-size: 0.875rem !important;
    border: 1px solid transparent !important;
}

.alert-info {
    background: #eff6ff !important;
    color: #1e40af !important;
    border-color: #bfdbfe !important;
}

.alert-success {
    background: #f0fdf4 !important;
    color: #166534 !important;
    border-color: #bbf7d0 !important;
}

.alert-warning {
    background: #fffbeb !important;
    color: #92400e !important;
    border-color: #fed7aa !important;
}

.alert-danger {
    background: #fef2f2 !important;
    color: #dc2626 !important;
    border-color: #fecaca !important;
}

/* ==================== Professional Modals ==================== */
.modal-content {
    border-radius: 0.75rem !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    border: none !important;
}

.modal-header {
    padding: 1.5rem 1.5rem 1rem !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.modal-body {
    padding: 1rem 1.5rem !important;
}

.modal-footer {
    padding: 1rem 1.5rem 1.5rem !important;
    border-top: 1px solid #f3f4f6 !important;
    background: #f9fafb !important;
}

/* ==================== Professional Progress Bars ==================== */
.progress {
    height: 0.5rem !important;
    background: #f3f4f6 !important;
    border-radius: 9999px !important;
    overflow: hidden !important;
}

.progress-fill {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    height: 100% !important;
    border-radius: 9999px !important;
    transition: width 0.3s ease !important;
}

/* ==================== Professional Responsive Fixes ==================== */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem !important;
    }
    
    .section {
        padding: 2rem 0 !important;
    }
    
    .card-header, .card-body, .card-content, .card-footer {
        padding: 1rem !important;
    }
    
    h1 { font-size: 1.875rem !important; }
    h2 { font-size: 1.5rem !important; }
    h3 { font-size: 1.25rem !important; }
}

/* ==================== Professional Utilities ==================== */
.text-xs { font-size: 0.75rem !important; }
.text-sm { font-size: 0.875rem !important; }
.text-base { font-size: 1rem !important; }
.text-lg { font-size: 1.125rem !important; }
.text-xl { font-size: 1.25rem !important; }

.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

.rounded { border-radius: 0.25rem !important; }
.rounded-md { border-radius: 0.375rem !important; }
.rounded-lg { border-radius: 0.5rem !important; }
.rounded-xl { border-radius: 0.75rem !important; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important; }

/* ==================== Homepage Specific Fixes ==================== */
.hero {
    padding: 4rem 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.hero h1 {
    font-size: 3rem !important;
    font-weight: 700 !important;
    line-height: 1.1 !important;
    margin-bottom: 1.5rem !important;
}

.hero p {
    font-size: 1.25rem !important;
    line-height: 1.6 !important;
    margin-bottom: 2rem !important;
    opacity: 0.9 !important;
}

.hero .btn {
    font-size: 1rem !important;
    padding: 0.875rem 2rem !important;
    min-height: 3rem !important;
}

.feature-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 2rem !important;
    margin-top: 3rem !important;
}

.feature-card {
    padding: 2rem !important;
    text-align: center !important;
    border-radius: 0.75rem !important;
    background: white !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    transition: all 0.3s ease !important;
}

.feature-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.feature-card h3 {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    margin: 1rem 0 0.5rem !important;
    color: #111827 !important;
}

.feature-card p {
    color: #6b7280 !important;
    line-height: 1.6 !important;
}

/* ==================== Dashboard Specific Fixes ==================== */
.dashboard-header {
    background: white !important;
    border-bottom: 1px solid #f3f4f6 !important;
    padding: 1.5rem 2rem !important;
}

.dashboard-title {
    font-size: 1.875rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin: 0 !important;
}

.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    gap: 1.5rem !important;
    margin-bottom: 2rem !important;
}

.stat-card {
    background: white !important;
    padding: 1.5rem !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
}

.stat-value {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #111827 !important;
    margin-bottom: 0.25rem !important;
}

.stat-label {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
}

/* ==================== Content Generator Specific Fixes ==================== */
.step-indicator {
    display: flex !important;
    justify-content: center !important;
    margin-bottom: 2rem !important;
}

.step {
    width: 2rem !important;
    height: 2rem !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    margin: 0 0.5rem !important;
    background: #f3f4f6 !important;
    color: #6b7280 !important;
    border: 2px solid #f3f4f6 !important;
}

.step.active {
    background: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
}

.step.completed {
    background: #10b981 !important;
    color: white !important;
    border-color: #10b981 !important;
}

/* ==================== Mobile Responsive Improvements ==================== */
@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.25rem !important;
    }

    .hero p {
        font-size: 1.125rem !important;
    }

    .feature-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .stats-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    .dashboard-header {
        padding: 1rem !important;
    }

    .dashboard-title {
        font-size: 1.5rem !important;
    }
}

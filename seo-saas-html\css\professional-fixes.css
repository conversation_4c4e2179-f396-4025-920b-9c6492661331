/* Professional Design Fixes for SEO Pro */
/* This file contains all the professional design improvements */

/* ==================== Global Layout Improvements ==================== */
* {
    box-sizing: border-box !important;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    line-height: 1.6 !important;
    color: #374151 !important;
    background: #f9fafb !important;
}

.container {
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: 0 2rem !important;
    width: 100% !important;
}

.container-fluid {
    max-width: 100% !important;
    margin: 0 auto !important;
    padding: 0 2rem !important;
    width: 100% !important;
}

.container-sm {
    max-width: 800px !important;
    margin: 0 auto !important;
    padding: 0 2rem !important;
    width: 100% !important;
}

.container-lg {
    max-width: 1600px !important;
    margin: 0 auto !important;
    padding: 0 2rem !important;
    width: 100% !important;
}

/* Page Layout */
.page-wrapper {
    min-height: 100vh !important;
    display: flex !important;
    flex-direction: column !important;
}

.page-content {
    flex: 1 !important;
    padding: 2rem 0 !important;
}

.page-header {
    background: white !important;
    border-bottom: 1px solid #f3f4f6 !important;
    padding: 2rem 0 !important;
    margin-bottom: 2rem !important;
}

.page-title {
    font-size: 2.25rem !important;
    font-weight: 700 !important;
    color: #111827 !important;
    margin: 0 0 0.5rem 0 !important;
    line-height: 1.2 !important;
}

.page-subtitle {
    font-size: 1.125rem !important;
    color: #6b7280 !important;
    margin: 0 !important;
    line-height: 1.5 !important;
}

/* Section Layouts */
.section {
    padding: 4rem 0 !important;
}

.section-sm {
    padding: 2rem 0 !important;
}

.section-lg {
    padding: 6rem 0 !important;
}

.section-xl {
    padding: 8rem 0 !important;
}

/* Grid Layouts */
.grid {
    display: grid !important;
    gap: 2rem !important;
}

.grid-cols-1 {
    grid-template-columns: 1fr !important;
}

.grid-cols-2 {
    grid-template-columns: repeat(2, 1fr) !important;
}

.grid-cols-3 {
    grid-template-columns: repeat(3, 1fr) !important;
}

.grid-cols-4 {
    grid-template-columns: repeat(4, 1fr) !important;
}

.grid-auto {
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
}

.grid-auto-sm {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
}

.grid-auto-lg {
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
}

/* Flex Layouts */
.flex {
    display: flex !important;
}

.flex-col {
    flex-direction: column !important;
}

.flex-wrap {
    flex-wrap: wrap !important;
}

.items-center {
    align-items: center !important;
}

.items-start {
    align-items: flex-start !important;
}

.items-end {
    align-items: flex-end !important;
}

.justify-center {
    justify-content: center !important;
}

.justify-between {
    justify-content: space-between !important;
}

.justify-around {
    justify-content: space-around !important;
}

.justify-start {
    justify-content: flex-start !important;
}

.justify-end {
    justify-content: flex-end !important;
}

/* ==================== Professional Typography ==================== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.25;
    letter-spacing: -0.025em;
}

h1 { font-size: 2.25rem; }
h2 { font-size: 1.875rem; }
h3 { font-size: 1.5rem; }
h4 { font-size: 1.25rem; }
h5 { font-size: 1.125rem; }
h6 { font-size: 1rem; }

/* ==================== Professional Icons ==================== */
/* Override all icon sizes to be professional */
svg {
    width: 1rem !important;
    height: 1rem !important;
    flex-shrink: 0;
    vertical-align: middle;
}

/* Specific icon contexts */
.btn svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
    margin-right: 0.5rem;
}

.btn-sm svg {
    width: 0.75rem !important;
    height: 0.75rem !important;
    margin-right: 0.375rem;
}

.btn-lg svg {
    width: 1rem !important;
    height: 1rem !important;
    margin-right: 0.625rem;
}

.sidebar-link svg {
    width: 1rem !important;
    height: 1rem !important;
    margin-right: 0.75rem;
}

.nav svg {
    width: 1rem !important;
    height: 1rem !important;
}

.mobile-menu-btn svg {
    width: 1.125rem !important;
    height: 1.125rem !important;
}

.feature-icon svg {
    width: 1.25rem !important;
    height: 1.25rem !important;
}

.card-icon svg {
    width: 1rem !important;
    height: 1rem !important;
}

.table svg {
    width: 0.875rem !important;
    height: 0.875rem !important;
}

.notification-icon svg {
    width: 1rem !important;
    height: 1rem !important;
}

/* ==================== Professional Spacing ==================== */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 1.5rem;
}

.section {
    padding: 4rem 0;
}

.section-sm {
    padding: 2rem 0;
}

.section-lg {
    padding: 6rem 0;
}

/* ==================== Professional Cards ==================== */
.card {
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important;
    border: 1px solid #f3f4f6 !important;
}

.card:hover {
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
}

.card-header {
    padding: 1.25rem 1.5rem !important;
    background: #f9fafb !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.card-body, .card-content {
    padding: 1.5rem !important;
}

.card-footer {
    padding: 1.25rem 1.5rem !important;
    background: #f9fafb !important;
    border-top: 1px solid #f3f4f6 !important;
}

/* ==================== Professional Buttons ==================== */
.btn {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    padding: 0.625rem 1.25rem !important;
    border-radius: 0.5rem !important;
    min-height: 2.5rem !important;
    gap: 0.5rem !important;
    border: 1px solid transparent !important;
}

.btn-sm {
    font-size: 0.8125rem !important;
    padding: 0.5rem 1rem !important;
    min-height: 2rem !important;
    gap: 0.375rem !important;
}

.btn-lg {
    font-size: 1rem !important;
    padding: 0.75rem 1.5rem !important;
    min-height: 3rem !important;
    gap: 0.625rem !important;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
    box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.btn-secondary {
    background: white !important;
    color: #374151 !important;
    border-color: #d1d5db !important;
}

.btn-secondary:hover {
    background: #f9fafb !important;
    border-color: #9ca3af !important;
}

/* ==================== Professional Forms ==================== */
.form-input, .form-textarea, .form-select {
    font-size: 0.875rem !important;
    padding: 0.75rem 1rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    min-height: 2.5rem !important;
}

.form-input:focus, .form-textarea:focus, .form-select:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

.form-label {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #374151 !important;
    margin-bottom: 0.5rem !important;
}

.form-group {
    margin-bottom: 1.25rem !important;
}

/* ==================== Professional Navigation ==================== */
.header {
    height: 3.5rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.nav {
    height: 3.5rem !important;
    padding: 0 1.5rem !important;
}

.nav-link {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #6b7280 !important;
    padding: 0.5rem 1rem !important;
    border-radius: 0.375rem !important;
}

.nav-link:hover {
    color: #111827 !important;
    background: #f3f4f6 !important;
}

.nav-link.active {
    color: #3b82f6 !important;
    background: #eff6ff !important;
}

/* ==================== Professional Sidebar ==================== */
.sidebar-link {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    padding: 0.75rem 1rem !important;
    border-radius: 0.5rem !important;
    margin-bottom: 0.25rem !important;
    color: #6b7280 !important;
}

.sidebar-link:hover {
    background: #f3f4f6 !important;
    color: #111827 !important;
}

.sidebar-link.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    color: white !important;
}

/* ==================== Professional Tables ==================== */
.table {
    font-size: 0.875rem !important;
}

.table th {
    font-weight: 600 !important;
    color: #374151 !important;
    background: #f9fafb !important;
    padding: 0.75rem 1rem !important;
}

.table td {
    padding: 0.75rem 1rem !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

/* ==================== Professional Badges ==================== */
.badge {
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    padding: 0.25rem 0.75rem !important;
    border-radius: 9999px !important;
}

.badge-primary {
    background: #eff6ff !important;
    color: #1d4ed8 !important;
}

.badge-success {
    background: #f0fdf4 !important;
    color: #166534 !important;
}

.badge-warning {
    background: #fffbeb !important;
    color: #92400e !important;
}

.badge-danger {
    background: #fef2f2 !important;
    color: #dc2626 !important;
}

/* ==================== Professional Alerts ==================== */
.alert {
    padding: 1rem 1.25rem !important;
    border-radius: 0.5rem !important;
    font-size: 0.875rem !important;
    border: 1px solid transparent !important;
}

.alert-info {
    background: #eff6ff !important;
    color: #1e40af !important;
    border-color: #bfdbfe !important;
}

.alert-success {
    background: #f0fdf4 !important;
    color: #166534 !important;
    border-color: #bbf7d0 !important;
}

.alert-warning {
    background: #fffbeb !important;
    color: #92400e !important;
    border-color: #fed7aa !important;
}

.alert-danger {
    background: #fef2f2 !important;
    color: #dc2626 !important;
    border-color: #fecaca !important;
}

/* ==================== Professional Modals ==================== */
.modal-content {
    border-radius: 0.75rem !important;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
    border: none !important;
}

.modal-header {
    padding: 1.5rem 1.5rem 1rem !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.modal-body {
    padding: 1rem 1.5rem !important;
}

.modal-footer {
    padding: 1rem 1.5rem 1.5rem !important;
    border-top: 1px solid #f3f4f6 !important;
    background: #f9fafb !important;
}

/* ==================== Professional Progress Bars ==================== */
.progress {
    height: 0.5rem !important;
    background: #f3f4f6 !important;
    border-radius: 9999px !important;
    overflow: hidden !important;
}

.progress-fill {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    height: 100% !important;
    border-radius: 9999px !important;
    transition: width 0.3s ease !important;
}

/* ==================== Professional Responsive Fixes ==================== */
@media (max-width: 768px) {
    .container {
        padding: 0 1rem !important;
    }
    
    .section {
        padding: 2rem 0 !important;
    }
    
    .card-header, .card-body, .card-content, .card-footer {
        padding: 1rem !important;
    }
    
    h1 { font-size: 1.875rem !important; }
    h2 { font-size: 1.5rem !important; }
    h3 { font-size: 1.25rem !important; }
}

/* ==================== Professional Utilities ==================== */
.text-xs { font-size: 0.75rem !important; }
.text-sm { font-size: 0.875rem !important; }
.text-base { font-size: 1rem !important; }
.text-lg { font-size: 1.125rem !important; }
.text-xl { font-size: 1.25rem !important; }

.font-medium { font-weight: 500 !important; }
.font-semibold { font-weight: 600 !important; }
.font-bold { font-weight: 700 !important; }

.rounded { border-radius: 0.25rem !important; }
.rounded-md { border-radius: 0.375rem !important; }
.rounded-lg { border-radius: 0.5rem !important; }
.rounded-xl { border-radius: 0.75rem !important; }

.shadow-sm { box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05) !important; }
.shadow { box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06) !important; }
.shadow-md { box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important; }
.shadow-lg { box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important; }

/* ==================== Homepage Specific Fixes ==================== */
.hero {
    padding: 4rem 0 !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
}

.hero h1 {
    font-size: 3rem !important;
    font-weight: 700 !important;
    line-height: 1.1 !important;
    margin-bottom: 1.5rem !important;
}

.hero p {
    font-size: 1.25rem !important;
    line-height: 1.6 !important;
    margin-bottom: 2rem !important;
    opacity: 0.9 !important;
}

.hero .btn {
    font-size: 1rem !important;
    padding: 0.875rem 2rem !important;
    min-height: 3rem !important;
}

.feature-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 2rem !important;
    margin-top: 3rem !important;
}

.feature-card {
    padding: 2rem !important;
    text-align: center !important;
    border-radius: 0.75rem !important;
    background: white !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    transition: all 0.3s ease !important;
}

.feature-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.feature-card h3 {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    margin: 1rem 0 0.5rem !important;
    color: #111827 !important;
}

.feature-card p {
    color: #6b7280 !important;
    line-height: 1.6 !important;
}

/* ==================== Professional Dashboard Layout ==================== */
.dashboard-container {
    display: flex !important;
    min-height: 100vh !important;
}

.dashboard-sidebar {
    width: 280px !important;
    background: white !important;
    border-right: 1px solid #f3f4f6 !important;
    height: calc(100vh - 3.5rem) !important;
    position: fixed !important;
    top: 3.5rem !important;
    left: 0 !important;
    overflow-y: auto !important;
    transition: transform 0.3s ease !important;
    z-index: 40 !important;
}

.dashboard-main {
    flex: 1 !important;
    margin-left: 280px !important;
    min-height: calc(100vh - 3.5rem) !important;
    background: #f9fafb !important;
}

.dashboard-content {
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: 2rem !important;
    width: 100% !important;
}

.dashboard-header {
    background: white !important;
    border-bottom: 1px solid #f3f4f6 !important;
    padding: 2rem !important;
    margin-bottom: 2rem !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
}

.dashboard-title {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #111827 !important;
    margin: 0 0 0.5rem 0 !important;
}

.dashboard-subtitle {
    font-size: 1rem !important;
    color: #6b7280 !important;
    margin: 0 !important;
}

/* Wide Content Sections */
.content-section {
    background: white !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    margin-bottom: 2rem !important;
    overflow: hidden !important;
}

.section-header {
    padding: 1.5rem 2rem !important;
    border-bottom: 1px solid #f3f4f6 !important;
    background: #f9fafb !important;
}

.section-title {
    font-size: 1.25rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin: 0 !important;
}

.section-content {
    padding: 2rem !important;
}

/* Stats Grid - Wide Layout */
.stats-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)) !important;
    gap: 1.5rem !important;
    margin-bottom: 2rem !important;
}

.stat-card {
    background: white !important;
    padding: 2rem !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    text-align: center !important;
    transition: all 0.3s ease !important;
}

.stat-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.stat-value {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #111827 !important;
    margin-bottom: 0.5rem !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.stat-label {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
    text-transform: uppercase !important;
    letter-spacing: 0.05em !important;
}

.stat-change {
    font-size: 0.75rem !important;
    font-weight: 500 !important;
    margin-top: 0.5rem !important;
}

.stat-change.positive {
    color: #10b981 !important;
}

.stat-change.negative {
    color: #ef4444 !important;
}

/* Dashboard Tabs */
.dashboard-tabs {
    display: flex !important;
    border-bottom: 1px solid #f3f4f6 !important;
    margin-bottom: 2rem !important;
    background: white !important;
    border-radius: 0.75rem 0.75rem 0 0 !important;
    overflow: hidden !important;
}

.tab-button {
    flex: 1 !important;
    padding: 1rem 2rem !important;
    background: #f9fafb !important;
    border: none !important;
    border-bottom: 3px solid transparent !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
    font-size: 0.875rem !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
}

.tab-button:hover {
    background: #f3f4f6 !important;
    color: #374151 !important;
}

.tab-button.active {
    background: white !important;
    color: #3b82f6 !important;
    border-bottom-color: #3b82f6 !important;
}

/* Tab Content */
.tab-content {
    background: white !important;
    border-radius: 0 0 0.75rem 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    border-top: none !important;
}

.tab-pane {
    display: none !important;
    padding: 2rem !important;
}

.tab-pane.active {
    display: block !important;
}

/* Wide Form Layout */
.wide-form {
    max-width: 100% !important;
    margin: 0 auto !important;
}

.form-row {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 1.5rem !important;
    margin-bottom: 1.5rem !important;
}

.form-group-wide {
    margin-bottom: 1.5rem !important;
}

.form-input-wide, .form-textarea-wide, .form-select-wide {
    width: 100% !important;
    padding: 1rem 1.25rem !important;
    font-size: 0.875rem !important;
    border: 1px solid #d1d5db !important;
    border-radius: 0.5rem !important;
    min-height: 3rem !important;
    background: white !important;
    transition: all 0.3s ease !important;
}

.form-input-wide:focus, .form-textarea-wide:focus, .form-select-wide:focus {
    border-color: #3b82f6 !important;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
    outline: none !important;
}

.form-textarea-wide {
    min-height: 8rem !important;
    resize: vertical !important;
}

/* Dashboard Cards Grid */
.dashboard-grid {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr)) !important;
    gap: 2rem !important;
    margin-bottom: 2rem !important;
}

.dashboard-card {
    background: white !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    overflow: hidden !important;
    transition: all 0.3s ease !important;
}

.dashboard-card:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
}

.dashboard-card-header {
    padding: 1.5rem 2rem !important;
    background: #f9fafb !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.dashboard-card-title {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin: 0 !important;
}

.dashboard-card-content {
    padding: 2rem !important;
}

/* Recent Activity List */
.activity-list {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}

.activity-item {
    display: flex !important;
    align-items: center !important;
    padding: 1rem 0 !important;
    border-bottom: 1px solid #f3f4f6 !important;
}

.activity-item:last-child {
    border-bottom: none !important;
}

.activity-icon {
    width: 2.5rem !important;
    height: 2.5rem !important;
    border-radius: 50% !important;
    background: #eff6ff !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    margin-right: 1rem !important;
}

.activity-icon svg {
    width: 1.25rem !important;
    height: 1.25rem !important;
    color: #3b82f6 !important;
}

.activity-content {
    flex: 1 !important;
}

.activity-title {
    font-size: 0.875rem !important;
    font-weight: 500 !important;
    color: #111827 !important;
    margin: 0 0 0.25rem 0 !important;
}

.activity-time {
    font-size: 0.75rem !important;
    color: #6b7280 !important;
}

/* ==================== Content Generator Specific Fixes ==================== */
.step-indicator {
    display: flex !important;
    justify-content: center !important;
    margin-bottom: 2rem !important;
}

.step {
    width: 2rem !important;
    height: 2rem !important;
    border-radius: 50% !important;
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;
    font-size: 0.875rem !important;
    font-weight: 600 !important;
    margin: 0 0.5rem !important;
    background: #f3f4f6 !important;
    color: #6b7280 !important;
    border: 2px solid #f3f4f6 !important;
}

.step.active {
    background: #3b82f6 !important;
    color: white !important;
    border-color: #3b82f6 !important;
}

.step.completed {
    background: #10b981 !important;
    color: white !important;
    border-color: #10b981 !important;
}

/* ==================== Mobile Responsive Improvements ==================== */
@media (max-width: 1023px) {
    .dashboard-sidebar {
        transform: translateX(-100%) !important;
    }

    .dashboard-sidebar.active {
        transform: translateX(0) !important;
    }

    .dashboard-main {
        margin-left: 0 !important;
    }

    .dashboard-content {
        padding: 1rem !important;
    }

    .dashboard-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .form-row {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    .stats-grid {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)) !important;
    }
}

@media (max-width: 768px) {
    .hero h1 {
        font-size: 2.25rem !important;
    }

    .hero p {
        font-size: 1.125rem !important;
    }

    .feature-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem !important;
    }

    .stats-grid {
        grid-template-columns: 1fr !important;
        gap: 1rem !important;
    }

    .dashboard-header {
        padding: 1.5rem 1rem !important;
    }

    .dashboard-title {
        font-size: 1.5rem !important;
    }

    .section-header {
        padding: 1rem 1.5rem !important;
    }

    .section-content {
        padding: 1.5rem !important;
    }

    .dashboard-card-header {
        padding: 1rem 1.5rem !important;
    }

    .dashboard-card-content {
        padding: 1.5rem !important;
    }

    .tab-button {
        padding: 0.75rem 1rem !important;
        font-size: 0.8125rem !important;
    }

    .tab-pane {
        padding: 1.5rem !important;
    }

    .form-input-wide, .form-textarea-wide, .form-select-wide {
        padding: 0.875rem 1rem !important;
        min-height: 2.75rem !important;
    }
}

/* ==================== Content Generator Layout ==================== */
.generator-container {
    max-width: 1200px !important;
    margin: 0 auto !important;
    padding: 2rem !important;
}

.generator-header {
    text-align: center !important;
    margin-bottom: 3rem !important;
    background: white !important;
    padding: 3rem 2rem !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
}

.generator-title {
    font-size: 2.5rem !important;
    font-weight: 700 !important;
    color: #111827 !important;
    margin-bottom: 1rem !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.generator-subtitle {
    font-size: 1.25rem !important;
    color: #6b7280 !important;
    margin: 0 !important;
    max-width: 600px !important;
    margin: 0 auto !important;
}

/* Step Wizard */
.step-wizard {
    background: white !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    overflow: hidden !important;
    margin-bottom: 2rem !important;
}

/* Step Content */
.step-content {
    padding: 3rem !important;
}

.step-title {
    font-size: 1.5rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin-bottom: 0.5rem !important;
    text-align: center !important;
}

.step-description {
    font-size: 1rem !important;
    color: #6b7280 !important;
    text-align: center !important;
    margin-bottom: 2rem !important;
    max-width: 600px !important;
    margin-left: auto !important;
    margin-right: auto !important;
}

/* Form Layout in Steps */
.step-form {
    max-width: 800px !important;
    margin: 0 auto !important;
}

.form-section {
    margin-bottom: 2rem !important;
}

.form-section-title {
    font-size: 1.125rem !important;
    font-weight: 600 !important;
    color: #111827 !important;
    margin-bottom: 1rem !important;
}

/* Navigation Buttons */
.step-navigation {
    display: flex !important;
    justify-content: space-between !important;
    align-items: center !important;
    padding: 2rem 3rem !important;
    background: #f9fafb !important;
    border-top: 1px solid #f3f4f6 !important;
}

.nav-buttons {
    display: flex !important;
    gap: 1rem !important;
}

/* Results Section */
.results-section {
    background: white !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    overflow: hidden !important;
    margin-top: 2rem !important;
}

.results-header {
    padding: 2rem !important;
    background: #f9fafb !important;
    border-bottom: 1px solid #f3f4f6 !important;
    text-align: center !important;
}

.results-content {
    padding: 2rem !important;
}

.generated-content {
    background: #f9fafb !important;
    border: 1px solid #f3f4f6 !important;
    border-radius: 0.5rem !important;
    padding: 2rem !important;
    font-family: 'Georgia', serif !important;
    line-height: 1.8 !important;
    color: #374151 !important;
    min-height: 400px !important;
}

/* Content Actions */
.content-actions {
    display: flex !important;
    justify-content: center !important;
    gap: 1rem !important;
    padding: 2rem !important;
    background: #f9fafb !important;
    border-top: 1px solid #f3f4f6 !important;
    flex-wrap: wrap !important;
}

/* Progress Indicator */
.generation-progress {
    text-align: center !important;
    padding: 3rem !important;
}

.progress-circle {
    width: 4rem !important;
    height: 4rem !important;
    border: 3px solid #f3f4f6 !important;
    border-top: 3px solid #3b82f6 !important;
    border-radius: 50% !important;
    margin: 0 auto 1rem !important;
    animation: spin 1s linear infinite !important;
}

.progress-text {
    font-size: 1.125rem !important;
    color: #6b7280 !important;
    margin-bottom: 0.5rem !important;
}

.progress-step {
    font-size: 0.875rem !important;
    color: #9ca3af !important;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ==================== SEO Analysis Layout ==================== */
.analysis-container {
    max-width: 1400px !important;
    margin: 0 auto !important;
    padding: 2rem !important;
}

.analysis-form {
    background: white !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    padding: 2rem !important;
    margin-bottom: 2rem !important;
}

.analysis-results {
    background: white !important;
    border-radius: 0.75rem !important;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1) !important;
    border: 1px solid #f3f4f6 !important;
    overflow: hidden !important;
}

.score-circle {
    width: 8rem !important;
    height: 8rem !important;
    margin: 0 auto !important;
}

.module-scores {
    display: grid !important;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)) !important;
    gap: 1.5rem !important;
    margin: 2rem 0 !important;
}

.module-card {
    background: white !important;
    border: 1px solid #f3f4f6 !important;
    border-radius: 0.5rem !important;
    padding: 1.5rem !important;
    text-align: center !important;
}

.module-score {
    font-size: 2rem !important;
    font-weight: 700 !important;
    color: #3b82f6 !important;
    margin-bottom: 0.5rem !important;
}

.module-name {
    font-size: 0.875rem !important;
    color: #6b7280 !important;
    font-weight: 500 !important;
}

/* ==================== Utility Classes ==================== */
.w-full { width: 100% !important; }
.h-full { height: 100% !important; }
.max-w-full { max-width: 100% !important; }
.min-h-screen { min-height: 100vh !important; }

.p-0 { padding: 0 !important; }
.p-1 { padding: 0.25rem !important; }
.p-2 { padding: 0.5rem !important; }
.p-3 { padding: 0.75rem !important; }
.p-4 { padding: 1rem !important; }
.p-6 { padding: 1.5rem !important; }
.p-8 { padding: 2rem !important; }

.m-0 { margin: 0 !important; }
.m-1 { margin: 0.25rem !important; }
.m-2 { margin: 0.5rem !important; }
.m-3 { margin: 0.75rem !important; }
.m-4 { margin: 1rem !important; }
.m-6 { margin: 1.5rem !important; }
.m-8 { margin: 2rem !important; }

.mb-2 { margin-bottom: 0.5rem !important; }
.mb-3 { margin-bottom: 0.75rem !important; }
.mb-4 { margin-bottom: 1rem !important; }
.mb-6 { margin-bottom: 1.5rem !important; }
.mb-8 { margin-bottom: 2rem !important; }

.mt-2 { margin-top: 0.5rem !important; }
.mt-3 { margin-top: 0.75rem !important; }
.mt-4 { margin-top: 1rem !important; }
.mt-6 { margin-top: 1.5rem !important; }
.mt-8 { margin-top: 2rem !important; }

.gap-2 { gap: 0.5rem !important; }
.gap-3 { gap: 0.75rem !important; }
.gap-4 { gap: 1rem !important; }
.gap-6 { gap: 1.5rem !important; }

.hidden { display: none !important; }
.block { display: block !important; }
.inline-block { display: inline-block !important; }
.inline-flex { display: inline-flex !important; }

.text-center { text-align: center !important; }
.text-left { text-align: left !important; }
.text-right { text-align: right !important; }

.text-white { color: white !important; }
.text-gray-600 { color: #6b7280 !important; }
.text-gray-700 { color: #374151 !important; }
.text-gray-900 { color: #111827 !important; }

const express = require('express');
const router = express.Router();
const { verifyToken } = require('../middleware/auth');
const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_KEY
);

// Export content as HTML
router.post('/html', verifyToken, async (req, res) => {
  try {
    const { contentId, includeMetadata = true } = req.body;

    if (!contentId) {
      return res.status(400).json({ error: 'Content ID is required' });
    }

    // Get content from database
    const { data: content, error } = await supabase
      .from('generated_content')
      .select('*')
      .eq('id', contentId)
      .eq('user_id', req.user.id)
      .single();

    if (error || !content) {
      return res.status(404).json({ error: 'Content not found' });
    }

    // Generate HTML
    const html = generateHTML(content, includeMetadata);

    res.setHeader('Content-Type', 'text/html');
    res.setHeader('Content-Disposition', `attachment; filename="${content.keyword.replace(/\s+/g, '-')}.html"`);
    res.send(html);
  } catch (error) {
    console.error('Export HTML error:', error);
    res.status(500).json({ error: 'Failed to export content' });
  }
});

// Export content as Markdown
router.post('/markdown', verifyToken, async (req, res) => {
  try {
    const { contentId, includeMetadata = true } = req.body;

    if (!contentId) {
      return res.status(400).json({ error: 'Content ID is required' });
    }

    // Get content from database
    const { data: content, error } = await supabase
      .from('generated_content')
      .select('*')
      .eq('id', contentId)
      .eq('user_id', req.user.id)
      .single();

    if (error || !content) {
      return res.status(404).json({ error: 'Content not found' });
    }

    // Generate Markdown
    const markdown = generateMarkdown(content, includeMetadata);

    res.setHeader('Content-Type', 'text/markdown');
    res.setHeader('Content-Disposition', `attachment; filename="${content.keyword.replace(/\s+/g, '-')}.md"`);
    res.send(markdown);
  } catch (error) {
    console.error('Export Markdown error:', error);
    res.status(500).json({ error: 'Failed to export content' });
  }
});

// Export multiple contents as JSON
router.post('/json', verifyToken, async (req, res) => {
  try {
    const { contentIds = [], startDate, endDate } = req.body;

    let query = supabase
      .from('generated_content')
      .select('*')
      .eq('user_id', req.user.id);

    // Filter by content IDs if provided
    if (contentIds.length > 0) {
      query = query.in('id', contentIds);
    }

    // Filter by date range if provided
    if (startDate) {
      query = query.gte('created_at', startDate);
    }
    if (endDate) {
      query = query.lte('created_at', endDate);
    }

    const { data: contents, error } = await query;

    if (error) {
      throw error;
    }

    res.setHeader('Content-Type', 'application/json');
    res.setHeader('Content-Disposition', 'attachment; filename="seo-content-export.json"');
    res.json({
      exportDate: new Date().toISOString(),
      totalContents: contents.length,
      contents
    });
  } catch (error) {
    console.error('Export JSON error:', error);
    res.status(500).json({ error: 'Failed to export content' });
  }
});

// Helper function to generate HTML
function generateHTML(content, includeMetadata) {
  const metadata = content.metadata || {};
  
  let html = `<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${content.keyword}</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        h1, h2, h3 { color: #2c3e50; }
        .metadata {
            background: #f4f4f4;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 30px;
        }
        .metadata h3 { margin-top: 0; }
        .metadata ul { margin: 0; padding-left: 20px; }
    </style>
</head>
<body>`;

  if (includeMetadata) {
    html += `
    <div class="metadata">
        <h3>Content Metadata</h3>
        <ul>
            <li><strong>Keyword:</strong> ${content.keyword}</li>
            <li><strong>Type:</strong> ${content.content_type}</li>
            <li><strong>Generated:</strong> ${new Date(content.created_at).toLocaleDateString()}</li>
            ${metadata.tone ? `<li><strong>Tone:</strong> ${metadata.tone}</li>` : ''}
            ${metadata.industry ? `<li><strong>Industry:</strong> ${metadata.industry}</li>` : ''}
            ${metadata.wordCount ? `<li><strong>Word Count:</strong> ${metadata.wordCount}</li>` : ''}
        </ul>
    </div>`;
  }

  html += `
    <article>
        ${content.content}
    </article>
</body>
</html>`;

  return html;
}

// Helper function to generate Markdown
function generateMarkdown(content, includeMetadata) {
  const metadata = content.metadata || {};
  let markdown = '';

  if (includeMetadata) {
    markdown += `---
keyword: ${content.keyword}
type: ${content.content_type}
generated: ${new Date(content.created_at).toISOString()}
${metadata.tone ? `tone: ${metadata.tone}` : ''}
${metadata.industry ? `industry: ${metadata.industry}` : ''}
---

`;
  }

  // Convert HTML content to Markdown (simplified conversion)
  let markdownContent = content.content;
  
  // Convert headings
  markdownContent = markdownContent.replace(/<h1>(.*?)<\/h1>/gi, '# $1\n');
  markdownContent = markdownContent.replace(/<h2>(.*?)<\/h2>/gi, '## $1\n');
  markdownContent = markdownContent.replace(/<h3>(.*?)<\/h3>/gi, '### $1\n');
  
  // Convert paragraphs
  markdownContent = markdownContent.replace(/<p>(.*?)<\/p>/gi, '$1\n\n');
  
  // Convert lists
  markdownContent = markdownContent.replace(/<ul>/gi, '');
  markdownContent = markdownContent.replace(/<\/ul>/gi, '\n');
  markdownContent = markdownContent.replace(/<li>(.*?)<\/li>/gi, '- $1\n');
  
  // Convert bold and italic
  markdownContent = markdownContent.replace(/<strong>(.*?)<\/strong>/gi, '**$1**');
  markdownContent = markdownContent.replace(/<em>(.*?)<\/em>/gi, '*$1*');
  
  // Remove remaining HTML tags
  markdownContent = markdownContent.replace(/<[^>]*>/g, '');
  
  markdown += markdownContent;

  return markdown;
}

module.exports = router;
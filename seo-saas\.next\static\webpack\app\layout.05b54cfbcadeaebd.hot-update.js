"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/layout",{

/***/ "(app-pages-browser)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSubscription: function() { return /* binding */ createSubscription; },\n/* harmony export */   createSupabaseComponentClient: function() { return /* binding */ createSupabaseComponentClient; },\n/* harmony export */   getProfile: function() { return /* binding */ getProfile; },\n/* harmony export */   getSession: function() { return /* binding */ getSession; },\n/* harmony export */   getUser: function() { return /* binding */ getUser; },\n/* harmony export */   getUserSubscription: function() { return /* binding */ getUserSubscription; },\n/* harmony export */   handleSupabaseError: function() { return /* binding */ handleSupabaseError; },\n/* harmony export */   logApiUsage: function() { return /* binding */ logApiUsage; },\n/* harmony export */   logUserActivity: function() { return /* binding */ logUserActivity; },\n/* harmony export */   resetPassword: function() { return /* binding */ resetPassword; },\n/* harmony export */   signIn: function() { return /* binding */ signIn; },\n/* harmony export */   signOut: function() { return /* binding */ signOut; },\n/* harmony export */   signUp: function() { return /* binding */ signUp; },\n/* harmony export */   subscribeToApiUsage: function() { return /* binding */ subscribeToApiUsage; },\n/* harmony export */   subscribeToContentGenerations: function() { return /* binding */ subscribeToContentGenerations; },\n/* harmony export */   supabase: function() { return /* binding */ supabase; },\n/* harmony export */   supabaseAdmin: function() { return /* binding */ supabaseAdmin; },\n/* harmony export */   updatePassword: function() { return /* binding */ updatePassword; },\n/* harmony export */   updateProfile: function() { return /* binding */ updateProfile; }\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(app-pages-browser)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(app-pages-browser)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* provided dependency */ var process = __webpack_require__(/*! process */ \"(app-pages-browser)/./node_modules/next/dist/build/polyfills/process.js\");\n// Supabase Client Configuration\n\n\n// Client-side Supabase client (for use in components)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://zqrmpanonghggoxdjirq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY\", {\n    auth: {\n        persistSession: true,\n        autoRefreshToken: true,\n        detectSessionInUrl: true\n    },\n    db: {\n        schema: \"public\"\n    },\n    global: {\n        headers: {\n            \"X-Client-Info\": \"seo-saas@1.0.0\"\n        }\n    }\n});\n// Client component client (for use in client components)\nconst createSupabaseComponentClient = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// Server component client is in a separate server-only file\n// Service role client (for admin operations)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://zqrmpanonghggoxdjirq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    },\n    db: {\n        schema: \"public\"\n    },\n    global: {\n        headers: {\n            \"X-Client-Info\": \"seo-saas-admin@1.0.0\"\n        }\n    }\n});\n// Auth helpers\nasync function getSession() {\n    const supabase = createSupabaseComponentClient();\n    const { data: { session }, error } = await supabase.auth.getSession();\n    if (error) {\n        console.error(\"Error getting session:\", error);\n        return null;\n    }\n    return session;\n}\nasync function getUser() {\n    const supabase = createSupabaseComponentClient();\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n    return user;\n}\nasync function signIn(email, password) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\nasync function signUp(email, password, fullName) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: {\n                full_name: fullName\n            }\n        }\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\nasync function signOut() {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n        throw new Error(error.message);\n    }\n}\nasync function resetPassword(email) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: \"\".concat(\"http://localhost:3000\" || 0, \"/auth/reset-password\")\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n}\nasync function updatePassword(newPassword) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.auth.updateUser({\n        password: newPassword\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n}\n// Profile helpers\nasync function getProfile(userId) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", userId).single();\n    if (error) {\n        console.error(\"Error getting profile:\", error);\n        return null;\n    }\n    return data;\n}\nasync function updateProfile(userId, updates) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"profiles\").update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq(\"id\", userId).select().single();\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\n// Subscription helpers\nasync function getUserSubscription(userId) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"user_subscriptions\").select(\"*\").eq(\"user_id\", userId).single();\n    if (error && error.code !== \"PGRST116\") {\n        console.error(\"Error getting subscription:\", error);\n        return null;\n    }\n    return data;\n}\nasync function createSubscription(subscription) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"user_subscriptions\").insert(subscription).select().single();\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\n// API usage tracking\nasync function logApiUsage(logData) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.from(\"api_usage_logs\").insert(logData);\n    if (error) {\n        console.error(\"Error logging API usage:\", error);\n    }\n}\n// Activity logging\nasync function logUserActivity(activity) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.from(\"user_activity_logs\").insert(activity);\n    if (error) {\n        console.error(\"Error logging user activity:\", error);\n    }\n}\n// Real-time subscriptions\nfunction subscribeToContentGenerations(userId, callback) {\n    const supabase = createSupabaseComponentClient();\n    return supabase.channel(\"content_generations\").on(\"postgres_changes\", {\n        event: \"*\",\n        schema: \"public\",\n        table: \"content_generations\",\n        filter: \"user_id=eq.\".concat(userId)\n    }, callback).subscribe();\n}\nfunction subscribeToApiUsage(userId, callback) {\n    const supabase = createSupabaseComponentClient();\n    return supabase.channel(\"api_usage\").on(\"postgres_changes\", {\n        event: \"INSERT\",\n        schema: \"public\",\n        table: \"api_usage_logs\",\n        filter: \"user_id=eq.\".concat(userId)\n    }, callback).subscribe();\n}\n// Error handling helper\nfunction handleSupabaseError(error) {\n    if (error === null || error === void 0 ? void 0 : error.message) {\n        return error.message;\n    }\n    if (error === null || error === void 0 ? void 0 : error.code) {\n        switch(error.code){\n            case \"auth/invalid-email\":\n                return \"Invalid email address\";\n            case \"auth/user-disabled\":\n                return \"This account has been disabled\";\n            case \"auth/user-not-found\":\n                return \"No account found with this email\";\n            case \"auth/wrong-password\":\n                return \"Incorrect password\";\n            case \"auth/too-many-requests\":\n                return \"Too many attempts. Please try again later\";\n            case \"auth/weak-password\":\n                return \"Password should be at least 6 characters\";\n            case \"auth/email-already-in-use\":\n                return \"An account with this email already exists\";\n            default:\n                return \"An unexpected error occurred\";\n        }\n    }\n    return \"An unexpected error occurred\";\n}\n/* harmony default export */ __webpack_exports__[\"default\"] = (supabase);\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/lib/supabase.ts\n"));

/***/ })

});
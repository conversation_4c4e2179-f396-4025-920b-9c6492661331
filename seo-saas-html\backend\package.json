{"name": "seo-saas-backend", "version": "1.0.0", "description": "Backend API server for SEO SAAS application", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "jest"}, "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "dotenv": "^16.3.1", "axios": "^1.6.2", "express-rate-limit": "^7.1.5", "helmet": "^7.1.0", "compression": "^1.7.4", "morgan": "^1.10.0", "cheerio": "^1.0.0-rc.12", "natural": "^6.10.4", "stopword": "^2.0.8", "jsonwebtoken": "^9.0.2", "bcryptjs": "^2.4.3", "@supabase/supabase-js": "^2.39.3"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3"}, "engines": {"node": ">=18.0.0"}}
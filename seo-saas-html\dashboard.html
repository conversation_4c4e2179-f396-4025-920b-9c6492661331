<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Dashboard - SEO Pro</title>
    <meta name="description" content="SEO Pro Dashboard - Manage your SEO content generation and analysis">
    
    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="images/favicon.ico">
    
    <!-- Stylesheets -->
    <link href="https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700;800&display=swap" rel="stylesheet">
    <link href="css/main.css" rel="stylesheet">
    <link href="css/components.css" rel="stylesheet">
    <link href="css/responsive.css" rel="stylesheet">
    <link href="css/animations.css" rel="stylesheet">
    <link href="css/professional-fixes.css" rel="stylesheet">
    
    <style>
        /* Dashboard Specific Styles - Enhanced */
        .dashboard-layout {
            display: flex;
            min-height: 100vh;
        }

        .dashboard-sidebar {
            width: 280px;
            background: white;
            border-right: 1px solid #f3f4f6;
            height: calc(100vh - 3.5rem);
            position: fixed;
            top: 3.5rem;
            left: 0;
            overflow-y: auto;
            transition: transform 0.3s ease;
            z-index: 40;
        }

        .dashboard-main {
            flex: 1;
            margin-left: 280px;
            min-height: calc(100vh - 3.5rem);
            background: #f9fafb;
        }

        .dashboard-content {
            max-width: 1400px;
            margin: 0 auto;
            padding: 2rem;
            width: 100%;
        }

        @media (max-width: 1023px) {
            .dashboard-sidebar {
                transform: translateX(-100%);
            }

            .dashboard-sidebar.active {
                transform: translateX(0);
            }

            .dashboard-main {
                margin-left: 0;
            }

            .dashboard-content {
                padding: 1rem;
            }
        }
        
        .sidebar-nav {
            padding: var(--space-6);
        }
        
        .sidebar-link {
            display: flex;
            align-items: center;
            padding: var(--space-3) var(--space-4);
            margin-bottom: var(--space-2);
            color: var(--gray-700);
            text-decoration: none;
            border-radius: var(--radius-md);
            transition: all var(--transition-fast);
            font-weight: var(--font-medium);
        }
        
        .sidebar-link:hover {
            background: var(--gray-100);
            color: var(--primary-blue);
        }
        
        .sidebar-link.active {
            background: var(--primary-gradient);
            color: var(--white);
        }
        
        .sidebar-link svg {
            width: 1.25rem;
            height: 1.25rem;
            margin-right: var(--space-3);
        }
        
        .dashboard-header {
            background: var(--white);
            padding: var(--space-6);
            border-bottom: 1px solid var(--gray-200);
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: var(--space-6);
            margin-bottom: var(--space-8);
        }
        
        .chart-placeholder {
            background: var(--white);
            height: 300px;
            border-radius: var(--radius-lg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--gray-500);
            border: 2px dashed var(--gray-300);
        }
        
        .activity-item {
            display: flex;
            align-items: start;
            padding: var(--space-4);
            border-bottom: 1px solid var(--gray-100);
        }
        
        .activity-item:last-child {
            border-bottom: none;
        }
        
        .activity-icon {
            width: 2.5rem;
            height: 2.5rem;
            border-radius: var(--radius-full);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: var(--space-4);
            flex-shrink: 0;
        }
        
        .activity-content {
            flex: 1;
        }
        
        .activity-time {
            font-size: var(--text-sm);
            color: var(--gray-500);
        }
    </style>
</head>
<body>
    <!-- Header Navigation -->
    <header class="header">
        <nav class="nav container-fluid px-6">
            <div class="flex items-center">
                <!-- Mobile Sidebar Toggle -->
                <button class="mobile-menu-btn mr-4 lg:hidden" onclick="toggleSidebar()" aria-label="Toggle sidebar">
                    <span class="hamburger"></span>
                </button>
                
                <a href="index.html" class="logo">
                    <span class="logo-text">SEO Pro</span>
                </a>
            </div>
            
            <!-- Header Actions -->
            <div class="flex items-center gap-4">
                <!-- Search (Desktop) -->
                <div class="hidden md:block">
                    <div class="input-group">
                        <input type="search" class="form-input" placeholder="Search..." style="width: 300px;">
                    </div>
                </div>
                
                <!-- Notifications -->
                <button class="relative p-2 text-gray-600 hover:text-gray-900">
                    <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
                    </svg>
                    <span class="absolute top-0 right-0 w-2 h-2 bg-error rounded-full"></span>
                </button>
                
                <!-- User Menu -->
                <div class="relative">
                    <button class="flex items-center gap-2 p-2" onclick="toggleUserMenu()">
                        <div class="w-8 h-8 bg-gradient rounded-full flex items-center justify-center text-white font-semibold">
                            JD
                        </div>
                        <svg class="w-4 h-4 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                        </svg>
                    </button>
                    
                    <!-- User Dropdown Menu -->
                    <div id="userMenu" class="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-2 hidden">
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Profile</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Settings</a>
                        <a href="#" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Billing</a>
                        <hr class="my-2">
                        <a href="login.html" class="block px-4 py-2 text-gray-700 hover:bg-gray-100">Sign Out</a>
                    </div>
                </div>
            </div>
        </nav>
    </header>
    
    <!-- Dashboard Layout -->
    <div class="dashboard-layout">
        <!-- Sidebar -->
        <aside class="dashboard-sidebar" id="sidebar">
            <nav class="sidebar-nav">
                <a href="dashboard.html" class="sidebar-link active">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"></path>
                    </svg>
                    Dashboard
                </a>
                
                <a href="content-generator.html" class="sidebar-link">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"></path>
                    </svg>
                    Content Generator
                </a>
                
                <a href="seo-analysis.html" class="sidebar-link">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                    </svg>
                    SEO Analysis
                </a>
                
                <a href="#" class="sidebar-link">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                    </svg>
                    Keyword Research
                </a>
                
                <a href="#" class="sidebar-link">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 7v10a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-6l-2-2H5a2 2 0 00-2 2z"></path>
                    </svg>
                    Projects
                </a>
                
                <a href="#" class="sidebar-link">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 8v8m-4-5v5m-4-2v2m-2 4h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                    </svg>
                    Analytics
                </a>
                
                <hr class="my-4 border-gray-200">
                
                <a href="#" class="sidebar-link">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                    </svg>
                    Settings
                </a>
                
                <a href="#" class="sidebar-link">
                    <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                    </svg>
                    Help & Support
                </a>
            </nav>
            
            <!-- Upgrade Banner -->
            <div class="p-6">
                <div class="bg-gradient rounded-lg p-4 text-white">
                    <h3 class="font-semibold mb-2">Upgrade to Pro</h3>
                    <p class="text-sm mb-3 opacity-90">Unlock all features and generate unlimited content</p>
                    <a href="pricing.html" class="btn btn-primary bg-white text-primary btn-sm w-full">
                        Upgrade Now
                    </a>
                </div>
            </div>
        </aside>
        
        <!-- Main Content -->
        <main class="dashboard-main">
            <div class="dashboard-content">
                <!-- Dashboard Header -->
                <div class="dashboard-header">
                    <h1 class="dashboard-title">Welcome back, John!</h1>
                    <p class="dashboard-subtitle">Here's an overview of your SEO performance and recent activity</p>
                </div>

                <!-- Dashboard Tabs -->
                <div class="dashboard-tabs">
                    <button class="tab-button active" onclick="switchTab('overview')">Overview</button>
                    <button class="tab-button" onclick="switchTab('content')">Content</button>
                    <button class="tab-button" onclick="switchTab('analytics')">Analytics</button>
                    <button class="tab-button" onclick="switchTab('settings')">Settings</button>
                </div>

                <!-- Tab Content -->
                <div class="tab-content">
                    <!-- Overview Tab -->
                    <div id="overview-tab" class="tab-pane active">
                        <!-- Stats Grid -->
                        <div class="stats-grid">
                            <div class="stat-card">
                                <div class="stat-value">156</div>
                                <div class="stat-label">Content Generated</div>
                                <div class="stat-change positive">+12% this month</div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-value">89.5%</div>
                                <div class="stat-label">Average SEO Score</div>
                                <div class="stat-change positive">+23% improvement</div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-value">342</div>
                                <div class="stat-label">Keywords Tracked</div>
                                <div class="stat-change positive">+45 new keywords</div>
                            </div>

                            <div class="stat-card">
                                <div class="stat-value">24.5h</div>
                                <div class="stat-label">Time Saved</div>
                                <div class="stat-change positive">+8.2h this week</div>
                            </div>
                        </div>

                        <!-- Quick Actions -->
                        <div class="content-section">
                            <div class="section-header">
                                <h2 class="section-title">Quick Actions</h2>
                            </div>
                            <div class="section-content">
                                <div class="dashboard-grid">
                                    <div class="dashboard-card">
                                        <div class="dashboard-card-header">
                                            <h3 class="dashboard-card-title">Generate Content</h3>
                                        </div>
                                        <div class="dashboard-card-content">
                                            <p class="text-gray-600 mb-4">Create SEO-optimized content with AI assistance</p>
                                            <a href="content-generator.html" class="btn btn-primary w-full">Start Generating</a>
                                        </div>
                                    </div>

                                    <div class="dashboard-card">
                                        <div class="dashboard-card-header">
                                            <h3 class="dashboard-card-title">SEO Analysis</h3>
                                        </div>
                                        <div class="dashboard-card-content">
                                            <p class="text-gray-600 mb-4">Analyze your content for SEO optimization</p>
                                            <a href="seo-analysis.html" class="btn btn-secondary w-full">Analyze Content</a>
                                        </div>
                                    </div>

                                    <div class="dashboard-card">
                                        <div class="dashboard-card-header">
                                            <h3 class="dashboard-card-title">Keyword Research</h3>
                                        </div>
                                        <div class="dashboard-card-content">
                                            <p class="text-gray-600 mb-4">Discover high-performing keywords for your niche</p>
                                            <button class="btn btn-secondary w-full">Research Keywords</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Recent Activity -->
                        <div class="content-section">
                            <div class="section-header">
                                <h2 class="section-title">Recent Activity</h2>
                            </div>
                            <div class="section-content">
                                <div class="activity-list">
                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">Generated blog post: "10 SEO Tips for 2024"</div>
                                            <div class="activity-time">2 hours ago</div>
                                        </div>
                                    </div>

                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">Completed SEO analysis for homepage</div>
                                            <div class="activity-time">5 hours ago</div>
                                        </div>
                                    </div>

                                    <div class="activity-item">
                                        <div class="activity-icon">
                                            <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 7a2 2 0 012 2m4 0a6 6 0 01-7.743 5.743L11 17H9v2H7v2H4a1 1 0 01-1-1v-2.586a1 1 0 01.293-.707l5.964-5.964A6 6 0 1121 9z"></path>
                                            </svg>
                                        </div>
                                        <div class="activity-content">
                                            <div class="activity-title">Added 15 new keywords to tracking</div>
                                            <div class="activity-time">1 day ago</div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Content Tab -->
                    <div id="content-tab" class="tab-pane">
                        <div class="content-section">
                            <div class="section-header">
                                <h2 class="section-title">Content Management</h2>
                            </div>
                            <div class="section-content">
                                <div class="wide-form">
                                    <div class="form-row">
                                        <div class="form-group-wide">
                                            <label class="form-label">Target URL</label>
                                            <input type="url" class="form-input-wide" placeholder="https://example.com/page">
                                        </div>
                                        <div class="form-group-wide">
                                            <label class="form-label">Content Type</label>
                                            <select class="form-select-wide">
                                                <option>Blog Post</option>
                                                <option>Product Page</option>
                                                <option>Landing Page</option>
                                                <option>Category Page</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="form-group-wide">
                                        <label class="form-label">Target Keywords</label>
                                        <input type="text" class="form-input-wide" placeholder="Enter keywords separated by commas">
                                    </div>

                                    <div class="form-group-wide">
                                        <label class="form-label">Content Brief</label>
                                        <textarea class="form-textarea-wide" placeholder="Describe what you want to create..."></textarea>
                                    </div>

                                    <div class="form-row">
                                        <div class="form-group-wide">
                                            <label class="form-label">Word Count</label>
                                            <select class="form-select-wide">
                                                <option>500-800 words</option>
                                                <option>800-1200 words</option>
                                                <option>1200-2000 words</option>
                                                <option>2000+ words</option>
                                            </select>
                                        </div>
                                        <div class="form-group-wide">
                                            <label class="form-label">Tone</label>
                                            <select class="form-select-wide">
                                                <option>Professional</option>
                                                <option>Casual</option>
                                                <option>Technical</option>
                                                <option>Conversational</option>
                                            </select>
                                        </div>
                                    </div>

                                    <div class="flex justify-end gap-3">
                                        <button class="btn btn-secondary">Save Draft</button>
                                        <button class="btn btn-primary">Generate Content</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Analytics Tab -->
                    <div id="analytics-tab" class="tab-pane">
                        <div class="content-section">
                            <div class="section-header">
                                <h2 class="section-title">Performance Analytics</h2>
                            </div>
                            <div class="section-content">
                                <div class="dashboard-grid">
                                    <div class="dashboard-card">
                                        <div class="dashboard-card-header">
                                            <h3 class="dashboard-card-title">Content Performance</h3>
                                        </div>
                                        <div class="dashboard-card-content">
                                            <div class="text-center">
                                                <div class="text-3xl font-bold text-blue-600 mb-2">85%</div>
                                                <div class="text-gray-600">Average SEO Score</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="dashboard-card">
                                        <div class="dashboard-card-header">
                                            <h3 class="dashboard-card-title">Keyword Rankings</h3>
                                        </div>
                                        <div class="dashboard-card-content">
                                            <div class="text-center">
                                                <div class="text-3xl font-bold text-green-600 mb-2">127</div>
                                                <div class="text-gray-600">Top 10 Rankings</div>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="dashboard-card">
                                        <div class="dashboard-card-header">
                                            <h3 class="dashboard-card-title">Traffic Growth</h3>
                                        </div>
                                        <div class="dashboard-card-content">
                                            <div class="text-center">
                                                <div class="text-3xl font-bold text-purple-600 mb-2">+34%</div>
                                                <div class="text-gray-600">This Month</div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Settings Tab -->
                    <div id="settings-tab" class="tab-pane">
                        <div class="content-section">
                            <div class="section-header">
                                <h2 class="section-title">Dashboard Settings</h2>
                            </div>
                            <div class="section-content">
                                <div class="wide-form">
                                    <div class="form-group-wide">
                                        <label class="form-label">Default Industry</label>
                                        <select class="form-select-wide">
                                            <option>Technology</option>
                                            <option>Healthcare</option>
                                            <option>Finance</option>
                                            <option>E-commerce</option>
                                            <option>Education</option>
                                        </select>
                                    </div>

                                    <div class="form-group-wide">
                                        <label class="form-label">Default Content Length</label>
                                        <select class="form-select-wide">
                                            <option>800-1200 words</option>
                                            <option>500-800 words</option>
                                            <option>1200-2000 words</option>
                                            <option>2000+ words</option>
                                        </select>
                                    </div>

                                    <div class="form-group-wide">
                                        <label class="form-label">Email Notifications</label>
                                        <div class="flex items-center gap-4">
                                            <label class="flex items-center">
                                                <input type="checkbox" class="mr-2" checked>
                                                Content generation complete
                                            </label>
                                            <label class="flex items-center">
                                                <input type="checkbox" class="mr-2" checked>
                                                Weekly performance reports
                                            </label>
                                        </div>
                                    </div>

                                    <div class="flex justify-end">
                                        <button class="btn btn-primary">Save Settings</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Mobile Sidebar Overlay -->
    <div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 hidden lg:hidden z-30" onclick="toggleSidebar()"></div>

    <script>
        // Tab switching functionality
        function switchTab(tabName) {
            // Remove active class from all tab buttons
            document.querySelectorAll('.tab-button').forEach(btn => {
                btn.classList.remove('active');
            });

            // Hide all tab panes
            document.querySelectorAll('.tab-pane').forEach(pane => {
                pane.classList.remove('active');
            });

            // Add active class to clicked button
            event.target.classList.add('active');

            // Show corresponding tab pane
            document.getElementById(tabName + '-tab').classList.add('active');
        }

        </main>
    </div>
    
    <!-- Mobile Sidebar Overlay -->
    <div id="sidebarOverlay" class="fixed inset-0 bg-black bg-opacity-50 hidden lg:hidden z-30" onclick="toggleSidebar()"></div>
    
    <script>
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const menuBtn = document.querySelector('.mobile-menu-btn');
            
            sidebar.classList.toggle('active');
            overlay.classList.toggle('hidden');
            menuBtn.classList.toggle('active');
        }

        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            const overlay = document.getElementById('sidebarOverlay');
            const menuBtn = document.querySelector('.mobile-menu-btn');

            sidebar.classList.toggle('active');
            overlay.classList.toggle('hidden');
            menuBtn.classList.toggle('active');
        }

        function toggleUserMenu() {
            const userMenu = document.getElementById('userMenu');
            userMenu.classList.toggle('hidden');
        }

        // Close user menu when clicking outside
        document.addEventListener('click', function(event) {
            const userMenu = document.getElementById('userMenu');
            const userMenuButton = event.target.closest('button[onclick="toggleUserMenu()"]');

            if (!userMenu.contains(event.target) && !userMenuButton) {
                userMenu.classList.add('hidden');
            }
        });

        // Add active class to current page in sidebar
        const currentPath = window.location.pathname;
        const sidebarLinks = document.querySelectorAll('.sidebar-link');

        sidebarLinks.forEach(link => {
            if (link.getAttribute('href') === currentPath.split('/').pop()) {
                link.classList.add('active');
            } else {
                link.classList.remove('active');
            }
        });
    </script>
</body>
</html>
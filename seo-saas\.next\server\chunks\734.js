"use strict";exports.id=734,exports.ids=[734],exports.modules={7734:(e,t,a)=>{a.d(t,{vG:()=>i});var s=a(917),o=a(8280),r=a(6517);class n{constructor(){this.cache=new Map,this.CACHE_TTL={search:9e5,analysis:18e5,keywords:36e5},this.apiKey=s.vc.apis.serper.apiKey,this.baseUrl=s.vc.apis.serper.baseUrl,setInterval(()=>this.cleanupCache(),3e5)}async search(e,t){return(0,o.bi)("serper",async()=>{let a=Date.now(),s=this.generateCacheKey("search",e),o=this.getFromCache(s);if(o)return console.log("Returning cached Serper search results"),o;console.log("Fetching fresh SERP data from Serper...",{query:e.query,location:e.location,device:e.device});let n={q:e.query,...e.location&&{location:e.location},...e.country&&{gl:e.country},...e.language&&{hl:e.language},...e.device&&{device:e.device},num:e.num||10,...e.page&&{start:(e.page-1)*(e.num||10)},...e.type&&"search"!==e.type&&{type:e.type},...e.tbs&&{tbs:e.tbs},...e.safe&&{safe:e.safe},autocorrect:!1!==e.autocorrect},i=await fetch(`${this.baseUrl}/search`,{method:"POST",headers:{"X-API-KEY":this.apiKey,"Content-Type":"application/json"},body:JSON.stringify(n)});if(!i.ok)throw Error(`Serper API error: ${i.status} ${i.statusText}`);let h=await i.json(),l=Date.now()-a;return t&&await (0,r.xc)({user_id:t,api_name:"serper",endpoint:"search",method:"POST",tokens_used:0,cost:this.calculateCost(h.credits||1),response_time_ms:l,status_code:i.status}),this.setCache(s,h,this.CACHE_TTL.search),console.log("Serper search completed",{responseTime:l,resultsCount:h.organic?.length||0,credits:h.credits}),h},{method:"POST",endpoint:"search",...e},t)}async analyzeCompetitors(e,t="",a={},s){return(0,o.bi)("serper",async()=>{let o=Date.now(),{analyzeTop:r=5,includeContent:n=!0,includeMetrics:i=!0}=a,h=this.generateCacheKey("analysis",{query:e,location:t,options:a}),l=this.getFromCache(h);if(l)return console.log("Returning cached competitor analysis"),l;console.log("Starting comprehensive competitor analysis...",{query:e,location:t,analyzeTop:r});let c=await this.search({query:e,location:t,num:Math.max(r,10),device:"desktop"},s);if(!c.organic||0===c.organic.length)throw Error("No organic search results found for analysis");let u=[],d=[],p={h1:[],h2:[],h3:[],h4:[]},m=[],y=new Set;for(let t=0;t<Math.min(r,c.organic.length);t++){let a=c.organic[t];try{console.log(`Analyzing competitor ${t+1}: ${a.domain}`);let s={position:a.position,domain:a.domain,title:a.title,url:a.link,snippet:a.snippet};if(n){let t=this.estimateWordCount(a),o=this.estimateHeadings(a),r=this.estimateKeywordDensity(a,e);s.wordCount=t,s.headingStructure=o,s.keywordDensity=r,d.push(t),m.push(r),Object.entries(o).forEach(([e,t])=>{p[e]&&p[e].push(t)}),this.extractKeywords(a.title+" "+a.snippet).forEach(e=>y.add(e))}i&&(s.domainAuthority=this.estimateDomainAuthority(a.domain),s.estimatedTraffic=this.estimateTraffic(a.position),s.contentScore=this.calculateContentScore(s),s.technicalScore=this.calculateTechnicalScore(a)),u.push(s)}catch(e){console.warn(`Failed to analyze competitor ${a.domain}:`,e)}}let g=d.length>0?Math.round(d.reduce((e,t)=>e+t,0)/d.length):800,w={};Object.entries(p).forEach(([e,t])=>{w[e]=t.length>0?Math.round(t.reduce((e,t)=>e+t,0)/t.length):0});let f=m.length>0?Math.round(m.reduce((e,t)=>e+t,0)/m.length*100)/100:2,C=this.identifySearchFeatures(c),S=this.calculateDifficulty(u,C),k=this.identifyContentGaps(c,e),A={query:e,location:t,totalResults:c.organic.length,averageWordCount:g,averageHeadings:w,commonKeywords:Array.from(y).slice(0,20),averageKeywordDensity:f,contentGaps:k,topCompetitors:u,searchFeatures:C,difficulty:S};return this.setCache(h,A,this.CACHE_TTL.analysis),console.log("Competitor analysis completed",{responseTime:Date.now()-o,competitorsAnalyzed:u.length,averageWordCount:g,difficulty:S}),A},{method:"POST",endpoint:"analyze",query:e,location:t,...a},s)}async getPeopleAlsoAsk(e,t,a){return(await this.search({query:e,location:t,num:10},a)).peopleAlsoAsk||[]}async getRelatedSearches(e,t,a){return(await this.search({query:e,location:t,num:10},a)).relatedSearches||[]}async getKeywordSuggestions(e,t,a){return(0,o.bi)("serper",async()=>{let s=this.generateCacheKey("keywords",{query:e,location:t}),o=this.getFromCache(s);if(o)return o;let[r,n]=await Promise.all([this.getRelatedSearches(e,t,a),this.getPeopleAlsoAsk(e,t,a)]),i=new Set;r.forEach(e=>{i.add(e.query)}),n.forEach(e=>{this.extractKeywords(e.question).forEach(e=>{i.add(e)})});let h=Array.from(i).slice(0,50);return this.setCache(s,h,this.CACHE_TTL.keywords),h},{method:"POST",endpoint:"keywords",query:e,location:t},a)}estimateWordCount(e){let t=25*e.snippet.split(/\s+/).length;return e.domain.includes("wikipedia")?Math.max(3*t,2e3):e.domain.includes("blog")||e.title.toLowerCase().includes("guide")?Math.max(2*t,1500):Math.max(t,500)}estimateHeadings(e){let t=this.estimateWordCount(e);return{h1:1,h2:Math.max(2,Math.floor(t/300)),h3:Math.max(0,Math.floor(t/500)),h4:Math.max(0,Math.floor(t/800))}}estimateKeywordDensity(e,t){let a=`${e.title} ${e.snippet}`.toLowerCase(),s=t.toLowerCase().split(/\s+/),o=a.split(/\s+/),r=0;return s.forEach(e=>{r+=(a.match(RegExp(e,"g"))||[]).length}),Math.min(r/o.length*100,5)}estimateDomainAuthority(e){return["wikipedia.org","gov","edu","forbes.com","nytimes.com","bbc.com","cnn.com","techcrunch.com","medium.com"].some(t=>e.includes(t))?Math.floor(20*Math.random())+80:Math.floor(50*Math.random())+30}estimateTraffic(e){return Math.floor((Math.floor(1e4*Math.random())+1e3)*([.31,.24,.18,.13,.09,.06,.04,.03,.02,.02][e-1]||.01))}calculateContentScore(e){let t=0;if(e.wordCount&&(e.wordCount>=1e3?t+=30:e.wordCount>=500?t+=20:t+=10),e.headingStructure){let{h1:a,h2:s,h3:o}=e.headingStructure;1===a&&(t+=5),s>=3&&(t+=10),o>=2&&(t+=10)}return e.keywordDensity&&(e.keywordDensity>=1&&e.keywordDensity<=3?t+=20:e.keywordDensity>=.5&&e.keywordDensity<=5&&(t+=10)),Math.min(t+=Math.max(0,25-(e.position-1)*5),100)}calculateTechnicalScore(e){let t=50;return e.link.startsWith("https://")&&(t+=10),e.sitelinks&&e.sitelinks.length>0&&(t+=15),e.domain.length<15&&(t+=5),e.domain.includes("-")||(t+=5),e.title.length>=30&&e.title.length<=60&&(t+=10),e.snippet.length>=120&&e.snippet.length<=160&&(t+=5),Math.min(t,100)}identifySearchFeatures(e){let t=[];return t.push({type:"featured_snippet",present:!!e.answerBox,position:e.answerBox?0:void 0,content:e.answerBox}),t.push({type:"people_also_ask",present:!!(e.peopleAlsoAsk&&e.peopleAlsoAsk.length>0),content:e.peopleAlsoAsk}),t.push({type:"knowledge_graph",present:!!e.knowledgeGraph,content:e.knowledgeGraph}),t}calculateDifficulty(e,t){let a=0,s=e.reduce((e,t)=>e+(t.domainAuthority||50),0)/e.length;s>80?a+=3:s>60?a+=2:s>40&&(a+=1);let o=e.reduce((e,t)=>e+(t.contentScore||50),0)/e.length;o>80?a+=3:o>60?a+=2:o>40&&(a+=1);let r=t.filter(e=>e.present).length;return(r>3?a+=2:r>1&&(a+=1),a>=7)?"very-high":a>=5?"high":a>=3?"medium":"low"}identifyContentGaps(e,t){let a=[];if(e.peopleAlsoAsk){let s=e.peopleAlsoAsk.map(e=>e.question.toLowerCase());["how","what","why","when","where","which"].forEach(e=>{s.some(t=>t.startsWith(e))||a.push(`${e.charAt(0).toUpperCase()+e.slice(1)} questions about ${t}`)})}return a.push(...["Step-by-step guides","Comparison tables","Pro and cons lists"]),a}extractKeywords(e){let t=e.toLowerCase().replace(/[^\w\s]/g,"").split(/\s+/).filter(e=>e.length>3),a=new Set(["the","and","or","but","in","on","at","to","for","of","with","by","from","as","is","was","are","were","been","have","has","had","do","does","did","will","would","could","should","may","might","must","can","this","that","these","those","what","when","where","why","how","which","who"]);return t.filter(e=>!a.has(e))}generateCacheKey(e,t){let a=JSON.stringify(t);return`${e}:${Buffer.from(a).toString("base64")}`}getFromCache(e){let t=this.cache.get(e);return t?Date.now()>t.timestamp+t.ttl?(this.cache.delete(e),null):t.data:null}setCache(e,t,a){this.cache.set(e,{data:t,timestamp:Date.now(),ttl:a})}cleanupCache(){let e=Date.now();for(let[t,a]of this.cache.entries())e>a.timestamp+a.ttl&&this.cache.delete(t)}calculateCost(e){return .001*e}async healthCheck(){let e=Date.now();try{let t=await fetch(`${this.baseUrl}/search`,{method:"POST",headers:{"X-API-KEY":this.apiKey,"Content-Type":"application/json"},body:JSON.stringify({q:"test",num:1})}),a=Date.now()-e;return{status:t.ok?"healthy":"unhealthy",latency:a}}catch(t){return{status:"unhealthy",latency:Date.now()-e}}}}let i=new n}};
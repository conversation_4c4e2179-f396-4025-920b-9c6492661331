// Configuration file for SEO Pro application
// This file contains all the configuration settings for the application

const CONFIG = {
    // Supabase Configuration
    SUPABASE: {
        URL: 'https://xpcbyzcaidfukddqniny.supabase.co',
        ANON_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDY3OTA1MzYsImV4cCI6MjA2MjM2NjUzNn0.k6gsHXOoRFTc-lYTn3gvH-pB71tXwTVzQF5OFu5mV1A',
        SERVICE_ROLE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InhwY2J5emNhaWRmdWtkZHFuaW55Iiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc0Njc5MDUzNywiZXhwIjoyMDYyMzY2NTM3fQ.rcH_G_p6zeqz1LPhGvJIDDnKwu7bXjY7qqBFMw9ZTC4'
    },

    // API Configuration
    APIS: {
        GROQ: {
            BASE_URL: 'https://api.groq.com/openai/v1',
            API_KEY: '********************************************************',
            MODELS: {
                LLAMA_3_1: 'llama-3.1-70b-versatile',
                LLAMA_3_1_8B: 'llama-3.1-8b-instant',
                MIXTRAL: 'mixtral-8x7b-32768'
            },
            DEFAULT_MODEL: 'llama-3.1-70b-versatile',
            MAX_TOKENS: 4000,
            TEMPERATURE: 0.7
        },
        SERPER: {
            BASE_URL: 'https://google.serper.dev',
            API_KEY: '4ce37b02808e4325e42068eb815b03490a5519e5',
            ENDPOINTS: {
                SEARCH: '/search',
                NEWS: '/news',
                IMAGES: '/images',
                PLACES: '/places'
            }
        }
    },

    // Application Settings
    APP: {
        NAME: 'SEO Pro',
        VERSION: '1.0.0',
        ENVIRONMENT: 'development', // development, staging, production
        BASE_URL: window.location.origin,
        DEBUG: true
    },

    // Content Generation Settings
    CONTENT: {
        DEFAULT_WORD_COUNT: 1000,
        MIN_WORD_COUNT: 300,
        MAX_WORD_COUNT: 3000,
        DEFAULT_TONE: 'professional',
        SUPPORTED_TONES: [
            'professional',
            'casual',
            'friendly',
            'authoritative',
            'conversational',
            'technical'
        ],
        SUPPORTED_CONTENT_TYPES: [
            'blog_post',
            'article',
            'product_description',
            'landing_page',
            'meta_description',
            'social_media_post',
            'email_content',
            'press_release'
        ],
        SUPPORTED_INDUSTRIES: [
            'technology',
            'healthcare',
            'finance',
            'ecommerce',
            'realestate',
            'legal',
            'education',
            'travel',
            'food',
            'fitness',
            'fashion',
            'automotive',
            'saas',
            'consulting',
            'nonprofit'
        ]
    },

    // SEO Analysis Settings
    SEO: {
        ANALYSIS_MODULES: [
            'keyword_density',
            'heading_structure',
            'content_quality',
            'lsi_keywords',
            'competitor_analysis',
            'eeat_compliance'
        ],
        KEYWORD_DENSITY: {
            OPTIMAL_MIN: 1.0,
            OPTIMAL_MAX: 3.0,
            WARNING_THRESHOLD: 4.0,
            DANGER_THRESHOLD: 6.0
        },
        CONTENT_QUALITY: {
            MIN_WORD_COUNT: 300,
            OPTIMAL_WORD_COUNT: 1000,
            READABILITY_TARGETS: {
                FLESCH_KINCAID: 60,
                GUNNING_FOG: 12
            }
        }
    },

    // Subscription Plans
    PLANS: {
        FREE: {
            id: 'free',
            name: 'Free',
            price: 0,
            billing_period: 'month',
            features: {
                content_generations: 5,
                seo_analyses: 10,
                projects: 1,
                team_members: 1,
                api_calls: 100,
                support: 'community'
            }
        },
        PROFESSIONAL: {
            id: 'professional',
            name: 'Professional',
            price: 29,
            billing_period: 'month',
            features: {
                content_generations: 100,
                seo_analyses: 200,
                projects: -1, // unlimited
                team_members: 5,
                api_calls: 10000,
                support: 'priority',
                export_formats: true,
                integrations: true
            }
        },
        BUSINESS: {
            id: 'business',
            name: 'Business',
            price: 79,
            billing_period: 'month',
            features: {
                content_generations: 500,
                seo_analyses: 1000,
                projects: -1, // unlimited
                team_members: 15,
                api_calls: 50000,
                support: 'priority',
                export_formats: true,
                integrations: true,
                white_label: true,
                api_access: true,
                custom_integrations: true
            }
        },
        ENTERPRISE: {
            id: 'enterprise',
            name: 'Enterprise',
            price: 'custom',
            billing_period: 'month',
            features: {
                content_generations: -1, // unlimited
                seo_analyses: -1, // unlimited
                projects: -1, // unlimited
                team_members: -1, // unlimited
                api_calls: -1, // unlimited
                support: 'dedicated',
                export_formats: true,
                integrations: true,
                white_label: true,
                api_access: true,
                custom_integrations: true,
                on_premise: true,
                sla: true,
                custom_models: true
            }
        }
    },

    // UI Settings
    UI: {
        THEME: {
            DEFAULT: 'light',
            SUPPORTED: ['light', 'dark', 'auto']
        },
        ANIMATIONS: {
            DURATION: {
                FAST: 150,
                NORMAL: 300,
                SLOW: 500
            },
            EASING: 'cubic-bezier(0.4, 0, 0.2, 1)'
        },
        BREAKPOINTS: {
            MOBILE: 768,
            TABLET: 1024,
            DESKTOP: 1280,
            LARGE: 1536
        }
    },

    // Error Messages
    ERRORS: {
        NETWORK: 'Network error. Please check your connection and try again.',
        AUTHENTICATION: 'Authentication failed. Please log in again.',
        AUTHORIZATION: 'You do not have permission to perform this action.',
        RATE_LIMIT: 'Rate limit exceeded. Please try again later.',
        VALIDATION: 'Please check your input and try again.',
        SERVER: 'Server error. Please try again later.',
        NOT_FOUND: 'The requested resource was not found.',
        QUOTA_EXCEEDED: 'You have exceeded your usage quota. Please upgrade your plan.'
    },

    // Success Messages
    SUCCESS: {
        CONTENT_GENERATED: 'Content generated successfully!',
        ANALYSIS_COMPLETE: 'SEO analysis completed successfully!',
        PROFILE_UPDATED: 'Profile updated successfully!',
        SETTINGS_SAVED: 'Settings saved successfully!',
        PROJECT_CREATED: 'Project created successfully!',
        EXPORT_COMPLETE: 'Export completed successfully!'
    },

    // Local Storage Keys
    STORAGE_KEYS: {
        USER_PREFERENCES: 'seo_pro_user_preferences',
        THEME: 'seo_pro_theme',
        LANGUAGE: 'seo_pro_language',
        RECENT_PROJECTS: 'seo_pro_recent_projects',
        DRAFT_CONTENT: 'seo_pro_draft_content'
    },

    // Analytics Events
    ANALYTICS: {
        EVENTS: {
            CONTENT_GENERATED: 'content_generated',
            SEO_ANALYSIS_RUN: 'seo_analysis_run',
            COMPETITOR_ANALYSIS_RUN: 'competitor_analysis_run',
            PROJECT_CREATED: 'project_created',
            EXPORT_COMPLETED: 'export_completed',
            PLAN_UPGRADED: 'plan_upgraded'
        }
    }
};

// Utility functions for configuration
const ConfigUtils = {
    // Get API endpoint URL
    getApiUrl: (service, endpoint) => {
        const serviceConfig = CONFIG.APIS[service.toUpperCase()];
        if (!serviceConfig) {
            throw new Error(`Unknown service: ${service}`);
        }
        return serviceConfig.BASE_URL + (serviceConfig.ENDPOINTS?.[endpoint.toUpperCase()] || endpoint);
    },

    // Get plan features
    getPlanFeatures: (planId) => {
        const plan = CONFIG.PLANS[planId.toUpperCase()];
        return plan ? plan.features : null;
    },

    // Check if feature is available for plan
    hasFeature: (planId, feature) => {
        const features = ConfigUtils.getPlanFeatures(planId);
        return features && features[feature] !== undefined;
    },

    // Get usage limit for plan
    getUsageLimit: (planId, resource) => {
        const features = ConfigUtils.getPlanFeatures(planId);
        if (!features) return 0;
        const limit = features[resource];
        return limit === -1 ? Infinity : limit;
    },

    // Check if user has exceeded usage limit
    isUsageExceeded: (planId, resource, currentUsage) => {
        const limit = ConfigUtils.getUsageLimit(planId, resource);
        return limit !== Infinity && currentUsage >= limit;
    },

    // Get error message
    getErrorMessage: (errorType) => {
        return CONFIG.ERRORS[errorType.toUpperCase()] || 'An unexpected error occurred.';
    },

    // Get success message
    getSuccessMessage: (successType) => {
        return CONFIG.SUCCESS[successType.toUpperCase()] || 'Operation completed successfully!';
    },

    // Check if environment is development
    isDevelopment: () => {
        return CONFIG.APP.ENVIRONMENT === 'development';
    },

    // Check if environment is production
    isProduction: () => {
        return CONFIG.APP.ENVIRONMENT === 'production';
    },

    // Get storage key
    getStorageKey: (key) => {
        return CONFIG.STORAGE_KEYS[key.toUpperCase()];
    }
};

// Export configuration for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { CONFIG, ConfigUtils };
} else {
    window.CONFIG = CONFIG;
    window.ConfigUtils = ConfigUtils;
}

// Initialize configuration on load
document.addEventListener('DOMContentLoaded', function() {
    // Set up global error handling
    window.addEventListener('error', function(event) {
        if (CONFIG.APP.DEBUG) {
            console.error('Global error:', event.error);
        }
    });

    // Set up unhandled promise rejection handling
    window.addEventListener('unhandledrejection', function(event) {
        if (CONFIG.APP.DEBUG) {
            console.error('Unhandled promise rejection:', event.reason);
        }
    });

    // Initialize theme
    const savedTheme = localStorage.getItem(CONFIG.STORAGE_KEYS.THEME);
    if (savedTheme && CONFIG.UI.THEME.SUPPORTED.includes(savedTheme)) {
        document.documentElement.setAttribute('data-theme', savedTheme);
    }

    console.log('SEO Pro Configuration Loaded:', CONFIG.APP.VERSION);
});

/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/not-found";
exports.ids = ["app/not-found"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n          '__DEFAULT__',\n          {},\n          {\n            defaultPage: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/parallel-route-default */ \"(rsc)/./node_modules/next/dist/client/components/parallel-route-default.js\", 23)), \"next/dist/client/components/parallel-route-default\"],\n          }\n        ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/not-found\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/not-found\",\n        pathname: \"/not-found\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp%5Cglobals.css&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Ccontexts%5Cauth-context.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp%5Cglobals.css&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Ccontexts%5Cauth-context.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/auth-context.tsx */ \"(ssr)/./src/contexts/auth-context.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RiUzQSU1Q0NsYXVkZS1Db2RlLVNldHVwJTVDU0VPJTIwU0FBUyUyMEFQUCU1Q3Nlby1zYWFzJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz1GJTNBJTVDQ2xhdWRlLUNvZGUtU2V0dXAlNUNTRU8lMjBTQUFTJTIwQVBQJTVDc2VvLXNhYXMlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUYlM0ElNUNDbGF1ZGUtQ29kZS1TZXR1cCU1Q1NFTyUyMFNBQVMlMjBBUFAlNUNzZW8tc2FhcyU1Q3NyYyU1Q2NvbnRleHRzJTVDYXV0aC1jb250ZXh0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tc2Fhcy8/ZjJiNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkY6XFxcXENsYXVkZS1Db2RlLVNldHVwXFxcXFNFTyBTQUFTIEFQUFxcXFxzZW8tc2Fhc1xcXFxzcmNcXFxcY29udGV4dHNcXFxcYXV0aC1jb250ZXh0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp%5Cglobals.css&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Ccontexts%5Cauth-context.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthGuard: () => (/* binding */ useAuthGuard),\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,useSubscription,useAuthGuard auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subscription, setSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseComponentClient)();\n    // Fetch user profile\n    const fetchProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching profile:\", error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n            return null;\n        }\n    };\n    // Fetch user subscription\n    const fetchSubscription = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"user_subscriptions\").select(\"*\").eq(\"user_id\", userId).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error fetching subscription:\", error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching subscription:\", error);\n            return null;\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                // Get initial session\n                const { data: { session }, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                    setLoading(false);\n                    return;\n                }\n                if (session?.user) {\n                    setSession(session);\n                    setUser(session.user);\n                    // Fetch profile and subscription in parallel\n                    const [profileData, subscriptionData] = await Promise.all([\n                        fetchProfile(session.user.id),\n                        fetchSubscription(session.user.id)\n                    ]);\n                    setProfile(profileData);\n                    setSubscription(subscriptionData);\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n        // Listen for auth changes\n        const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.id);\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                // Fetch profile and subscription for new session\n                const [profileData, subscriptionData] = await Promise.all([\n                    fetchProfile(session.user.id),\n                    fetchSubscription(session.user.id)\n                ]);\n                setProfile(profileData);\n                setSubscription(subscriptionData);\n            } else {\n                // Clear profile and subscription on sign out\n                setProfile(null);\n                setSubscription(null);\n            }\n            setLoading(false);\n        });\n        return ()=>{\n            authSubscription.unsubscribe();\n        };\n    }, []);\n    // Sign in\n    const signIn = async (email, password)=>{\n        const { error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    };\n    // Sign up\n    const signUp = async (email, password, fullName)=>{\n        const { error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    };\n    // Sign out\n    const signOut = async ()=>{\n        const { error } = await supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    };\n    // Reset password\n    const resetPassword = async (email)=>{\n        const { error } = await supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    };\n    // Update profile\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            throw new Error(\"No user logged in\");\n        }\n        const { data, error } = await supabase.from(\"profiles\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", user.id).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        setProfile(data);\n    };\n    // Refresh profile\n    const refreshProfile = async ()=>{\n        if (!user) return;\n        const profileData = await fetchProfile(user.id);\n        setProfile(profileData);\n    };\n    // Refresh subscription\n    const refreshSubscription = async ()=>{\n        if (!user) return;\n        const subscriptionData = await fetchSubscription(user.id);\n        setSubscription(subscriptionData);\n    };\n    const value = {\n        user,\n        session,\n        profile,\n        subscription,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshProfile,\n        refreshSubscription\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Helper hook for checking subscription status\nfunction useSubscription() {\n    const { subscription } = useAuth();\n    const hasActiveSubscription = subscription?.status === \"active\";\n    const isOnTrial = subscription?.status === \"trialing\";\n    const isPastDue = subscription?.status === \"past_due\";\n    const isCancelled = subscription?.status === \"cancelled\";\n    const canAccessFeature = (feature)=>{\n        if (!subscription) return false;\n        if (feature === \"pro\") {\n            return [\n                \"pro\",\n                \"enterprise\"\n            ].includes(subscription.plan_type) && hasActiveSubscription;\n        }\n        if (feature === \"enterprise\") {\n            return subscription.plan_type === \"enterprise\" && hasActiveSubscription;\n        }\n        return false;\n    };\n    return {\n        subscription,\n        hasActiveSubscription,\n        isOnTrial,\n        isPastDue,\n        isCancelled,\n        canAccessFeature\n    };\n}\n// Helper hook for authentication guards\nfunction useAuthGuard() {\n    const { user, loading } = useAuth();\n    const isAuthenticated = !!user;\n    const isLoading = loading;\n    return {\n        isAuthenticated,\n        isLoading,\n        user\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/config.ts":
/*!***************************!*\
  !*** ./src/lib/config.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   appConfig: () => (/* binding */ appConfig),\n/* harmony export */   authConfig: () => (/* binding */ authConfig),\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   featureConfig: () => (/* binding */ featureConfig),\n/* harmony export */   groqConfig: () => (/* binding */ groqConfig),\n/* harmony export */   isDevelopment: () => (/* binding */ isDevelopment),\n/* harmony export */   isProduction: () => (/* binding */ isProduction),\n/* harmony export */   isTest: () => (/* binding */ isTest),\n/* harmony export */   serperConfig: () => (/* binding */ serperConfig),\n/* harmony export */   supabaseConfig: () => (/* binding */ supabaseConfig),\n/* harmony export */   validateConfig: () => (/* binding */ validateConfig)\n/* harmony export */ });\n// Application Configuration\n// This file centralizes all environment variables and configuration\nconst requiredEnvVars = [\n    \"NEXT_PUBLIC_SUPABASE_URL\",\n    \"NEXT_PUBLIC_SUPABASE_ANON_KEY\",\n    \"SUPABASE_SERVICE_ROLE_KEY\",\n    \"GROQ_API_KEY\",\n    \"SERPER_API_KEY\",\n    \"NEXTAUTH_SECRET\"\n];\n// Validate required environment variables\nfunction validateEnvVars() {\n    const missingVars = requiredEnvVars.filter((varName)=>!process.env[varName]);\n    if (missingVars.length > 0) {\n        throw new Error(`Missing required environment variables: ${missingVars.join(\", \")}`);\n    }\n}\n// Validate environment variables on module load\nif (true) {\n    // Only validate on server-side\n    validateEnvVars();\n}\nconst config = {\n    supabase: {\n        url: \"https://zqrmpanonghggoxdjirq.supabase.co\",\n        anonKey: \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY\",\n        serviceRoleKey: process.env.SUPABASE_SERVICE_ROLE_KEY\n    },\n    apis: {\n        groq: {\n            apiKey: process.env.GROQ_API_KEY,\n            baseUrl: \"https://api.groq.com/openai/v1\",\n            model: \"llama3-8b-8192\",\n            rateLimitPerMinute: parseInt(process.env.GROQ_RATE_LIMIT_PER_MINUTE || \"60\")\n        },\n        serper: {\n            apiKey: process.env.SERPER_API_KEY,\n            baseUrl: \"https://google.serper.dev\",\n            rateLimitPerMinute: parseInt(process.env.SERPER_RATE_LIMIT_PER_MINUTE || \"100\")\n        }\n    },\n    app: {\n        url: \"http://localhost:3000\" || 0,\n        name: \"SEO Content Generator\",\n        version: \"1.0.0\",\n        environment: \"development\" || 0\n    },\n    auth: {\n        secret: process.env.NEXTAUTH_SECRET,\n        url: process.env.NEXTAUTH_URL || \"http://localhost:3000\"\n    },\n    features: {\n        analytics: \"true\" === \"true\",\n        testing: \"false\" === \"true\"\n    }\n};\n// Export individual configurations for convenience\nconst supabaseConfig = config.supabase;\nconst groqConfig = config.apis.groq;\nconst serperConfig = config.apis.serper;\nconst appConfig = config.app;\nconst authConfig = config.auth;\nconst featureConfig = config.features;\n// Environment helpers\nconst isDevelopment = config.app.environment === \"development\";\nconst isProduction = config.app.environment === \"production\";\nconst isTest = config.app.environment === \"test\";\n// Validation helper\nfunction validateConfig() {\n    try {\n        validateEnvVars();\n        // Additional validation\n        if (!config.supabase.url.startsWith(\"https://\")) {\n            throw new Error(\"Supabase URL must start with https://\");\n        }\n        if (config.apis.groq.apiKey.length < 10) {\n            throw new Error(\"Groq API key appears to be invalid\");\n        }\n        if (config.apis.serper.apiKey.length < 10) {\n            throw new Error(\"Serper API key appears to be invalid\");\n        }\n        return true;\n    } catch (error) {\n        console.error(\"Configuration validation failed:\", error);\n        return false;\n    }\n}\n// Export default configuration\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (config);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/config.ts\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSubscription: () => (/* binding */ createSubscription),\n/* harmony export */   createSupabaseComponentClient: () => (/* binding */ createSupabaseComponentClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getProfile: () => (/* binding */ getProfile),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   getUserSubscription: () => (/* binding */ getUserSubscription),\n/* harmony export */   handleSupabaseError: () => (/* binding */ handleSupabaseError),\n/* harmony export */   logApiUsage: () => (/* binding */ logApiUsage),\n/* harmony export */   logUserActivity: () => (/* binding */ logUserActivity),\n/* harmony export */   resetPassword: () => (/* binding */ resetPassword),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp),\n/* harmony export */   subscribeToApiUsage: () => (/* binding */ subscribeToApiUsage),\n/* harmony export */   subscribeToContentGenerations: () => (/* binding */ subscribeToContentGenerations),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin),\n/* harmony export */   updatePassword: () => (/* binding */ updatePassword),\n/* harmony export */   updateProfile: () => (/* binding */ updateProfile)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _config__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./config */ \"(ssr)/./src/lib/config.ts\");\n// Supabase Client Configuration\n\n\n\n// Client-side Supabase client (for use in components)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(_config__WEBPACK_IMPORTED_MODULE_1__.config.supabase.url, _config__WEBPACK_IMPORTED_MODULE_1__.config.supabase.anonKey, {\n    auth: {\n        persistSession: true,\n        autoRefreshToken: true,\n        detectSessionInUrl: true\n    },\n    db: {\n        schema: \"public\"\n    },\n    global: {\n        headers: {\n            \"X-Client-Info\": \"seo-saas@1.0.0\"\n        }\n    }\n});\n// Client component client (for use in client components)\nconst createSupabaseComponentClient = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// Server component client is in a separate server-only file\n// Service role client (for admin operations)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_2__.createClient)(_config__WEBPACK_IMPORTED_MODULE_1__.config.supabase.url, _config__WEBPACK_IMPORTED_MODULE_1__.config.supabase.serviceRoleKey, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    },\n    db: {\n        schema: \"public\"\n    },\n    global: {\n        headers: {\n            \"X-Client-Info\": \"seo-saas-admin@1.0.0\"\n        }\n    }\n});\n// Auth helpers\nasync function getSession() {\n    const supabase = createSupabaseComponentClient();\n    const { data: { session }, error } = await supabase.auth.getSession();\n    if (error) {\n        console.error(\"Error getting session:\", error);\n        return null;\n    }\n    return session;\n}\nasync function getUser() {\n    const supabase = createSupabaseComponentClient();\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n    return user;\n}\nasync function signIn(email, password) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\nasync function signUp(email, password, fullName) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: {\n                full_name: fullName\n            }\n        }\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\nasync function signOut() {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n        throw new Error(error.message);\n    }\n}\nasync function resetPassword(email) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${_config__WEBPACK_IMPORTED_MODULE_1__.config.app.url}/auth/reset-password`\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n}\nasync function updatePassword(newPassword) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.auth.updateUser({\n        password: newPassword\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n}\n// Profile helpers\nasync function getProfile(userId) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", userId).single();\n    if (error) {\n        console.error(\"Error getting profile:\", error);\n        return null;\n    }\n    return data;\n}\nasync function updateProfile(userId, updates) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"profiles\").update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq(\"id\", userId).select().single();\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\n// Subscription helpers\nasync function getUserSubscription(userId) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"user_subscriptions\").select(\"*\").eq(\"user_id\", userId).single();\n    if (error && error.code !== \"PGRST116\") {\n        console.error(\"Error getting subscription:\", error);\n        return null;\n    }\n    return data;\n}\nasync function createSubscription(subscription) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"user_subscriptions\").insert(subscription).select().single();\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\n// API usage tracking\nasync function logApiUsage(logData) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.from(\"api_usage_logs\").insert(logData);\n    if (error) {\n        console.error(\"Error logging API usage:\", error);\n    }\n}\n// Activity logging\nasync function logUserActivity(activity) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.from(\"user_activity_logs\").insert(activity);\n    if (error) {\n        console.error(\"Error logging user activity:\", error);\n    }\n}\n// Real-time subscriptions\nfunction subscribeToContentGenerations(userId, callback) {\n    const supabase = createSupabaseComponentClient();\n    return supabase.channel(\"content_generations\").on(\"postgres_changes\", {\n        event: \"*\",\n        schema: \"public\",\n        table: \"content_generations\",\n        filter: `user_id=eq.${userId}`\n    }, callback).subscribe();\n}\nfunction subscribeToApiUsage(userId, callback) {\n    const supabase = createSupabaseComponentClient();\n    return supabase.channel(\"api_usage\").on(\"postgres_changes\", {\n        event: \"INSERT\",\n        schema: \"public\",\n        table: \"api_usage_logs\",\n        filter: `user_id=eq.${userId}`\n    }, callback).subscribe();\n}\n// Error handling helper\nfunction handleSupabaseError(error) {\n    if (error?.message) {\n        return error.message;\n    }\n    if (error?.code) {\n        switch(error.code){\n            case \"auth/invalid-email\":\n                return \"Invalid email address\";\n            case \"auth/user-disabled\":\n                return \"This account has been disabled\";\n            case \"auth/user-not-found\":\n                return \"No account found with this email\";\n            case \"auth/wrong-password\":\n                return \"Incorrect password\";\n            case \"auth/too-many-requests\":\n                return \"Too many attempts. Please try again later\";\n            case \"auth/weak-password\":\n                return \"Password should be at least 6 characters\";\n            case \"auth/email-already-in-use\":\n                return \"An account with this email already exists\";\n            default:\n                return \"An unexpected error occurred\";\n        }\n    }\n    return \"An unexpected error occurred\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"08d6a4bc6fab\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLXNhYXMvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzg3YjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIwOGQ2YTRiYzZmYWJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./src/contexts/auth-context.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"SEO Content Generator - Professional SEO Content Creation\",\n    description: \"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.\",\n    keywords: [\n        \"SEO\",\n        \"content generation\",\n        \"AI writing\",\n        \"keyword optimization\",\n        \"competitor analysis\"\n    ],\n    authors: [\n        {\n            name: \"SEO Content Generator\"\n        }\n    ],\n    openGraph: {\n        title: \"SEO Content Generator - Professional SEO Content Creation\",\n        description: \"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SEO Content Generator - Professional SEO Content Creation\",\n        description: \"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    },\n    viewport: {\n        width: \"device-width\",\n        initialScale: 1\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased bg-gray-50 text-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useAuthGuard: () => (/* binding */ e3),
/* harmony export */   useSubscription: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx#useSubscription`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx#useAuthGuard`);


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/@swc","vendor-chunks/whatwg-url","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/set-cookie-parser"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fnot-found&page=%2Fnot-found&appPaths=&pagePath=..%2Fnode_modules%2Fnext%2Fdist%2Fclient%2Fcomponents%2Fnot-found-error.js&appDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
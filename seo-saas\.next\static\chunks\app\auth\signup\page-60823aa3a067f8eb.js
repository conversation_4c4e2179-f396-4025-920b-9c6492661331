(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[271],{328:function(e,t,r){Promise.resolve().then(r.bind(r,9971))},9971:function(e,t,r){"use strict";r.r(t),r.d(t,{default:function(){return h}});var s=r(7437),a=r(2265),n=r(1396),l=r.n(n),i=r(575),o=r(2782),c=r(5671),d=r(1809),m=r(4424),u=r(4238),f=r(1543),x=r(9367);function h(){let[e,t]=a.useState({firstName:"",lastName:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1}),[r,n]=a.useState(!1),[h,p]=a.useState(!1),[g,v]=a.useState(!1),[b,w]=a.useState({}),N=(e,r)=>{t(t=>({...t,[e]:r})),b[e]&&w(t=>({...t,[e]:""}))},j=()=>{let t={};return e.firstName.trim()||(t.firstName="First name is required"),e.lastName.trim()||(t.lastName="Last name is required"),e.email.trim()?/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(e.email)||(t.email="Please enter a valid email address"):t.email="Email is required",e.password?e.password.length<8?t.password="Password must be at least 8 characters":/(?=.*[a-z])(?=.*[A-Z])(?=.*\d)/.test(e.password)||(t.password="Password must contain at least one uppercase letter, one lowercase letter, and one number"):t.password="Password is required",e.confirmPassword?e.password!==e.confirmPassword&&(t.confirmPassword="Passwords do not match"):t.confirmPassword="Please confirm your password",e.agreeToTerms||(t.agreeToTerms="You must agree to the Terms of Service"),w(t),0===Object.keys(t).length},y=async e=>{if(e.preventDefault(),j()){v(!0);try{await new Promise(e=>setTimeout(e,2e3)),window.location.href="/dashboard"}catch(e){w({general:"Failed to create account. Please try again."})}finally{v(!1)}}};return(0,s.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 flex items-center justify-center p-4",children:(0,s.jsxs)("div",{className:"w-full max-w-md",children:[(0,s.jsxs)("div",{className:"text-center mb-8",children:[(0,s.jsx)(l(),{href:"/",className:"inline-block",children:(0,s.jsx)("h1",{className:"text-3xl font-bold text-gray-900 mb-2",children:"SEO Pro"})}),(0,s.jsx)("p",{className:"text-gray-600",children:"Create your account and start optimizing"})]}),(0,s.jsxs)(c.Zb,{className:"shadow-lg",children:[(0,s.jsxs)(c.Ol,{className:"space-y-1",children:[(0,s.jsx)(c.ll,{className:"text-2xl font-bold text-center",children:"Create Account"}),(0,s.jsx)(c.SZ,{className:"text-center",children:"Join thousands of users creating SEO-optimized content"})]}),(0,s.jsxs)(c.aY,{children:[(0,s.jsxs)("form",{onSubmit:y,className:"space-y-4",children:[b.general&&(0,s.jsx)("div",{className:"p-3 bg-red-50 border border-red-200 rounded-md",children:(0,s.jsx)("p",{className:"text-sm text-red-600",children:b.general})}),(0,s.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"firstName",className:"text-sm font-medium text-gray-700",children:"First Name"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(d.Z,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)(o.I,{id:"firstName",type:"text",placeholder:"John",value:e.firstName,onChange:e=>N("firstName",e.target.value),error:b.firstName,className:"pl-10",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"lastName",className:"text-sm font-medium text-gray-700",children:"Last Name"}),(0,s.jsx)(o.I,{id:"lastName",type:"text",placeholder:"Doe",value:e.lastName,onChange:e=>N("lastName",e.target.value),error:b.lastName,required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"email",className:"text-sm font-medium text-gray-700",children:"Email Address"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(m.Z,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)(o.I,{id:"email",type:"email",placeholder:"<EMAIL>",value:e.email,onChange:e=>N("email",e.target.value),error:b.email,className:"pl-10",required:!0})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"password",className:"text-sm font-medium text-gray-700",children:"Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(u.Z,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)(o.I,{id:"password",type:r?"text":"password",placeholder:"Create a strong password",value:e.password,onChange:e=>N("password",e.target.value),error:b.password,className:"pl-10 pr-10",required:!0}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>n(!r),children:r?(0,s.jsx)(f.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,s.jsx)(x.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsx)("label",{htmlFor:"confirmPassword",className:"text-sm font-medium text-gray-700",children:"Confirm Password"}),(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none",children:(0,s.jsx)(u.Z,{className:"h-5 w-5 text-gray-400"})}),(0,s.jsx)(o.I,{id:"confirmPassword",type:h?"text":"password",placeholder:"Confirm your password",value:e.confirmPassword,onChange:e=>N("confirmPassword",e.target.value),error:b.confirmPassword,className:"pl-10 pr-10",required:!0}),(0,s.jsx)("button",{type:"button",className:"absolute inset-y-0 right-0 pr-3 flex items-center",onClick:()=>p(!h),children:h?(0,s.jsx)(f.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"}):(0,s.jsx)(x.Z,{className:"h-5 w-5 text-gray-400 hover:text-gray-600"})})]})]}),(0,s.jsxs)("div",{className:"space-y-2",children:[(0,s.jsxs)("label",{className:"flex items-start space-x-2",children:[(0,s.jsx)("input",{type:"checkbox",checked:e.agreeToTerms,onChange:e=>N("agreeToTerms",e.target.checked),className:"rounded border-gray-300 text-blue-600 focus:ring-blue-500 mt-1"}),(0,s.jsxs)("span",{className:"text-sm text-gray-600",children:["I agree to the"," ",(0,s.jsx)(l(),{href:"/terms",className:"text-blue-600 hover:text-blue-500",children:"Terms of Service"})," ","and"," ",(0,s.jsx)(l(),{href:"/privacy",className:"text-blue-600 hover:text-blue-500",children:"Privacy Policy"})]})]}),b.agreeToTerms&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:b.agreeToTerms})]}),(0,s.jsx)(i.z,{type:"submit",className:"w-full",loading:g,disabled:g,children:g?"Creating Account...":"Create Account"})]}),(0,s.jsx)("div",{className:"mt-6",children:(0,s.jsxs)("div",{className:"relative",children:[(0,s.jsx)("div",{className:"absolute inset-0 flex items-center",children:(0,s.jsx)("div",{className:"w-full border-t border-gray-300"})}),(0,s.jsx)("div",{className:"relative flex justify-center text-sm",children:(0,s.jsx)("span",{className:"px-2 bg-white text-gray-500",children:"Or sign up with"})})]})}),(0,s.jsxs)("div",{className:"mt-6 grid grid-cols-2 gap-3",children:[(0,s.jsxs)(i.z,{variant:"outline",className:"w-full",children:[(0,s.jsxs)("svg",{className:"w-5 h-5 mr-2",viewBox:"0 0 24 24",children:[(0,s.jsx)("path",{fill:"currentColor",d:"M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"}),(0,s.jsx)("path",{fill:"currentColor",d:"M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"})]}),"Google"]}),(0,s.jsxs)(i.z,{variant:"outline",className:"w-full",children:[(0,s.jsx)("svg",{className:"w-5 h-5 mr-2",fill:"currentColor",viewBox:"0 0 24 24",children:(0,s.jsx)("path",{d:"M24 4.557c-.883.392-1.832.656-2.828.775 1.017-.609 1.798-1.574 2.165-2.724-.951.564-2.005.974-3.127 1.195-.897-.957-2.178-1.555-3.594-1.555-3.179 0-5.515 2.966-4.797 6.045-4.091-.205-7.719-2.165-10.148-5.144-1.29 2.213-.669 5.108 1.523 6.574-.806-.026-1.566-.247-2.229-.616-.054 2.281 1.581 4.415 3.949 4.89-.693.188-1.452.232-2.224.084.626 1.956 2.444 3.379 4.6 3.419-2.07 1.623-4.678 2.348-7.29 2.04 2.179 1.397 4.768 2.212 7.548 2.212 9.142 0 14.307-7.721 13.995-14.646.962-.695 1.797-1.562 2.457-2.549z"})}),"Twitter"]})]}),(0,s.jsx)("div",{className:"mt-6 text-center",children:(0,s.jsxs)("p",{className:"text-sm text-gray-600",children:["Already have an account?"," ",(0,s.jsx)(l(),{href:"/auth/signin",className:"font-medium text-blue-600 hover:text-blue-500",children:"Sign in"})]})})]})]})]})})}},575:function(e,t,r){"use strict";r.d(t,{z:function(){return o}});var s=r(7437),a=r(2265),n=r(6061),l=r(2169);let i=(0,n.j)("inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",{variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}}),o=a.forwardRef((e,t)=>{let{className:r,variant:a,size:n,loading:o,children:c,disabled:d,...m}=e;return(0,s.jsxs)("button",{className:(0,l.cn)(i({variant:a,size:n,className:r})),ref:t,disabled:d||o,...m,children:[o&&(0,s.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[(0,s.jsx)("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),(0,s.jsx)("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),c]})});o.displayName="Button"},5671:function(e,t,r){"use strict";r.d(t,{Ol:function(){return i},SZ:function(){return c},Zb:function(){return l},aY:function(){return d},ll:function(){return o}});var s=r(7437),a=r(2265),n=r(2169);let l=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("rounded-lg border bg-card text-card-foreground shadow-sm",r),...a})});l.displayName="Card";let i=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex flex-col space-y-1.5 p-6",r),...a})});i.displayName="CardHeader";let o=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("h3",{ref:t,className:(0,n.cn)("text-2xl font-semibold leading-none tracking-tight",r),...a})});o.displayName="CardTitle";let c=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("p",{ref:t,className:(0,n.cn)("text-sm text-muted-foreground",r),...a})});c.displayName="CardDescription";let d=a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("p-6 pt-0",r),...a})});d.displayName="CardContent",a.forwardRef((e,t)=>{let{className:r,...a}=e;return(0,s.jsx)("div",{ref:t,className:(0,n.cn)("flex items-center p-6 pt-0",r),...a})}).displayName="CardFooter"},2782:function(e,t,r){"use strict";r.d(t,{I:function(){return l}});var s=r(7437),a=r(2265),n=r(2169);let l=a.forwardRef((e,t)=>{let{className:r,type:l,label:i,error:o,helperText:c,...d}=e,m=a.useId();return(0,s.jsxs)("div",{className:"space-y-2",children:[i&&(0,s.jsxs)("label",{htmlFor:m,className:"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70",children:[i,d.required&&(0,s.jsx)("span",{className:"text-red-500 ml-1",children:"*"})]}),(0,s.jsx)("input",{id:m,type:l,className:(0,n.cn)("flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50",o&&"border-red-500 focus-visible:ring-red-500",r),ref:t,...d}),o&&(0,s.jsx)("p",{className:"text-sm text-red-600",children:o}),c&&!o&&(0,s.jsx)("p",{className:"text-sm text-muted-foreground",children:c})]})});l.displayName="Input"},2169:function(e,t,r){"use strict";r.d(t,{KG:function(){return d},cn:function(){return n},p6:function(){return i},rl:function(){return o},uf:function(){return l},vQ:function(){return c}});var s=r(7042),a=r(4769);function n(){for(var e=arguments.length,t=Array(e),r=0;r<e;r++)t[r]=arguments[r];return(0,a.m6)((0,s.W)(t))}function l(e){return e.toLocaleString()}function i(e){return("string"==typeof e?new Date(e):e).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"})}function o(e){return"".concat(e.toFixed(1),"%")}function c(e){if(navigator.clipboard)return navigator.clipboard.writeText(e);let t=document.createElement("textarea");return t.value=e,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),Promise.resolve()}function d(e,t){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"text/plain",s=new Blob([e],{type:r}),a=URL.createObjectURL(s),n=document.createElement("a");n.href=a,n.download=t,document.body.appendChild(n),n.click(),document.body.removeChild(n),URL.revokeObjectURL(a)}},1396:function(e,t,r){e.exports=r(5250)},4424:function(e,t,r){"use strict";var s=r(2265);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M21.75 6.75v10.5a2.25 2.25 0 0 1-2.25 2.25h-15a2.25 2.25 0 0 1-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0 0 19.5 4.5h-15a2.25 2.25 0 0 0-2.25 2.25m19.5 0v.243a2.25 2.25 0 0 1-1.07 1.916l-7.5 4.615a2.25 2.25 0 0 1-2.36 0L3.32 8.91a2.25 2.25 0 0 1-1.07-1.916V6.75"}))});t.Z=a},9367:function(e,t,r){"use strict";var s=r(2265);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M2.036 12.322a1.012 1.012 0 0 1 0-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.431 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178Z"}),s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15 12a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"}))});t.Z=a},1543:function(e,t,r){"use strict";var s=r(2265);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3.98 8.223A10.477 10.477 0 0 0 1.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.451 10.451 0 0 1 12 4.5c4.756 0 8.773 3.162 10.065 7.498a10.522 10.522 0 0 1-4.293 5.774M6.228 6.228 3 3m3.228 3.228 3.65 3.65m7.894 7.894L21 21m-3.228-3.228-3.65-3.65m0 0a3 3 0 1 0-4.243-4.243m4.242 4.242L9.88 9.88"}))});t.Z=a},4238:function(e,t,r){"use strict";var s=r(2265);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M16.5 10.5V6.75a4.5 4.5 0 1 0-9 0v3.75m-.75 11.25h10.5a2.25 2.25 0 0 0 2.25-2.25v-6.75a2.25 2.25 0 0 0-2.25-2.25H6.75a2.25 2.25 0 0 0-2.25 2.25v6.75a2.25 2.25 0 0 0 2.25 2.25Z"}))});t.Z=a},1809:function(e,t,r){"use strict";var s=r(2265);let a=s.forwardRef(function({title:e,titleId:t,...r},a){return s.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:a,"aria-labelledby":t},r),e?s.createElement("title",{id:t},e):null,s.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M15.75 6a3.75 3.75 0 1 1-7.5 0 3.75 3.75 0 0 1 7.5 0ZM4.501 20.118a7.5 7.5 0 0 1 14.998 0A17.933 17.933 0 0 1 12 21.75c-2.676 0-5.216-.584-7.499-1.632Z"}))});t.Z=a}},function(e){e.O(0,[696,250,971,938,744],function(){return e(e.s=328)}),_N_E=e.O()}]);
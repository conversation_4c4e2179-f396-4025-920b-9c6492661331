(()=>{var e={};e.id=474,e.ids=[474],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},1017:e=>{"use strict";e.exports=require("path")},7310:e=>{"use strict";e.exports=require("url")},3229:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>i.a,__next_app__:()=>p,originalPathname:()=>m,pages:()=>d,routeModule:()=>u,tree:()=>c});var a=r(482),s=r(9108),n=r(2563),i=r.n(n),o=r(8300),l={};for(let e in o)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>o[e]);r.d(t,l);let c=["",{children:["dashboard",{children:["analysis",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,3637)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/dashboard/analysis/page.tsx"]}]},{}]},{layout:[()=>Promise.resolve().then(r.bind(r,7639)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/dashboard/layout.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1342)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/dashboard/analysis/page.tsx"],m="/dashboard/analysis/page",p={require:r,loadChunk:()=>Promise.resolve()},u=new a.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/dashboard/analysis/page",pathname:"/dashboard/analysis",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},4558:(e,t,r)=>{Promise.resolve().then(r.bind(r,4895))},4895:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>x});var a=r(2295),s=r(3729),n=r(1565),i=r(3673),o=r(5094),l=r(6540),c=r(1638),d=r(7210),m=r(5965),p=r(454),u=r(2954);let y=[{value:"US",label:"United States"},{value:"UK",label:"United Kingdom"},{value:"CA",label:"Canada"},{value:"AU",label:"Australia"},{value:"DE",label:"Germany"},{value:"FR",label:"France"}];function x(){let[e,t]=s.useState(0),[r,x]=s.useState("url"),[h,g]=s.useState(!1),[v,f]=s.useState(null),[j,b]=s.useState(null),[w,S]=s.useState({url:"",content:"",keyword:"",location:"US",competitorUrls:[]}),[N,A]=s.useState(""),[k,C]=s.useState({}),P=()=>{let e={};if(w.keyword.trim()||(e.keyword="Target keyword is required"),"url"===r){if(w.url?.trim())try{new URL(w.url)}catch{e.url="Please enter a valid URL"}else e.url="URL is required"}else w.content?.trim()?w.content.length<100&&(e.content="Content must be at least 100 characters"):e.content="Content is required";return C(e),0===Object.keys(e).length},U=async e=>{if(e.preventDefault(),P()){g(!0),b(null),t(1);try{await new Promise(e=>setTimeout(e,3e3)),f({overallScore:73,keywordAnalysis:{score:68,primaryKeywordDensity:1.8,secondaryKeywordDensity:.9,recommendations:[{type:"keyword",priority:"high",description:"Increase primary keyword density to 2-3% for better optimization",impact:8},{type:"keyword",priority:"medium",description:"Add more LSI keywords to improve semantic relevance",impact:6}]},headingAnalysis:{score:82,totalHeadings:8,optimizedHeadings:6,hierarchy:{isValid:!0,issues:[]},recommendations:[{type:"structure",priority:"medium",description:"Add target keyword to H2 headings for better optimization",impact:5}]},contentQuality:{score:69,wordCount:847,readabilityScore:74,uniqueWords:312,recommendations:[{type:"content",priority:"high",description:"Expand content to 1200+ words to match top competitors",impact:9},{type:"readability",priority:"low",description:"Content readability is good, maintain current style",impact:3}]},competitorAnalysis:{averageWordCount:1250,averageScore:78,topPerformerScore:89,gaps:["Missing FAQ section that competitors have","Lack of internal linking structure","No schema markup implementation"],opportunities:[{type:"content",description:"Add comprehensive FAQ section to match competitor content depth",impact:"high"},{type:"technical",description:"Implement schema markup for better SERP features",impact:"medium"}]},lsiKeywords:[{keyword:"search engine optimization",relevanceScore:.92,frequency:3,category:"primary"},{keyword:"digital marketing",relevanceScore:.85,frequency:2,category:"secondary"},{keyword:"content strategy",relevanceScore:.78,frequency:1,category:"supporting"},{keyword:"keyword research",relevanceScore:.88,frequency:2,category:"secondary"},{keyword:"SERP ranking",relevanceScore:.82,frequency:1,category:"supporting"}],recommendations:[{type:"content",priority:"high",title:"Expand Content Length",description:"Increase content to 1200+ words to match competitor standards and improve ranking potential",expectedImpact:9},{type:"keyword",priority:"high",title:"Optimize Keyword Density",description:"Increase primary keyword density to 2-3% while maintaining natural flow",expectedImpact:8},{type:"structure",priority:"medium",title:"Add FAQ Section",description:"Include comprehensive FAQ section to match competitor content depth",expectedImpact:7},{type:"technical",priority:"medium",title:"Implement Schema Markup",description:"Add structured data markup for better SERP features and visibility",expectedImpact:6}]}),t(2)}catch(e){b("Failed to analyze content. Please try again."),console.error("Analysis error:",e)}finally{g(!1)}}},z=e=>{S(t=>({...t,competitorUrls:t.competitorUrls.filter((t,r)=>r!==e)}))},q=()=>{t(0),f(null),b(null),S({url:"",content:"",keyword:"",location:"US",competitorUrls:[]})};return(0,a.jsxs)("div",{className:"space-y-8",children:[(0,a.jsxs)("div",{children:[a.jsx("h1",{className:"text-3xl font-bold text-gray-900",children:"SEO Analysis"}),a.jsx("p",{className:"text-gray-600 mt-2",children:"Analyze your content's SEO performance and get actionable recommendations"})]}),a.jsx(d.Kl,{steps:["Configure","Analyze","Results"],currentStep:e}),j&&a.jsx(i.Zb,{className:"border-red-200 bg-red-50",children:a.jsx(i.aY,{className:"p-4",children:(0,a.jsxs)("div",{className:"flex items-center space-x-2",children:[a.jsx("div",{className:"text-red-600",children:"⚠️"}),a.jsx("p",{className:"text-red-800",children:j}),a.jsx(o.z,{variant:"outline",size:"sm",onClick:q,children:"Try Again"})]})})}),0===e&&(0,a.jsxs)(i.Zb,{className:"w-full max-w-4xl mx-auto",children:[(0,a.jsxs)(i.Ol,{children:[a.jsx(i.ll,{children:"Configure SEO Analysis"}),a.jsx(i.SZ,{children:"Enter your content details and target keywords for comprehensive analysis"})]}),a.jsx(i.aY,{children:(0,a.jsxs)("form",{onSubmit:U,className:"space-y-6",children:[(0,a.jsxs)("div",{className:"space-y-4",children:[a.jsx("label",{className:"text-sm font-medium",children:"Analysis Type"}),(0,a.jsxs)("div",{className:"grid grid-cols-2 gap-4",children:[a.jsx(o.z,{type:"button",variant:"url"===r?"default":"outline",onClick:()=>x("url"),className:"h-auto p-4 justify-start",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(m.Z,{className:"h-5 w-5 mt-1"}),(0,a.jsxs)("div",{className:"text-left",children:[a.jsx("div",{className:"font-medium",children:"Analyze URL"}),a.jsx("div",{className:"text-sm opacity-70",children:"Analyze an existing webpage"})]})]})}),a.jsx(o.z,{type:"button",variant:"content"===r?"default":"outline",onClick:()=>x("content"),className:"h-auto p-4 justify-start",children:(0,a.jsxs)("div",{className:"flex items-start space-x-3",children:[a.jsx(p.Z,{className:"h-5 w-5 mt-1"}),(0,a.jsxs)("div",{className:"text-left",children:[a.jsx("div",{className:"font-medium",children:"Analyze Content"}),a.jsx("div",{className:"text-sm opacity-70",children:"Paste content directly"})]})]})})]})]}),"url"===r?a.jsx(l.I,{label:"Website URL",placeholder:"https://example.com/page",value:w.url||"",onChange:e=>S(t=>({...t,url:e.target.value})),error:k.url,required:!0,helperText:"Enter the URL you want to analyze"}):(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("label",{className:"text-sm font-medium",children:"Content to Analyze"}),a.jsx("textarea",{placeholder:"Paste your content here...",value:w.content||"",onChange:e=>S(t=>({...t,content:e.target.value})),className:"w-full h-40 p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500",required:!0}),k.content&&a.jsx("p",{className:"text-sm text-red-600",children:k.content}),a.jsx("p",{className:"text-sm text-gray-500",children:"Minimum 100 characters required"})]}),(0,a.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-6",children:[a.jsx(l.I,{label:"Target Keyword",placeholder:"e.g., SEO services",value:w.keyword,onChange:e=>S(t=>({...t,keyword:e.target.value})),error:k.keyword,required:!0,helperText:"The main keyword you want to rank for"}),a.jsx(c.P,{label:"Target Location",options:y,value:w.location,onChange:e=>S(t=>({...t,location:e})),required:!0})]}),(0,a.jsxs)("div",{className:"space-y-4",children:[(0,a.jsxs)("label",{className:"text-sm font-medium",children:["Competitor URLs (Optional)",a.jsx("span",{className:"text-gray-500 ml-1",children:"- Up to 5 URLs for comparison"})]}),(0,a.jsxs)("div",{className:"flex space-x-2",children:[a.jsx(l.I,{placeholder:"https://competitor.com",value:N,onChange:e=>A(e.target.value),error:k.competitorUrl,className:"flex-1"}),a.jsx(o.z,{type:"button",variant:"outline",onClick:()=>{if(N.trim()&&w.competitorUrls.length<5)try{new URL(N),S(e=>({...e,competitorUrls:[...e.competitorUrls,N.trim()]})),A("")}catch{C(e=>({...e,competitorUrl:"Please enter a valid URL"}))}},disabled:!N.trim()||w.competitorUrls.length>=5,children:"Add"})]}),w.competitorUrls.length>0&&(0,a.jsxs)("div",{className:"space-y-2",children:[a.jsx("p",{className:"text-sm text-gray-600",children:"Competitor URLs to analyze:"}),a.jsx("div",{className:"space-y-2",children:w.competitorUrls.map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between bg-gray-50 p-2 rounded",children:[a.jsx("span",{className:"text-sm truncate flex-1",children:e}),a.jsx(o.z,{type:"button",variant:"ghost",size:"sm",onClick:()=>z(t),className:"ml-2",children:"Remove"})]},t))})]})]}),a.jsx("div",{className:"flex justify-end",children:a.jsx(o.z,{type:"submit",loading:h,disabled:h,className:"min-w-[120px]",children:h?"Analyzing...":"Start Analysis"})})]})})]}),h&&a.jsx(i.Zb,{children:a.jsx(i.aY,{className:"p-8",children:(0,a.jsxs)("div",{className:"flex flex-col items-center space-y-4",children:[a.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-b-2 border-primary"}),(0,a.jsxs)("div",{className:"text-center",children:[a.jsx("h3",{className:"text-lg font-medium",children:"Analyzing Your Content..."}),a.jsx("p",{className:"text-gray-600",children:"Running comprehensive SEO analysis and competitor comparison"})]})]})})}),v&&e>=2&&(0,a.jsxs)(a.Fragment,{children:[a.jsx(n.D,{results:v}),(0,a.jsxs)("div",{className:"flex justify-center space-x-4",children:[a.jsx(o.z,{variant:"outline",onClick:q,children:"Analyze New Content"}),(0,a.jsxs)(o.z,{children:[a.jsx(u.Z,{className:"h-4 w-4 mr-2"}),"Generate Optimized Content"]})]})]})]})}},3637:(e,t,r)=>{"use strict";r.r(t),r.d(t,{$$typeof:()=>n,__esModule:()=>s,default:()=>i});let a=(0,r(6843).createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/dashboard/analysis/page.tsx`),{__esModule:s,$$typeof:n}=a,i=a.default}};var t=require("../../../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),a=t.X(0,[638,312,337,439,783,21,395],()=>r(3229));module.exports=a})();
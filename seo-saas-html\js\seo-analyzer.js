// SEO Analysis Engine for SEO Pro
// This file implements the 6 core SEO analysis modules

class SEOAnalyzer {
    constructor() {
        this.analysisModules = [
            'keyword_density',
            'heading_structure', 
            'content_quality',
            'lsi_keywords',
            'competitor_analysis',
            'eeat_compliance'
        ];
        this.results = {};
    }

    // Main analysis method
    async analyzeContent(content, targetKeywords, options = {}) {
        try {
            const {
                url = '',
                competitorUrls = [],
                industry = 'general'
            } = options;

            this.results = {
                url,
                targetKeywords,
                overallScore: 0,
                moduleScores: {},
                recommendations: [],
                analysisData: {},
                timestamp: new Date().toISOString()
            };

            // Run all analysis modules
            await this.runKeywordDensityAnalysis(content, targetKeywords);
            await this.runHeadingStructureAnalysis(content, targetKeywords);
            await this.runContentQualityAnalysis(content);
            await this.runLSIKeywordsAnalysis(content, targetKeywords);
            await this.runCompetitorAnalysis(targetKeywords, competitorUrls);
            await this.runEEATComplianceAnalysis(content, industry);

            // Calculate overall score
            this.calculateOverallScore();

            return {
                success: true,
                results: this.results
            };
        } catch (error) {
            console.error('SEO analysis error:', error);
            return {
                success: false,
                error: error.message
            };
        }
    }

    // Module 1: Keyword Density Analysis
    async runKeywordDensityAnalysis(content, targetKeywords) {
        const words = this.extractWords(content);
        const totalWords = words.length;
        const densityData = {};
        let score = 100;
        const recommendations = [];

        for (const keyword of targetKeywords) {
            const keywordWords = keyword.toLowerCase().split(' ');
            let count = 0;

            // Count exact matches and partial matches
            if (keywordWords.length === 1) {
                count = words.filter(word => word === keywordWords[0]).length;
            } else {
                // Multi-word keyword matching
                const contentLower = content.toLowerCase();
                const regex = new RegExp(`\\b${keyword.toLowerCase()}\\b`, 'gi');
                const matches = contentLower.match(regex);
                count = matches ? matches.length : 0;
            }

            const density = totalWords > 0 ? (count / totalWords) * 100 : 0;
            densityData[keyword] = {
                count,
                density: parseFloat(density.toFixed(2)),
                optimal: density >= CONFIG.SEO.KEYWORD_DENSITY.OPTIMAL_MIN && 
                        density <= CONFIG.SEO.KEYWORD_DENSITY.OPTIMAL_MAX
            };

            // Score calculation and recommendations
            if (density < CONFIG.SEO.KEYWORD_DENSITY.OPTIMAL_MIN) {
                score -= 15;
                recommendations.push({
                    type: 'keyword_density',
                    priority: 'medium',
                    message: `Increase density of "${keyword}" (current: ${density.toFixed(2)}%, optimal: ${CONFIG.SEO.KEYWORD_DENSITY.OPTIMAL_MIN}-${CONFIG.SEO.KEYWORD_DENSITY.OPTIMAL_MAX}%)`
                });
            } else if (density > CONFIG.SEO.KEYWORD_DENSITY.WARNING_THRESHOLD) {
                score -= 25;
                recommendations.push({
                    type: 'keyword_density',
                    priority: 'high',
                    message: `Reduce density of "${keyword}" to avoid keyword stuffing (current: ${density.toFixed(2)}%, max recommended: ${CONFIG.SEO.KEYWORD_DENSITY.OPTIMAL_MAX}%)`
                });
            }
        }

        this.results.moduleScores.keywordDensity = Math.max(0, score);
        this.results.analysisData.keywordDensity = densityData;
        this.results.recommendations.push(...recommendations);
    }

    // Module 2: Heading Structure Analysis
    async runHeadingStructureAnalysis(content, targetKeywords) {
        const headings = this.extractHeadings(content);
        let score = 100;
        const recommendations = [];
        const structureData = {
            headings,
            hasH1: false,
            h1Count: 0,
            keywordInHeadings: 0,
            hierarchyIssues: []
        };

        // Check H1 presence and count
        const h1s = headings.filter(h => h.level === 1);
        structureData.h1Count = h1s.length;
        structureData.hasH1 = h1s.length > 0;

        if (h1s.length === 0) {
            score -= 30;
            recommendations.push({
                type: 'heading_structure',
                priority: 'high',
                message: 'Add an H1 heading to your content'
            });
        } else if (h1s.length > 1) {
            score -= 15;
            recommendations.push({
                type: 'heading_structure',
                priority: 'medium',
                message: 'Use only one H1 heading per page'
            });
        }

        // Check keyword usage in headings
        let keywordInHeadings = 0;
        for (const keyword of targetKeywords) {
            const keywordInH1 = h1s.some(h => h.text.toLowerCase().includes(keyword.toLowerCase()));
            const keywordInOtherHeadings = headings.some(h => 
                h.level > 1 && h.text.toLowerCase().includes(keyword.toLowerCase())
            );
            
            if (keywordInH1 || keywordInOtherHeadings) {
                keywordInHeadings++;
            }
        }

        structureData.keywordInHeadings = keywordInHeadings;

        if (keywordInHeadings === 0) {
            score -= 20;
            recommendations.push({
                type: 'heading_structure',
                priority: 'high',
                message: 'Include target keywords in your headings'
            });
        }

        // Check heading hierarchy
        let previousLevel = 0;
        for (const heading of headings) {
            if (heading.level > previousLevel + 1 && previousLevel > 0) {
                structureData.hierarchyIssues.push(`Skipped heading level: H${previousLevel} to H${heading.level}`);
            }
            previousLevel = heading.level;
        }

        if (structureData.hierarchyIssues.length > 0) {
            score -= 10;
            recommendations.push({
                type: 'heading_structure',
                priority: 'low',
                message: 'Maintain proper heading hierarchy (don\'t skip levels)'
            });
        }

        this.results.moduleScores.headingStructure = Math.max(0, score);
        this.results.analysisData.headingStructure = structureData;
        this.results.recommendations.push(...recommendations);
    }

    // Module 3: Content Quality Analysis
    async runContentQualityAnalysis(content) {
        const words = this.extractWords(content);
        const sentences = this.extractSentences(content);
        const paragraphs = this.extractParagraphs(content);
        
        let score = 100;
        const recommendations = [];
        const qualityData = {
            wordCount: words.length,
            sentenceCount: sentences.length,
            paragraphCount: paragraphs.length,
            avgWordsPerSentence: sentences.length > 0 ? words.length / sentences.length : 0,
            avgSentencesPerParagraph: paragraphs.length > 0 ? sentences.length / paragraphs.length : 0,
            readabilityScore: this.calculateReadabilityScore(content),
            uniqueWords: new Set(words).size,
            lexicalDiversity: 0
        };

        qualityData.lexicalDiversity = qualityData.uniqueWords / qualityData.wordCount;

        // Word count analysis
        if (qualityData.wordCount < CONFIG.SEO.CONTENT_QUALITY.MIN_WORD_COUNT) {
            score -= 30;
            recommendations.push({
                type: 'content_quality',
                priority: 'high',
                message: `Increase content length (current: ${qualityData.wordCount} words, minimum: ${CONFIG.SEO.CONTENT_QUALITY.MIN_WORD_COUNT})`
            });
        } else if (qualityData.wordCount < CONFIG.SEO.CONTENT_QUALITY.OPTIMAL_WORD_COUNT) {
            score -= 10;
            recommendations.push({
                type: 'content_quality',
                priority: 'medium',
                message: `Consider expanding content for better SEO (current: ${qualityData.wordCount} words, optimal: ${CONFIG.SEO.CONTENT_QUALITY.OPTIMAL_WORD_COUNT}+)`
            });
        }

        // Readability analysis
        if (qualityData.readabilityScore < CONFIG.SEO.CONTENT_QUALITY.READABILITY_TARGETS.FLESCH_KINCAID) {
            score -= 15;
            recommendations.push({
                type: 'content_quality',
                priority: 'medium',
                message: 'Improve readability by using shorter sentences and simpler words'
            });
        }

        // Sentence length analysis
        if (qualityData.avgWordsPerSentence > 25) {
            score -= 10;
            recommendations.push({
                type: 'content_quality',
                priority: 'low',
                message: 'Consider breaking up long sentences for better readability'
            });
        }

        // Lexical diversity
        if (qualityData.lexicalDiversity < 0.4) {
            score -= 10;
            recommendations.push({
                type: 'content_quality',
                priority: 'low',
                message: 'Use more varied vocabulary to improve content quality'
            });
        }

        this.results.moduleScores.contentQuality = Math.max(0, score);
        this.results.analysisData.contentQuality = qualityData;
        this.results.recommendations.push(...recommendations);
    }

    // Module 4: LSI Keywords Analysis
    async runLSIKeywordsAnalysis(content, targetKeywords) {
        let score = 100;
        const recommendations = [];
        const lsiData = {
            suggestedLSI: [],
            foundLSI: [],
            coverage: 0
        };

        try {
            // Generate LSI keywords for each target keyword
            for (const keyword of targetKeywords) {
                const lsiKeywords = await this.generateLSIKeywords(keyword);
                lsiData.suggestedLSI.push(...lsiKeywords);
                
                // Check which LSI keywords are present in content
                const foundInContent = lsiKeywords.filter(lsi => 
                    content.toLowerCase().includes(lsi.toLowerCase())
                );
                lsiData.foundLSI.push(...foundInContent);
            }

            // Remove duplicates
            lsiData.suggestedLSI = [...new Set(lsiData.suggestedLSI)];
            lsiData.foundLSI = [...new Set(lsiData.foundLSI)];

            // Calculate coverage
            lsiData.coverage = lsiData.suggestedLSI.length > 0 ? 
                (lsiData.foundLSI.length / lsiData.suggestedLSI.length) * 100 : 0;

            // Score based on LSI coverage
            if (lsiData.coverage < 30) {
                score -= 25;
                recommendations.push({
                    type: 'lsi_keywords',
                    priority: 'high',
                    message: 'Include more related keywords and synonyms to improve semantic relevance'
                });
            } else if (lsiData.coverage < 50) {
                score -= 15;
                recommendations.push({
                    type: 'lsi_keywords',
                    priority: 'medium',
                    message: 'Consider adding more related terms to strengthen topical relevance'
                });
            }

            // Provide specific LSI keyword suggestions
            const missingLSI = lsiData.suggestedLSI.filter(lsi => !lsiData.foundLSI.includes(lsi));
            if (missingLSI.length > 0) {
                recommendations.push({
                    type: 'lsi_keywords',
                    priority: 'low',
                    message: `Consider including these related terms: ${missingLSI.slice(0, 5).join(', ')}`
                });
            }

        } catch (error) {
            console.error('LSI analysis error:', error);
            score = 70; // Default score if LSI analysis fails
        }

        this.results.moduleScores.lsiKeywords = Math.max(0, score);
        this.results.analysisData.lsiKeywords = lsiData;
        this.results.recommendations.push(...recommendations);
    }

    // Module 5: Competitor Analysis
    async runCompetitorAnalysis(targetKeywords, competitorUrls = []) {
        let score = 100;
        const recommendations = [];
        const competitorData = {
            competitors: [],
            averageContentLength: 0,
            commonKeywords: [],
            contentGaps: []
        };

        try {
            if (competitorUrls.length === 0) {
                // Get competitors from search results
                for (const keyword of targetKeywords.slice(0, 2)) { // Limit to 2 keywords to avoid rate limits
                    const searchResult = await apiClient.searchGoogle(keyword, { num: 5 });
                    if (searchResult.success && searchResult.data.organic) {
                        const competitors = searchResult.data.organic.slice(0, 3).map(result => ({
                            url: result.link,
                            title: result.title,
                            snippet: result.snippet,
                            position: result.position
                        }));
                        competitorData.competitors.push(...competitors);
                    }
                }
            }

            // Analyze competitor content (simplified)
            if (competitorData.competitors.length > 0) {
                const avgLength = competitorData.competitors.reduce((sum, comp) => 
                    sum + (comp.snippet ? comp.snippet.length * 10 : 1000), 0) / competitorData.competitors.length;
                competitorData.averageContentLength = Math.round(avgLength);

                recommendations.push({
                    type: 'competitor_analysis',
                    priority: 'medium',
                    message: `Top competitors average ~${competitorData.averageContentLength} words. Consider matching or exceeding this length.`
                });
            } else {
                score -= 20;
                recommendations.push({
                    type: 'competitor_analysis',
                    priority: 'low',
                    message: 'Unable to analyze competitors. Consider researching top-ranking pages manually.'
                });
            }

        } catch (error) {
            console.error('Competitor analysis error:', error);
            score = 70;
        }

        this.results.moduleScores.competitorAnalysis = Math.max(0, score);
        this.results.analysisData.competitorAnalysis = competitorData;
        this.results.recommendations.push(...recommendations);
    }

    // Module 6: E-E-A-T Compliance Analysis
    async runEEATComplianceAnalysis(content, industry) {
        let score = 100;
        const recommendations = [];
        const eeatData = {
            experienceIndicators: 0,
            expertiseIndicators: 0,
            authoritativenessIndicators: 0,
            trustworthinessIndicators: 0,
            overallEEAT: 0
        };

        // Experience indicators
        const experienceKeywords = ['experience', 'tested', 'used', 'tried', 'personally', 'hands-on', 'real-world'];
        eeatData.experienceIndicators = this.countKeywordMatches(content, experienceKeywords);

        // Expertise indicators
        const expertiseKeywords = ['research', 'study', 'analysis', 'data', 'statistics', 'expert', 'professional'];
        eeatData.expertiseIndicators = this.countKeywordMatches(content, expertiseKeywords);

        // Authoritativeness indicators
        const authorityKeywords = ['source', 'reference', 'citation', 'published', 'journal', 'official'];
        eeatData.authoritativenessIndicators = this.countKeywordMatches(content, authorityKeywords);

        // Trustworthiness indicators
        const trustKeywords = ['accurate', 'verified', 'fact-checked', 'reliable', 'transparent', 'honest'];
        eeatData.trustworthinessIndicators = this.countKeywordMatches(content, trustKeywords);

        // Calculate overall E-E-A-T score
        const totalIndicators = eeatData.experienceIndicators + eeatData.expertiseIndicators + 
                               eeatData.authoritativenessIndicators + eeatData.trustworthinessIndicators;
        eeatData.overallEEAT = Math.min(100, totalIndicators * 5);

        // Recommendations based on E-E-A-T
        if (eeatData.experienceIndicators < 2) {
            score -= 15;
            recommendations.push({
                type: 'eeat_compliance',
                priority: 'medium',
                message: 'Add more personal experience and real-world examples to demonstrate first-hand knowledge'
            });
        }

        if (eeatData.expertiseIndicators < 3) {
            score -= 15;
            recommendations.push({
                type: 'eeat_compliance',
                priority: 'medium',
                message: 'Include more research, data, and expert insights to demonstrate expertise'
            });
        }

        if (eeatData.authoritativenessIndicators < 2) {
            score -= 20;
            recommendations.push({
                type: 'eeat_compliance',
                priority: 'high',
                message: 'Add authoritative sources, citations, and references to boost credibility'
            });
        }

        if (eeatData.trustworthinessIndicators < 2) {
            score -= 15;
            recommendations.push({
                type: 'eeat_compliance',
                priority: 'medium',
                message: 'Emphasize accuracy, transparency, and fact-checking to build trust'
            });
        }

        // Industry-specific recommendations
        if (industry === 'healthcare' || industry === 'finance') {
            recommendations.push({
                type: 'eeat_compliance',
                priority: 'high',
                message: `For ${industry} content, ensure all claims are backed by authoritative medical/financial sources`
            });
        }

        this.results.moduleScores.eeatCompliance = Math.max(0, score);
        this.results.analysisData.eeatCompliance = eeatData;
        this.results.recommendations.push(...recommendations);
    }

    // Calculate overall SEO score
    calculateOverallScore() {
        const scores = this.results.moduleScores;
        const weights = {
            keywordDensity: 0.20,
            headingStructure: 0.15,
            contentQuality: 0.25,
            lsiKeywords: 0.15,
            competitorAnalysis: 0.10,
            eeatCompliance: 0.15
        };

        let weightedSum = 0;
        let totalWeight = 0;

        Object.keys(weights).forEach(module => {
            if (scores[module] !== undefined) {
                weightedSum += scores[module] * weights[module];
                totalWeight += weights[module];
            }
        });

        this.results.overallScore = totalWeight > 0 ? Math.round(weightedSum / totalWeight) : 0;
    }

    // Utility methods
    extractWords(content) {
        return content.toLowerCase()
            .replace(/[^\w\s]/g, ' ')
            .split(/\s+/)
            .filter(word => word.length > 0);
    }

    extractSentences(content) {
        return content.split(/[.!?]+/).filter(sentence => sentence.trim().length > 0);
    }

    extractParagraphs(content) {
        return content.split(/\n\s*\n/).filter(para => para.trim().length > 0);
    }

    extractHeadings(content) {
        const headings = [];
        const headingRegex = /<h([1-6])[^>]*>(.*?)<\/h[1-6]>/gi;
        let match;

        while ((match = headingRegex.exec(content)) !== null) {
            headings.push({
                level: parseInt(match[1]),
                text: match[2].replace(/<[^>]*>/g, '').trim()
            });
        }

        return headings;
    }

    calculateReadabilityScore(content) {
        const words = this.extractWords(content);
        const sentences = this.extractSentences(content);
        
        if (sentences.length === 0 || words.length === 0) return 0;

        const avgWordsPerSentence = words.length / sentences.length;
        const avgSyllablesPerWord = this.calculateAvgSyllables(words);

        // Simplified Flesch Reading Ease formula
        return Math.max(0, 206.835 - (1.015 * avgWordsPerSentence) - (84.6 * avgSyllablesPerWord));
    }

    calculateAvgSyllables(words) {
        const totalSyllables = words.reduce((sum, word) => sum + this.countSyllables(word), 0);
        return words.length > 0 ? totalSyllables / words.length : 0;
    }

    countSyllables(word) {
        word = word.toLowerCase();
        if (word.length <= 3) return 1;
        
        const vowels = 'aeiouy';
        let syllables = 0;
        let previousWasVowel = false;

        for (let i = 0; i < word.length; i++) {
            const isVowel = vowels.includes(word[i]);
            if (isVowel && !previousWasVowel) {
                syllables++;
            }
            previousWasVowel = isVowel;
        }

        if (word.endsWith('e')) syllables--;
        return Math.max(1, syllables);
    }

    async generateLSIKeywords(keyword) {
        // Simplified LSI keyword generation
        const lsiKeywords = [];
        const baseWords = keyword.split(' ');
        
        // Add common related terms
        const relatedTerms = {
            'seo': ['optimization', 'ranking', 'search engine', 'organic', 'visibility'],
            'content': ['writing', 'creation', 'marketing', 'strategy', 'quality'],
            'marketing': ['digital', 'online', 'strategy', 'campaign', 'promotion'],
            'business': ['company', 'enterprise', 'organization', 'corporate', 'commercial']
        };

        baseWords.forEach(word => {
            if (relatedTerms[word.toLowerCase()]) {
                lsiKeywords.push(...relatedTerms[word.toLowerCase()]);
            }
        });

        // Add generic related terms
        lsiKeywords.push(
            `${keyword} guide`,
            `${keyword} tips`,
            `${keyword} best practices`,
            `${keyword} strategy`,
            `${keyword} tools`
        );

        return lsiKeywords.slice(0, 10); // Limit to 10 LSI keywords
    }

    countKeywordMatches(content, keywords) {
        const contentLower = content.toLowerCase();
        return keywords.reduce((count, keyword) => {
            const regex = new RegExp(`\\b${keyword}\\b`, 'gi');
            const matches = contentLower.match(regex);
            return count + (matches ? matches.length : 0);
        }, 0);
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SEOAnalyzer;
} else {
    window.SEOAnalyzer = SEOAnalyzer;
}

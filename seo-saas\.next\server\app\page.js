/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "./action-async-storage.external":
/*!****************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external" ***!
  \****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external");

/***/ }),

/***/ "./request-async-storage.external":
/*!*****************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external" ***!
  \*****************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external");

/***/ }),

/***/ "./static-generation-async-storage.external":
/*!***************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external" ***!
  \***************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!**********************************************************************************!*\
  !*** external "next/dist\\client\\components\\action-async-storage.external.js" ***!
  \**********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!***********************************************************************************!*\
  !*** external "next/dist\\client\\components\\request-async-storage.external.js" ***!
  \***********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!*********************************************************************************************!*\
  !*** external "next/dist\\client\\components\\static-generation-async-storage.external.js" ***!
  \*********************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist\\client\\components\\static-generation-async-storage.external.js");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "net":
/*!**********************!*\
  !*** external "net" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("net");

/***/ }),

/***/ "path":
/*!***********************!*\
  !*** external "path" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("path");

/***/ }),

/***/ "punycode":
/*!***************************!*\
  !*** external "punycode" ***!
  \***************************/
/***/ ((module) => {

"use strict";
module.exports = require("punycode");

/***/ }),

/***/ "stream":
/*!*************************!*\
  !*** external "stream" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("stream");

/***/ }),

/***/ "tls":
/*!**********************!*\
  !*** external "tls" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("tls");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "?32c4":
/*!****************************!*\
  !*** bufferutil (ignored) ***!
  \****************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "?66e9":
/*!********************************!*\
  !*** utf-8-validate (ignored) ***!
  \********************************/
/***/ (() => {

/* (ignored) */

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?9d97\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/page.tsx */ \"(rsc)/./src/app/page.tsx\")), \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\"],\n          metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/app/layout.tsx */ \"(rsc)/./src/app/layout.tsx\")), \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        metadata: {\n    icon: [(async (props) => (await Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! next-metadata-image-loader?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ */ \"(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\"))).default(props))],\n    apple: [],\n    openGraph: [],\n    twitter: [],\n    manifest: undefined\n  }\n      }\n      ]\n      }.children;\nconst pages = [\"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js */ \"(ssr)/./node_modules/next/dist/client/components/static-generation-searchparams-bailout-provider.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Capp-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cerror-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Clayout-router.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cnot-found-boundary.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Crender-from-template-context.js&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Ccomponents%5Cstatic-generation-searchparams-bailout-provider.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!":
/*!*********************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RiUzQSU1Q0NsYXVkZS1Db2RlLVNldHVwJTVDU0VPJTIwU0FBUyUyMEFQUCU1Q3Nlby1zYWFzJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2Rpc3QlNUNjbGllbnQlNUNsaW5rLmpzJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1zYWFzLz9kZmY2Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiICovIFwiRjpcXFxcQ2xhdWRlLUNvZGUtU2V0dXBcXFxcU0VPIFNBQVMgQVBQXFxcXHNlby1zYWFzXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIikiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cdist%5Cclient%5Clink.js&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp%5Cglobals.css&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Ccontexts%5Cauth-context.tsx&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp%5Cglobals.css&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Ccontexts%5Cauth-context.tsx&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./src/contexts/auth-context.tsx */ \"(ssr)/./src/contexts/auth-context.tsx\"))//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9RiUzQSU1Q0NsYXVkZS1Db2RlLVNldHVwJTVDU0VPJTIwU0FBUyUyMEFQUCU1Q3Nlby1zYWFzJTVDbm9kZV9tb2R1bGVzJTVDbmV4dCU1Q2ZvbnQlNUNnb29nbGUlNUN0YXJnZXQuY3NzJTNGJTdCJTIycGF0aCUyMiUzQSUyMnNyYyU1QyU1Q2FwcCU1QyU1Q2xheW91dC50c3glMjIlMkMlMjJpbXBvcnQlMjIlM0ElMjJJbnRlciUyMiUyQyUyMmFyZ3VtZW50cyUyMiUzQSU1QiU3QiUyMnN1YnNldHMlMjIlM0ElNUIlMjJsYXRpbiUyMiU1RCUyQyUyMnZhcmlhYmxlJTIyJTNBJTIyLS1mb250LWludGVyJTIyJTdEJTVEJTJDJTIydmFyaWFibGVOYW1lJTIyJTNBJTIyaW50ZXIlMjIlN0QmbW9kdWxlcz1GJTNBJTVDQ2xhdWRlLUNvZGUtU2V0dXAlNUNTRU8lMjBTQUFTJTIwQVBQJTVDc2VvLXNhYXMlNUNzcmMlNUNhcHAlNUNnbG9iYWxzLmNzcyZtb2R1bGVzPUYlM0ElNUNDbGF1ZGUtQ29kZS1TZXR1cCU1Q1NFTyUyMFNBQVMlMjBBUFAlNUNzZW8tc2FhcyU1Q3NyYyU1Q2NvbnRleHRzJTVDYXV0aC1jb250ZXh0LnRzeCZzZXJ2ZXI9dHJ1ZSEiLCJtYXBwaW5ncyI6IkFBQUEiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tc2Fhcy8/ZjJiNyJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkY6XFxcXENsYXVkZS1Db2RlLVNldHVwXFxcXFNFTyBTQUFTIEFQUFxcXFxzZW8tc2Fhc1xcXFxzcmNcXFxcY29udGV4dHNcXFxcYXV0aC1jb250ZXh0LnRzeFwiKSJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Cnode_modules%5Cnext%5Cfont%5Cgoogle%5Ctarget.css%3F%7B%22path%22%3A%22src%5C%5Capp%5C%5Clayout.tsx%22%2C%22import%22%3A%22Inter%22%2C%22arguments%22%3A%5B%7B%22subsets%22%3A%5B%22latin%22%5D%2C%22variable%22%3A%22--font-inter%22%7D%5D%2C%22variableName%22%3A%22inter%22%7D&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp%5Cglobals.css&modules=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Ccontexts%5Cauth-context.tsx&server=true!\n");

/***/ }),

/***/ "(ssr)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   useAuthGuard: () => (/* binding */ useAuthGuard),\n/* harmony export */   useSubscription: () => (/* binding */ useSubscription)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_supabase__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/supabase */ \"(ssr)/./src/lib/supabase.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,useSubscription,useAuthGuard auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [session, setSession] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [profile, setProfile] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [subscription, setSubscription] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const supabase = (0,_lib_supabase__WEBPACK_IMPORTED_MODULE_2__.createSupabaseComponentClient)();\n    // Fetch user profile\n    const fetchProfile = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", userId).single();\n            if (error) {\n                console.error(\"Error fetching profile:\", error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching profile:\", error);\n            return null;\n        }\n    };\n    // Fetch user subscription\n    const fetchSubscription = async (userId)=>{\n        try {\n            const { data, error } = await supabase.from(\"user_subscriptions\").select(\"*\").eq(\"user_id\", userId).single();\n            if (error && error.code !== \"PGRST116\") {\n                console.error(\"Error fetching subscription:\", error);\n                return null;\n            }\n            return data;\n        } catch (error) {\n            console.error(\"Error fetching subscription:\", error);\n            return null;\n        }\n    };\n    // Initialize auth state\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const initializeAuth = async ()=>{\n            try {\n                // Get initial session\n                const { data: { session }, error } = await supabase.auth.getSession();\n                if (error) {\n                    console.error(\"Error getting session:\", error);\n                    setLoading(false);\n                    return;\n                }\n                if (session?.user) {\n                    setSession(session);\n                    setUser(session.user);\n                    // Fetch profile and subscription in parallel\n                    const [profileData, subscriptionData] = await Promise.all([\n                        fetchProfile(session.user.id),\n                        fetchSubscription(session.user.id)\n                    ]);\n                    setProfile(profileData);\n                    setSubscription(subscriptionData);\n                }\n            } catch (error) {\n                console.error(\"Error initializing auth:\", error);\n            } finally{\n                setLoading(false);\n            }\n        };\n        initializeAuth();\n        // Listen for auth changes\n        const { data: { subscription: authSubscription } } = supabase.auth.onAuthStateChange(async (event, session)=>{\n            console.log(\"Auth state changed:\", event, session?.user?.id);\n            setSession(session);\n            setUser(session?.user ?? null);\n            if (session?.user) {\n                // Fetch profile and subscription for new session\n                const [profileData, subscriptionData] = await Promise.all([\n                    fetchProfile(session.user.id),\n                    fetchSubscription(session.user.id)\n                ]);\n                setProfile(profileData);\n                setSubscription(subscriptionData);\n            } else {\n                // Clear profile and subscription on sign out\n                setProfile(null);\n                setSubscription(null);\n            }\n            setLoading(false);\n        });\n        return ()=>{\n            authSubscription.unsubscribe();\n        };\n    }, []);\n    // Sign in\n    const signIn = async (email, password)=>{\n        const { error } = await supabase.auth.signInWithPassword({\n            email,\n            password\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    };\n    // Sign up\n    const signUp = async (email, password, fullName)=>{\n        const { error } = await supabase.auth.signUp({\n            email,\n            password,\n            options: {\n                data: {\n                    full_name: fullName\n                }\n            }\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    };\n    // Sign out\n    const signOut = async ()=>{\n        const { error } = await supabase.auth.signOut();\n        if (error) {\n            throw new Error(error.message);\n        }\n    };\n    // Reset password\n    const resetPassword = async (email)=>{\n        const { error } = await supabase.auth.resetPasswordForEmail(email, {\n            redirectTo: `${window.location.origin}/auth/reset-password`\n        });\n        if (error) {\n            throw new Error(error.message);\n        }\n    };\n    // Update profile\n    const updateProfile = async (updates)=>{\n        if (!user) {\n            throw new Error(\"No user logged in\");\n        }\n        const { data, error } = await supabase.from(\"profiles\").update({\n            ...updates,\n            updated_at: new Date().toISOString()\n        }).eq(\"id\", user.id).select().single();\n        if (error) {\n            throw new Error(error.message);\n        }\n        setProfile(data);\n    };\n    // Refresh profile\n    const refreshProfile = async ()=>{\n        if (!user) return;\n        const profileData = await fetchProfile(user.id);\n        setProfile(profileData);\n    };\n    // Refresh subscription\n    const refreshSubscription = async ()=>{\n        if (!user) return;\n        const subscriptionData = await fetchSubscription(user.id);\n        setSubscription(subscriptionData);\n    };\n    const value = {\n        user,\n        session,\n        profile,\n        subscription,\n        loading,\n        signIn,\n        signUp,\n        signOut,\n        resetPassword,\n        updateProfile,\n        refreshProfile,\n        refreshSubscription\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\contexts\\\\auth-context.tsx\",\n        lineNumber: 247,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Helper hook for checking subscription status\nfunction useSubscription() {\n    const { subscription } = useAuth();\n    const hasActiveSubscription = subscription?.status === \"active\";\n    const isOnTrial = subscription?.status === \"trialing\";\n    const isPastDue = subscription?.status === \"past_due\";\n    const isCancelled = subscription?.status === \"cancelled\";\n    const canAccessFeature = (feature)=>{\n        if (!subscription) return false;\n        if (feature === \"pro\") {\n            return [\n                \"pro\",\n                \"enterprise\"\n            ].includes(subscription.plan_type) && hasActiveSubscription;\n        }\n        if (feature === \"enterprise\") {\n            return subscription.plan_type === \"enterprise\" && hasActiveSubscription;\n        }\n        return false;\n    };\n    return {\n        subscription,\n        hasActiveSubscription,\n        isOnTrial,\n        isPastDue,\n        isCancelled,\n        canAccessFeature\n    };\n}\n// Helper hook for authentication guards\nfunction useAuthGuard() {\n    const { user, loading } = useAuth();\n    const isAuthenticated = !!user;\n    const isLoading = loading;\n    return {\n        isAuthenticated,\n        isLoading,\n        user\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/contexts/auth-context.tsx\n");

/***/ }),

/***/ "(ssr)/./src/lib/supabase.ts":
/*!*****************************!*\
  !*** ./src/lib/supabase.ts ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createSubscription: () => (/* binding */ createSubscription),\n/* harmony export */   createSupabaseComponentClient: () => (/* binding */ createSupabaseComponentClient),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   getProfile: () => (/* binding */ getProfile),\n/* harmony export */   getSession: () => (/* binding */ getSession),\n/* harmony export */   getUser: () => (/* binding */ getUser),\n/* harmony export */   getUserSubscription: () => (/* binding */ getUserSubscription),\n/* harmony export */   handleSupabaseError: () => (/* binding */ handleSupabaseError),\n/* harmony export */   logApiUsage: () => (/* binding */ logApiUsage),\n/* harmony export */   logUserActivity: () => (/* binding */ logUserActivity),\n/* harmony export */   resetPassword: () => (/* binding */ resetPassword),\n/* harmony export */   signIn: () => (/* binding */ signIn),\n/* harmony export */   signOut: () => (/* binding */ signOut),\n/* harmony export */   signUp: () => (/* binding */ signUp),\n/* harmony export */   subscribeToApiUsage: () => (/* binding */ subscribeToApiUsage),\n/* harmony export */   subscribeToContentGenerations: () => (/* binding */ subscribeToContentGenerations),\n/* harmony export */   supabase: () => (/* binding */ supabase),\n/* harmony export */   supabaseAdmin: () => (/* binding */ supabaseAdmin),\n/* harmony export */   updatePassword: () => (/* binding */ updatePassword),\n/* harmony export */   updateProfile: () => (/* binding */ updateProfile)\n/* harmony export */ });\n/* harmony import */ var _supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @supabase/supabase-js */ \"(ssr)/./node_modules/@supabase/supabase-js/dist/module/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @supabase/auth-helpers-nextjs */ \"(ssr)/./node_modules/@supabase/auth-helpers-nextjs/dist/index.js\");\n/* harmony import */ var _supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__);\n// Supabase Client Configuration\n\n\n// Client-side Supabase client (for use in components)\nconst supabase = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://zqrmpanonghggoxdjirq.supabase.co\", \"eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Inpxcm1wYW5vbmdoZ2dveGRqaXJxIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDk2MDk3NDEsImV4cCI6MjA2NTE4NTc0MX0.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY\", {\n    auth: {\n        persistSession: true,\n        autoRefreshToken: true,\n        detectSessionInUrl: true\n    },\n    db: {\n        schema: \"public\"\n    },\n    global: {\n        headers: {\n            \"X-Client-Info\": \"seo-saas@1.0.0\"\n        }\n    }\n});\n// Client component client (for use in client components)\nconst createSupabaseComponentClient = ()=>(0,_supabase_auth_helpers_nextjs__WEBPACK_IMPORTED_MODULE_0__.createClientComponentClient)();\n// Server component client is in a separate server-only file\n// Service role client (for admin operations)\nconst supabaseAdmin = (0,_supabase_supabase_js__WEBPACK_IMPORTED_MODULE_1__.createClient)(\"https://zqrmpanonghggoxdjirq.supabase.co\", process.env.SUPABASE_SERVICE_ROLE_KEY, {\n    auth: {\n        autoRefreshToken: false,\n        persistSession: false\n    },\n    db: {\n        schema: \"public\"\n    },\n    global: {\n        headers: {\n            \"X-Client-Info\": \"seo-saas-admin@1.0.0\"\n        }\n    }\n});\n// Auth helpers\nasync function getSession() {\n    const supabase = createSupabaseComponentClient();\n    const { data: { session }, error } = await supabase.auth.getSession();\n    if (error) {\n        console.error(\"Error getting session:\", error);\n        return null;\n    }\n    return session;\n}\nasync function getUser() {\n    const supabase = createSupabaseComponentClient();\n    const { data: { user }, error } = await supabase.auth.getUser();\n    if (error) {\n        console.error(\"Error getting user:\", error);\n        return null;\n    }\n    return user;\n}\nasync function signIn(email, password) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.auth.signInWithPassword({\n        email,\n        password\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\nasync function signUp(email, password, fullName) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.auth.signUp({\n        email,\n        password,\n        options: {\n            data: {\n                full_name: fullName\n            }\n        }\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\nasync function signOut() {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.auth.signOut();\n    if (error) {\n        throw new Error(error.message);\n    }\n}\nasync function resetPassword(email) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.auth.resetPasswordForEmail(email, {\n        redirectTo: `${\"http://localhost:3000\" || 0}/auth/reset-password`\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n}\nasync function updatePassword(newPassword) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.auth.updateUser({\n        password: newPassword\n    });\n    if (error) {\n        throw new Error(error.message);\n    }\n}\n// Profile helpers\nasync function getProfile(userId) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"profiles\").select(\"*\").eq(\"id\", userId).single();\n    if (error) {\n        console.error(\"Error getting profile:\", error);\n        return null;\n    }\n    return data;\n}\nasync function updateProfile(userId, updates) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"profiles\").update({\n        ...updates,\n        updated_at: new Date().toISOString()\n    }).eq(\"id\", userId).select().single();\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\n// Subscription helpers\nasync function getUserSubscription(userId) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"user_subscriptions\").select(\"*\").eq(\"user_id\", userId).single();\n    if (error && error.code !== \"PGRST116\") {\n        console.error(\"Error getting subscription:\", error);\n        return null;\n    }\n    return data;\n}\nasync function createSubscription(subscription) {\n    const supabase = createSupabaseComponentClient();\n    const { data, error } = await supabase.from(\"user_subscriptions\").insert(subscription).select().single();\n    if (error) {\n        throw new Error(error.message);\n    }\n    return data;\n}\n// API usage tracking\nasync function logApiUsage(logData) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.from(\"api_usage_logs\").insert(logData);\n    if (error) {\n        console.error(\"Error logging API usage:\", error);\n    }\n}\n// Activity logging\nasync function logUserActivity(activity) {\n    const supabase = createSupabaseComponentClient();\n    const { error } = await supabase.from(\"user_activity_logs\").insert(activity);\n    if (error) {\n        console.error(\"Error logging user activity:\", error);\n    }\n}\n// Real-time subscriptions\nfunction subscribeToContentGenerations(userId, callback) {\n    const supabase = createSupabaseComponentClient();\n    return supabase.channel(\"content_generations\").on(\"postgres_changes\", {\n        event: \"*\",\n        schema: \"public\",\n        table: \"content_generations\",\n        filter: `user_id=eq.${userId}`\n    }, callback).subscribe();\n}\nfunction subscribeToApiUsage(userId, callback) {\n    const supabase = createSupabaseComponentClient();\n    return supabase.channel(\"api_usage\").on(\"postgres_changes\", {\n        event: \"INSERT\",\n        schema: \"public\",\n        table: \"api_usage_logs\",\n        filter: `user_id=eq.${userId}`\n    }, callback).subscribe();\n}\n// Error handling helper\nfunction handleSupabaseError(error) {\n    if (error?.message) {\n        return error.message;\n    }\n    if (error?.code) {\n        switch(error.code){\n            case \"auth/invalid-email\":\n                return \"Invalid email address\";\n            case \"auth/user-disabled\":\n                return \"This account has been disabled\";\n            case \"auth/user-not-found\":\n                return \"No account found with this email\";\n            case \"auth/wrong-password\":\n                return \"Incorrect password\";\n            case \"auth/too-many-requests\":\n                return \"Too many attempts. Please try again later\";\n            case \"auth/weak-password\":\n                return \"Password should be at least 6 characters\";\n            case \"auth/email-already-in-use\":\n                return \"An account with this email already exists\";\n            default:\n                return \"An unexpected error occurred\";\n        }\n    }\n    return \"An unexpected error occurred\";\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (supabase);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./src/lib/supabase.ts\n");

/***/ }),

/***/ "(rsc)/./src/app/globals.css":
/*!*****************************!*\
  !*** ./src/app/globals.css ***!
  \*****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"20e231f2ed77\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvYXBwL2dsb2JhbHMuY3NzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQSxpRUFBZSxjQUFjO0FBQzdCLElBQUksS0FBVSxFQUFFLEVBQXVCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vc2VvLXNhYXMvLi9zcmMvYXBwL2dsb2JhbHMuY3NzPzg3YjciXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCIyMGUyMzFmMmVkNzdcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./src/app/globals.css\n");

/***/ }),

/***/ "(rsc)/./src/app/layout.tsx":
/*!****************************!*\
  !*** ./src/app/layout.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"src\\\\app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"src\\\\\\\\app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./src/app/globals.css\");\n/* harmony import */ var _contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/contexts/auth-context */ \"(rsc)/./src/contexts/auth-context.tsx\");\n\n\n\n\nconst metadata = {\n    title: \"SEO Content Generator - Professional SEO Content Creation\",\n    description: \"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.\",\n    keywords: [\n        \"SEO\",\n        \"content generation\",\n        \"AI writing\",\n        \"keyword optimization\",\n        \"competitor analysis\"\n    ],\n    authors: [\n        {\n            name: \"SEO Content Generator\"\n        }\n    ],\n    openGraph: {\n        title: \"SEO Content Generator - Professional SEO Content Creation\",\n        description: \"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.\",\n        type: \"website\",\n        locale: \"en_US\"\n    },\n    twitter: {\n        card: \"summary_large_image\",\n        title: \"SEO Content Generator - Professional SEO Content Creation\",\n        description: \"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.\"\n    },\n    robots: {\n        index: true,\n        follow: true\n    },\n    viewport: {\n        width: \"device-width\",\n        initialScale: 1\n    }\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: (next_font_google_target_css_path_src_app_layout_tsx_import_Inter_arguments_subsets_latin_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_3___default().variable),\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: \"font-sans antialiased bg-gray-50 text-gray-900\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_contexts_auth_context__WEBPACK_IMPORTED_MODULE_2__.AuthProvider, {\n                children: children\n            }, void 0, false, {\n                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\layout.tsx\",\n                lineNumber: 45,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\layout.tsx\",\n            lineNumber: 44,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\layout.tsx\",\n        lineNumber: 43,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./src/app/page.tsx":
/*!**************************!*\
  !*** ./src/app/page.tsx ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/link.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_link__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./src/components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./src/components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,DocumentTextIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/DocumentTextIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,DocumentTextIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/ChartBarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,DocumentTextIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,DocumentTextIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,DocumentTextIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/ArrowRightIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRightIcon,ChartBarIcon,CheckCircleIcon,DocumentTextIcon,SparklesIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(rsc)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n// Professional SEO SAAS Landing Page\n// Enterprise-grade landing page with modern design\n\n\n\n\n\nfunction Home() {\n    const features = [\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n            title: \"AI Content Generation\",\n            description: \"Generate SEO-optimized content with advanced AI that understands your industry and target audience.\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n            title: \"Competitor Analysis\",\n            description: \"Analyze top-ranking competitors and identify content gaps to outrank them in search results.\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n            title: \"Multi-Industry Support\",\n            description: \"Specialized templates and optimization for technology, healthcare, finance, and 10+ other industries.\"\n        },\n        {\n            icon: _barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n            title: \"Advanced SEO Engine\",\n            description: \"Comprehensive SEO analysis with keyword density, heading optimization, and LSI keyword extraction.\"\n        }\n    ];\n    const benefits = [\n        \"Generate 1000+ word SEO-optimized content in under 2 minutes\",\n        \"Analyze competitor strategies and identify ranking opportunities\",\n        \"Built-in E-E-A-T compliance for Google's quality guidelines\",\n        \"Real-time SERP analysis and keyword research\",\n        \"Professional content templates for all industries\",\n        \"Advanced NLP and semantic keyword optimization\"\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"header\", {\n                className: \"border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-2xl font-bold text-gray-900\",\n                                    children: \"SEO Pro\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 56,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/auth/signin\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            variant: \"ghost\",\n                                            children: \"Sign In\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 60,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 59,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                        href: \"/auth/signup\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            children: \"Get Started\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 63,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 62,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 58,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 54,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 53,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 52,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto text-center\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-5xl md:text-6xl font-bold text-gray-900 mb-6\",\n                            children: [\n                                \"Generate SEO Content That\",\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"text-blue-600 block\",\n                                    children: \"Ranks #1 on Google\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 75,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 73,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-xl text-gray-600 mb-8 max-w-3xl mx-auto\",\n                            children: \"Create high-quality, SEO-optimized content with our advanced AI engine. Analyze competitors, optimize keywords, and generate content that outranks the competition.\"\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/auth/signup\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        className: \"text-lg px-8 py-3\",\n                                        children: [\n                                            \"Start Free Trial\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"ml-2 h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 85,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 83,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 82,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                    href: \"/dashboard\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        variant: \"outline\",\n                                        size: \"lg\",\n                                        className: \"text-lg px-8 py-3\",\n                                        children: \"View Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 89,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 81,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 72,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 71,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-white\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-gray-900 mb-4\",\n                                    children: \"Everything You Need to Dominate Search Rankings\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 101,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-gray-600 max-w-3xl mx-auto\",\n                                    children: \"Our comprehensive SEO platform combines AI content generation with advanced competitor analysis\"\n                                }, void 0, false, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 104,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 100,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8\",\n                            children: features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"text-center hover:shadow-lg transition-shadow\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(feature.icon, {\n                                                        className: \"h-6 w-6 text-blue-600\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 114,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 113,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl\",\n                                                    children: feature.title\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 116,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 112,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                className: \"text-gray-600\",\n                                                children: feature.description\n                                            }, void 0, false, {\n                                                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 119,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, index, true, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 111,\n                                    columnNumber: 15\n                                }, this))\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 109,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 99,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 98,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 bg-gray-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                        className: \"text-4xl font-bold text-gray-900 mb-6\",\n                                        children: \"Why Choose SEO Pro?\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 134,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-gray-600 mb-8\",\n                                        children: \"Join thousands of content creators, marketers, and businesses who trust our platform to generate high-ranking content that drives organic traffic and conversions.\"\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 137,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"space-y-4\",\n                                        children: benefits.map((benefit, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-start space-x-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRightIcon_ChartBarIcon_CheckCircleIcon_DocumentTextIcon_SparklesIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-6 w-6 text-green-500 flex-shrink-0 mt-0.5\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 144,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-gray-700\",\n                                                        children: benefit\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 145,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, index, true, {\n                                                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 143,\n                                                columnNumber: 19\n                                            }, this))\n                                    }, void 0, false, {\n                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                        lineNumber: 141,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 133,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"lg:pl-8\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"p-8 bg-gradient-to-br from-blue-500 to-purple-600 text-white\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            className: \"text-center pb-6\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-3xl font-bold text-white\",\n                                                    children: \"Ready to Get Started?\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 153,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-blue-100 text-lg\",\n                                                    children: \"Join our free trial and see the results for yourself\"\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 156,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 152,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            className: \"text-center\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"space-y-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl font-bold\",\n                                                        children: \"$0\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-100\",\n                                                        children: \"14-day free trial\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 163,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-blue-100\",\n                                                        children: \"No credit card required\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 164,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/auth/signup\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                                            size: \"lg\",\n                                                            variant: \"secondary\",\n                                                            className: \"w-full mt-6\",\n                                                            children: \"Start Your Free Trial\"\n                                                        }, void 0, false, {\n                                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                            lineNumber: 166,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 165,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                lineNumber: 161,\n                                                columnNumber: 19\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 151,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 150,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                        lineNumber: 132,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 131,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 130,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"footer\", {\n                className: \"bg-gray-900 text-white py-12\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid grid-cols-1 md:grid-cols-4 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                            className: \"text-xl font-bold mb-4\",\n                                            children: \"SEO Pro\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 183,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-400\",\n                                            children: \"Professional SEO content generation platform powered by advanced AI.\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 182,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Product\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 189,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/features\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Features\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 191,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/pricing\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Pricing\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 192,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 192,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/demo\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Demo\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 193,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 193,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 190,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 188,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Company\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 197,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/about\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"About\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 199,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 199,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/contact\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Contact\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 200,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 200,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/blog\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Blog\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 201,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 201,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 198,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 196,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h4\", {\n                                            className: \"font-semibold mb-4\",\n                                            children: \"Support\"\n                                        }, void 0, false, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                            className: \"space-y-2 text-gray-400\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/help\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Help Center\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 207,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/docs\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"Documentation\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 208,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 208,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((next_link__WEBPACK_IMPORTED_MODULE_1___default()), {\n                                                        href: \"/api\",\n                                                        className: \"hover:text-white\",\n                                                        children: \"API\"\n                                                    }, void 0, false, {\n                                                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                        lineNumber: 209,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                            lineNumber: 206,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 181,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                children: \"\\xa9 2024 SEO Pro. All rights reserved.\"\n                            }, void 0, false, {\n                                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                                lineNumber: 214,\n                                columnNumber: 13\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                    lineNumber: 180,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n                lineNumber: 179,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\app\\\\page.tsx\",\n        lineNumber: 50,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/button.tsx":
/*!**************************************!*\
  !*** ./src/components/ui/button.tsx ***!
  \**************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n// Professional Button Component\n// Enterprise-grade button with variants and loading states\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background\", {\n    variants: {\n        variant: {\n            default: \"bg-primary text-primary-foreground hover:bg-primary/90\",\n            destructive: \"bg-destructive text-destructive-foreground hover:bg-destructive/90\",\n            outline: \"border border-input hover:bg-accent hover:text-accent-foreground\",\n            secondary: \"bg-secondary text-secondary-foreground hover:bg-secondary/80\",\n            ghost: \"hover:bg-accent hover:text-accent-foreground\",\n            link: \"underline-offset-4 hover:underline text-primary\"\n        },\n        size: {\n            default: \"h-10 py-2 px-4\",\n            sm: \"h-9 px-3 rounded-md\",\n            lg: \"h-11 px-8 rounded-md\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, loading, children, disabled, ...props }, ref)=>{\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        disabled: disabled || loading,\n        ...props,\n        children: [\n            loading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                className: \"animate-spin -ml-1 mr-3 h-4 w-4\",\n                xmlns: \"http://www.w3.org/2000/svg\",\n                fill: \"none\",\n                viewBox: \"0 0 24 24\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"circle\", {\n                        className: \"opacity-25\",\n                        cx: \"12\",\n                        cy: \"12\",\n                        r: \"10\",\n                        stroke: \"currentColor\",\n                        strokeWidth: \"4\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 57,\n                        columnNumber: 13\n                    }, undefined),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                        className: \"opacity-75\",\n                        fill: \"currentColor\",\n                        d: \"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"\n                    }, void 0, false, {\n                        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                        lineNumber: 65,\n                        columnNumber: 13\n                    }, undefined)\n                ]\n            }, void 0, true, {\n                fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\button.tsx\",\n                lineNumber: 51,\n                columnNumber: 11\n            }, undefined),\n            children\n        ]\n    }, void 0, true, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 44,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9zcmMvY29tcG9uZW50cy91aS9idXR0b24udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7O0FBQUEsZ0NBQWdDO0FBQ2hDLDJEQUEyRDs7QUFFN0I7QUFDbUM7QUFDakM7QUFFaEMsTUFBTUcsaUJBQWlCRiw2REFBR0EsQ0FDeEIsd1FBQ0E7SUFDRUcsVUFBVTtRQUNSQyxTQUFTO1lBQ1BDLFNBQVM7WUFDVEMsYUFBYTtZQUNiQyxTQUFTO1lBQ1RDLFdBQVc7WUFDWEMsT0FBTztZQUNQQyxNQUFNO1FBQ1I7UUFDQUMsTUFBTTtZQUNKTixTQUFTO1lBQ1RPLElBQUk7WUFDSkMsSUFBSTtZQUNKQyxNQUFNO1FBQ1I7SUFDRjtJQUNBQyxpQkFBaUI7UUFDZlgsU0FBUztRQUNUTyxNQUFNO0lBQ1I7QUFDRjtBQVVGLE1BQU1LLHVCQUFTakIsNkNBQWdCLENBQzdCLENBQUMsRUFBRW1CLFNBQVMsRUFBRWQsT0FBTyxFQUFFTyxJQUFJLEVBQUVRLE9BQU8sRUFBRUMsUUFBUSxFQUFFQyxRQUFRLEVBQUUsR0FBR0MsT0FBTyxFQUFFQztJQUNwRSxxQkFDRSw4REFBQ0M7UUFDQ04sV0FBV2pCLDhDQUFFQSxDQUFDQyxlQUFlO1lBQUVFO1lBQVNPO1lBQU1PO1FBQVU7UUFDeERLLEtBQUtBO1FBQ0xGLFVBQVVBLFlBQVlGO1FBQ3JCLEdBQUdHLEtBQUs7O1lBRVJILHlCQUNDLDhEQUFDTTtnQkFDQ1AsV0FBVTtnQkFDVlEsT0FBTTtnQkFDTkMsTUFBSztnQkFDTEMsU0FBUTs7a0NBRVIsOERBQUNDO3dCQUNDWCxXQUFVO3dCQUNWWSxJQUFHO3dCQUNIQyxJQUFHO3dCQUNIQyxHQUFFO3dCQUNGQyxRQUFPO3dCQUNQQyxhQUFZOzs7Ozs7a0NBRWQsOERBQUNDO3dCQUNDakIsV0FBVTt3QkFDVlMsTUFBSzt3QkFDTFMsR0FBRTs7Ozs7Ozs7Ozs7O1lBSVBoQjs7Ozs7OztBQUdQO0FBRUZKLE9BQU9xQixXQUFXLEdBQUc7QUFFWSIsInNvdXJjZXMiOlsid2VicGFjazovL3Nlby1zYWFzLy4vc3JjL2NvbXBvbmVudHMvdWkvYnV0dG9uLnRzeD82YTBhIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIFByb2Zlc3Npb25hbCBCdXR0b24gQ29tcG9uZW50XG4vLyBFbnRlcnByaXNlLWdyYWRlIGJ1dHRvbiB3aXRoIHZhcmlhbnRzIGFuZCBsb2FkaW5nIHN0YXRlc1xuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgY3ZhLCB0eXBlIFZhcmlhbnRQcm9wcyB9IGZyb20gXCJjbGFzcy12YXJpYW5jZS1hdXRob3JpdHlcIlxuaW1wb3J0IHsgY24gfSBmcm9tIFwiQC9saWIvdXRpbHNcIlxuXG5jb25zdCBidXR0b25WYXJpYW50cyA9IGN2YShcbiAgXCJpbmxpbmUtZmxleCBpdGVtcy1jZW50ZXIganVzdGlmeS1jZW50ZXIgcm91bmRlZC1tZCB0ZXh0LXNtIGZvbnQtbWVkaXVtIHRyYW5zaXRpb24tY29sb3JzIGZvY3VzLXZpc2libGU6b3V0bGluZS1ub25lIGZvY3VzLXZpc2libGU6cmluZy0yIGZvY3VzLXZpc2libGU6cmluZy1yaW5nIGZvY3VzLXZpc2libGU6cmluZy1vZmZzZXQtMiBkaXNhYmxlZDpvcGFjaXR5LTUwIGRpc2FibGVkOnBvaW50ZXItZXZlbnRzLW5vbmUgcmluZy1vZmZzZXQtYmFja2dyb3VuZFwiLFxuICB7XG4gICAgdmFyaWFudHM6IHtcbiAgICAgIHZhcmlhbnQ6IHtcbiAgICAgICAgZGVmYXVsdDogXCJiZy1wcmltYXJ5IHRleHQtcHJpbWFyeS1mb3JlZ3JvdW5kIGhvdmVyOmJnLXByaW1hcnkvOTBcIixcbiAgICAgICAgZGVzdHJ1Y3RpdmU6IFwiYmctZGVzdHJ1Y3RpdmUgdGV4dC1kZXN0cnVjdGl2ZS1mb3JlZ3JvdW5kIGhvdmVyOmJnLWRlc3RydWN0aXZlLzkwXCIsXG4gICAgICAgIG91dGxpbmU6IFwiYm9yZGVyIGJvcmRlci1pbnB1dCBob3ZlcjpiZy1hY2NlbnQgaG92ZXI6dGV4dC1hY2NlbnQtZm9yZWdyb3VuZFwiLFxuICAgICAgICBzZWNvbmRhcnk6IFwiYmctc2Vjb25kYXJ5IHRleHQtc2Vjb25kYXJ5LWZvcmVncm91bmQgaG92ZXI6Ymctc2Vjb25kYXJ5LzgwXCIsXG4gICAgICAgIGdob3N0OiBcImhvdmVyOmJnLWFjY2VudCBob3Zlcjp0ZXh0LWFjY2VudC1mb3JlZ3JvdW5kXCIsXG4gICAgICAgIGxpbms6IFwidW5kZXJsaW5lLW9mZnNldC00IGhvdmVyOnVuZGVybGluZSB0ZXh0LXByaW1hcnlcIixcbiAgICAgIH0sXG4gICAgICBzaXplOiB7XG4gICAgICAgIGRlZmF1bHQ6IFwiaC0xMCBweS0yIHB4LTRcIixcbiAgICAgICAgc206IFwiaC05IHB4LTMgcm91bmRlZC1tZFwiLFxuICAgICAgICBsZzogXCJoLTExIHB4LTggcm91bmRlZC1tZFwiLFxuICAgICAgICBpY29uOiBcImgtMTAgdy0xMFwiLFxuICAgICAgfSxcbiAgICB9LFxuICAgIGRlZmF1bHRWYXJpYW50czoge1xuICAgICAgdmFyaWFudDogXCJkZWZhdWx0XCIsXG4gICAgICBzaXplOiBcImRlZmF1bHRcIixcbiAgICB9LFxuICB9XG4pXG5cbmV4cG9ydCBpbnRlcmZhY2UgQnV0dG9uUHJvcHNcbiAgZXh0ZW5kcyBSZWFjdC5CdXR0b25IVE1MQXR0cmlidXRlczxIVE1MQnV0dG9uRWxlbWVudD4sXG4gICAgVmFyaWFudFByb3BzPHR5cGVvZiBidXR0b25WYXJpYW50cz4ge1xuICBsb2FkaW5nPzogYm9vbGVhblxuICBhc0NoaWxkPzogYm9vbGVhblxufVxuXG5jb25zdCBCdXR0b24gPSBSZWFjdC5mb3J3YXJkUmVmPEhUTUxCdXR0b25FbGVtZW50LCBCdXR0b25Qcm9wcz4oXG4gICh7IGNsYXNzTmFtZSwgdmFyaWFudCwgc2l6ZSwgbG9hZGluZywgY2hpbGRyZW4sIGRpc2FibGVkLCAuLi5wcm9wcyB9LCByZWYpID0+IHtcbiAgICByZXR1cm4gKFxuICAgICAgPGJ1dHRvblxuICAgICAgICBjbGFzc05hbWU9e2NuKGJ1dHRvblZhcmlhbnRzKHsgdmFyaWFudCwgc2l6ZSwgY2xhc3NOYW1lIH0pKX1cbiAgICAgICAgcmVmPXtyZWZ9XG4gICAgICAgIGRpc2FibGVkPXtkaXNhYmxlZCB8fCBsb2FkaW5nfVxuICAgICAgICB7Li4ucHJvcHN9XG4gICAgICA+XG4gICAgICAgIHtsb2FkaW5nICYmIChcbiAgICAgICAgICA8c3ZnXG4gICAgICAgICAgICBjbGFzc05hbWU9XCJhbmltYXRlLXNwaW4gLW1sLTEgbXItMyBoLTQgdy00XCJcbiAgICAgICAgICAgIHhtbG5zPVwiaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmdcIlxuICAgICAgICAgICAgZmlsbD1cIm5vbmVcIlxuICAgICAgICAgICAgdmlld0JveD1cIjAgMCAyNCAyNFwiXG4gICAgICAgICAgPlxuICAgICAgICAgICAgPGNpcmNsZVxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJvcGFjaXR5LTI1XCJcbiAgICAgICAgICAgICAgY3g9XCIxMlwiXG4gICAgICAgICAgICAgIGN5PVwiMTJcIlxuICAgICAgICAgICAgICByPVwiMTBcIlxuICAgICAgICAgICAgICBzdHJva2U9XCJjdXJyZW50Q29sb3JcIlxuICAgICAgICAgICAgICBzdHJva2VXaWR0aD1cIjRcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxwYXRoXG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cIm9wYWNpdHktNzVcIlxuICAgICAgICAgICAgICBmaWxsPVwiY3VycmVudENvbG9yXCJcbiAgICAgICAgICAgICAgZD1cIm00IDEyYTggOCAwIDAxOC04VjBDNS4zNzMgMCAwIDUuMzczIDAgMTJoNHptMiA1LjI5MUE3Ljk2MiA3Ljk2MiAwIDAxNCAxMkgwYzAgMy4wNDIgMS4xMzUgNS44MjQgMyA3LjkzOGwzLTIuNjQ3elwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvc3ZnPlxuICAgICAgICApfVxuICAgICAgICB7Y2hpbGRyZW59XG4gICAgICA8L2J1dHRvbj5cbiAgICApXG4gIH1cbilcbkJ1dHRvbi5kaXNwbGF5TmFtZSA9IFwiQnV0dG9uXCJcblxuZXhwb3J0IHsgQnV0dG9uLCBidXR0b25WYXJpYW50cyB9XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJjdmEiLCJjbiIsImJ1dHRvblZhcmlhbnRzIiwidmFyaWFudHMiLCJ2YXJpYW50IiwiZGVmYXVsdCIsImRlc3RydWN0aXZlIiwib3V0bGluZSIsInNlY29uZGFyeSIsImdob3N0IiwibGluayIsInNpemUiLCJzbSIsImxnIiwiaWNvbiIsImRlZmF1bHRWYXJpYW50cyIsIkJ1dHRvbiIsImZvcndhcmRSZWYiLCJjbGFzc05hbWUiLCJsb2FkaW5nIiwiY2hpbGRyZW4iLCJkaXNhYmxlZCIsInByb3BzIiwicmVmIiwiYnV0dG9uIiwic3ZnIiwieG1sbnMiLCJmaWxsIiwidmlld0JveCIsImNpcmNsZSIsImN4IiwiY3kiLCJyIiwic3Ryb2tlIiwic3Ryb2tlV2lkdGgiLCJwYXRoIiwiZCIsImRpc3BsYXlOYW1lIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./src/components/ui/card.tsx":
/*!************************************!*\
  !*** ./src/components/ui/card.tsx ***!
  \************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./src/lib/utils.ts\");\n// Professional Card Component\n// Enterprise-grade card with header, content, and footer sections\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-lg border bg-card text-card-foreground shadow-sm\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 11,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 26,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 38,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 53,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 65,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"F:\\\\Claude-Code-Setup\\\\SEO SAAS APP\\\\seo-saas\\\\src\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 73,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./src/contexts/auth-context.tsx":
/*!***************************************!*\
  !*** ./src/contexts/auth-context.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   useAuthGuard: () => (/* binding */ e3),
/* harmony export */   useSubscription: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

const proxy = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx`)

// Accessing the __esModule property and exporting $$typeof are required here.
// The __esModule getter forces the proxy target to create the default export
// and the $$typeof value is for rendering logic to determine if the module
// is a client boundary.
const { __esModule, $$typeof } = proxy;
const __default__ = proxy.default;

const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx#useSubscription`);

const e3 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`F:\Claude-Code-Setup\SEO SAAS APP\seo-saas\src\contexts\auth-context.tsx#useAuthGuard`);


/***/ }),

/***/ "(rsc)/./src/lib/utils.ts":
/*!**************************!*\
  !*** ./src/lib/utils.ts ***!
  \**************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   calculateKeywordDensity: () => (/* binding */ calculateKeywordDensity),\n/* harmony export */   calculatePercentage: () => (/* binding */ calculatePercentage),\n/* harmony export */   calculateReadingTime: () => (/* binding */ calculateReadingTime),\n/* harmony export */   capitalizeFirst: () => (/* binding */ capitalizeFirst),\n/* harmony export */   cn: () => (/* binding */ cn),\n/* harmony export */   copyToClipboard: () => (/* binding */ copyToClipboard),\n/* harmony export */   countHeadings: () => (/* binding */ countHeadings),\n/* harmony export */   countWords: () => (/* binding */ countWords),\n/* harmony export */   debounce: () => (/* binding */ debounce),\n/* harmony export */   downloadAsFile: () => (/* binding */ downloadAsFile),\n/* harmony export */   extractDomain: () => (/* binding */ extractDomain),\n/* harmony export */   extractTextFromHtml: () => (/* binding */ extractTextFromHtml),\n/* harmony export */   formatDate: () => (/* binding */ formatDate),\n/* harmony export */   formatDateTime: () => (/* binding */ formatDateTime),\n/* harmony export */   formatFileSize: () => (/* binding */ formatFileSize),\n/* harmony export */   formatNumber: () => (/* binding */ formatNumber),\n/* harmony export */   formatPercentage: () => (/* binding */ formatPercentage),\n/* harmony export */   generateColorFromString: () => (/* binding */ generateColorFromString),\n/* harmony export */   generateRandomString: () => (/* binding */ generateRandomString),\n/* harmony export */   generateSlug: () => (/* binding */ generateSlug),\n/* harmony export */   getPriorityColor: () => (/* binding */ getPriorityColor),\n/* harmony export */   getScoreBgColor: () => (/* binding */ getScoreBgColor),\n/* harmony export */   getScoreColor: () => (/* binding */ getScoreColor),\n/* harmony export */   isUrl: () => (/* binding */ isUrl),\n/* harmony export */   isValidEmail: () => (/* binding */ isValidEmail),\n/* harmony export */   safeJsonParse: () => (/* binding */ safeJsonParse),\n/* harmony export */   sleep: () => (/* binding */ sleep),\n/* harmony export */   throttle: () => (/* binding */ throttle),\n/* harmony export */   timeAgo: () => (/* binding */ timeAgo),\n/* harmony export */   toTitleCase: () => (/* binding */ toTitleCase),\n/* harmony export */   truncateText: () => (/* binding */ truncateText)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\n// Utility for combining Tailwind classes\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n// Debounce function for search inputs\nfunction debounce(func, delay) {\n    let timeoutId;\n    return (...args)=>{\n        clearTimeout(timeoutId);\n        timeoutId = setTimeout(()=>func(...args), delay);\n    };\n}\n// Throttle function for API calls\nfunction throttle(func, limit) {\n    let inThrottle;\n    return (...args)=>{\n        if (!inThrottle) {\n            func(...args);\n            inThrottle = true;\n            setTimeout(()=>inThrottle = false, limit);\n        }\n    };\n}\n// Format numbers with commas\nfunction formatNumber(num) {\n    return num.toLocaleString();\n}\n// Calculate percentage\nfunction calculatePercentage(value, total) {\n    if (total === 0) return 0;\n    return Math.round(value / total * 100 * 100) / 100;\n}\n// Calculate keyword density\nfunction calculateKeywordDensity(keyword, content) {\n    const words = content.toLowerCase().split(/\\s+/);\n    const keywordWords = keyword.toLowerCase().split(/\\s+/);\n    const totalWords = words.length;\n    if (totalWords === 0) return 0;\n    let count = 0;\n    for(let i = 0; i <= totalWords - keywordWords.length; i++){\n        const phrase = words.slice(i, i + keywordWords.length).join(\" \");\n        if (phrase === keyword.toLowerCase()) {\n            count++;\n        }\n    }\n    return calculatePercentage(count, totalWords);\n}\n// Count words in content\nfunction countWords(content) {\n    return content.trim().split(/\\s+/).filter((word)=>word.length > 0).length;\n}\n// Count headings by level\nfunction countHeadings(content) {\n    const headingCounts = {\n        h1: 0,\n        h2: 0,\n        h3: 0,\n        h4: 0,\n        h5: 0,\n        h6: 0\n    };\n    const headingRegex = /<h([1-6])[^>]*>.*?<\\/h[1-6]>/gi;\n    let match;\n    while((match = headingRegex.exec(content)) !== null){\n        const level = parseInt(match[1]);\n        headingCounts[`h${level}`]++;\n    }\n    return headingCounts;\n}\n// Extract text content from HTML\nfunction extractTextFromHtml(html) {\n    return html.replace(/<[^>]*>/g, \"\").trim();\n}\n// Generate SEO-friendly slug\nfunction generateSlug(text) {\n    return text.toLowerCase().replace(/[^\\w\\s-]/g, \"\").replace(/\\s+/g, \"-\").replace(/--+/g, \"-\").trim();\n}\n// Validate email format\nfunction isValidEmail(email) {\n    const emailRegex = /^[^\\s@]+@[^\\s@]+\\.[^\\s@]+$/;\n    return emailRegex.test(email);\n}\n// Generate random string\nfunction generateRandomString(length) {\n    const chars = \"ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789\";\n    let result = \"\";\n    for(let i = 0; i < length; i++){\n        result += chars.charAt(Math.floor(Math.random() * chars.length));\n    }\n    return result;\n}\n// Format date\nfunction formatDate(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return dateObj.toLocaleDateString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\"\n    });\n}\n// Format date with time\nfunction formatDateTime(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    return dateObj.toLocaleString(\"en-US\", {\n        year: \"numeric\",\n        month: \"long\",\n        day: \"numeric\",\n        hour: \"2-digit\",\n        minute: \"2-digit\"\n    });\n}\n// Calculate time ago\nfunction timeAgo(date) {\n    const dateObj = typeof date === \"string\" ? new Date(date) : date;\n    const now = new Date();\n    const diffInSeconds = Math.floor((now.getTime() - dateObj.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n        return \"just now\";\n    } else if (diffInSeconds < 3600) {\n        const minutes = Math.floor(diffInSeconds / 60);\n        return `${minutes} minute${minutes > 1 ? \"s\" : \"\"} ago`;\n    } else if (diffInSeconds < 86400) {\n        const hours = Math.floor(diffInSeconds / 3600);\n        return `${hours} hour${hours > 1 ? \"s\" : \"\"} ago`;\n    } else {\n        const days = Math.floor(diffInSeconds / 86400);\n        return `${days} day${days > 1 ? \"s\" : \"\"} ago`;\n    }\n}\n// Truncate text\nfunction truncateText(text, maxLength) {\n    if (text.length <= maxLength) return text;\n    return text.slice(0, maxLength) + \"...\";\n}\n// Capitalize first letter\nfunction capitalizeFirst(text) {\n    return text.charAt(0).toUpperCase() + text.slice(1);\n}\n// Convert to title case\nfunction toTitleCase(text) {\n    return text.split(\" \").map((word)=>capitalizeFirst(word.toLowerCase())).join(\" \");\n}\n// Sleep function for delays\nfunction sleep(ms) {\n    return new Promise((resolve)=>setTimeout(resolve, ms));\n}\n// Safe JSON parse\nfunction safeJsonParse(json, fallback) {\n    try {\n        return JSON.parse(json);\n    } catch  {\n        return fallback;\n    }\n}\n// Check if string is URL\nfunction isUrl(string) {\n    try {\n        new URL(string);\n        return true;\n    } catch  {\n        return false;\n    }\n}\n// Extract domain from URL\nfunction extractDomain(url) {\n    try {\n        const urlObj = new URL(url);\n        return urlObj.hostname;\n    } catch  {\n        return \"\";\n    }\n}\n// Calculate reading time\nfunction calculateReadingTime(content) {\n    const wordsPerMinute = 200;\n    const wordCount = countWords(content);\n    return Math.ceil(wordCount / wordsPerMinute);\n}\n// Generate color from string\nfunction generateColorFromString(str) {\n    let hash = 0;\n    for(let i = 0; i < str.length; i++){\n        hash = str.charCodeAt(i) + ((hash << 5) - hash);\n    }\n    const hue = hash % 360;\n    return `hsl(${hue}, 70%, 50%)`;\n}\n// Format file size\nfunction formatFileSize(bytes) {\n    if (bytes === 0) return \"0 Bytes\";\n    const k = 1024;\n    const sizes = [\n        \"Bytes\",\n        \"KB\",\n        \"MB\",\n        \"GB\"\n    ];\n    const i = Math.floor(Math.log(bytes) / Math.log(k));\n    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + \" \" + sizes[i];\n}\n// Additional utility functions for SEO SAAS\nfunction getScoreColor(score) {\n    if (score >= 80) return \"text-green-600\";\n    if (score >= 60) return \"text-yellow-600\";\n    if (score >= 40) return \"text-orange-600\";\n    return \"text-red-600\";\n}\nfunction getScoreBgColor(score) {\n    if (score >= 80) return \"bg-green-100\";\n    if (score >= 60) return \"bg-yellow-100\";\n    if (score >= 40) return \"bg-orange-100\";\n    return \"bg-red-100\";\n}\nfunction getPriorityColor(priority) {\n    switch(priority){\n        case \"high\":\n            return \"text-red-600 bg-red-100\";\n        case \"medium\":\n            return \"text-yellow-600 bg-yellow-100\";\n        case \"low\":\n            return \"text-green-600 bg-green-100\";\n        default:\n            return \"text-gray-600 bg-gray-100\";\n    }\n}\nfunction formatPercentage(num) {\n    return `${num.toFixed(1)}%`;\n}\nfunction copyToClipboard(text) {\n    if (navigator.clipboard) {\n        return navigator.clipboard.writeText(text);\n    }\n    // Fallback for older browsers\n    const textArea = document.createElement(\"textarea\");\n    textArea.value = text;\n    document.body.appendChild(textArea);\n    textArea.select();\n    document.execCommand(\"copy\");\n    document.body.removeChild(textArea);\n    return Promise.resolve();\n}\nfunction downloadAsFile(content, filename, type = \"text/plain\") {\n    const blob = new Blob([\n        content\n    ], {\n        type\n    });\n    const url = URL.createObjectURL(blob);\n    const link = document.createElement(\"a\");\n    link.href = url;\n    link.download = filename;\n    document.body.appendChild(link);\n    link.click();\n    document.body.removeChild(link);\n    URL.revokeObjectURL(url);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./src/lib/utils.ts\n");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__":
/*!**************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__ ***!
  \**************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/lib/metadata/get-metadata-route */ \"(rsc)/./node_modules/next/dist/lib/metadata/get-metadata-route.js\");\n/* harmony import */ var next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__);\n  \n\n  /* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((props) => {\n    const imageData = {\"type\":\"image/x-icon\",\"sizes\":\"16x16\"}\n    const imageUrl = (0,next_dist_lib_metadata_get_metadata_route__WEBPACK_IMPORTED_MODULE_0__.fillMetadataSegment)(\".\", props.params, \"favicon.ico\")\n\n    return [{\n      ...imageData,\n      url: imageUrl + \"\",\n    }]\n  });//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LW1ldGFkYXRhLWltYWdlLWxvYWRlci5qcz90eXBlPWZhdmljb24mc2VnbWVudD0mYmFzZVBhdGg9JnBhZ2VFeHRlbnNpb25zPXRzeCZwYWdlRXh0ZW5zaW9ucz10cyZwYWdlRXh0ZW5zaW9ucz1qc3gmcGFnZUV4dGVuc2lvbnM9anMhLi9zcmMvYXBwL2Zhdmljb24uaWNvP19fbmV4dF9tZXRhZGF0YV9fIiwibWFwcGluZ3MiOiI7Ozs7OztBQUFBLEVBQWlGOztBQUVqRixFQUFFLGlFQUFlO0FBQ2pCLHVCQUF1QjtBQUN2QixxQkFBcUIsOEZBQW1COztBQUV4QztBQUNBO0FBQ0E7QUFDQSxLQUFLO0FBQ0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9zZW8tc2Fhcy8uL3NyYy9hcHAvZmF2aWNvbi5pY28/ZDZmZSJdLCJzb3VyY2VzQ29udGVudCI6WyIgIGltcG9ydCB7IGZpbGxNZXRhZGF0YVNlZ21lbnQgfSBmcm9tICduZXh0L2Rpc3QvbGliL21ldGFkYXRhL2dldC1tZXRhZGF0YS1yb3V0ZSdcblxuICBleHBvcnQgZGVmYXVsdCAocHJvcHMpID0+IHtcbiAgICBjb25zdCBpbWFnZURhdGEgPSB7XCJ0eXBlXCI6XCJpbWFnZS94LWljb25cIixcInNpemVzXCI6XCIxNngxNlwifVxuICAgIGNvbnN0IGltYWdlVXJsID0gZmlsbE1ldGFkYXRhU2VnbWVudChcIi5cIiwgcHJvcHMucGFyYW1zLCBcImZhdmljb24uaWNvXCIpXG5cbiAgICByZXR1cm4gW3tcbiAgICAgIC4uLmltYWdlRGF0YSxcbiAgICAgIHVybDogaW1hZ2VVcmwgKyBcIlwiLFxuICAgIH1dXG4gIH0iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-metadata-image-loader.js?type=favicon&segment=&basePath=&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js!./src/app/favicon.ico?__next_metadata__\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@supabase","vendor-chunks/ws","vendor-chunks/@heroicons","vendor-chunks/@swc","vendor-chunks/whatwg-url","vendor-chunks/jose","vendor-chunks/isows","vendor-chunks/tr46","vendor-chunks/tailwind-merge","vendor-chunks/clsx","vendor-chunks/class-variance-authority","vendor-chunks/set-cookie-parser"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas%5Csrc%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=F%3A%5CClaude-Code-Setup%5CSEO%20SAAS%20APP%5Cseo-saas&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();
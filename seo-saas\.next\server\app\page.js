(()=>{var e={};e.id=931,e.ids=[931],e.modules={7849:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external")},2934:e=>{"use strict";e.exports=require("next/dist/client/components/action-async-storage.external.js")},5403:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external")},4580:e=>{"use strict";e.exports=require("next/dist/client/components/request-async-storage.external.js")},4749:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external")},5869:e=>{"use strict";e.exports=require("next/dist/client/components/static-generation-async-storage.external.js")},399:e=>{"use strict";e.exports=require("next/dist/compiled/next-server/app-page.runtime.prod.js")},4300:e=>{"use strict";e.exports=require("buffer")},6113:e=>{"use strict";e.exports=require("crypto")},2361:e=>{"use strict";e.exports=require("events")},3685:e=>{"use strict";e.exports=require("http")},5687:e=>{"use strict";e.exports=require("https")},1808:e=>{"use strict";e.exports=require("net")},1017:e=>{"use strict";e.exports=require("path")},5477:e=>{"use strict";e.exports=require("punycode")},2781:e=>{"use strict";e.exports=require("stream")},4404:e=>{"use strict";e.exports=require("tls")},7310:e=>{"use strict";e.exports=require("url")},9796:e=>{"use strict";e.exports=require("zlib")},8359:()=>{},3739:()=>{},8960:(e,t,r)=>{"use strict";r.r(t),r.d(t,{GlobalError:()=>a.a,__next_app__:()=>p,originalPathname:()=>u,pages:()=>d,routeModule:()=>m,tree:()=>c});var o=r(482),s=r(9108),n=r(2563),a=r.n(n),i=r(8300),l={};for(let e in i)0>["default","tree","pages","GlobalError","originalPathname","__next_app__","routeModule"].indexOf(e)&&(l[e]=()=>i[e]);r.d(t,l);let c=["",{children:["__PAGE__",{},{page:[()=>Promise.resolve().then(r.bind(r,6258)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/page.tsx"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}]},{layout:[()=>Promise.resolve().then(r.bind(r,1623)),"/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/layout.tsx"],"not-found":[()=>Promise.resolve().then(r.t.bind(r,9361,23)),"next/dist/client/components/not-found-error"],metadata:{icon:[async e=>(await Promise.resolve().then(r.bind(r,3881))).default(e)],apple:[],openGraph:[],twitter:[],manifest:void 0}}],d=["/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/app/page.tsx"],u="/page",p={require:r,loadChunk:()=>Promise.resolve()},m=new o.AppPageRouteModule({definition:{kind:s.x.APP_PAGE,page:"/page",pathname:"/",bundlePath:"",filename:"",appPaths:[]},userland:{loaderTree:c}})},8775:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,2583,23)),Promise.resolve().then(r.t.bind(r,6840,23)),Promise.resolve().then(r.t.bind(r,8771,23)),Promise.resolve().then(r.t.bind(r,3225,23)),Promise.resolve().then(r.t.bind(r,9295,23)),Promise.resolve().then(r.t.bind(r,3982,23))},3056:(e,t,r)=>{Promise.resolve().then(r.t.bind(r,1476,23))},6315:(e,t,r)=>{Promise.resolve().then(r.bind(r,3750))},3750:(e,t,r)=>{"use strict";r.r(t),r.d(t,{AuthProvider:()=>i,useAuth:()=>l,useAuthGuard:()=>d,useSubscription:()=>c});var o=r(2295),s=r(3729),n=r(8704);let a=(0,s.createContext)(void 0);function i({children:e}){let[t,r]=(0,s.useState)(null),[i,l]=(0,s.useState)(null),[c,d]=(0,s.useState)(null),[u,p]=(0,s.useState)(null),[m,h]=(0,s.useState)(!0),x=(0,n.R9)(),g=async e=>{try{let{data:t,error:r}=await x.from("profiles").select("*").eq("id",e).single();if(r)return console.error("Error fetching profile:",r),null;return t}catch(e){return console.error("Error fetching profile:",e),null}},f=async e=>{try{let{data:t,error:r}=await x.from("user_subscriptions").select("*").eq("user_id",e).single();if(r&&"PGRST116"!==r.code)return console.error("Error fetching subscription:",r),null;return t}catch(e){return console.error("Error fetching subscription:",e),null}};(0,s.useEffect)(()=>{(async()=>{try{let{data:{session:e},error:t}=await x.auth.getSession();if(t){console.error("Error getting session:",t),h(!1);return}if(e?.user){l(e),r(e.user);let[t,o]=await Promise.all([g(e.user.id),f(e.user.id)]);d(t),p(o)}}catch(e){console.error("Error initializing auth:",e)}finally{h(!1)}})();let{data:{subscription:e}}=x.auth.onAuthStateChange(async(e,t)=>{if(console.log("Auth state changed:",e,t?.user?.id),l(t),r(t?.user??null),t?.user){let[e,r]=await Promise.all([g(t.user.id),f(t.user.id)]);d(e),p(r)}else d(null),p(null);h(!1)});return()=>{e.unsubscribe()}},[]);let b=async(e,t)=>{let{error:r}=await x.auth.signInWithPassword({email:e,password:t});if(r)throw Error(r.message)},v=async(e,t,r)=>{let{error:o}=await x.auth.signUp({email:e,password:t,options:{data:{full_name:r}}});if(o)throw Error(o.message)},y=async()=>{let{error:e}=await x.auth.signOut();if(e)throw Error(e.message)},w=async e=>{let{error:t}=await x.auth.resetPasswordForEmail(e,{redirectTo:`${window.location.origin}/auth/reset-password`});if(t)throw Error(t.message)},j=async e=>{if(!t)throw Error("No user logged in");let{data:r,error:o}=await x.from("profiles").update({...e,updated_at:new Date().toISOString()}).eq("id",t.id).select().single();if(o)throw Error(o.message);d(r)},k=async()=>{t&&d(await g(t.id))},S=async()=>{t&&p(await f(t.id))};return o.jsx(a.Provider,{value:{user:t,session:i,profile:c,subscription:u,loading:m,signIn:b,signUp:v,signOut:y,resetPassword:w,updateProfile:j,refreshProfile:k,refreshSubscription:S},children:e})}function l(){let e=(0,s.useContext)(a);if(void 0===e)throw Error("useAuth must be used within an AuthProvider");return e}function c(){let{subscription:e}=l(),t=e?.status==="active",r=e?.status==="trialing",o=e?.status==="past_due",s=e?.status==="cancelled";return{subscription:e,hasActiveSubscription:t,isOnTrial:r,isPastDue:o,isCancelled:s,canAccessFeature:r=>!!e&&("pro"===r?["pro","enterprise"].includes(e.plan_type)&&t:"enterprise"===r&&"enterprise"===e.plan_type&&t)}}function d(){let{user:e,loading:t}=l();return{isAuthenticated:!!e,isLoading:t,user:e}}},8704:(e,t,r)=>{"use strict";r.d(t,{OQ:()=>n,R9:()=>a});var o=r(6300),s=r(7435);let n=(0,o.eI)("https://zqrmpanonghggoxdjirq.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.Bj-bxzx868RJvHQq9wvaHldBXKFbPksByk7UyLJ__lY",{auth:{persistSession:!0,autoRefreshToken:!0,detectSessionInUrl:!0},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas@1.0.0"}}}),a=()=>(0,s.createClientComponentClient)();(0,o.eI)("https://zqrmpanonghggoxdjirq.supabase.co",process.env.SUPABASE_SERVICE_ROLE_KEY,{auth:{autoRefreshToken:!1,persistSession:!1},db:{schema:"public"},global:{headers:{"X-Client-Info":"seo-saas-admin@1.0.0"}}})},8026:(e,t,r)=>{let{createProxy:o}=r(6843);e.exports=o("/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/node_modules/next/dist/client/link.js")},646:(e,t,r)=>{e.exports=r(8026)},1623:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>p,metadata:()=>u});var o=r(5036),s=r(5968),n=r.n(s);r(5023);var a=r(6843);let i=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx`),{__esModule:l,$$typeof:c}=i;i.default;let d=(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#AuthProvider`);(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useAuth`),(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useSubscription`),(0,a.createProxy)(String.raw`/mnt/f/Claude-Code-Setup/SEO SAAS APP/seo-saas/src/contexts/auth-context.tsx#useAuthGuard`);let u={title:"SEO Content Generator - Professional SEO Content Creation",description:"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.",keywords:["SEO","content generation","AI writing","keyword optimization","competitor analysis"],authors:[{name:"SEO Content Generator"}],openGraph:{title:"SEO Content Generator - Professional SEO Content Creation",description:"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results.",type:"website",locale:"en_US"},twitter:{card:"summary_large_image",title:"SEO Content Generator - Professional SEO Content Creation",description:"Generate high-quality, SEO-optimized content with AI. Analyze competitors, optimize keywords, and create content that ranks higher in search results."},robots:{index:!0,follow:!0},viewport:{width:"device-width",initialScale:1}};function p({children:e}){return o.jsx("html",{lang:"en",className:n().variable,children:o.jsx("body",{className:"font-sans antialiased bg-gray-50 text-gray-900",children:o.jsx(d,{children:e})})})}},6258:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>eb});var o,s,n=r(5036),a=r(646),i=r.n(a),l=r(2);function c(){for(var e,t,r=0,o="",s=arguments.length;r<s;r++)(e=arguments[r])&&(t=function e(t){var r,o,s="";if("string"==typeof t||"number"==typeof t)s+=t;else if("object"==typeof t){if(Array.isArray(t)){var n=t.length;for(r=0;r<n;r++)t[r]&&(o=e(t[r]))&&(s&&(s+=" "),s+=o)}else for(o in t)t[o]&&(s&&(s+=" "),s+=o)}return s}(e))&&(o&&(o+=" "),o+=t);return o}let d=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,u=e=>{let t=x(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:o}=e;return{getClassGroupId:e=>{let r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),p(r,t)||h(e)},getConflictingClassGroupIds:(e,t)=>{let s=r[e]||[];return t&&o[e]?[...s,...o[e]]:s}}},p=(e,t)=>{if(0===e.length)return t.classGroupId;let r=e[0],o=t.nextPart.get(r),s=o?p(e.slice(1),o):void 0;if(s)return s;if(0===t.validators.length)return;let n=e.join("-");return t.validators.find(({validator:e})=>e(n))?.classGroupId},m=/^\[(.+)\]$/,h=e=>{if(m.test(e)){let t=m.exec(e)[1],r=t?.substring(0,t.indexOf(":"));if(r)return"arbitrary.."+r}},x=e=>{let{theme:t,prefix:r}=e,o={nextPart:new Map,validators:[]};return v(Object.entries(e.classGroups),r).forEach(([e,r])=>{g(r,o,e,t)}),o},g=(e,t,r,o)=>{e.forEach(e=>{if("string"==typeof e){(""===e?t:f(t,e)).classGroupId=r;return}if("function"==typeof e){if(b(e)){g(e(o),t,r,o);return}t.validators.push({validator:e,classGroupId:r});return}Object.entries(e).forEach(([e,s])=>{g(s,f(t,e),r,o)})})},f=(e,t)=>{let r=e;return t.split("-").forEach(e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)}),r},b=e=>e.isThemeGetter,v=(e,t)=>t?e.map(([e,r])=>[e,r.map(e=>"string"==typeof e?t+e:"object"==typeof e?Object.fromEntries(Object.entries(e).map(([e,r])=>[t+e,r])):e)]):e,y=e=>{if(e<1)return{get:()=>void 0,set:()=>{}};let t=0,r=new Map,o=new Map,s=(s,n)=>{r.set(s,n),++t>e&&(t=0,o=r,r=new Map)};return{get(e){let t=r.get(e);return void 0!==t?t:void 0!==(t=o.get(e))?(s(e,t),t):void 0},set(e,t){r.has(e)?r.set(e,t):s(e,t)}}},w=e=>{let{separator:t,experimentalParseClassName:r}=e,o=1===t.length,s=t[0],n=t.length,a=e=>{let r;let a=[],i=0,l=0;for(let c=0;c<e.length;c++){let d=e[c];if(0===i){if(d===s&&(o||e.slice(c,c+n)===t)){a.push(e.slice(l,c)),l=c+n;continue}if("/"===d){r=c;continue}}"["===d?i++:"]"===d&&i--}let c=0===a.length?e:e.substring(l),d=c.startsWith("!"),u=d?c.substring(1):c;return{modifiers:a,hasImportantModifier:d,baseClassName:u,maybePostfixModifierPosition:r&&r>l?r-l:void 0}};return r?e=>r({className:e,parseClassName:a}):a},j=e=>{if(e.length<=1)return e;let t=[],r=[];return e.forEach(e=>{"["===e[0]?(t.push(...r.sort(),e),r=[]):r.push(e)}),t.push(...r.sort()),t},k=e=>({cache:y(e.cacheSize),parseClassName:w(e),...u(e)}),S=/\s+/,N=(e,t)=>{let{parseClassName:r,getClassGroupId:o,getConflictingClassGroupIds:s}=t,n=[],a=e.trim().split(S),i="";for(let e=a.length-1;e>=0;e-=1){let t=a[e],{modifiers:l,hasImportantModifier:c,baseClassName:d,maybePostfixModifierPosition:u}=r(t),p=!!u,m=o(p?d.substring(0,u):d);if(!m){if(!p||!(m=o(d))){i=t+(i.length>0?" "+i:i);continue}p=!1}let h=j(l).join(":"),x=c?h+"!":h,g=x+m;if(n.includes(g))continue;n.push(g);let f=s(m,p);for(let e=0;e<f.length;++e){let t=f[e];n.push(x+t)}i=t+(i.length>0?" "+i:i)}return i};function E(){let e,t,r=0,o="";for(;r<arguments.length;)(e=arguments[r++])&&(t=C(e))&&(o&&(o+=" "),o+=t);return o}let C=e=>{let t;if("string"==typeof e)return e;let r="";for(let o=0;o<e.length;o++)e[o]&&(t=C(e[o]))&&(r&&(r+=" "),r+=t);return r},P=e=>{let t=t=>t[e]||[];return t.isThemeGetter=!0,t},z=/^\[(?:([a-z-]+):)?(.+)\]$/i,A=/^\d+\/\d+$/,O=new Set(["px","full","screen"]),I=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,q=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,G=/^(rgba?|hsla?|hwb|(ok)?(lab|lch))\(.+\)$/,R=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,_=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,M=e=>B(e)||O.has(e)||A.test(e),L=e=>Q(e,"length",K),B=e=>!!e&&!Number.isNaN(Number(e)),T=e=>Q(e,"number",B),Z=e=>!!e&&Number.isInteger(Number(e)),W=e=>e.endsWith("%")&&B(e.slice(0,-1)),J=e=>z.test(e),V=e=>I.test(e),$=new Set(["length","size","percentage"]),D=e=>Q(e,$,ee),F=e=>Q(e,"position",ee),H=new Set(["image","url"]),X=e=>Q(e,H,er),Y=e=>Q(e,"",et),U=()=>!0,Q=(e,t,r)=>{let o=z.exec(e);return!!o&&(o[1]?"string"==typeof t?o[1]===t:t.has(o[1]):r(o[2]))},K=e=>q.test(e)&&!G.test(e),ee=()=>!1,et=e=>R.test(e),er=e=>_.test(e);Symbol.toStringTag;let eo=function(e){let t,r,o;let s=function(a){return r=(t=k([].reduce((e,t)=>t(e),e()))).cache.get,o=t.cache.set,s=n,n(a)};function n(e){let s=r(e);if(s)return s;let n=N(e,t);return o(e,n),n}return function(){return s(E.apply(null,arguments))}}(()=>{let e=P("colors"),t=P("spacing"),r=P("blur"),o=P("brightness"),s=P("borderColor"),n=P("borderRadius"),a=P("borderSpacing"),i=P("borderWidth"),l=P("contrast"),c=P("grayscale"),d=P("hueRotate"),u=P("invert"),p=P("gap"),m=P("gradientColorStops"),h=P("gradientColorStopPositions"),x=P("inset"),g=P("margin"),f=P("opacity"),b=P("padding"),v=P("saturate"),y=P("scale"),w=P("sepia"),j=P("skew"),k=P("space"),S=P("translate"),N=()=>["auto","contain","none"],E=()=>["auto","hidden","clip","visible","scroll"],C=()=>["auto",J,t],z=()=>[J,t],A=()=>["",M,L],O=()=>["auto",B,J],I=()=>["bottom","center","left","left-bottom","left-top","right","right-bottom","right-top","top"],q=()=>["solid","dashed","dotted","double","none"],G=()=>["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"],R=()=>["start","end","center","between","around","evenly","stretch"],_=()=>["","0",J],$=()=>["auto","avoid","all","avoid-page","page","left","right","column"],H=()=>[B,J];return{cacheSize:500,separator:":",theme:{colors:[U],spacing:[M,L],blur:["none","",V,J],brightness:H(),borderColor:[e],borderRadius:["none","","full",V,J],borderSpacing:z(),borderWidth:A(),contrast:H(),grayscale:_(),hueRotate:H(),invert:_(),gap:z(),gradientColorStops:[e],gradientColorStopPositions:[W,L],inset:C(),margin:C(),opacity:H(),padding:z(),saturate:H(),scale:H(),sepia:_(),skew:H(),space:z(),translate:z()},classGroups:{aspect:[{aspect:["auto","square","video",J]}],container:["container"],columns:[{columns:[V]}],"break-after":[{"break-after":$()}],"break-before":[{"break-before":$()}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:[...I(),J]}],overflow:[{overflow:E()}],"overflow-x":[{"overflow-x":E()}],"overflow-y":[{"overflow-y":E()}],overscroll:[{overscroll:N()}],"overscroll-x":[{"overscroll-x":N()}],"overscroll-y":[{"overscroll-y":N()}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:[x]}],"inset-x":[{"inset-x":[x]}],"inset-y":[{"inset-y":[x]}],start:[{start:[x]}],end:[{end:[x]}],top:[{top:[x]}],right:[{right:[x]}],bottom:[{bottom:[x]}],left:[{left:[x]}],visibility:["visible","invisible","collapse"],z:[{z:["auto",Z,J]}],basis:[{basis:C()}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["wrap","wrap-reverse","nowrap"]}],flex:[{flex:["1","auto","initial","none",J]}],grow:[{grow:_()}],shrink:[{shrink:_()}],order:[{order:["first","last","none",Z,J]}],"grid-cols":[{"grid-cols":[U]}],"col-start-end":[{col:["auto",{span:["full",Z,J]},J]}],"col-start":[{"col-start":O()}],"col-end":[{"col-end":O()}],"grid-rows":[{"grid-rows":[U]}],"row-start-end":[{row:["auto",{span:[Z,J]},J]}],"row-start":[{"row-start":O()}],"row-end":[{"row-end":O()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":["auto","min","max","fr",J]}],"auto-rows":[{"auto-rows":["auto","min","max","fr",J]}],gap:[{gap:[p]}],"gap-x":[{"gap-x":[p]}],"gap-y":[{"gap-y":[p]}],"justify-content":[{justify:["normal",...R()]}],"justify-items":[{"justify-items":["start","end","center","stretch"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch"]}],"align-content":[{content:["normal",...R(),"baseline"]}],"align-items":[{items:["start","end","center","baseline","stretch"]}],"align-self":[{self:["auto","start","end","center","stretch","baseline"]}],"place-content":[{"place-content":[...R(),"baseline"]}],"place-items":[{"place-items":["start","end","center","baseline","stretch"]}],"place-self":[{"place-self":["auto","start","end","center","stretch"]}],p:[{p:[b]}],px:[{px:[b]}],py:[{py:[b]}],ps:[{ps:[b]}],pe:[{pe:[b]}],pt:[{pt:[b]}],pr:[{pr:[b]}],pb:[{pb:[b]}],pl:[{pl:[b]}],m:[{m:[g]}],mx:[{mx:[g]}],my:[{my:[g]}],ms:[{ms:[g]}],me:[{me:[g]}],mt:[{mt:[g]}],mr:[{mr:[g]}],mb:[{mb:[g]}],ml:[{ml:[g]}],"space-x":[{"space-x":[k]}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":[k]}],"space-y-reverse":["space-y-reverse"],w:[{w:["auto","min","max","fit","svw","lvw","dvw",J,t]}],"min-w":[{"min-w":[J,t,"min","max","fit"]}],"max-w":[{"max-w":[J,t,"none","full","min","max","fit","prose",{screen:[V]},V]}],h:[{h:[J,t,"auto","min","max","fit","svh","lvh","dvh"]}],"min-h":[{"min-h":[J,t,"min","max","fit","svh","lvh","dvh"]}],"max-h":[{"max-h":[J,t,"min","max","fit","svh","lvh","dvh"]}],size:[{size:[J,t,"auto","min","max","fit"]}],"font-size":[{text:["base",V,L]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:["thin","extralight","light","normal","medium","semibold","bold","extrabold","black",T]}],"font-family":[{font:[U]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:["tighter","tight","normal","wide","wider","widest",J]}],"line-clamp":[{"line-clamp":["none",B,T]}],leading:[{leading:["none","tight","snug","normal","relaxed","loose",M,J]}],"list-image":[{"list-image":["none",J]}],"list-style-type":[{list:["none","disc","decimal",J]}],"list-style-position":[{list:["inside","outside"]}],"placeholder-color":[{placeholder:[e]}],"placeholder-opacity":[{"placeholder-opacity":[f]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"text-color":[{text:[e]}],"text-opacity":[{"text-opacity":[f]}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:[...q(),"wavy"]}],"text-decoration-thickness":[{decoration:["auto","from-font",M,L]}],"underline-offset":[{"underline-offset":["auto",M,J]}],"text-decoration-color":[{decoration:[e]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:z()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",J]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",J]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-opacity":[{"bg-opacity":[f]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:[...I(),F]}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","round","space"]}]}],"bg-size":[{bg:["auto","cover","contain",D]}],"bg-image":[{bg:["none",{"gradient-to":["t","tr","r","br","b","bl","l","tl"]},X]}],"bg-color":[{bg:[e]}],"gradient-from-pos":[{from:[h]}],"gradient-via-pos":[{via:[h]}],"gradient-to-pos":[{to:[h]}],"gradient-from":[{from:[m]}],"gradient-via":[{via:[m]}],"gradient-to":[{to:[m]}],rounded:[{rounded:[n]}],"rounded-s":[{"rounded-s":[n]}],"rounded-e":[{"rounded-e":[n]}],"rounded-t":[{"rounded-t":[n]}],"rounded-r":[{"rounded-r":[n]}],"rounded-b":[{"rounded-b":[n]}],"rounded-l":[{"rounded-l":[n]}],"rounded-ss":[{"rounded-ss":[n]}],"rounded-se":[{"rounded-se":[n]}],"rounded-ee":[{"rounded-ee":[n]}],"rounded-es":[{"rounded-es":[n]}],"rounded-tl":[{"rounded-tl":[n]}],"rounded-tr":[{"rounded-tr":[n]}],"rounded-br":[{"rounded-br":[n]}],"rounded-bl":[{"rounded-bl":[n]}],"border-w":[{border:[i]}],"border-w-x":[{"border-x":[i]}],"border-w-y":[{"border-y":[i]}],"border-w-s":[{"border-s":[i]}],"border-w-e":[{"border-e":[i]}],"border-w-t":[{"border-t":[i]}],"border-w-r":[{"border-r":[i]}],"border-w-b":[{"border-b":[i]}],"border-w-l":[{"border-l":[i]}],"border-opacity":[{"border-opacity":[f]}],"border-style":[{border:[...q(),"hidden"]}],"divide-x":[{"divide-x":[i]}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":[i]}],"divide-y-reverse":["divide-y-reverse"],"divide-opacity":[{"divide-opacity":[f]}],"divide-style":[{divide:q()}],"border-color":[{border:[s]}],"border-color-x":[{"border-x":[s]}],"border-color-y":[{"border-y":[s]}],"border-color-s":[{"border-s":[s]}],"border-color-e":[{"border-e":[s]}],"border-color-t":[{"border-t":[s]}],"border-color-r":[{"border-r":[s]}],"border-color-b":[{"border-b":[s]}],"border-color-l":[{"border-l":[s]}],"divide-color":[{divide:[s]}],"outline-style":[{outline:["",...q()]}],"outline-offset":[{"outline-offset":[M,J]}],"outline-w":[{outline:[M,L]}],"outline-color":[{outline:[e]}],"ring-w":[{ring:A()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:[e]}],"ring-opacity":[{"ring-opacity":[f]}],"ring-offset-w":[{"ring-offset":[M,L]}],"ring-offset-color":[{"ring-offset":[e]}],shadow:[{shadow:["","inner","none",V,Y]}],"shadow-color":[{shadow:[U]}],opacity:[{opacity:[f]}],"mix-blend":[{"mix-blend":[...G(),"plus-lighter","plus-darker"]}],"bg-blend":[{"bg-blend":G()}],filter:[{filter:["","none"]}],blur:[{blur:[r]}],brightness:[{brightness:[o]}],contrast:[{contrast:[l]}],"drop-shadow":[{"drop-shadow":["","none",V,J]}],grayscale:[{grayscale:[c]}],"hue-rotate":[{"hue-rotate":[d]}],invert:[{invert:[u]}],saturate:[{saturate:[v]}],sepia:[{sepia:[w]}],"backdrop-filter":[{"backdrop-filter":["","none"]}],"backdrop-blur":[{"backdrop-blur":[r]}],"backdrop-brightness":[{"backdrop-brightness":[o]}],"backdrop-contrast":[{"backdrop-contrast":[l]}],"backdrop-grayscale":[{"backdrop-grayscale":[c]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[d]}],"backdrop-invert":[{"backdrop-invert":[u]}],"backdrop-opacity":[{"backdrop-opacity":[f]}],"backdrop-saturate":[{"backdrop-saturate":[v]}],"backdrop-sepia":[{"backdrop-sepia":[w]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":[a]}],"border-spacing-x":[{"border-spacing-x":[a]}],"border-spacing-y":[{"border-spacing-y":[a]}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["none","all","","colors","opacity","shadow","transform",J]}],duration:[{duration:H()}],ease:[{ease:["linear","in","out","in-out",J]}],delay:[{delay:H()}],animate:[{animate:["none","spin","ping","pulse","bounce",J]}],transform:[{transform:["","gpu","none"]}],scale:[{scale:[y]}],"scale-x":[{"scale-x":[y]}],"scale-y":[{"scale-y":[y]}],rotate:[{rotate:[Z,J]}],"translate-x":[{"translate-x":[S]}],"translate-y":[{"translate-y":[S]}],"skew-x":[{"skew-x":[j]}],"skew-y":[{"skew-y":[j]}],"transform-origin":[{origin:["center","top","top-right","right","bottom-right","bottom","bottom-left","left","top-left",J]}],accent:[{accent:["auto",e]}],appearance:[{appearance:["none","auto"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",J]}],"caret-color":[{caret:[e]}],"pointer-events":[{"pointer-events":["none","auto"]}],resize:[{resize:["none","y","x",""]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":z()}],"scroll-mx":[{"scroll-mx":z()}],"scroll-my":[{"scroll-my":z()}],"scroll-ms":[{"scroll-ms":z()}],"scroll-me":[{"scroll-me":z()}],"scroll-mt":[{"scroll-mt":z()}],"scroll-mr":[{"scroll-mr":z()}],"scroll-mb":[{"scroll-mb":z()}],"scroll-ml":[{"scroll-ml":z()}],"scroll-p":[{"scroll-p":z()}],"scroll-px":[{"scroll-px":z()}],"scroll-py":[{"scroll-py":z()}],"scroll-ps":[{"scroll-ps":z()}],"scroll-pe":[{"scroll-pe":z()}],"scroll-pt":[{"scroll-pt":z()}],"scroll-pr":[{"scroll-pr":z()}],"scroll-pb":[{"scroll-pb":z()}],"scroll-pl":[{"scroll-pl":z()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",J]}],fill:[{fill:[e,"none"]}],"stroke-w":[{stroke:[M,L,T]}],stroke:[{stroke:[e,"none"]}],sr:["sr-only","not-sr-only"],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]}}});function es(...e){return eo(c(e))}let en=(o="inline-flex items-center justify-center rounded-md text-sm font-medium transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:opacity-50 disabled:pointer-events-none ring-offset-background",s={variants:{variant:{default:"bg-primary text-primary-foreground hover:bg-primary/90",destructive:"bg-destructive text-destructive-foreground hover:bg-destructive/90",outline:"border border-input hover:bg-accent hover:text-accent-foreground",secondary:"bg-secondary text-secondary-foreground hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground",link:"underline-offset-4 hover:underline text-primary"},size:{default:"h-10 py-2 px-4",sm:"h-9 px-3 rounded-md",lg:"h-11 px-8 rounded-md",icon:"h-10 w-10"}},defaultVariants:{variant:"default",size:"default"}},e=>{var t;if((null==s?void 0:s.variants)==null)return c(o,null==e?void 0:e.class,null==e?void 0:e.className);let{variants:r,defaultVariants:n}=s,a=Object.keys(r).map(t=>{let o=null==e?void 0:e[t],s=null==n?void 0:n[t];if(null===o)return null;let a=d(o)||d(s);return r[t][a]}),i=e&&Object.entries(e).reduce((e,t)=>{let[r,o]=t;return void 0===o||(e[r]=o),e},{});return c(o,a,null==s?void 0:null===(t=s.compoundVariants)||void 0===t?void 0:t.reduce((e,t)=>{let{class:r,className:o,...s}=t;return Object.entries(s).every(e=>{let[t,r]=e;return Array.isArray(r)?r.includes({...n,...i}[t]):({...n,...i})[t]===r})?[...e,r,o]:e},[]),null==e?void 0:e.class,null==e?void 0:e.className)}),ea=l.forwardRef(({className:e,variant:t,size:r,loading:o,children:s,disabled:a,...i},l)=>(0,n.jsxs)("button",{className:es(en({variant:t,size:r,className:e})),ref:l,disabled:a||o,...i,children:[o&&(0,n.jsxs)("svg",{className:"animate-spin -ml-1 mr-3 h-4 w-4",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[n.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),n.jsx("path",{className:"opacity-75",fill:"currentColor",d:"m4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),s]}));ea.displayName="Button";let ei=l.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:es("rounded-lg border bg-card text-card-foreground shadow-sm",e),...t}));ei.displayName="Card";let el=l.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:es("flex flex-col space-y-1.5 p-6",e),...t}));el.displayName="CardHeader";let ec=l.forwardRef(({className:e,...t},r)=>n.jsx("h3",{ref:r,className:es("text-2xl font-semibold leading-none tracking-tight",e),...t}));ec.displayName="CardTitle";let ed=l.forwardRef(({className:e,...t},r)=>n.jsx("p",{ref:r,className:es("text-sm text-muted-foreground",e),...t}));ed.displayName="CardDescription";let eu=l.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:es("p-6 pt-0",e),...t}));eu.displayName="CardContent",l.forwardRef(({className:e,...t},r)=>n.jsx("div",{ref:r,className:es("flex items-center p-6 pt-0",e),...t})).displayName="CardFooter";let ep=l.forwardRef(function({title:e,titleId:t,...r},o){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M19.5 14.25v-2.625a3.375 3.375 0 0 0-3.375-3.375h-1.5A1.125 1.125 0 0 1 13.5 7.125v-1.5a3.375 3.375 0 0 0-3.375-3.375H8.25m0 12.75h7.5m-7.5 3H12M10.5 2.25H5.625c-.621 0-1.125.504-1.125 1.125v17.25c0 .621.504 1.125 1.125 1.125h12.75c.621 0 1.125-.504 1.125-1.125V11.25a9 9 0 0 0-9-9Z"}))}),em=l.forwardRef(function({title:e,titleId:t,...r},o){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M3 13.125C3 12.504 3.504 12 4.125 12h2.25c.621 0 1.125.504 1.125 1.125v6.75C7.5 20.496 6.996 21 6.375 21h-2.25A1.125 1.125 0 0 1 3 19.875v-6.75ZM9.75 8.625c0-.621.504-1.125 1.125-1.125h2.25c.621 0 1.125.504 1.125 1.125v11.25c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V8.625ZM16.5 4.125c0-.621.504-1.125 1.125-1.125h2.25C20.496 3 21 3.504 21 4.125v15.75c0 .621-.504 1.125-1.125 1.125h-2.25a1.125 1.125 0 0 1-1.125-1.125V4.125Z"}))}),eh=l.forwardRef(function({title:e,titleId:t,...r},o){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z"}))}),ex=l.forwardRef(function({title:e,titleId:t,...r},o){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z"}))}),eg=l.forwardRef(function({title:e,titleId:t,...r},o){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M13.5 4.5 21 12m0 0-7.5 7.5M21 12H3"}))}),ef=l.forwardRef(function({title:e,titleId:t,...r},o){return l.createElement("svg",Object.assign({xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",strokeWidth:1.5,stroke:"currentColor","aria-hidden":"true","data-slot":"icon",ref:o,"aria-labelledby":t},r),e?l.createElement("title",{id:t},e):null,l.createElement("path",{strokeLinecap:"round",strokeLinejoin:"round",d:"M9 12.75 11.25 15 15 9.75M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"}))});function eb(){return(0,n.jsxs)("div",{className:"min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50",children:[n.jsx("header",{className:"border-b bg-white/80 backdrop-blur-sm sticky top-0 z-50",children:n.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"flex justify-between items-center h-16",children:[n.jsx("div",{className:"flex items-center",children:n.jsx("h1",{className:"text-2xl font-bold text-gray-900",children:"SEO Pro"})}),(0,n.jsxs)("div",{className:"flex items-center space-x-4",children:[n.jsx(i(),{href:"/auth/signin",children:n.jsx(ea,{variant:"ghost",children:"Sign In"})}),n.jsx(i(),{href:"/auth/signup",children:n.jsx(ea,{children:"Get Started"})})]})]})})}),n.jsx("section",{className:"py-20 px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto text-center",children:[(0,n.jsxs)("h1",{className:"text-5xl md:text-6xl font-bold text-gray-900 mb-6",children:["Generate SEO Content That",n.jsx("span",{className:"text-blue-600 block",children:"Ranks #1 on Google"})]}),n.jsx("p",{className:"text-xl text-gray-600 mb-8 max-w-3xl mx-auto",children:"Create high-quality, SEO-optimized content with our advanced AI engine. Analyze competitors, optimize keywords, and generate content that outranks the competition."}),(0,n.jsxs)("div",{className:"flex flex-col sm:flex-row gap-4 justify-center",children:[n.jsx(i(),{href:"/auth/signup",children:(0,n.jsxs)(ea,{size:"lg",className:"text-lg px-8 py-3",children:["Start Free Trial",n.jsx(eg,{className:"ml-2 h-5 w-5"})]})}),n.jsx(i(),{href:"/dashboard",children:n.jsx(ea,{variant:"outline",size:"lg",className:"text-lg px-8 py-3",children:"View Demo"})})]})]})}),n.jsx("section",{className:"py-20 bg-white",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"text-center mb-16",children:[n.jsx("h2",{className:"text-4xl font-bold text-gray-900 mb-4",children:"Everything You Need to Dominate Search Rankings"}),n.jsx("p",{className:"text-xl text-gray-600 max-w-3xl mx-auto",children:"Our comprehensive SEO platform combines AI content generation with advanced competitor analysis"})]}),n.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8",children:[{icon:ep,title:"AI Content Generation",description:"Generate SEO-optimized content with advanced AI that understands your industry and target audience."},{icon:em,title:"Competitor Analysis",description:"Analyze top-ranking competitors and identify content gaps to outrank them in search results."},{icon:eh,title:"Multi-Industry Support",description:"Specialized templates and optimization for technology, healthcare, finance, and 10+ other industries."},{icon:ex,title:"Advanced SEO Engine",description:"Comprehensive SEO analysis with keyword density, heading optimization, and LSI keyword extraction."}].map((e,t)=>(0,n.jsxs)(ei,{className:"text-center hover:shadow-lg transition-shadow",children:[(0,n.jsxs)(el,{children:[n.jsx("div",{className:"mx-auto w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center mb-4",children:n.jsx(e.icon,{className:"h-6 w-6 text-blue-600"})}),n.jsx(ec,{className:"text-xl",children:e.title})]}),n.jsx(eu,{children:n.jsx(ed,{className:"text-gray-600",children:e.description})})]},t))})]})}),n.jsx("section",{className:"py-20 bg-gray-50",children:n.jsx("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:(0,n.jsxs)("div",{className:"grid grid-cols-1 lg:grid-cols-2 gap-12 items-center",children:[(0,n.jsxs)("div",{children:[n.jsx("h2",{className:"text-4xl font-bold text-gray-900 mb-6",children:"Why Choose SEO Pro?"}),n.jsx("p",{className:"text-lg text-gray-600 mb-8",children:"Join thousands of content creators, marketers, and businesses who trust our platform to generate high-ranking content that drives organic traffic and conversions."}),n.jsx("div",{className:"space-y-4",children:["Generate 1000+ word SEO-optimized content in under 2 minutes","Analyze competitor strategies and identify ranking opportunities","Built-in E-E-A-T compliance for Google's quality guidelines","Real-time SERP analysis and keyword research","Professional content templates for all industries","Advanced NLP and semantic keyword optimization"].map((e,t)=>(0,n.jsxs)("div",{className:"flex items-start space-x-3",children:[n.jsx(ef,{className:"h-6 w-6 text-green-500 flex-shrink-0 mt-0.5"}),n.jsx("span",{className:"text-gray-700",children:e})]},t))})]}),n.jsx("div",{className:"lg:pl-8",children:(0,n.jsxs)(ei,{className:"p-8 bg-gradient-to-br from-blue-500 to-purple-600 text-white",children:[(0,n.jsxs)(el,{className:"text-center pb-6",children:[n.jsx(ec,{className:"text-3xl font-bold text-white",children:"Ready to Get Started?"}),n.jsx(ed,{className:"text-blue-100 text-lg",children:"Join our free trial and see the results for yourself"})]}),n.jsx(eu,{className:"text-center",children:(0,n.jsxs)("div",{className:"space-y-4",children:[n.jsx("div",{className:"text-4xl font-bold",children:"$0"}),n.jsx("div",{className:"text-blue-100",children:"14-day free trial"}),n.jsx("div",{className:"text-blue-100",children:"No credit card required"}),n.jsx(i(),{href:"/auth/signup",children:n.jsx(ea,{size:"lg",variant:"secondary",className:"w-full mt-6",children:"Start Your Free Trial"})})]})})]})})]})})}),n.jsx("footer",{className:"bg-gray-900 text-white py-12",children:(0,n.jsxs)("div",{className:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8",children:[(0,n.jsxs)("div",{className:"grid grid-cols-1 md:grid-cols-4 gap-8",children:[(0,n.jsxs)("div",{children:[n.jsx("h3",{className:"text-xl font-bold mb-4",children:"SEO Pro"}),n.jsx("p",{className:"text-gray-400",children:"Professional SEO content generation platform powered by advanced AI."})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-semibold mb-4",children:"Product"}),(0,n.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[n.jsx("li",{children:n.jsx(i(),{href:"/features",className:"hover:text-white",children:"Features"})}),n.jsx("li",{children:n.jsx(i(),{href:"/pricing",className:"hover:text-white",children:"Pricing"})}),n.jsx("li",{children:n.jsx(i(),{href:"/demo",className:"hover:text-white",children:"Demo"})})]})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-semibold mb-4",children:"Company"}),(0,n.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[n.jsx("li",{children:n.jsx(i(),{href:"/about",className:"hover:text-white",children:"About"})}),n.jsx("li",{children:n.jsx(i(),{href:"/contact",className:"hover:text-white",children:"Contact"})}),n.jsx("li",{children:n.jsx(i(),{href:"/blog",className:"hover:text-white",children:"Blog"})})]})]}),(0,n.jsxs)("div",{children:[n.jsx("h4",{className:"font-semibold mb-4",children:"Support"}),(0,n.jsxs)("ul",{className:"space-y-2 text-gray-400",children:[n.jsx("li",{children:n.jsx(i(),{href:"/help",className:"hover:text-white",children:"Help Center"})}),n.jsx("li",{children:n.jsx(i(),{href:"/docs",className:"hover:text-white",children:"Documentation"})}),n.jsx("li",{children:n.jsx(i(),{href:"/api",className:"hover:text-white",children:"API"})})]})]})]}),n.jsx("div",{className:"border-t border-gray-800 mt-8 pt-8 text-center text-gray-400",children:n.jsx("p",{children:"\xa9 2024 SEO Pro. All rights reserved."})})]})})]})}},3881:(e,t,r)=>{"use strict";r.r(t),r.d(t,{default:()=>s});var o=r(337);let s=e=>[{type:"image/x-icon",sizes:"16x16",url:(0,o.fillMetadataSegment)(".",e.params,"favicon.ico")+""}]},5023:()=>{}};var t=require("../webpack-runtime.js");t.C(e);var r=e=>t(t.s=e),o=t.X(0,[638,575,337,476],()=>r(8960));module.exports=o})();